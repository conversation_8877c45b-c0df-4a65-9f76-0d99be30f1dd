ARG NODEJS_VERSION=18.20.7
ARG ALPINE_VERSION=3.20
ARG SECRETS_INIT_IMAGE=europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine

ARG ALPINE_BASE_IMAGE=alpine:${ALPINE_VERSION}
ARG NODEJS_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}


FROM ${SECRETS_INIT_IMAGE} AS secrets-init

#
# ---- Base Node ----
FROM ${ALPINE_BASE_IMAGE} AS base
RUN    apk add --no-cache \
         bash \
         nodejs-current \
         tini \
         libstdc++ dumb-init
RUN    addgroup -g 1000 allo && adduser -u 1000 -G allo -s /bin/sh -D allo \
    && chown allo:allo ./

USER allo
WORKDIR /opt/allo/

#COPY --chown=allo --chmod=0755 --link secrets-init-linux-amd64 ./secrets-init
COPY --chown=allo --from=secrets-init --chmod=0755 /secrets-init ./secrets-init

#ENTRYPOINT ["/sbin/tini", "--"]
#ENTRYPOINT ["/usr/bin/dumb-init", "--"]
ENTRYPOINT ["/opt/allo/secrets-init", "--provider", "google", "--exit-early"]
CMD ["/bin/bash", "-c", "echo", "allo"]
# ----   ----
#


#
# ---- Release ----
FROM base AS release

ENV NODE_ENV=production

COPY --link --chown=allo:allo node_modules ./node_modules
COPY --link --chown=allo:allo .next* ./.next
COPY --link --chown=allo:allo redux* ./redux
COPY --link --chown=allo:allo pages* ./pages
COPY --link --chown=allo:allo public* ./public
COPY --link --chown=allo:allo server* ./server
COPY --link --chown=allo:allo .env* .
COPY --link --chown=allo:allo *.*js .
COPY --link --chown=allo:allo src* ./src
COPY --link --chown=allo:allo styles* ./styles
COPY --link --chown=allo:allo package*.json ./

ARG START_COMMAND="node_modules/.bin/next start"
ENV START_COMMAND=$START_COMMAND

# expose port and define CMD
EXPOSE 3000

CMD printenv && $START_COMMAND
# ----   ----
#
