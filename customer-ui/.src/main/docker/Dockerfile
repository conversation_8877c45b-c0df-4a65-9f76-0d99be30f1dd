ARG NODEJS_VERSION=12.22.12
ARG ALPINE_VERSION=3.15

ARG ALPINE_BASE_IMAGE=alpine:${ALPINE_VERSION}
ARG NODEJS_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}


#
# ---- Base Node ----
FROM ${ALPINE_BASE_IMAGE} as base
RUN    apk add --no-cache \
         nodejs-current \
         tini \
         libstdc++ dumb-init
RUN    addgroup -g 1000 node && adduser -u 1000 -G node -s /bin/sh -D node \
    && chown node:node ./

USER node
WORKDIR /opt/allo/
#ENTRYPOINT ["/sbin/tini", "--"]
ENTRYPOINT ["dumb-init", "--"]
# ----   ----
#


#
# ---- Release ----
FROM base AS release

ENV NODE_ENV production

COPY --chown=node:node node_modules ./node_modules
COPY --chown=node:node .next* ./.next
COPY --chown=node:node redux* ./redux
COPY --chown=node:node pages* ./pages
COPY --chown=node:node public* ./public
COPY --chown=node:node server* ./server
COPY --chown=node:node .env* .
COPY --chown=node:node *.js .
COPY --chown=node:node src* ./src
COPY --chown=node:node styles* ./styles
COPY --chown=node:node package*.json ./

ARG START_COMMAND="node server/app.js"
ENV START_COMMAND=$START_COMMAND

# expose port and define CMD
EXPOSE 3000

RUN \
    echo "$START_COMMAND" >> ./tmp.sh && \
    chmod +x ./tmp.sh

CMD "./tmp.sh"
# ----   ----
#
