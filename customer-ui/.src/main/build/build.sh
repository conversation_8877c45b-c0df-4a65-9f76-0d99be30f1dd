#!/bin/bash
echo "$@"

export COREPACK_ENABLE_DOWNLOAD_PROMPT=0
export JS_BUILD_ALLO_GCLOUD_PROJECT="${JS_BUILD_ALLO_GCLOUD_PROJECT:-endless-gizmo-264508}"
export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER="${JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER:-https://europe-npm.pkg.dev/$JS_BUILD_ALLO_GCLOUD_PROJECT/allo-npm/}"
export JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER_TOKEN="${JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER_TOKEN:-$(gcloud auth print-access-token --project=$JS_BUILD_PUBLISH_GCLOUD_PROJECT)}"
export JS_BUILD_GCLOUD_PROJECT="${JS_BUILD_GCLOUD_PROJECT:-iac-dev-432418}"
export JS_BUILD_GCLOUD_REGISTRY_SERVER="${JS_BUILD_GCLOUD_REGISTRY_SERVER:-https://europe-west3-npm.pkg.dev/$JS_BUILD_GCLOUD_PROJECT/registry-npmjs-org/}"
export JS_BUILD_GCLOUD_REGISTRY_SERVER_TOKEN="${JS_BUILD_GCLOUD_REGISTRY_SERVER_TOKEN:-$(gcloud auth print-access-token --project=$JS_BUILD_GCLOUD_PROJECT)}"

if [ -f yarn.lock ]; then 
	corepack enable yarn;
	corepack yarn config set --json 'npmAuthToken' "\"$JS_BUILD_GCLOUD_REGISTRY_SERVER_TOKEN\"";
	corepack yarn config set --json 'npmAlwaysAuth' "true";
	corepack yarn config set --json 'npmRegistryServer' "\"$JS_BUILD_GCLOUD_REGISTRY_SERVER\"";
    corepack yarn config set --json 'npmScopes.allo.npmAuthToken' "\"$JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER_TOKEN\"";
    corepack yarn config set --json 'npmScopes.allo.npmAlwaysAuth' "true";
    corepack yarn config set --json 'npmScopes.allo.npmRegistryServer' "\"$JS_BUILD_GCLOUD_REGISTRY_ALLO_SERVER\"";
    corepack yarn config set nodeLinker node-modules
	yarn "$@";
elif [ -f package-lock.json ]; then npm "$@"; \
elif [ -f pnpm-lock.yaml ]; then corepack enable pnpm && pnpm "$@"; \
else echo "Lockfile not found." && exit 1; \
fi
