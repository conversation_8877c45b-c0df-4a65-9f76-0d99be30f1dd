---
apiVersion: "v1"
kind: "List"
items:
  - apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: "$INT_SERVICE_NAME"
          #app.kubernetes.io/name: "$INT_SERVICE_NAME"
          #app.kubernetes.io/instance: "$INT_SERVICE_NAME"
  - apiVersion: "v1"
    kind: "Service"
    metadata:
      annotations:
        cloud.google.com/backend-config: '{"default":"bc-global-default"}' # backend configuration to use (eg for cloud armor)
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      ports:
        - name: "http"
          port: 3000
          targetPort: 3000
      selector:
        app: "$INT_SERVICE_NAME"
  - apiVersion: "apps/v1"
    kind: "Deployment"
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
        kube-monkey/enabled: enabled
        kube-monkey/identifier: "$INT_SERVICE_NAME"
        kube-monkey/kill-mode: fixed
        kube-monkey/kill-value: "1"
        kube-monkey/mtbf: "2"
      name: "$INT_SERVICE_NAME"
    spec:
      replicas: 2
      selector:
        matchLabels:
          app: "$INT_SERVICE_NAME"
      template:
        metadata:
          labels:
            app: "$INT_SERVICE_NAME"
            app.kubernetes.io/name: "$INT_SERVICE_NAME"
            app.kubernetes.io/instance: "$INT_SERVICE_NAME"
            app.kubernetes.io/version: "$INT_SERVICE_VERSION"
            app.kubernetes.io/component: "$INT_SERVICE_NAME"
            app.kubernetes.io/component-type: "nodejs"
            app.kubernetes.io/part-of: "allO"
            kube-monkey/enabled: enabled
            kube-monkey/identifier: "$INT_SERVICE_NAME"
            kube-monkey/kill-mode: fixed
            kube-monkey/kill-value: "1"
            kube-monkey/mtbf: "2"
          annotations:
            # important for emptyDir volume mounts            
            cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        spec:
          topologySpreadConstraints:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/name: "$INT_SERVICE_NAME"
                  app.kubernetes.io/instance: "$INT_SERVICE_NAME"
              matchLabelKeys:
                - pod-template-hash
              maxSkew: 1
              whenUnsatisfiable: DoNotSchedule
              topologyKey: "kubernetes.io/hostname"
          imagePullSecrets:
            - name: artifact-registry-publisher
          containers:
            - env:
                - name: "KUBERNETES_NAMESPACE"
                  valueFrom:
                    fieldRef:
                      fieldPath: "metadata.namespace"
                - name: "KUBERNETES_POD_IP"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "HOSTNAME"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "PORT"
                  value: "3000"
                - name: "NPM_CONFIG_LOGLEVEL"
                  value: "warn"
                - name: "NODE_ENV"
                  value: "$INT_ENV_ENVIRONMENT"
                - name: "NEXT_PUBLIC_LOG_LEVEL"
                  value: "info"
                - name: "NEXT_TELEMETRY_DISABLED"
                  value: "1"
                - name: "NEXT_PUBLIC_DEFAULT_API_URL"
                  value: "https://customer-ui.dev.allo.restaurant"
                - name: "NEXT_PUBLIC_LEVIEE_ENV"
                  value: "$INT_LEVIEE_ENV_ENVIRONMENT"
              image: "$INT_SERVICE_IMAGE"
              imagePullPolicy: "IfNotPresent"
              name: "$INT_SERVICE_NAME"
              ports:
                - containerPort: 3000
                  name: "http"
                  protocol: "TCP"
              resources:
                requests:
                  cpu: 15m
                  memory: 128Mi
                limits:
                  memory: 512Mi # high limit added here - to protect the node and other services just in case ...
                  ephemeral-storage: 2Gi # limit added here - to protect the node and other services just in case ...
              lifecycle:
                preStop:
                  exec:
                    command: 
                    - "/bin/sh"
                    - "-ce" 
                    - |
                      sleep 10 # give k8s svc balancer the chance to unregister routing before shutting down
              startupProbe:
                httpGet:
                  scheme: HTTP
                  path: /health-check
                  port: 3000
                periodSeconds: 5
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 60 # number of tries every periodSeconds - total time would be failureThreshold * periodSeconds
              readinessProbe:
                httpGet:
                  scheme: HTTP
                  path: /health-check
                  port: 3000
                initialDelaySeconds: 5
                periodSeconds: 10
                timeoutSeconds: 3
                successThreshold: 2
                failureThreshold: 10
              livenessProbe:
                httpGet:
                  #Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.
                  scheme: HTTP
                  #Path to access on the HTTP server.
                  path: /health-check
                  #Name or number of the port to access on the container. Number must be in the range 1 to 65535.
                  port: 3000
                #Number of seconds after the container has started before liveness or readiness probes are initiated. Defaults to 0 seconds. Minimum value is 0.
                initialDelaySeconds: 5
                #How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.
                periodSeconds: 10
                #Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1.
                timeoutSeconds: 3
                #Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                successThreshold: 1
                #When a Pod starts and the probe fails, Kubernetes will try failureThreshold times before giving up. Giving up in case of liveness probe means restarting the container. In case of readiness probe the Pod will be marked Unready. Defaults to 3. Minimum value is 1
                failureThreshold: 10
              securityContext:
                allowPrivilegeEscalation: false
                readOnlyRootFilesystem: true
                seccompProfile:
                  type: "RuntimeDefault"
                capabilities:
                  drop:
                    - "ALL"
              volumeMounts:
                - mountPath: /tmp
                  name: tmp-volume
                - mountPath: /var/run
                  name: tmp-run-volume
                - mountPath: /var/log
                  name: tmp-log-volume
                - mountPath: /opt/allo/.next/cache/images
                  name: tmp-next-cache-images-volume
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          terminationGracePeriodSeconds: 20
          volumes:
          - name: tmp-volume
            emptyDir:
              sizeLimit: 1Gi
          - name: tmp-run-volume
            emptyDir:
              sizeLimit: 1Gi
          - name: tmp-log-volume
            emptyDir: 
              sizeLimit: 1Gi
          - name: tmp-next-cache-images-volume
            emptyDir:
              sizeLimit: 1Gi