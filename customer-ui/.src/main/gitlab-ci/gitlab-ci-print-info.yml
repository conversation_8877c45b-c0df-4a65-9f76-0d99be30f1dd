---
.gitlab-ci-print-info.print_info:
  - |
    # https://www.tutorialspoint.com/gitlab/gitlab_ci_cd_variables.htm
    # https://docs.gitlab.com/ee/ci/variables/predefined_variables.html
    echo "===================================="
    echo "="
    echo "=   CI_PROJECT_NAME                   = ${CI_PROJECT_NAME}"
    echo "=   CI_PROJECT_URL                    = ${CI_PROJECT_URL}"
    echo "="
    echo "= --------------------------------------------------------------"
    echo "="
    echo "=   CI_BUILD_STAGE                    = ${CI_BUILD_STAGE}"
    echo "=   CI_JOB_URL                        = ${CI_JOB_URL}"
    echo "="
    echo "=   CI_PROJECT_TITLE                  = ${CI_PROJECT_TITLE}"
    echo "=   CI_COMMIT_AUTHOR                  = ${CI_COMMIT_AUTHOR}"         # The author of the commit in Name <email> format.
    echo "=   CI_COMMIT_BEFORE_SHA              = ${CI_COMMIT_BEFORE_SHA}"     # The previous latest commit present on a branch or tag. Is always 0000000000000000000000000000000000000000 for merge request pipelines, the first commit in pipelines for branches or tags, or when manually running a pip
    echo "=   CI_COMMIT_BRANCH                  = ${CI_COMMIT_BRANCH}"         # The commit branch name. Available in branch pipelines, including pipelines for the default branch. Not available in merge request pipelines or tag pipelines.
    echo "=   CI_COMMIT_DESCRIPTION             = ${CI_COMMIT_DESCRIPTION}"    # The description of the commit. If the title is shorter than 100 characters, the message without the first line.
    echo "=   CI_COMMIT_MESSAGE                 = ${CI_COMMIT_MESSAGE}"        # The full commit message.
    echo "=   CI_COMMIT_REF_NAME                = ${CI_COMMIT_REF_NAME}"       # The branch or tag name for which project is built.
    echo "=   CI_COMMIT_REF_PROTECTED           = ${CI_COMMIT_REF_PROTECTED}"  # true if the job is running for a protected reference, false otherwise.
    echo "=   CI_COMMIT_REF_SLUG                = ${CI_COMMIT_REF_SLUG}"       # CI_COMMIT_REF_NAME in lowercase, shortened to 63 bytes, and with everything except 0-9 and a-z replaced with -. No leading / trailing -. Use in URLs, host names and domain names.
    echo "=   CI_COMMIT_SHA                     = ${CI_COMMIT_SHA}"            # The commit revision the project is built for.
    echo "=   CI_COMMIT_SHORT_SHA               = ${CI_COMMIT_SHORT_SHA}"      # The first eight characters of CI_COMMIT_SHA.
    echo "=   CI_COMMIT_TAG                     = ${CI_COMMIT_TAG}"            # The commit tag name. Available only in pipelines for tags.
    echo "=   CI_COMMIT_TAG_MESSAGE             = ${CI_COMMIT_TAG_MESSAGE}"    # The commit tag message. Available only in pipelines for tags.
    echo "=   CI_COMMIT_TIMESTAMP               = ${CI_COMMIT_TIMESTAMP}"      # The timestamp of the commit in the ISO 8601 format. For example, 2022-01-31T16:47:55Z.
    echo "=   CI_COMMIT_TITLE                   = ${CI_COMMIT_TITLE}"          # The title of the commit. The full first line of the message.
    echo "="
    echo "= --------------------------------------------------------------"
    echo "="
    echo "=   INT_CONTAINER_IMAGE_REGISTRY      = ${INT_CONTAINER_IMAGE_REGISTRY}"
    echo "=   INT_CONTAINER_IMAGE_REGISTRY_USER = ${INT_CONTAINER_IMAGE_REGISTRY_USER}"
    echo "="
    echo "=   PIPELINE_PUSH_IMAGE = ${PIPELINE_PUSH_IMAGE}"
    echo "="
    echo "===================================="
