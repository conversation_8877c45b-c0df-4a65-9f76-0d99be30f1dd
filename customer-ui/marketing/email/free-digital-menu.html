<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta content="width=device-width" name="viewport"/>
	<meta content="text/html; charset=UTF-8" http-equiv="Content-Type"/>
	<title>Your order</title>
	<style>
		img {
			border: none;
			-ms-interpolation-mode: bicubic;
			max-width: 100%;
		}

		body {
			font-family: sans-serif;
			-webkit-font-smoothing: antialiased;
			font-size: 14px;
			line-height: 1.4;
			margin: 0;
			padding: 0;
			-ms-text-size-adjust: 100%;
			-webkit-text-size-adjust: 100%;
		}

		table {
			border-collapse: separate;
			mso-table-lspace: 0pt;
			mso-table-rspace: 0pt;
			width: 100%;
			border-spacing: 0px;
		}

		table td {
			font-family: sans-serif;
			font-size: 14px;
			vertical-align: top;
		}

		/* -------------------------------------
				BODY & CONTAINER
		------------------------------------- */

		.body {
			width: 100%;
		}

		.table {
			background-color: #F7F8F9;
			width: 100%;
		}

		/* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
		.container {
			display: block;
			margin: 0 auto !important;
			/* makes it centered */
			max-width: 580px;
			padding: 10px;
			width: 580px;
		}

		/* This should also be a block element, so that it will fill 100% of the .container */
		.content {
			box-sizing: border-box;
			display: block;
			margin: 0 auto;
			max-width: 580px;
			padding: 10px;
		}

		/* -------------------------------------
				HEADER, FOOTER, MAIN
		------------------------------------- */
		.main {
			background: #ffffff;
			border-radius: 3px;
			width: 100%;
		}

		.wrapper {
			box-sizing: border-box;
		}

		.content-block {
			padding-bottom: 10px;
			padding-top: 10px;
		}

		.footer {
			clear: both;
			margin-top: 10px;
			text-align: center;
			width: 100%;
		}

		.footer td,
		.footer p,
		.footer span,
		.footer a {
			color: #999999;
			font-size: 12px;
			text-align: center;
		}

		/* -------------------------------------
				TYPOGRAPHY
		------------------------------------- */
		h1,
		h2,
		h3,
		h4 {
			color: #04172F;
			font-family: sans-serif;
			font-weight: 400;
			line-height: 1.4;
			margin: 0;
			margin-bottom: 30px;
		}

		h1 {
			font-size: 28px;
			font-weight: 600;
			line-height: 34px;
			letter-spacing: 0.48px;
		}

		h2 {
			font-size: 20px;
			font-weight: 600;
			line-height: 24px;
			letter-spacing: 0.2px;
			margin-bottom: 16px;
		}

		p,
		ul,
		ol {
			font-family: sans-serif;
			font-size: 14px;
			font-weight: normal;
			margin: 0;
			margin-bottom: 15px;
		}

		p li,
		ul li,
		ol li {
			list-style-position: inside;
			margin-left: 5px;
		}

		a {
			color: #5CBAA7;
			text-decoration: underline;
		}

		p {
			font-size: 15px;
			font-weight: 400;
			line-height: 20px;
			letter-spacing: -0.01px;
		}

		/* -------------------------------------
				BUTTONS
		------------------------------------- */
		.btn {
			box-sizing: border-box;
			width: 100%;
		}

		.btn > tbody > tr > td {
			padding-bottom: 15px;
		}

		.btn table {
			width: auto;
		}

		.btn table td {
			background-color: #ffffff;
			border-radius: 5px;
			text-align: center;
		}

		.btn a {
			background-color: #ffffff;
			border: solid 1px #5CBAA7;
			border-radius: 5px;
			box-sizing: border-box;
			color: #5CBAA7;
			cursor: pointer;
			display: inline-block;
			font-size: 14px;
			font-weight: bold;
			margin: 0;
			padding: 12px 25px;
			text-decoration: none;
			text-transform: capitalize;
		}

		.btn-primary table td {
			background-color: #5CBAA7;
		}

		.btn-primary a {
			background-color: #5CBAA7;
			border-color: #5CBAA7;
			color: #ffffff;
		}

		/* -------------------------------------
				OTHER STYLES THAT MIGHT BE USEFUL
		------------------------------------- */
		.last {
			margin-bottom: 0;
		}

		.first {
			margin-top: 0;
		}

		.align-center {
			text-align: center;
		}

		.align-right {
			text-align: right;
		}

		.align-left {
			text-align: left;
		}

		.clear {
			clear: both;
		}

		.mt0 {
			margin-top: 0;
		}

		.mb0 {
			margin-bottom: 0;
		}

		.bold {
			font-weight: 600;
			color: #5CBAA7;
		}

		.small-text {
			font-size: 12px;
		}

		.panel {
			padding-top: 30px;
			padding-bottom: 30px;
		}

		.preheader {
			color: transparent;
			display: none;
			height: 0;
			max-height: 0;
			max-width: 0;
			opacity: 0;
			overflow: hidden;
			mso-hide: all;
			visibility: hidden;
			width: 0;
		}

		.powered-by a {
			text-decoration: none;
		}

		hr {
			border: 0;
			border-bottom: 1px solid #f6f6f6;
			margin: 20px 0;
		}

		/* -------------------------------------
				RESPONSIVE AND MOBILE FRIENDLY STYLES
		------------------------------------- */
		@media only screen and (max-width: 620px) {
			.table h1 {
				margin-bottom: 10px !important;
			}

			.table p,
			.table ul,
			.table ol,
			.table td,
			.table span,
			.table a {
			}

			.table .wrapper,
			.table .article {
				/*padding: 10px !important;*/
			}

			.table .content {
				padding: 0 !important;
			}

			.table .container {
				padding: 0 !important;
				width: 100% !important;
			}

			.table .main {
				border-left-width: 0 !important;
				border-radius: 0 !important;
				border-right-width: 0 !important;
			}

			.table .btn table {
				width: 100% !important;
			}

			.table .btn a {
				width: 100% !important;
			}

			.table .img-responsive {
				height: auto !important;
				max-width: 100% !important;
				width: auto !important;
			}

			.divider-full {
			}
		}

		/* -------------------------------------
				PRESERVE THESE STYLES IN THE HEAD
		------------------------------------- */
		@media all {
			.ExternalClass {
				width: 100%;
			}

			.ExternalClass,
			.ExternalClass p,
			.ExternalClass span,
			.ExternalClass font,
			.ExternalClass td,
			.ExternalClass div {
				line-height: 100%;
			}

			.apple-link a {
				color: inherit !important;
				font-family: inherit !important;
				font-size: inherit !important;
				font-weight: inherit !important;
				line-height: inherit !important;
				text-decoration: none !important;
			}

			#MessageViewBody a {
				color: inherit;
				text-decoration: none;
				font-size: inherit;
				font-family: inherit;
				font-weight: inherit;
				line-height: inherit;
			}

			.btn-primary table td:hover {
				background-color: #5CBAA7 !important;
			}

			.btn-primary a:hover {
				background-color: #5CBAA7 !important;
				border-color: #5CBAA7 !important;
			}
		}

		.brand-border {
			border-top: 10px solid #5CBAA7;
		}

		.order-items {
			margin-bottom: 16px;
		}

		.order-item-layout {
			display: flex;
			flex-direction: row;
			/*align-items: top;*/
			justify-content: space-between;
		}

		.order-item-text {
			display: flex;
			flex-grow: 1;
			width: 100%
		}

		.order-item-count {
			min-width: 20px;
			margin-right: 8px;
		}

		.order-item-notes {
			color: #919CA9;
		}

		.vat-disclaimer {
			color: #919CA9;
		}

		.divider-full {
			border-bottom: 1px solid #04172F;
			opacity: 0.12;
			margin-bottom: 16px;
		}

		.restaurant-img-container {
			/*margin-top: -22px;*/
			/*margin-right: -22px;*/
			/*margin-left: -22px;*/
			margin-bottom: 32px;
			height: 160px;
			width: 160px;
			border-radius: 8px;
			background-position: center;
			background-repeat: no-repeat;
			background-size: cover;
			margin-left: auto;
			margin-right: auto;
		}

		.restaurant-img {
			height: 160px;
			object-fit: cover;
			width: 100%;
		}

		.brand-logo {
			height: 25px;
			margin: 0 auto;
			text-align: center;
			display: block;
			padding-top: 12px;
			padding-bottom: 32px;
		}

		@media only screen and (max-width: 620px) {
			.brand-logo {
				height: 18px;
				padding-top: 12px;
				padding-bottom: 22px;
			}
		}

		.brand-btn-a {
			background-color: #5CBAA7;
			border-color: #5CBAA7;
			color: #ffffff;
		}

	</style>
</head>
<body class="">
<!--prehead text generation-->
<span class="preheader">Information about your order {{orderUUIDSub}}</span>
<div>
	<table border="0" cellpadding="0" cellspacing="0" class="table" role="presentation">
		<tr>
			<td class="container">
				<div class="content">
					<table class="main" role="presentation">
						<tr>
							<td class="wrapper">
								<table border="0" cellpadding="0" cellspacing="0" role="presentation">
									<tr>
										<td>
											<div style="padding: 20px;">
												<img src="http://cdn.mcauto-images-production.sendgrid.net/031d3ba684601784/d88c2af5-5849-41af-ab9a-a7349f9e9bee/72x28.png" alt="leviee-logo" class="brand-logo">
												<!--												<div class="restaurant-img-container"-->
												<!--												     style="background-image: linear-gradient(180deg,rgba(0,0,0,.18) 0,rgba(0,0,0,.18) 3.5%,rgba(0,0,0,.19) 7%,rgba(0,0,0,.17) 10.35%,rgba(0,0,0,.15)), url({{restaurant.thumbnailUrl}})">-->
												<!--													&lt;!&ndash;												<img src="{{restaurant.thumbnailUrl}}" alt={{restaurant.name}} class="restaurant-img">&ndash;&gt;-->
												<!--												</div>-->
												<h2>Hi {{customer.firstName}},<br/> there is an issue with your order.</h2>
												<p>
													We are sorry to inform you that your order <span class="bold">{{orderUUIDSub}}</span> has been rejected by <span class="bold">{{restaurant.name}}</span>.
													Please call the number below to clarify further. Thank you very much for your understanding!
												</p>
												<table border="0" cellpadding="0" cellspacing="0" class="btn btn-primary panel"
												       role="presentation">
													<tbody>
													<tr>
														<td align="center">
															<table border="0" cellpadding="0" cellspacing="0" role="presentation">
																<tbody>
																<tr>
																	<td>
																		<a href="tel:{{restaurant.phone}}" class="brand-btn-a">{{restaurant.phone}}</a>
																	</td>
																</tr>
																</tbody>
															</table>
														</td>
													</tr>
													</tbody>
												</table>
											</div>
										</td>
									</tr>
								</table>
							</td>
						</tr>
					</table>
					<div class="footer">
						<table border="0" cellpadding="0" cellspacing="0" role="presentation">
							<tr>
								<td class="content-block">
									<span class="apple-link">allO Technology GmbH, Hopfenstraße 8, 80335 Munich, Germany</span>
									<br><a href="https://company.leviee.de/legal-2/imprint">Imprint</a> | <a href="https://company.leviee.de/legal-2/data-privacy">Privacy Policy</a> | <a href="https://company.leviee.de/gtc-for-users">Terms and Conditions</a>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</td>
		</tr>
	</table>
</div>
</body>
</html>

