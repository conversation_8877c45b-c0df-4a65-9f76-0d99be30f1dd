import SEO from '../next-seo.config';
import React from 'react';
import { ThemeProvider } from '@material-ui/core/styles';
import CssBaseline from '@material-ui/core/CssBaseline';
import { appWithTranslation } from '../i18n';
import theme from '../styles/theme';
import Footer from "../src/components/Footer";
import App from '../src/components/App';

import { wrapper } from '../redux/store';
import '../styles/autofill.css';
import '../styles/swiperjs.css';
import '../styles/input.css';
import {DefaultSeo} from "next-seo";
import Head from 'next/head'
import AxiosInterceptor from "../redux/axiosInterceptorErrorMessage";

const MyApp = ({ Component, pageProps }) => {
  React.useEffect(() => {
    // Remove the server-side injected CSS.
    // eslint-disable-next-line no-undef
    const jssStyles = document.querySelector('#jss-server-side');
    if (jssStyles) {
      jssStyles.parentElement.removeChild(jssStyles);
    }
  }, []);

  return (
    <>
      {/*https://nextjs.org/docs/messages/no-document-viewport-meta*/}
      <Head>
        <meta name="viewport" content="minimum-scale=1, initial-scale=1, user-scalable=no, maximum-scale=1, width=device-width" />
      </Head>
      <DefaultSeo {...SEO} />
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <App>
          <AxiosInterceptor />
          <Component {...pageProps} />
        </App>
      </ThemeProvider>
    </>

  );
};

export default wrapper.withRedux(appWithTranslation(MyApp));
