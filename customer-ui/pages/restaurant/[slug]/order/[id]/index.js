import React from 'react';
import { useRouter } from 'next/router';
import Order from '../../../../../src/features/order';
import Layout from '../../../../../src/components/Layout';

const OrderPage = () => {
  const router = useRouter();
  const { id } = router.query;

  return (
    <div>
      <main>
        <Layout>
          <Order id={id} />
        </Layout>
      </main>
    </div>
  );
};

OrderPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default OrderPage;
