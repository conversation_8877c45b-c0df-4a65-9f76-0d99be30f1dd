import React from 'react';
import { useRouter } from 'next/router';
import Welcome from '../../../../src/features/welcome';

const WelcomePage = () => {
  const router = useRouter();
  const { slug } = router.query;
  return (
    <div>
      <main style={{ minHeight: "100vh" }}>
        <Welcome slug={slug} />
      </main>
    </div>
  );
};

WelcomePage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default WelcomePage;
