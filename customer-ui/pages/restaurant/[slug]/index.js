import React from 'react';
import { useRouter } from 'next/router';
import Restaurant from '../../../src/features/restaurant';

const RestaurantPage = () => {
  const router = useRouter();
  const { slug, booking } = router.query;

  return (
    <main>
      <Restaurant slug={slug} booking={booking} />
    </main>
  );
};

RestaurantPage.getInitialProps = async () => ({
  namespacesRequired: ['common']
});

export default RestaurantPage;
