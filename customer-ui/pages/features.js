import React from 'react';
import Head from 'next/head';
import Layout from '../src/components/Layout';
import NavigationBar from '../src/components/NavigationBar';
import GetStarted from '../src/sections/GetStarted';
import Footer from '../src/sections/Footer';
import FeaturesHero from '../src/sections/FeaturesHero';
import FeaturesGlance from '../src/sections/FeaturesGlance';
import TestimonialLarge from '../src/sections/TestimonialLarge';
import EstimationsDark from '../src/sections/EstimationsDark';
import NotTraditionalPos from '../src/sections/NotTraditionalPos';
import DigitalMenu from '../src/sections/DigitalMenu';
import NotTraditionalPosExtended from '../src/sections/NotTraditionalPosExtended';
import IncreaseEfficiency from '../src/sections/IncreaseEfficiency';
import IncreaseTakeawayRevenue from '../src/sections/IncreaseTakeawayRevenue';
import SellCoupons from '../src/sections/SellCoupons';
import LoyaltyProgram from '../src/sections/LoyaltyProgram';

const FeaturesPage = () => (
  <>
    <Head>
      <title>Features | Leviee Restaurant Management System</title>
      <link rel="canonical" href="https://www.leviee.de/features" />
    </Head>
    <div>
      <main>
        <Layout>
          <NavigationBar />
          <FeaturesHero />
          <FeaturesGlance />
          <TestimonialLarge />
          <EstimationsDark />
          <NotTraditionalPos />
          <DigitalMenu />
          <NotTraditionalPosExtended />
          <IncreaseEfficiency />
          <IncreaseTakeawayRevenue />
          <SellCoupons />
          <LoyaltyProgram />
          <GetStarted />
          <Footer />
        </Layout>
      </main>
    </div>
  </>
);

export default FeaturesPage;
