{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "test": {"cache": false, "persistent": true}, "test:watch": {"cache": false, "persistent": true}, "test:e2e": {"env": ["CI"]}, "dev": {"cache": false, "persistent": true}}}