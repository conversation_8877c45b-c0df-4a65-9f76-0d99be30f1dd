import playwrightConfig from '@monorepo/jest-playwright-config/playwright-config';
import { defineConfig } from '@playwright/test';

export default defineConfig({
  ...playwrightConfig,
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:3000',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',
    //headless: true,
    //args: ['--no-sandbox', '--disable-setuid-sandbox'],
  },
});
