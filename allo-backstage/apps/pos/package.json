{"name": "pos", "version": "1.0.0", "type": "module", "private": true, "scripts": {"dev": "NEXT_INTERNAL_SERVICES_POS_SERVICE_URL=https://app-dev.allo.restaurant NEXT_PUBLIC_BASE_URL=https://app-dev.allo.restaurant OPERATIONS_CENTER_DOMAIN=http://localhost:3001 next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "format": "prettier --write ./src", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "engines": {"node": ">=20 <24", "npm": ">=10"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "dependencies": {"@allo/ui": "^0.0.29", "@fontsource-variable/bricolage-grotesque": "^5.1.1", "@fontsource-variable/inter": "^5.1.1", "@hookform/resolvers": "^4.1.3", "@monorepo/utils": "*", "date-fns": "^4.1.0", "lucide-react": "^0.476.0", "next": "15.2.0", "next-runtime-env": "^3.2.2", "openapi-fetch": "^0.13.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "sharp": "^0.33.5", "swr": "^2.3.3", "zod": "^3.24.2"}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "@monorepo/jest-playwright-config": "*", "@monorepo/typescript-config": "*", "@next/eslint-plugin-next": "^15.2.0", "@tailwindcss/postcss": "^4.0.9", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.9", "ts-node": "^10.9.2", "typescript": "5.7.3"}}