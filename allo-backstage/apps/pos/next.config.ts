import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  output: 'export',
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    dangerouslyAllowSVG: true,
    unoptimized: true,
    remotePatterns: [
      {
        hostname: 'storage.googleapis.com',
      },
      {
        hostname: 'unsplash.com',
      },
      {
        hostname: 'cdn.allo.restaurant',
      },
      {
        hostname: 'cdn.dev.allo.restaurant',
      },
    ],
  },
  async rewrites() {
    return [
      {
        source: '/operations-center',
        destination: `${process.env.OPERATIONS_CENTER_DOMAIN}/operations-center`,
      },
      {
        source: '/operations-center/:path+',
        destination: `${process.env.OPERATIONS_CENTER_DOMAIN}/operations-center/:path+`,
      },
    ];
  },
};

export default nextConfig;
