import { isServer } from '@monorepo/utils';
import { env } from 'next-runtime-env';
import createClient from 'openapi-fetch';

export const client = createClient({
  baseUrl: `${isServer() ? env('NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL') || process.env.NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL || '' : env('NEXT_PUBLIC_BASE_URL') || process.env.NEXT_PUBLIC_BASE_URL || ''}/reservation-service`,
});
