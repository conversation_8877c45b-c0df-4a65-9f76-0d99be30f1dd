import { isServer } from '@monorepo/utils';
import { useCallback, useLayoutEffect, useRef } from 'react';

type OriginalStyle = {
  overflow: CSSStyleDeclaration['overflow'];
  paddingRight: CSSStyleDeclaration['paddingRight'];
};

export function useScrollLock(
  condition: boolean = true,
  target: HTMLElement | null = null
): void {
  const originalStyle = useRef<OriginalStyle | null>(null);

  const lock = useCallback(() => {
    const element = target ?? document.body;

    const { overflow, paddingRight } = element.style;

    // Save the original styles
    originalStyle.current = { overflow, paddingRight };

    // Prevent width reflow

    // Use window inner width if body is the target as global scrollbar isn't part of the document
    const offsetWidth =
      element === document.body ? window.innerWidth : element.offsetWidth;
    // Get current computed padding right in pixels
    const currentPaddingRight =
      parseInt(window.getComputedStyle(element).paddingRight, 10) || 0;

    const scrollbarWidth = offsetWidth - element.scrollWidth;
    element.style.paddingRight = `${scrollbarWidth + currentPaddingRight}px`;

    // Lock the scroll
    element.style.overflow = 'hidden';
  }, [target]);

  const unlock = useCallback(() => {
    const element = target ?? document.body;

    if (element && originalStyle.current) {
      element.style.overflow = originalStyle.current.overflow;

      // Only reset padding right if we changed it
      element.style.paddingRight = originalStyle.current.paddingRight;
    }
  }, [target]);

  useLayoutEffect(() => {
    if (isServer()) return;

    if (condition) {
      lock();
    } else unlock();

    return () => {
      unlock();
    };
  }, [condition, lock, unlock]);
}
