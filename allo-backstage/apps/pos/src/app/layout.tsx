import './globals.css';

import '@fontsource-variable/bricolage-grotesque';
import '@fontsource-variable/inter';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <head>
        <title>allO POS</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="description" content="Point of Sale" />
        <link rel="icon" href="/apps/pos/src/app/favicon.ico" sizes="any" />
      </head>
      <body>{children}</body>
    </html>
  );
}
