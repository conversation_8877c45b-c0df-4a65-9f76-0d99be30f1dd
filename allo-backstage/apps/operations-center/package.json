{"name": "operations-center", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint --max-warnings 0", "format": "prettier --write ./src", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "engines": {"node": ">=20 <24", "npm": ">=10"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.2.4", "@allo/ui": "^0.0.29"}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "@monorepo/jest-playwright-config": "*", "@monorepo/typescript-config": "*", "@next/eslint-plugin-next": "^15.2.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/jest": "^29.5.14", "@tailwindcss/postcss": "^4", "autoprefixer": "^10.4.20", "eslint": "^9.21.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.9", "ts-node": "^10.9.2", "typescript": "5.7.3"}}