# variables:
#   DEFAULT_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-git"
#   DEFAULT_SERVICE_DIND_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/registry-docker-io/library/docker:27.3.1-dind"
#   AUTH_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/google.com/cloudsdktool/cloud-sdk"
#   BUILD_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/allo-docker-public/allo-pipeline:node20_gcloud_512"
#   BUILD_DOCKER_FILE: Dockerfile.build-node20-corepack
#   RUNTIME_IMAGE: "europe-docker.pkg.dev/endless-gizmo-264508/allo-docker/allo/nginx:1.27.3-debian-vts"
#   RUNTIME_DOCKER_FILE: Dockerfile.runtime
#   PUBLISH_CONTAINER_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/kaniko-project/executor:v1.23.2-debug"
#   DEPLOY_KUBERNETES_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/gcr-io/google.com/cloudsdktool/cloud-sdk"
#   E2E_IMAGE: "europe-west3-docker.pkg.dev/iac-dev-432418/mcr-microsoft-com/playwright:v1.50.0-noble"
#   START_COMMAND: "node_modules/.bin/next start"
#   NODEJS_VERSION: "20.18.1"
#   ALPINE_VERSION: "3.20"
#   TEST__NEW_RELIC_APP_ID: "0"
#   PROD__NEW_RELIC_APP_ID: "0"

  
# stages:
#   - preprepare
#   - prepare
#   - setup
#   - build
#   - prepublish
#   - publish
#   - deploy
#   - e2e-test
#   - rollback

# include:
#   - local: .src/main/gitlab-ci/gitlab-ci-authenticate-gcloud.yml
#   - local: .src/main/gitlab-ci/gitlab-ci-authenticate-gcloud-gke.yml
#   - local: .src/main/gitlab-ci/gitlab-ci-print-info.yml
#   - local: .src/main/gitlab-ci/gitlab-ci-print-timestamp-logging.yml
#   - local: .src/main/gitlab-ci/gitlab-********************yaml.yml
#   - local: .src/main/gitlab-ci/gitlab-ci-cache-npm.yml
#   - local: .src/main/gitlab-ci/gitlab-ci-cache-yarn.yml
  
# image: $DEFAULT_IMAGE
# services:
#   - $DEFAULT_SERVICE_DIND_IMAGE

# # Set default config for jobs
# # see https://docs.gitlab.com/ee/ci/yaml/#default
# default:
#   retry:
#     max: 2
#     # specify retry on certain conditions
#     # see https://docs.gitlab.com/ee/ci/yaml/index.html#retrywhen
#     when:
#       - unknown_failure
#       - api_failure
#       - runner_system_failure
#       - job_execution_timeout
#       - stuck_or_timeout_failure


# .cache_node_modules_key: &cache_node_modules_key
#   cache-node-modules-$CI_COMMIT_REF_SLUG

# .cache_node_modules_fallback_keys: &cache_node_modules_fallback_keys
#   - cache-node-modules-$CI_DEFAULT_BRANCH

# .cache_node_modules_paths: &cache_node_modules_paths
#   - package-lock.json.sha256sum
#   - yarn.lock.sha256sum
#   - node_modules/
#   - .yarn/
#   - "**/package-lock.json.sha256sum"
#   - "**/yarn.lock.sha256sum"
#   - "**/node_modules/"
#   - "**/.yarn/"

# .cache_build_key: &cache_build_key
#   cache-build-$CI_COMMIT_REF_SLUG

# .cache_build_fallback_keys: &cache_build_fallback_keys
#   - cache-build-$CI_DEFAULT_BRANCH

# .cache_build_paths: &cache_build_paths
#   - .npm/
#   - .turbo/
#   - "**/.npm/"
#   - "**/node_modules/.cache/turbo/"
#   - "**/.turbo/"



# ##########################################################
# # 
# # PREPREPARE
# #
# ##########################################################
# preprepare-gcloud-auth-access-token:
#   stage: preprepare
#   image:
#     name: $AUTH_IMAGE
#     entrypoint: [""]
#   before_script: 
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - mkdir -p ${CI_PROJECT_DIR}/.auth-tmp/

#     - INT_GCLOUD_PROJECT_ID=$GCLOUD_PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$ARTIFACT_REGISTRY_PUBLISHER_JSON_KEY
#     - !reference [.gitlab-ci-authenticate.gcloud]
#     - gcloud auth print-access-token --project=$INT_GCLOUD_PROJECT_ID > ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config
#     - chmod +x ${CI_PROJECT_DIR}/.src/main/docker/generateGcloudDockerAuthFile.sh
#     - ${CI_PROJECT_DIR}/.src/main/docker/generateGcloudDockerAuthFile.sh ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json


#     - INT_GCLOUD_PROJECT_ID=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$GCLOUD_ARTIFACT_REGISTRY_NPM_JSON_KEY_BASE64
#     - !reference [.gitlab-ci-authenticate.gcloud]
#     - gcloud auth print-access-token --project=$INT_GCLOUD_PROJECT_ID > ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-access-token


#     - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__ARTIFACT_REGISTRY_READER_SA_JSON_KEY_BASE64
#     - !reference [.gitlab-ci-authenticate.gcloud]
#     - gcloud auth print-access-token --project=$INT_GCLOUD_PROJECT_ID > ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-access-token-local
#   artifacts:
#     paths:
#       - ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-access-token
#       - ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-access-token-local
#       - ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json
#     expire_in: 1 day



# preprepare-versions:
#   stage: preprepare
#   image:
#     name: $AUTH_IMAGE
#     entrypoint: [""]
#   before_script:
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - mkdir -p ${CI_PROJECT_DIR}/.tmp/
#     - date_now=$(date -d "today" '+%Y-%m-%d-%H-%M-%S')
#     - echo $date_now
#     - CLEAN_CI_COMMIT_REF_SLUG=$(echo -n "${CI_COMMIT_REF_SLUG}" | sed 's/[^a-zA-Z0-9]/-/g' | head -c40 | sed 's/[^a-zA-Z0-9]$/x/g')
#     - CLEAN_CI_PROJECT_NAME=$(echo -n "${CI_PROJECT_NAME}" | sed 's/[^a-zA-Z0-9]/-/g')
#     - echo -n "${CLEAN_CI_COMMIT_REF_SLUG}-${date_now}-${CI_COMMIT_SHORT_SHA}"  | head -c63 | sed 's/[^a-zA-Z0-9]$/x/g' >> ${CI_PROJECT_DIR}/.tmp/release_version
#     - echo -n "${CLEAN_CI_PROJECT_NAME}" >> ${CI_PROJECT_DIR}/.tmp/service_name
#     - |
#       SERVICE_NAME_PREFIX=""
#       if grep -q "development" <<<"$CI_COMMIT_BRANCH"; then
#         SERVICE_NAME_PREFIX="development/";
#       elif grep -q "feature/" <<<"$CI_COMMIT_BRANCH"; then
#         SERVICE_NAME_PREFIX="feature/";
#       fi
#     - echo -n "${SERVICE_NAME_PREFIX}${CLEAN_CI_PROJECT_NAME}" >> ${CI_PROJECT_DIR}/.tmp/service_name_long
#     - echo -n "$(echo $(cat ${CI_PROJECT_DIR}/.tmp/service_name_long):$(cat ${CI_PROJECT_DIR}/.tmp/release_version))" >> ${CI_PROJECT_DIR}/.tmp/release_container_name
#     - echo -n "$(echo $(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)):latest" >> ${CI_PROJECT_DIR}/.tmp/latest_container_name

#     - cat ${CI_PROJECT_DIR}/.tmp/release_version
#     - cat ${CI_PROJECT_DIR}/.tmp/service_name
#     - cat ${CI_PROJECT_DIR}/.tmp/service_name_long
#     - cat ${CI_PROJECT_DIR}/.tmp/release_container_name
#     - cat ${CI_PROJECT_DIR}/.tmp/latest_container_name
#   artifacts:
#     paths:
#       - ${CI_PROJECT_DIR}/.tmp/release_version
#       - ${CI_PROJECT_DIR}/.tmp/service_name
#       - ${CI_PROJECT_DIR}/.tmp/service_name_long
#       - ${CI_PROJECT_DIR}/.tmp/release_container_name
#       - ${CI_PROJECT_DIR}/.tmp/latest_container_name
#     expire_in: 1 day



# ##########################################################
# # 
# # PREPARE
# #
# ##########################################################
# cache_job:
#   stage: prepare
#   image: $BUILD_IMAGE
#   before_script: 
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - !reference [.gitlab-ci-cache.npm-lock.early-exit]  # exits with 218 if something was renewed and package-lock.json is present
#     - !reference [.gitlab-ci-cache.yarn-lock.early-exit] # exits with 218 if something was renewed and yarn.lock is present

#     - export JS_BUILD_PREFER_CUSTOM_REGISTRY=""

#     - export JS_BUILD_CUSTOM_SCOPE="allo"
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY="${JS_BUILD_CUSTOM_SCOPE_REGISTRY:-https://europe-npm.pkg.dev/$JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT/allo-npm/}"
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(cat .auth-tmp/.gcloud-auth-access-token)

#     - export JS_BUILD_REGISTRY_GCLOUD_PROJECT=$TEST__PROJECT_ID
#     - export JS_BUILD_REGISTRY="${JS_BUILD_REGISTRY:-https://europe-west3-npm.pkg.dev/$JS_BUILD_REGISTRY_GCLOUD_PROJECT/registry-npmjs-org/}"
#     - export JS_BUILD_REGISTRY_AUTHTOKEN=$(cat .auth-tmp/.gcloud-auth-access-token-local)

#     - . .src/main/build-helper/build-dep.sh

#     - !reference [.gitlab-ci-cache.npm-lock.late-exit]  # exits with 218 if something was renewed and package-lock.json is present
#     - !reference [.gitlab-ci-cache.yarn-lock.late-exit] # exits with 218 if something was renewed and yarn.lock is present
#   allow_failure:
#     exit_codes: 218
#   cache:
#     - key: *cache_node_modules_key
#       fallback_keys:
#         *cache_node_modules_fallback_keys
#       paths:
#         *cache_node_modules_paths
#       policy: pull-push
#       when: on_failure
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true



# ##########################################################
# # 
# # BUILD
# #
# ##########################################################
# .build-turbo:
#   image: $BUILD_IMAGE
#   stage: build
#   before_script:
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - export JS_BUILD_PREFER_CUSTOM_REGISTRY=""

#     - export JS_BUILD_CUSTOM_SCOPE="allo"
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=$GCLOUD_ARTIFACT_REGISTRY_NPM_PROJECT_ID
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY="${JS_BUILD_CUSTOM_SCOPE_REGISTRY:-https://europe-npm.pkg.dev/$JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT/allo-npm/}"
#     - export JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN=$(cat .auth-tmp/.gcloud-auth-access-token)

#     - export JS_BUILD_REGISTRY_GCLOUD_PROJECT=$TEST__PROJECT_ID
#     - export JS_BUILD_REGISTRY="${JS_BUILD_REGISTRY:-https://europe-west3-npm.pkg.dev/$JS_BUILD_REGISTRY_GCLOUD_PROJECT/registry-npmjs-org/}"
#     - export JS_BUILD_REGISTRY_AUTHTOKEN=$(cat .auth-tmp/.gcloud-auth-access-token-local)

    
#     #- |
#     #  echo "===================================="
#     #  echo "="
#     #  echo "= run pre-commit to see if any files will change"
#     #  echo "="
#     #  echo "===================================="
#     #  echo "TODO: might not work as quality gate - as it only checks staged files ..."
#     #  set -euxo pipefail
#     #  . .src/main/build-helper/build.sh run format:check
#     #  if [ "$?" -ne 0 ]; then exit 1; fi;
#     - |
#       echo "===================================="
#       echo "="
#       echo "= run build"
#       echo "="
#       echo "===================================="
#       . .src/main/build-helper/build.sh run build$BUILD_FILTER
#   cache:
#     - key: *cache_node_modules_key
#       fallback_keys:
#         *cache_node_modules_fallback_keys
#       paths:
#         *cache_node_modules_paths
#       policy: pull
#     - key: *cache_build_key
#       fallback_keys:
#         *cache_build_fallback_keys
#       paths:
#         *cache_build_paths
#       policy: pull-push
#   artifacts:
#     paths:
#       - .next/
#       - "**/.next/"
#       - public/
#       - "**/public/"
#       - storybook-static/
#       - playwright-report/
#       - test-results/
#       #- node_modules/
#       #- "**/node_modules/"
#     expire_in: 1 day
#   needs:
#     - job: cache_job
#       artifacts: false


# build-turbo-kiosk:
#   extends: .build-turbo
#   before_script: 
#   - export BUILD_FILTER="-app-kiosk"
#   only:
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/kiosk/**/*

# build-turbo-pos:
#   extends: .build-turbo
#   before_script: 
#   - export BUILD_FILTER="-app-pos"
#   only:
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/pos/**/*


# ##########################################################
# # 
# # PUBLISH
# #
# ##########################################################
# .publish-container:
#   stage: publish
#   image:
#     name: $PUBLISH_CONTAINER_IMAGE
#     entrypoint: [""]
#   before_script: 
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - if [[ -f ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json && -s ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json ]];then echo "found ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json and its not empty"; else echo "ERROR!! - ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json not found or its empty"; exit 1; fi;
#     - if [[ -z "${ACTIVE_PROFILE_VALUE}" ]]; then echo "ERROR!! - ACTIVE_PROFILE_VALUE is not set"; exit 1; fi;
#     - echo "APP_DIRECTORY=${APP_DIRECTORY}"
#     - mkdir -p /kaniko/.docker/
#     - export GOOGLE_APPLICATION_CREDENTIALS="${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json"
#     - cp ${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json /kaniko/.docker/config.json
#     - set -euxo pipefail
#     - /kaniko/executor
#       --context "${CI_PROJECT_DIR}"
#       --dockerfile "${CI_PROJECT_DIR}/.src/main/docker/$RUNTIME_DOCKER_FILE"
#       --destination "europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)/${ALLO_FRONTEND_APP}:$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#       --reproducible
#       --snapshot-mode redo
#       --compressed-caching=false
#       --build-arg "NODEJS_VERSION=${NODEJS_VERSION}"
#       --build-arg "ALPINE_VERSION=${ALPINE_VERSION}"
#       --build-arg "START_COMMAND=${START_COMMAND}"
#       --build-arg "SERVICE_NAME=${CI_PROJECT_NAME}"
#       --build-arg "ENVIRONMENT=${ACTIVE_PROFILE_VALUE}"
#       --build-arg "APP_DIRECTORY=${APP_DIRECTORY}"
#       #--cache=true 
#       #--cache-run-layers
#       #--cache-copy-layers=true 
#       #--cache-ttl=672h 
#       #--cache-repo=europe-docker.pkg.dev/${GCLOUD_ARTIFACT_REGISTRY_DOCKER_PROJECT_ID}/allo-docker/allo/${CI_PROJECT_NAME}/cache
#       #--destination "europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#       #--destination "europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/latest_container_name)"
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     #- job: build-turbo
#     #  artifacts: true


# publish-container-pos-production:
#   extends: .publish-container
#   before_script: 
#   - export ACTIVE_PROFILE_VALUE=production
#   - export APP_DIRECTORY="apps/pos"
#   - export ALLO_FRONTEND_APP="pos"
#   only:
#     refs:
#       - main
#       - master
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/pos/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: build-turbo-pos
#       artifacts: true

# publish-container-pos-development:
#   extends: .publish-container
#   before_script: 
#   - export ACTIVE_PROFILE_VALUE=staging
#   - export APP_DIRECTORY="apps/pos"
#   - export ALLO_FRONTEND_APP="pos"
#   only:
#     refs:
#       - /^(dev|development|feature\/.+)$/
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/pos/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: build-turbo-pos
#       artifacts: true

# publish-container-kiosk-production:
#   extends: .publish-container
#   before_script: 
#   - export ACTIVE_PROFILE_VALUE=production
#   - export APP_DIRECTORY="apps/kiosk"
#   - export ALLO_FRONTEND_APP="kiosk"
#   only:
#     refs:
#       - main
#       - master
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/kiosk/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: build-turbo-kiosk
#       artifacts: true

# publish-container-kiosk-development:
#   extends: .publish-container
#   before_script: 
#   - export ACTIVE_PROFILE_VALUE=staging
#   - export APP_DIRECTORY="apps/kiosk"
#   - export ALLO_FRONTEND_APP="kiosk"
#   only:
#     refs:
#       - /^(dev|development|feature\/.+)$/
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/kiosk/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: build-turbo-kiosk
#       artifacts: true



# ##########################################################
# # 
# # DEPLOYMENT
# #
# ##########################################################
# .deploy-kubernetes:
#   stage: deploy
#   image: $DEPLOY_KUBERNETES_IMAGE
#   before_script: 
#     - !reference [.gitlab-ci-print-info.print_info]
#     - !reference [.gitlab-ci-print-timestamp-logging.logging]
#   script:
#     - INT_SERVICE_NAME="$(cat ${CI_PROJECT_DIR}/.tmp/service_name)-${ALLO_FRONTEND_APP}"
#     - INT_SERVICE_VERSION="$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#     - INT_KUBERNETES_FILE_INPUT_NAME="${CI_PROJECT_DIR}/.src/main/k8s/kubernetes.yaml"
#     - INT_KUBERNETES_FILE_OUTPUT_NAME=kubernetes-out.yaml
#     - apt-get -y install gettext
#     - !reference [.gitlab-ci-patch.kubernetes-yaml]
#     - INT_NEW_RELIC_APP_DEPLOYMENT_NAME="$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#     - export GOOGLE_APPLICATION_CREDENTIALS="${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json"
#     - kubectl apply -f kubernetes-out.yaml
#     - kubectl annotate deployment/$INT_SERVICE_NAME kubernetes.io/change-cause="$CI_COMMIT_AUTHOR - $CI_COMMIT_REF_SLUG - $CI_JOB_URL - $INT_SERVICE_VERSION"
#     - sleep 5
#     - |
#       echo ------------------
#       echo -
#       echo - current history deployed of $INT_SERVICE_NAME
#       echo - 
#       kubectl rollout history deployment/$INT_SERVICE_NAME
#       echo -
#       echo -
#       echo ------------------
#     #- curl -X POST "https://api.eu.newrelic.com/v2/applications/${INT_NEW_RELIC_APP_ID}/deployments.json" -H "Api-Key:${NEW_RELIC_API_KEY}" -H "Content-Type:application/json" -d "{\"deployment\":{\"revision\":\"${INT_NEW_RELIC_APP_DEPLOYMENT_NAME}\"}}"

# deploy-kubernetes-kiosk-production:
#   extends: .deploy-kubernetes
#   before_script:
#     - ALLO_FRONTEND_APP="kiosk"
#     - INT_GCLOUD_PROJECT_ID=$PROD__PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$PROD__K8S_SA_JSON_KEY_BASE64
#     - INT_GCLOUD_REGION=$PROD__K8S_COMPUTE_REGION
#     - INT_GCLOUD_CLUSTER_NAME=$PROD__K8S_CLUSTER_NAME
#     - !reference [.gitlab-ci-authenticate.gcloud-gke]
#     - INT_ENV_ENVIRONMENT="production"
#     #- INT_SERVICE_IMAGE="europe-docker.pkg.dev/${PROD__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#     - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${PROD__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)/${ALLO_FRONTEND_APP}:$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#     - INT_NEW_RELIC_APP_ID=${PROD__NEW_RELIC_APP_ID}
#   only:
#     refs:
#       - main
#       - master
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/kiosk/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: publish-container-kiosk-production
#       artifacts: false

# deploy-kubernetes-kiosk-development:
#   extends: .deploy-kubernetes
#   before_script:
#     - ALLO_FRONTEND_APP="kiosk"
#     - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__K8S_SA_JSON_KEY_BASE64
#     - INT_GCLOUD_REGION=$TEST__K8S_COMPUTE_REGION
#     - INT_GCLOUD_CLUSTER_NAME=$TEST__K8S_CLUSTER_NAME
#     - !reference [.gitlab-ci-authenticate.gcloud-gke]
#     - INT_ENV_ENVIRONMENT="staging"
#     #- INT_SERVICE_IMAGE="europe-docker.pkg.dev/${TEST__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#     - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${TEST__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)/${ALLO_FRONTEND_APP}:$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#     - INT_NEW_RELIC_APP_ID=${TEST__NEW_RELIC_APP_ID}
#   only:
#     refs:
#       - /^(dev|development|feature\/.+)$/
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/kiosk/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: publish-container-kiosk-development
#       artifacts: false

# deploy-kubernetes-pos-production:
#   extends: .deploy-kubernetes
#   before_script:
#     - ALLO_FRONTEND_APP="pos"
#     - INT_GCLOUD_PROJECT_ID=$PROD__PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$PROD__K8S_SA_JSON_KEY_BASE64
#     - INT_GCLOUD_REGION=$PROD__K8S_COMPUTE_REGION
#     - INT_GCLOUD_CLUSTER_NAME=$PROD__K8S_CLUSTER_NAME
#     - !reference [.gitlab-ci-authenticate.gcloud-gke]
#     - INT_ENV_ENVIRONMENT="production"
#     #- INT_SERVICE_IMAGE="europe-docker.pkg.dev/${PROD__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#     - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${PROD__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)/${ALLO_FRONTEND_APP}:$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#     - INT_NEW_RELIC_APP_ID=${PROD__NEW_RELIC_APP_ID}
#   only:
#     refs:
#       - main
#       - master
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/pos/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: publish-container-pos-production
#       artifacts: false


# deploy-kubernetes-pos-development:
#   extends: .deploy-kubernetes
#   before_script:
#     - ALLO_FRONTEND_APP="pos"
#     - INT_GCLOUD_PROJECT_ID=$TEST__PROJECT_ID
#     - INT_GCLOUD_SA_JSON_KEY_BASE64=$TEST__K8S_SA_JSON_KEY_BASE64
#     - INT_GCLOUD_REGION=$TEST__K8S_COMPUTE_REGION
#     - INT_GCLOUD_CLUSTER_NAME=$TEST__K8S_CLUSTER_NAME
#     - !reference [.gitlab-ci-authenticate.gcloud-gke]
#     - INT_ENV_ENVIRONMENT="staging"
#     #- INT_SERVICE_IMAGE="europe-docker.pkg.dev/${TEST__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
#     - INT_SERVICE_IMAGE="europe-docker.pkg.dev/${TEST__PROJECT_ID}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/service_name_long)/${ALLO_FRONTEND_APP}:$(cat ${CI_PROJECT_DIR}/.tmp/release_version)"
#     - INT_NEW_RELIC_APP_ID=${TEST__NEW_RELIC_APP_ID}
#   only:
#     refs:
#       - /^(dev|development|feature\/.+)$/
#     changes:
#       - "*"
#       - .src/**/*
#       - packages/**/*
#       - apps/pos/**/*
#   needs:
#     - job: preprepare-gcloud-auth-access-token
#       artifacts: true
#     - job: preprepare-versions
#       artifacts: true
#     - job: publish-container-pos-development
#       artifacts: false

