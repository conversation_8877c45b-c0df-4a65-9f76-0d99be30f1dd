# Ignore everything
*

# Allow files and directories

!.next/*
!public/*
!apps/*/.next/*
!apps/*/public/*


# Ignore unnecessary files inside allowed directories
# This should go after the allowed directories
#**/*~
#**/*.log
#**/.DS_Store
#**/Thumbs.db
#**/.vscode
#**/.idea


# Logs
#logs
#*.log

# Runtime data
#pids
#*.pid
#*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
#lib-cov

# Coverage directory used by tools like istanbul
#coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
#.grunt

# node-waf configuration
#.lock-wscript

