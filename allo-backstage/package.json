{"name": "allo-backstage", "private": true, "type": "module", "scripts": {"build": "turbo run build", "build-app-pos": "turbo run build --filter=pos", "dev": "turbo run dev", "test": "turbo test", "test:watch": "turbo test:watch", "lint": "turbo run lint", "lint-staged": "lint-staged", "check-types": "turbo run check-types", "format": "prettier --write --ignore-unknown \"**/*.{json,js,ts,jsx,tsx,md}\"", "format:check": "prettier --check \"**/*.{json,js,ts,jsx,tsx,md}\"", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{json,js,ts,jsx,tsx}": ["prettier --write --ignore-unknown"]}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "corepack": "^0.32.0", "husky": "^8.0.0", "lint-staged": "^15.5.0", "prettier": "^3.5.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.2.6", "turbo": "^2.4.4"}, "engines": {"node": ">=20"}, "nodeLinker": "node-modules", "packageManager": "npm@10.8.2", "workspaces": ["apps/*", "packages/*"], "overrides": {"react": "19.0.0", "react-dom": "19.0.0"}, "dependencies": {"sharp": "^0.32.6"}, "optionalDependencies": {"@img/sharp-linuxmusl-x64": "^0.33.2", "@img/sharp-linux-x64": "^0.33.2", "@img/sharp-wasm32": "^0.33.2", "@img/sharp-linux-arm64": "^0.33.2", "@img/sharp-darwin-x64": "^0.33.2", "@img/sharp-linux-s390x": "^0.33.2", "@img/sharp-linuxmusl-arm64": "^0.33.2", "@img/sharp-darwin-arm64": "^0.33.2", "@img/sharp-linux-arm": "^0.33.2", "@img/sharp-win32-x64": "^0.33.2", "@img/sharp-win32-ia32": "^0.33.2"}}