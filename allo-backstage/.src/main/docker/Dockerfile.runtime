ARG NODEJS_VERSION=20.18.1
ARG ALPINE_VERSION=3.20
ARG SECRETS_INIT_IMAGE=europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine

ARG ALPINE_BASE_IMAGE=alpine:${ALPINE_VERSION}
ARG NODEJS_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}


FROM ${SECRETS_INIT_IMAGE} AS secrets-init

FROM ${ALPINE_BASE_IMAGE} AS base


#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
FROM ${NODEJS_BASE_IMAGE} AS node

RUN    apk add --no-cache \
         bash \
         tini \
         libstdc++ dumb-init

# just in case pnpm is used
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
ENV COREPACK_ENABLE_DOWNLOAD_PROMPT=0

RUN \
  corepack disable &&\
  npm install -g corepack@latest --force &&\
  corepack enable

RUN    addgroup -g 1001 allo && adduser -u 1001 -G allo -s /bin/sh -D allo \
    && chown allo:allo ./
USER allo
WORKDIR /opt/allo/

RUN \
    npm install next

#COPY --chown=allo --chmod=0755 --link secrets-init-linux-amd64 ./secrets-init
COPY --chown=allo --chmod=0755 --from=secrets-init /secrets-init ./secrets-init

#ENTRYPOINT ["/sbin/tini", "--"]
#ENTRYPOINT ["/usr/bin/dumb-init", "--"]
ENTRYPOINT ["/opt/allo/secrets-init", "--provider", "google", "--exit-early"]
CMD ["/bin/bash", "-c", "echo", "allo"]
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#


#
# ---- Release ----    ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
FROM node AS release

ARG APP_DIRECTORY="."
ENV APP_DIRECTORY=$APP_DIRECTORY

# be aware to also edit the .dockerignore file if you touch anything here!
COPY --chown=allo:allo $APP_DIRECTORY/.next* ./.next
COPY --chown=allo:allo $APP_DIRECTORY/public* ./public

ARG START_COMMAND="node_modules/.bin/next start"
ENV START_COMMAND=$START_COMMAND

# expose port and define CMD
EXPOSE 3000

ENV NODE_ENV=production

CMD printenv && $START_COMMAND
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#
