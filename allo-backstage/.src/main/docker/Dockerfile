ARG NODEJS_VERSION=20.18.1
ARG ALPINE_VERSION=3.20
ARG SECRETS_INIT_IMAGE=europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine

ARG ALPINE_BASE_IMAGE=alpine:${ALPINE_VERSION}
ARG NODEJS_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}


FROM ${SECRETS_INIT_IMAGE} AS secrets-init

#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
FROM ${ALPINE_BASE_IMAGE} AS base
RUN    apk add --no-cache \
         bash \
         nodejs-current \
         tini \
         libstdc++ dumb-init
RUN    addgroup -g 1000 allo && adduser -u 1000 -G allo -s /bin/sh -D allo \
    && chown allo:allo ./

USER allo
WORKDIR /opt/allo/

#COPY --chown=allo --chmod=0755 --link secrets-init-linux-amd64 ./secrets-init
COPY --chown=allo --chmod=0755 --from=secrets-init /secrets-init ./secrets-init

#ENTRYPOINT ["/sbin/tini", "--"]
ENTRYPOINT ["/usr/bin/dumb-init", "--"]
#ENTRYPOINT ["/opt/allo/secrets-init", "--provider", "google", "--exit-early"]
CMD ["/bin/bash", "-c", "echo", "allo"]
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#


#
# ---- Release ----    ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
FROM base AS release

ARG APP_DIRECTORY="."
ENV APP_DIRECTORY=$APP_DIRECTORY

COPY --link --chown=allo:allo node_modules ./node_modules
COPY --link --chown=allo:allo $APP_DIRECTORY/.next* ./.next
COPY --link --chown=allo:allo $APP_DIRECTORY/public* ./public

ARG START_COMMAND="node_modules/.bin/next start"
ENV START_COMMAND=$START_COMMAND

# expose port and define CMD
EXPOSE 3000

ENV NODE_ENV=production

CMD printenv && $START_COMMAND
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#
