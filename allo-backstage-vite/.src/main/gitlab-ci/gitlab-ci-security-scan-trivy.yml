---
.scan-container:
  image:
    #name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/aquasec/trivy
    name: ${CONTAINER_SCANNER_IMAGE}
    entrypoint: [""]
  #tags:
  #  - small-runner
  #stage: test
  variables:
    # No need to clone the repo, we exclusively work on artifacts.  See
    # https://docs.gitlab.com/ee/ci/runners/README.html#git-strategy
    GIT_STRATEGY: none

    #
    # TRIVY feature flags
    #
    #TRIVY_USERNAME: "$CI_REGISTRY_USER"
    #TRIVY_PASSWORD: "$CI_REGISTRY_PASSWORD"
    #TRIVY_AUTH_URL: "$CI_REGISTRY"
    TRIVY_NO_PROGRESS: "true"
    SEVERITY: "UNKNOWN,LOW,MEDIUM,HIGH,CRITICAL"
    TRIVY_SEVERITY: "$SEVERITY"
    TRIVY_CACHE_DIR: ".trivycache/"
    TRIVY_IGNOREFILE_DIR: "./.trivyignore.yaml"

    TRIVY_RESULT_FILE_JSON: "${CI_PROJECT_TITLE}_${CI_JOB_NAME_SLUG}_trivy-result.json"
    TRIVY_RESULT_FILE_GITLAB_CONTAINER_SCANNING_REPORT_JSON: "gl-container-scanning-report.json"
    TRIVY_RESULT_FILE_TXT: "${CI_PROJECT_TITLE}_${CI_JOB_NAME_SLUG}_trivy-result.txt"
    TRIVY_RESULT_FILE_HTML: "${CI_PROJECT_TITLE}_${CI_JOB_NAME_SLUG}_trivy-result.html"

    TRIVY_RESULT_CRITICAL_FILE_TXT: "${CI_PROJECT_TITLE}_${CI_JOB_NAME_SLUG}_trivy-critical-result.txt"
    TRIVY_RESULT_CRITICAL_FILE_HTML: "${CI_PROJECT_TITLE}_${CI_JOB_NAME_SLUG}_trivy-critical-result.html"

    #
    # DOCKER AUTH file location
    #
    DOCKER_CONFIG_FILE: "${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json"
    GOOGLE_APPLICATION_CREDENTIALS: "${CI_PROJECT_DIR}/.auth-tmp/.gcloud-auth-docker-config.json"

    # Set to "backend" and "frontend" in the respective jobs in mono-repos.
    DIRECTORY: "./"
    # Is like that for backward-compatibility, previously we only had DIRECTORY.
    FILENAME: "gl-codeclimate-$CI_JOB_NAME_SLUG.json"

  before_script:
    - if [ ! -e ${TRIVY_CACHE_DIR} ]; then mkdir -p ${TRIVY_CACHE_DIR}; fi
    # Create trivyignore.yaml if it doesn't exist yet
    - 'if [ ! -e ${TRIVY_IGNOREFILE_DIR} ]; then echo "ambient_trivyignore_placeholder: None" > ${TRIVY_IGNOREFILE_DIR}; fi'
    - if [ ! -e ~/.docker/ ]; then mkdir -p ~/.docker/; fi
    - cp ${DOCKER_CONFIG_FILE} ~/.docker/config.json
    - |
      if [[ -f ${CI_PROJECT_DIR}/.tmp/release_container_name && -s ${CI_PROJECT_DIR}/.tmp/release_container_name ]];then
        echo "found ${CI_PROJECT_DIR}/.tmp/release_container_name and its not empty";
        export MICROSERVICE_BRANCH_OR_TAG_IMAGE="europe-docker.pkg.dev/${GCR_PROJECT}/allo-docker/allo/$(cat ${CI_PROJECT_DIR}/.tmp/release_container_name)"
      else 
        echo "ERROR!! - ${CI_PROJECT_DIR}/.tmp/release_container_name not found or its empty";
        exit 1;
      fi;
  allow_failure: true
  script:
    - trivy --version
    # cache cleanup is needed when scanning images with the same tags, it does not remove the database
    - trivy clean --scan-cache
    # update vulnerabilities db
    - trivy image --download-db-only
    - trivy image --download-java-db-only
    # --db-repository europe-west3-docker.pkg.dev/iac-dev-432418/ghcr-io/aquasecurity/trivy-db:2
    # Builds report and puts it in the default workdir $CI_PROJECT_DIR, so `artifacts:` can take it from there
    - trivy image --exit-code 1 --list-all-pkgs --format json --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" "$MICROSERVICE_BRANCH_OR_TAG_IMAGE" || IMAGE_CODE_CRITICAL=$?

    # generate different output format
    - trivy convert --exit-code 1 --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_TXT}" "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" || true
    - trivy convert --exit-code 1 --format template --template "@/contrib/html.tpl" --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_HTML}" "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" || true
    - trivy convert --exit-code 1 --format template --template "@/contrib/gitlab.tpl" --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_GITLAB_CONTAINER_SCANNING_REPORT_JSON}" "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" || true

    - trivy convert --exit-code 1 --severity CRITICAL --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_CRITICAL_FILE_TXT}" "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" || true
    - trivy convert --exit-code 1 --severity CRITICAL --format template --template "@/contrib/html.tpl" --output "${CI_PROJECT_DIR}/${TRIVY_RESULT_CRITICAL_FILE_HTML}" "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}" || true
    - exit $((IMAGE_CODE_CRITICAL))

    # Image report (Operating System Vulnerabilities)
    #- trivy image --exit-code 0 --ignorefile ${TRIVY_IGNOREFILE_DIR} --ignore-unfixed --scanners vuln --pkg-types os --format template --template "@/contrib/gitlab-codequality.tpl" -o gl-codeclimate-image.json $IMAGE >trivy-image.log 2>&1 || true
    # Filesystem report (Source Dependency Vulnerabilities)
    #- trivy filesystem --exit-code 0 --ignorefile ${TRIVY_IGNOREFILE_DIR} --ignore-unfixed --scanners misconfig,vuln --format template --template "@/contrib/gitlab-codequality.tpl" -o gl-codeclimate-fs.json $DIRECTORY >trivy-fs.log 2>&1 || true
    # Report results as table
    # Image report (Operating System Vulnerabilities)
    #- trivy image --exit-code 1 --ignorefile ${TRIVY_IGNOREFILE_DIR} --ignore-unfixed --scanners vuln --pkg-types os --format table $IMAGE || IMAGE_CODE=$?
    # Filesystem report (Source Dependency Vulnerabilities)
    #- trivy filesystem --exit-code 1 --ignorefile ${TRIVY_IGNOREFILE_DIR} --ignore-unfixed --scanners misconfig,vuln --dependency-tree --format table $DIRECTORY || FILE_CODE=$?
    # Combine report
    #- apk update && apk add jq sed
    #- jq -s 'add' gl-codeclimate-image.json gl-codeclimate-fs.json > ${FILENAME}
    #- exit $((IMAGE_CODE+FILE_CODE))
  after_script:
    - |
      if [[ -f ${TRIVY_RESULT_FILE_JSON} ]];then
       echo "====================================="
       echo "-"
       echo "- Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/${CI_PROJECT_PATH#${CI_PROJECT_ROOT_NAMESPACE}/}/-/jobs/$CI_JOB_ID/artifacts/${TRIVY_RESULT_FILE_TXT}"
       echo "- Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/${CI_PROJECT_PATH#${CI_PROJECT_ROOT_NAMESPACE}/}/-/jobs/$CI_JOB_ID/artifacts/${TRIVY_RESULT_FILE_HTML}"
       echo "-"
       echo "- CRITICAL Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/${CI_PROJECT_PATH#${CI_PROJECT_ROOT_NAMESPACE}/}/-/jobs/$CI_JOB_ID/artifacts/${TRIVY_RESULT_CRITICAL_FILE_TXT}"
       echo "- CRITICAL Report will be available at https://$CI_PROJECT_ROOT_NAMESPACE.$CI_PAGES_DOMAIN/-/${CI_PROJECT_PATH#${CI_PROJECT_ROOT_NAMESPACE}/}/-/jobs/$CI_JOB_ID/artifacts/${TRIVY_RESULT_CRITICAL_FILE_HTML}"
       echo "-"
       echo "====================================="
      fi
  cache:
    - key: cache-trivy-$CI_COMMIT_REF_SLUG
      fallback_keys:
        - cache-trivy-$CI_DEFAULT_BRANCH
      paths:
        - ${TRIVY_CACHE_DIR}
      policy: pull-push
  artifacts:
    access: 'developer'
    reports:
      # https://docs.gitlab.com/user/application_security/detect/security_scan_results/
      container_scanning: "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_GITLAB_CONTAINER_SCANNING_REPORT_JSON}"
      cyclonedx: "**/gl-sbom-*.cdx.json"
      codequality: $FILENAME
    paths:
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_JSON}"
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_TXT}"
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_HTML}"
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_FILE_GITLAB_CONTAINER_SCANNING_REPORT_JSON}"
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_CRITICAL_FILE_TXT}"
      - "${CI_PROJECT_DIR}/${TRIVY_RESULT_CRITICAL_FILE_HTML}"
      - "**/gl-sbom-*.cdx.json"
      - "trivy-*.log"
      - $FILENAME
    expire_in: 1 month
    when: always