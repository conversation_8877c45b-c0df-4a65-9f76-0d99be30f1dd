#!/bin/bash
#set -euxo pipefail
set -e

docker -v >/dev/null 2>&1 || { echo >&2 "E: I require docker but it's not installed. Aborting."; exit 1; }
gcloud -v >/dev/null 2>&1 || { echo >&2 "E: I require gcloud but it's not installed. Aborting."; exit 1; }
test 1 >/dev/null 2>&1 || { echo >&2 "E: I require test but it's not installed. Aborting."; exit 1; }
getent --version >/dev/null 2>&1 || { echo >&2 "E: I require getent but it's not installed. Aborting."; exit 1; }
cut --version >/dev/null 2>&1 || { echo >&2 "E: I require cut but it's not installed. Aborting."; exit 1; }
whoami --version >/dev/null 2>&1 || { echo >&2 "E: I require whoami but it's not installed. Aborting."; exit 1; }
id --version >/dev/null 2>&1 || { echo >&2 "E: I require id but it's not installed. Aborting."; exit 1; }

USER=`whoami`
USER_UID=`id -u ${USER}`
USER_GID=`id -g ${USER}`
DIR=`pwd`
DOCKER_GID=`getent group docker | cut --delimiter ':' --fields 3`
test -t 0 && DOCKER_USE_TTY="-it"

GCLOUD_CONFIG_DIR_CLOUDSDK_CONFIG=`gcloud info --format='value(config. paths. global_config_dir)'`
GCLOUD_CONFIG_ACCOUNT=`gcloud config get account`
GCLOUD_CONFIG_PROJECT=`gcloud config get project`
DOCKER_CONFIG=${DOCKER_CONFIG:-$HOME/.docker}

echo "------------------------------------"
echo "-"
echo "GCLOUD ACCOUNT      : [$GCLOUD_CONFIG_ACCOUNT]"
echo "GCLOUD PROJECT      : [$GCLOUD_CONFIG_PROJECT]"
echo "---"
echo "pwd                 : [$DIR]"
echo "USER                : [$USER]"
echo "USER_UID            : [$USER_UID]"
echo "USER_GID            : [$USER_GID]"
echo "DOCKER_GID          : [$DOCKER_GID]"
echo "DOCKER_USE_TTY      : [$DOCKER_USE_TTY]"
echo "DOCKER_CONFIG       : [$DOCKER_CONFIG]"
echo "GCLOUD_CONFIG_DIR   : [$GCLOUD_CONFIG_DIR_CLOUDSDK_CONFIG]"
echo "-"
echo "------------------------------------"

docker build --rm -t docker-build-bash -f .src/main/docker/Dockerfile.build-docker-bash .
docker build --rm -t docker-build-node -f .src/main/docker/Dockerfile.build-node .

docker run ${DOCKER_USE_TTY} --rm \
  -u "${USER_UID}":"${USER_GID}" \
  -v "${DIR}":"${DIR}"  \
  -w "${DIR}" \
  --group-add "root" \
  --group-add "${DOCKER_GID}" \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -e CLOUDSDK_CONFIG="${GCLOUD_CONFIG_DIR_CLOUDSDK_CONFIG}" \
  -v "${GCLOUD_CONFIG_DIR_CLOUDSDK_CONFIG}":"${GCLOUD_CONFIG_DIR_CLOUDSDK_CONFIG}":rw \
  -e DOCKER_CONFIG="${DOCKER_CONFIG}" \
  -v "${DOCKER_CONFIG}":"${DOCKER_CONFIG}":rw \
  -e HOME=${HOME} \
  -v "${HOME}/.gitconfig":"${HOME}/.gitconfig" \
  -e BUILD_IMAGE="docker-build-node" \
  -e BUILD_APP="${BUILD_APP}" \
  -e BUILD_APP_TEST="${BUILD_APP_TEST}" \
  -e BUILD_APP_IMAGE_RUN="${BUILD_APP_IMAGE_RUN}" \
  docker-build-bash bash -c './.src/main/build-helper/docker/build-docker.sh'
