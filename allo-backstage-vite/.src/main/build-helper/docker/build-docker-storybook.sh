#!/bin/bash
#set -euxo pipefail
set -e

USER=${USER:-`whoami`}
USER_UID=${USER_UID:-`id -u ${USER}`}
USER_GID=${USER_GID:-`id -g ${USER}`}
DIR=`pwd`
test -t 0 && DOCKER_USE_TTY="-it"

BUILD_IMAGE="${BUILD_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/node:20-slim-corepack-turbo}"

# ------------------------------------------------------

echo "- npm run build --filter=$BUILD_APP on $BUILD_IMAGE"
docker run ${DOCKER_USE_TTY} --rm \
    -u "${USER_UID}":"${USER_GID}" \
    -v "${DIR}":"${DIR}"  \
    -w "${DIR}" \
    -e BUILD_APP="$BUILD_APP" \
    --entrypoint /bin/bash \
    ${BUILD_IMAGE} -c 'export PATH="$(pwd)/node_modules/.bin":$PATH; echo $PATH; corepack npm run storybook:build --filter=$BUILD_APP...'
echo ""
