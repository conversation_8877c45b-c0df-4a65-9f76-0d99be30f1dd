---
apiVersion: "v1"
kind: "List"
items:
  - apiVersion: v1
    kind: ConfigMap
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nginx"
        app.kubernetes.io/part-of: "allO"
      creationTimestamp: null
      name: "$INT_SERVICE_NAME"
    data:
      nginx.conf: |-
        #https://www.f5.com/company/blog/nginx/avoiding-top-10-nginx-configuration-mistakes
        #https://github.com/die-net/nginx-config-example/blob/main/nginx.conf
        #https://www.getpagespeed.com/server-setup/nginx/tuning-proxy_buffer_size-in-nginx
        
        # How many child processes to spawn to handle requests.  Should at least be
        # number of non-HT cores available.
        worker_processes auto;
        
        # For safety, make sure the kernel allows us to use at least
        # worker_processes * worker_connections * 2 + 100 filedescriptors.
        worker_rlimit_nofile 262144;
        
        pid        /tmp/nginx.pid;
        
        # for docker containers log to stdout
        error_log /var/log/nginx/error.log notice;
        error_log /dev/stdout warn;
        
        
        events {
          # epoll is the scalable Linux event mechanism.  Die if we can't use it.
          use epoll;
          
          # Total allowed connections is worker_processes * worker_connections.
          # Expect to run into problems at 60-70% of that; accept() mutex load
          # balancing isn't perfect.
          worker_connections 65536;
          
          # Reduce overhead by repeatedly accept()ing until accept queue is empty.
          multi_accept on;
        }
        
        http {
          # Don't mention which NginX version we are running in responses.
          server_tokens off;
          
          # Which DNS resolvers to use for outbound HTTP connections.
          # "***********" is Amazon's "Magic" DNS server.
          resolver 8.8.8.8;
          resolver_timeout 5s;
          
          # Disable nagle and try to coalesce TCP packets.
          tcp_nodelay    on;
          tcp_nopush     on;
          
          # Send large chunks of local files without copying to userspace.
          sendfile           on;
          sendfile_max_chunk 1m;
          
          # Don't let a client get crazy with Range headers.
          max_ranges 10;
          
          # Allow connections to stay open after a response in case the client
          # wants to send another request.  Claim to allow 4 minutes (NAT devices
          # commonly timeout in 5 min), actually allow an extra 5 seconds just in
          # case.
          keepalive_timeout 245 240;
          
          # Chrome preconnects to servers it thinks it might use soon.  Match
          # keepalive_timeout here.  This is total time to wait for complete
          # headers.
          client_header_timeout 4m;
          
          # How long do we wait for another packet of the request body to arrive?
          client_body_timeout   20s;
          
          # Allow POSTs of 10 megs.
          client_max_body_size  10m;
          
          # How long do we wait for another 4k of the response to be ACKed?
          send_timeout          20s;
          
          # If we timeout an incomplete response, skip FIN_WAIT1 and send RST.
          reset_timedout_connection on;
          
          # Don't send port numbers for odd ports.
          port_in_redirect off;
          
          # Allow more server_names than default limit.
          server_names_hash_max_size 4096;
          server_names_hash_bucket_size 512;
          
          # Allow larger maps than default.
          map_hash_max_size 2048;
          map_hash_bucket_size 128;
          
          # Allow more variables than default.
          variables_hash_max_size 1024;
          variables_hash_bucket_size 128;
          
          # Where to store tempfiles.
          proxy_temp_path /tmp/proxy_temp;
          client_body_temp_path /tmp/client_temp;
          fastcgi_temp_path /tmp/fastcgi_temp;
          uwsgi_temp_path /tmp/uwsgi_temp;
          scgi_temp_path /tmp/scgi_temp;
          
          # Set up cache. (25 meg RAM = ~195,000 objects.)
          proxy_cache_path /var/cache/nginx/nginx_proxy_cache keys_zone=on:25m max_size=20g levels=1:2 inactive=1d;
          
          # Define a fast rate-limit bucket for use in acl/deny-all and include/static-assets.
          #limit_req_zone $binary_remote_net zone=fast_limit_buckets:10m rate=100r/s;
          
          include       /etc/nginx/mime.types;
          default_type  application/octet-stream;
          
          log_format  main     '$remote_addr - [$time_local] $status $request_method "$scheme://$host$request_uri" '
          '$body_bytes_sent "$http_user_agent"';
          
          #log_format combined '$remote_addr - $remote_user [$time_local] '
          #                    '"$request" $status $body_bytes_sent '
          #                    '"$http_referer" "$http_user_agent"';
          
          #log_format request '$msec'
          #                   '\t$remote_addr'
          #                   '\t$http_x_forwarded_for'
          #                   '\t$geoip_city_country_code/$geoip_region/$geoip_postal_code'
          #                   '\t$scheme://$host$request_uri'
          #                   '\t$ssl_protocol/$ssl_cipher'
          #                   '\t$server_protocol'
          #                   '\t$request_method'
          #                   '\t$status'
          #                   '\t$upstream_status'
          #                   '\t$body_bytes_sent'
          #                   '\t$gzip_ratio'
          #                   '\t$request_time'
          #                   '\t$upstream_response_time'
          #                   '\t-'  # '\t$tcpinfo_rtt/$tcpinfo_rttvar/$tcpinfo_snd_cwnd'
          #                   '\t$upstream_addr'
          #                   '\t$upstream_cache_status/$proxy_host'
          #                   '\t$cookie_u'
          #                   '\t$connection/$pid/$remote_port/$connection_requests/$x_limit_type/$user_agent_class/$is_mobile/$prefer_mime_type/$prefer_language'
          #                   '\t$http_referer'
          #                   '\t$http_user_agent'
          #                   '\t$cookie_username'
          #                   '\t$upstream_http_x_log'
          #                   ;
          
          
          # for docker containers log to stdout
          #access_log /var/log/nginx/access.log  main;
          #access_log /dev/stdout main;
          
          # Enable vts status module.
          #vhost_traffic_status_zone;
          
          # Enable vts latency histogram buckets.
          #vhost_traffic_status_histogram_buckets 0.005 0.01 0.05 0.1 0.25 0.5 1 2.5 5 10;
          #vhost_traffic_status_filter_by_host on;
          
          #vhost_traffic_status_filter_max_node 32
          
          include /etc/nginx/conf.d/*.conf;
        }

      default.conf: |-
        server {
          server_tokens   off;
          
          listen          3000;
          server_name     localhost;
          #env.sh will patch the files 
          #root            /usr/share/nginx/html;
          root            /tmp/nginx/html;
          include         mime.types;
          charset         UTF-8;
          source_charset  UTF-8;
          
          gzip            on;
          gzip_vary       on;
          gzip_http_version  1.1;
          gzip_comp_level 5;
          gzip_types
          application/atom+xml
          application/javascript
          application/json
          application/rss+xml
          application/vnd.ms-fontobject
          application/x-font-ttf
          application/x-web-app-manifest+json
          application/xhtml+xml
          application/xml
          font/opentype
          image/svg+xml
          image/x-icon
          text/css
          text/plain
          text/x-component;
          gzip_proxied    no-cache no-store private expired auth;
          gzip_min_length 32;
          gunzip          on;
          
          location = /healthz/stub_status {
            access_log   off;
            allow        127.0.0.1;
            deny all;
            default_type text/plain;
            stub_status  on;
          }
          
          location = /healthz {
            access_log   off;
            default_type text/html;
            return 200;
          }
          location = /assets/remoteEntry.js {
            expires off;
            add_header 'Access-Control-Allow-Origin' '*';
            add_header Cache-Control "public, max-age=0, s-maxage=0, must-revalidate" always;
            try_files $uri =404;
          }
          location ^~ /assets/ {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header Cache-Control "public, max-age=31536000, s-maxage=31536000, immutable";
            try_files $uri =404;
          }
          location / {
            autoindex off;
            expires off;
            add_header 'Access-Control-Allow-Origin' '*';
            add_header Cache-Control "public, max-age=0, s-maxage=0, must-revalidate" always;
            index index.html;
            try_files $uri $uri/ /index.html =404;
          }
        }

  # this backend config is used in multiple deployments - dont touch it without knowing what you do :D - and it will affect all of the apps in allo-frontend
  - apiVersion: cloud.google.com/v1
    kind: BackendConfig
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nginx"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      connectionDraining:
        drainingTimeoutSec: 60
      healthCheck:
        checkIntervalSec: 15
        port: 3000
        requestPath: /healthz
        type: HTTP
      securityPolicy:
        name: global-sp-default
      timeoutSec: 80
      customResponseHeaders:
        headers:
        - 'Strict-Transport-Security: max-age=86400; includeSubDomains'
        - 'X-Permitted-Cross-Domain-Policies: none'
        - "Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://*.allo.restaurant https://*.posthog.com https://*.osano.com https://js-agent.newrelic.com https://bam.nr-data.net ; connect-src 'self' https://*.allo.restaurant https://*.posthog.com https://*.osano.com https://*.nr-data.net; img-src 'self' blob: data: https://*.allo.restaurant https://storage.googleapis.com; style-src 'self' https://*.allo.restaurant https://*.osano.com 'unsafe-inline'; font-src 'self' https://*.allo.restaurant https://fonts.googleapis.com https://fonts.gstatic.com; frame-ancestors 'self' https://*.allo.restaurant; worker-src 'self' blob: data: https://*.osano.com; frame-src 'self' https://*.allo.restaurant https://*.osano.com;"
        - 'X-Frame-Options: SAMEORIGIN'
        - 'Cross-Origin-Resource-Policy: same-origin'
        - 'Cross-Origin-Embedder-Policy: same-origin'
        - 'Cross-Origin-Opener-Policy: same-origin'
        - 'X-Content-Type-Options: nosniff'
        - 'Referrer-Policy: strict-origin-when-cross-origin'
        - "Feature-Policy: camera 'none'; microphone 'none'; midi 'none'; geolocation 'none'; usb 'none'"
        - "Permissions-Policy: camera=(), microphone=(), midi=(), geolocation=(), interest-cohort=(), usb=()"
    spec:
      connectionDraining:
        drainingTimeoutSec: 60
      healthCheck:
        checkIntervalSec: 15
        port: 3000
        requestPath: /healthz
        type: HTTP
      securityPolicy:
        name: global-sp-default
      timeoutSec: 80
  - apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nginx"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          #app: "$INT_SERVICE_NAME"
          app.kubernetes.io/name: "$INT_SERVICE_NAME"
          app.kubernetes.io/instance: "$INT_SERVICE_NAME"
  - apiVersion: "v1"
    kind: "Service"
    metadata:
      annotations:
        cloud.google.com/backend-config: '{"default":"$INT_SERVICE_NAME"}' # backend configuration to use (eg for cloud armor)
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nginx"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      ports:
        - name: "http"
          port: 3000
          targetPort: 3000
      selector:
        #app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
  - apiVersion: "apps/v1"
    kind: "Deployment"
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nginx"
        app.kubernetes.io/part-of: "allO"
        kube-monkey/enabled: enabled
        kube-monkey/identifier: "$INT_SERVICE_NAME"
        kube-monkey/kill-mode: fixed
        kube-monkey/kill-value: "1"
        kube-monkey/mtbf: "2"
      name: "$INT_SERVICE_NAME"
    spec:
      replicas: 2
      selector:
        matchLabels:
          app: "$INT_SERVICE_NAME"
      template:
        metadata:
          labels:
            app: "$INT_SERVICE_NAME"
            app.kubernetes.io/name: "$INT_SERVICE_NAME"
            app.kubernetes.io/instance: "$INT_SERVICE_NAME"
            app.kubernetes.io/version: "$INT_SERVICE_VERSION"
            app.kubernetes.io/component: "$INT_SERVICE_NAME"
            app.kubernetes.io/component-type: "nginx"
            app.kubernetes.io/part-of: "allO"
            kube-monkey/enabled: enabled
            kube-monkey/identifier: "$INT_SERVICE_NAME"
            kube-monkey/kill-mode: fixed
            kube-monkey/kill-value: "1"
            kube-monkey/mtbf: "2"
          annotations:
            # prometheus scraping hint
            prometheus.io/path: "/healthz/vts_status/format/prometheus"
            prometheus.io/scrape: "true"
            prometheus.io/port: "3000"
            # important for emptyDir volume mounts
            cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        spec:
          topologySpreadConstraints:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/name: "$INT_SERVICE_NAME"
                  app.kubernetes.io/instance: "$INT_SERVICE_NAME"
              matchLabelKeys:
                - pod-template-hash
              maxSkew: 1
              whenUnsatisfiable: DoNotSchedule
              topologyKey: "kubernetes.io/hostname"
          imagePullSecrets:
            - name: artifact-registry-publisher
          containers:
            - env:
                - name: "KUBERNETES_NAMESPACE"
                  valueFrom:
                    fieldRef:
                      fieldPath: "metadata.namespace"
                - name: "KUBERNETES_POD_IP"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "HOSTNAME"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "MY_APP_VITE_PUBLIC_OPERATIONS_REMOTE_BASE_URL"
                  value: "https://operations.stage.allo.restaurant"
              image: "$INT_SERVICE_IMAGE"
              imagePullPolicy: "IfNotPresent"
              name: "$INT_SERVICE_NAME"
              ports:
                - containerPort: 3000
                  name: "http"
                  protocol: "TCP"
              resources:
                requests:
                  cpu: 15m
                  memory: 256Mi
                limits:
                  memory: 512Mi # high limit added here - to protect the node and other services just in case ...
                  ephemeral-storage: 128Mi # limit added here - to protect the node and other services just in case ...
              lifecycle:
                preStop:
                  exec:
                    command: 
                    - "/bin/sh"
                    - "-ce" 
                    - |
                      sleep 10 # give k8s svc balancer the chance to unregister routing before shutting down
              startupProbe:
                httpGet:
                  scheme: HTTP
                  path: /healthz
                  port: 3000
                periodSeconds: 5
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 60 # number of tries every periodSeconds - total time would be failureThreshold * periodSeconds
              readinessProbe:
                httpGet:
                  scheme: HTTP
                  path: /healthz
                  port: 3000
                initialDelaySeconds: 5
                periodSeconds: 10
                timeoutSeconds: 3
                successThreshold: 2
                failureThreshold: 10
              livenessProbe:
                httpGet:
                  #Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.
                  scheme: HTTP
                  #Path to access on the HTTP server.
                  path: /healthz
                  #Name or number of the port to access on the container. Number must be in the range 1 to 65535.
                  port: 3000
                #Number of seconds after the container has started before liveness or readiness probes are initiated. Defaults to 0 seconds. Minimum value is 0.
                initialDelaySeconds: 5
                #How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.
                periodSeconds: 10
                #Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1.
                timeoutSeconds: 3
                #Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                successThreshold: 1
                #When a Pod starts and the probe fails, Kubernetes will try failureThreshold times before giving up. Giving up in case of liveness probe means restarting the container. In case of readiness probe the Pod will be marked Unready. Defaults to 3. Minimum value is 1
                failureThreshold: 10
              securityContext:
                allowPrivilegeEscalation: false
                readOnlyRootFilesystem: true
                seccompProfile:
                  type: "RuntimeDefault"
                capabilities:
                  drop:
                    - "ALL"
              volumeMounts:
                - mountPath: /tmp
                  name: tmp-tmp
                - mountPath: /var/run
                  name: tmp-run
                - mountPath: /var/log/nginx
                  name: tmp-log
                - mountPath: /var/cache/nginx/nginx_proxy_cache
                  name: tmp-cache
                #- mountPath: /etc/nginx/conf.d
                #  name: etc-nginx-conf-d
                - mountPath: /etc/nginx/conf.d/default.conf
                  name: config-volume
                  subPath: default.conf
                - mountPath: /etc/nginx/nginx.conf
                  name: config-volume
                  subPath: nginx.conf
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          terminationGracePeriodSeconds: 20
          volumes:
            - name: tmp-tmp
              emptyDir:
                sizeLimit: 128Mi # limit added here - to protect the node and other services just in case ...
            - name: tmp-run
              emptyDir:
                sizeLimit: 128Mi # limit added here - to protect the node and other services just in case ...
            - name: tmp-log
              emptyDir:
                sizeLimit: 128Mi # limit added here - to protect the node and other services just in case ...
            - name: tmp-cache
              emptyDir:
                sizeLimit: 128Mi # limit added here - to protect the node and other services just in case ...
            - name: etc-nginx-conf-d
              emptyDir:
                sizeLimit: 128Mi # limit added here - to protect the node and other services just in case ...
            - configMap:
                defaultMode: 420
                name: "$INT_SERVICE_NAME"
              name: config-volume
