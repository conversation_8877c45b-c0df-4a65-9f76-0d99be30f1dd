import federation from '@originjs/vite-plugin-federation';
import tailwindcss from '@tailwindcss/vite';
import { TanStackRouterVite } from '@tanstack/router-plugin/vite';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';
import dynamicImport from 'vite-plugin-dynamic-import';

// https://vite.dev/config/

// @ts-nocheck
export default ({ mode }: { mode: any }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) };

  return defineConfig({
    plugins: [
      dynamicImport(),
      TanStackRouterVite({ target: 'react', autoCodeSplitting: true }),
      react(),
      tailwindcss(),
      federation({
        name: 'container',
        remotes: {
          // ['old-operations-center']: `${process.env.VITE_OPERATIONS_REMOTE_BASE_URL || 'http://localhost:3001'}/assets/remoteEntry.js`,
          ['operations']: `${process.env.VITE_PUBLIC_OPERATIONS_REMOTE_BASE_URL || 'http://localhost:3002'}/assets/remoteEntry.js`,
        },
        shared: ['react', 'react-dom', '@tanstack/react-router'],
      }),
    ],
    build: {
      target: 'esnext',
      minify: false,
      cssCodeSplit: false,
    },
    server: {
      host: 'localhost',
      port: 3000,
      strictPort: true,
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
    },
    // preview: {
    //   host: "localhost",
    //   port: 3000,
    //   strictPort: true,
    //   headers: {
    //     "Access-Control-Allow-Origin": "*",
    //   },
    // },
  });
};
