import { StrictMode } from "react";
import ReactD<PERSON> from "react-dom/client";
import { RouterProvider } from "@tanstack/react-router";

import { mainRouter } from "./routes/routes";
import "./App.css";

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof mainRouter;
  }
}

const rootElement = document.getElementById("root")!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);

  root.render(
    <StrictMode>
      <RouterProvider router={mainRouter} />
    </StrictMode>
  );
}
