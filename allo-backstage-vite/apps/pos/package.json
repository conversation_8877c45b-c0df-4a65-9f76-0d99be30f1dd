{"name": "pos", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "dev:federation": "concurrently \"vite build --watch\" \"vite preview --port 3000\"", "build": "tsc -b && VITE_PUBLIC_OPERATIONS_REMOTE_BASE_URL=MY_APP_VITE_PUBLIC_OPERATIONS_REMOTE_BASE_URL vite build", "lint": "eslint .", "preview": "vite preview --port 3000"}, "dependencies": {"@allo/ui": "^0.0.39", "@tailwindcss/vite": "^4.0.16", "@tanstack/react-router": "~1.114.32", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.21.0", "@originjs/vite-plugin-federation": "^1.4.0", "@tailwindcss/postcss": "^4.0.9", "@tanstack/react-router-devtools": "~1.114.32", "@tanstack/router-plugin": "~1.114.32", "@types/jest": "^29.5.14", "@types/node": "^22.13.5", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.5.3", "prettier": "^3.5.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.9", "ts-node": "^10.9.2", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-plugin-dynamic-import": "^1.6.0"}}