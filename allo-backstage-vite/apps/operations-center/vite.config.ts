import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import tailwindcss from "@tailwindcss/vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { VitePWA } from "vite-plugin-pwa";
import federation from "@originjs/vite-plugin-federation";
import legacy from "@vitejs/plugin-legacy";

// https://vitejs.dev/config/

export default defineConfig(() => {
  return {
    plugins: [
      TanStackRouterVite({
        target: "react",
        autoCodeSplitting: true,
      }),
      react(),
      tailwindcss(),
      tsconfigPaths(),
      VitePWA({
        registerType: "autoUpdate",
        manifest: {
          name: "allO POS Prototype",
          theme_color: "#FAFAFA",
        },
        workbox: {
          globIgnores: ["**/sb-manager/**", "**/storybook-static/**"],
        },
      }),
      federation({
        name: "operations",
        filename: "remoteEntry-legacy.js",
        exposes: {
          "./App": "./src/App.tsx",
          // "./router-tree": {
          //   import: "./src/routeTree.gen.ts",
          //   name: "router-tree",
          // },
        },
        // shared: ["react", "react-dom", "@tanstack/react-router", "@allo/ui"],
      }),
      !process.env.FEDERATION &&
        legacy({
          targets: ["defaults", "IE 11"],
          additionalLegacyPolyfills: ["regenerator-runtime/runtime"],
          polyfills: [
            "es.promise",
            "es.array.iterator",
            "es.object.assign",
            "es.symbol",
          ],
        }),
    ],
    build: {
      target: "esnext",
      // minify: false,
      // cssCodeSplit: false,
    },
    server: {
      host: true,
      port: 3002,
    },
  };
});
