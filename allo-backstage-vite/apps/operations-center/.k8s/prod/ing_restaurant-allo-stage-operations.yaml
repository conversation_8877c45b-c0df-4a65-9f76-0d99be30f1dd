apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: gce
    kubernetes.io/ingress.global-static-ip-name: restaurant-allo-stage-operations
    networking.gke.io/managed-certificates: restaurant-allo-stage-operations
    networking.gke.io/v1beta1.FrontendConfig: fc-default
  name: restaurant-allo-stage-operations
  namespace: default
spec:
  ingressClassName: gce
  rules:
    - host: operations.stage.allo.restaurant
      http:
        paths:
          - backend:
              service:
                name: communication-service
                port:
                  number: 8080
            path: /communication-service
            pathType: Prefix
          - backend:
              service:
                name: fiskal-service
                port:
                  number: 8080
            path: /fiskal-service
            pathType: Prefix
          - backend:
              service:
                name: gluttony-api
                port:
                  number: 8080
            path: /gluttony-api
            pathType: Prefix
          - backend:
              service:
                name: google-integration-service
                port:
                  number: 8080
            path: /google-integration-service
            pathType: Prefix
          - backend:
              service:
                name: keycloak-http
                port:
                  number: 80
            path: /keycloak
            pathType: Prefix
          - backend:
              service:
                name: marketing-service
                port:
                  number: 8080
            path: /marketing-service
            pathType: Prefix
          - backend:
              service:
                name: marketplace-service
                port:
                  number: 8080
            path: /marketplace-service
            pathType: Prefix
          - backend:
              service:
                name: restaurant-api
                port:
                  number: 8080
            path: /restaurant-api
            pathType: Prefix
          - backend:
              service:
                name: scheduler-service
                port:
                  number: 8080
            path: /scheduler-service
            pathType: Prefix
          - backend:
              service:
                name: ui-service
                port:
                  number: 8080
            path: /ui-service
            pathType: Prefix
          - backend:
              service:
                name: user-service
                port:
                  number: 8080
            path: /user-service
            pathType: Prefix
          - backend:
              service:
                name: websocket-service
                port:
                  number: 8080
            path: /websocket-service
            pathType: Prefix
          - backend:
              service:
                name: allo-backstage-operations-center
                port:
                  name: http
            path: /
            pathType: Prefix
