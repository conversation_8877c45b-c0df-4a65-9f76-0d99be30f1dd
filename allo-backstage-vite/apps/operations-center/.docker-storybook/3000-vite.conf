server {
    server_tokens   off;

    listen          3000;
    server_name     localhost;
    #root            /usr/share/nginx/html;
    root            /tmp/nginx/html;
    include         mime.types;
    charset         UTF-8;
    source_charset  UTF-8;

    gzip            on;
    gzip_vary       on;
    gzip_http_version  1.1;
    gzip_comp_level 5;
    gzip_types
                    application/atom+xml
                    application/javascript
                    application/json
                    application/rss+xml
                    application/vnd.ms-fontobject
                    application/x-font-ttf
                    application/x-web-app-manifest+json
                    application/xhtml+xml
                    application/xml
                    font/opentype
                    image/svg+xml
                    image/x-icon
                    text/css
                    text/plain
                    text/x-component;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_min_length 32;
    gunzip          on;

    location = /healthz/stub_status {
        access_log   off;
        allow        127.0.0.1;
        deny all;
        default_type text/plain;
        stub_status  on;
    }

    location = /healthz {
        access_log   off;
        default_type text/html;
        return 200;
    }
    location /storybook/ {
        #auth_basic           "allO’s private area :)";
        #auth_basic_user_file /etc/nginx/.htpasswd;
        index  index.html index.htm;
        try_files $uri$args $uri /index.html =404;
        break;
    }
    location / {
        default_type text/html;
        return 200;
        break;
    }

}
