import { cn, easings } from "@allo/ui";
import { create } from "zustand";
import { AnimatePresence, motion } from "motion/react";
import { ComponentPropsWithRef, SVGProps, useEffect, useRef } from "react";

// FIXME: should merge with @allo/ui Toast component

const DEFAULT_TOAST_DURATION = 2500;

type ToastVariant = "success" | "error";

type Toast = {
  id: string;
  message: string;
  variant: ToastVariant;
  icon: React.ComponentType<SVGProps<SVGSVGElement>> | null;
  duration: number;
  delay?: number;
};

type ToastStore = {
  toasts: Toast[];
  addToast: (toast: Omit<Toast, "id">) => void;
  removeToast: (id: string) => void;
};

const useToastStore = create<ToastStore>((set) => ({
  toasts: [],
  addToast: (toast) =>
    set((state) => ({
      toasts: [
        ...state.toasts,
        { ...toast, id: Math.random().toString(36).slice(2) },
      ],
    })),
  removeToast: (id) =>
    set((state) => ({
      toasts: state.toasts.filter((toast) => toast.id !== id),
    })),
}));

export const Toaster = ({
  className,
  ...props
}: ComponentPropsWithRef<"div">) => {
  const { toasts, removeToast } = useToastStore();
  const timers = useRef<Map<string, NodeJS.Timeout>>(new Map());

  useEffect(() => {
    toasts.forEach((toast) => {
      if (!timers.current.has(toast.id)) {
        const timerId = setTimeout(
          () => {
            removeToast(toast.id);
            timers.current.delete(toast.id);
          },
          toast.duration + (toast.delay ?? 0)
        );

        timers.current.set(toast.id, timerId);
      }
    });
  }, [toasts, removeToast]);

  return (
    <div
      className={cn(
        "pointer-events-none absolute top-full z-20 flex w-full flex-col-reverse items-center px-4",
        className
      )}
      {...props}
    >
      <AnimatePresence>
        {[...toasts]
          .reverse()
          .map(({ id, message, variant, icon: Icon, delay }) => (
            <motion.div
              key={id}
              className="pointer-events-auto relative mx-auto"
              layout="position"
              onClick={() => removeToast(id)}
              transition={{
                duration: 0.4,
                ease: easings["emphasized-decelerate"],
              }}
            >
              <motion.div
                className="py-1"
                initial={{ y: "-100%", scale: 0.925 }}
                animate={{
                  y: 0,
                  scale: 1,
                  transition: {
                    duration: 0.4,
                    delay: (delay ?? 0) / 1000,
                    ease: easings["emphasized-decelerate"],
                  },
                }}
                exit={{
                  y: "-133%",
                  transition: {
                    duration: 0.25,
                    ease: easings["emphasized-accelerate"],
                  },
                }}
              >
                <div
                  role="alert"
                  aria-live="polite"
                  className="text-foreground bg-background mx-auto flex w-fit items-center gap-2 rounded-xl p-3 text-xs font-medium"
                >
                  {Icon && (
                    <Icon
                      className={cn(
                        "size-4 shrink-0",
                        variant === "success" && "text-positive",
                        variant === "error" && "text-negative"
                      )}
                    />
                  )}
                  <span>{message}</span>
                </div>
              </motion.div>
            </motion.div>
          ))}
      </AnimatePresence>
    </div>
  );
};

// eslint-disable-next-line react-refresh/only-export-components
export const useToast = () => {
  const { addToast } = useToastStore();

  return {
    toast: ({
      message,
      variant = "success",
      icon = null,
      delay = 0,
      duration = DEFAULT_TOAST_DURATION,
    }: Omit<Toast, "id" | "variant" | "icon" | "duration"> &
      Partial<Pick<Toast, "variant" | "icon" | "duration">>) => {
      addToast({ message, variant, icon, delay, duration });
    },
  };
};
