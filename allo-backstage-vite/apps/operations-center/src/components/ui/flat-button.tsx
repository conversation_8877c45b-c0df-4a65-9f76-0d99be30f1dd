import { cn, cva, Slot, Slottable, Spinner } from "@allo/ui";
import { ComponentPropsWithRef } from "react";

export interface ButtonProps extends ComponentPropsWithRef<"button"> {
  asChild?: boolean;
  square?: boolean;
  isLoading?: boolean;
}

const flatButtonStyle = cva({
  base: [
    "relative h-10 w-fit rounded-xl px-3.5",
    "flex items-center justify-center gap-2.5",
    "text-xs leading-none text-(--button-text-color) [--button-text-color:var(--color-foreground)]",
    "[&_.lucide]:stroke-1 [&_.lucide]:text-xl",
    "focus-visible:ring-focus focus:outline-none focus-visible:ring-4",
  ],
  variants: {
    square: {
      true: "w-12 px-0",
      false: "",
    },
  },
});

const FlatButton = ({
  children,
  className,
  asChild = false,
  isLoading = false,
  square = false,
  ref,
  ...props
}: ButtonProps) => {
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      className={cn(
        flatButtonStyle({ square }),
        isLoading && "text-transparent",
        className
      )}
      ref={ref}
      {...props}
    >
      <Slottable child={children} asChild={asChild}>
        {(child) => (
          <>
            {child}
            {isLoading && (
              <span
                data-button-spinner
                className={cn(
                  "absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2",
                  "text-(--button-text-color)"
                )}
              >
                <Spinner />
              </span>
            )}
          </>
        )}
      </Slottable>
    </Comp>
  );
};

export { FlatButton };
