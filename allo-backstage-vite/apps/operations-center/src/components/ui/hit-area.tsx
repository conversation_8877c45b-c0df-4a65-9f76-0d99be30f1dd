import { cn } from "@allo/ui";

interface HitAreaProps {
  offset?: number;
}

/**
 * Extends the clickable area of a parent element by creating an invisible overlay that is larger than the parent element.
 *
 * @param offset - Number of spacing units to extend the hit area by. Defaults to 2.
 */

export const HitArea = ({ offset = 2 }: HitAreaProps) => {
  return (
    <div
      style={{
        position: "absolute",
        inset: `calc(-1 * var(--spacing) * ${offset})`,
      }}
      data-hit-area
    />
  );
};
