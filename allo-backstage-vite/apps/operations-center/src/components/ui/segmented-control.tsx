import { motion } from "motion/react";
import { cn } from "@allo/ui";
import { ComponentPropsWithRef, createContext, use } from "react";

type SegmentedControlContextType = {
  value: string;
  onChange: (value: string) => void;
};

const SegmentedControlContext =
  createContext<SegmentedControlContextType | null>(null);

const useSegmentedControlContext = () => {
  const context = use(SegmentedControlContext);

  if (!context) {
    throw new Error(
      "SegmentedControlItem must be used within a SegmentedControl"
    );
  }

  return context;
};

export type SegmentedControlProps = {
  value: string;
  onChange: (value: string) => void;
} & React.ComponentPropsWithRef<"div">;

export const SegmentedControl = ({
  value,
  onChange,
  className,
  children,
  ...props
}: SegmentedControlProps) => {
  return (
    <SegmentedControlContext value={{ value, onChange }}>
      <div
        className={cn(
          "bg-background mx-auto flex w-fit gap-2 rounded-3xl p-1",
          className
        )}
        {...props}
      >
        {children}
      </div>
    </SegmentedControlContext>
  );
};

interface SegmentedControlItemProps
  extends Omit<ComponentPropsWithRef<"button">, "value"> {
  value: string;
}

export const SegmentedControlItem = ({
  value,
  className,
  children,
  ...props
}: SegmentedControlItemProps) => {
  const { value: selectedValue, onChange } = useSegmentedControlContext();
  const isSelected = value === selectedValue;

  return (
    <button
      type="button"
      className={cn(
        "relative flex h-10 items-center justify-center rounded-xl px-4 text-xs",
        className
      )}
      onClick={() => onChange(value)}
      {...props}
    >
      {isSelected && (
        <motion.span
          data-tab-indicator="true"
          layoutId="tab-indicator"
          aria-hidden="true"
          className="bg-foreground/12 absolute inset-0 z-0 rounded-xl"
          transition={{ type: "spring", duration: 0.25, bounce: 0.1 }}
        />
      )}
      <span className="relative z-10">{children}</span>
    </button>
  );
};
