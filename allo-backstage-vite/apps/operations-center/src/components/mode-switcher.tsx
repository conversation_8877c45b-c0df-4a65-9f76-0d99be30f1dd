import { modes } from "@/lib/mock-data/modes";
import { FlatButton } from "./ui/flat-button";
import { AnimatePresence, motion } from "motion/react";
import { useLocation, useNavigate } from "@tanstack/react-router";
import { cn } from "@allo/ui";

export const ModeSwitcher = () => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const currentMode = modes.find((mode) => pathname.includes(mode.slug));

  const handleModeSwitch = () => {
    const currIndex = modes.findIndex(({ slug }) => slug === currentMode?.slug);
    const nextMode = modes[(currIndex + 1) % modes.length];

    navigate({ to: `/${nextMode.slug}` });
  };

  return (
    <FlatButton onClick={handleModeSwitch}>
      <div className="flex gap-0.75">
        {modes.map((item) => {
          return (
            <span
              key={item.slug}
              className={cn(
                "bg-foreground/12 h-3 w-0.75 rounded-full",
                item.slug === currentMode?.slug && "bg-foreground"
              )}
            />
          );
        })}
      </div>
      <div className="grid overflow-hidden text-left *:col-start-1 *:row-start-1">
        {modes.map((item) => {
          return (
            <AnimatePresence key={item.slug} initial={false} mode="wait">
              {item.slug === currentMode?.slug && (
                <motion.div
                  className="text-foreground w-full"
                  initial={{ y: "105%" }}
                  animate={{ y: 0 }}
                  exit={{ y: "-105%" }}
                  transition={{ duration: 0.25, ease: "easeInOut" }}
                >
                  {item.name}
                </motion.div>
              )}
            </AnimatePresence>
          );
        })}
      </div>
    </FlatButton>
  );
};
