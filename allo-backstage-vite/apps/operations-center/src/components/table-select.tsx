import {
  ComponentPropsWithRef,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Badge, cn, Spinner } from "@allo/ui";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchTable, fetchSpace, fetchTableOrder } from "@/lib/mock-data/api";
import { Table } from "@/lib/mock-data/tables";
import { Space } from "@/lib/mock-data/spaces";
import { Ban, CircleAlert, TriangleAlert } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { NumericKeyboard } from "@/components/numeric-keyboard";
import { useDebouncedValue } from "@/lib/hooks/use-debounced-value";
import { getVersionedFloors } from "@/lib/queries";

interface QueryResult {
  table: Table;
  space: Space;
  hasActiveOrder: boolean;
  isCurrent: boolean;
}

interface TableSelectProps
  extends Omit<ComponentPropsWithRef<"div">, "onChange"> {
  onChange: (table: Table | null) => void;
  currentTableId?: string | null;
}

export const TableSelect = ({
  currentTableId,
  onChange,
  className,
  ...props
}: TableSelectProps) => {
  const [isDeviceKeyboard, setIsDeviceKeyboard] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const [value, setValue] = useState<string>("");
  const debouncedValue = useDebouncedValue(value, 256);
  const queryClient = useQueryClient();

  const { data, isPending, isSuccess } = useQuery({
    throwOnError: false,
    queryKey: ["table-select", debouncedValue],
    queryFn: async (): Promise<QueryResult> => {
      const floorsQueryKey = getVersionedFloors().queryKey;
      const floors = queryClient.getQueryData(floorsQueryKey)?.flat();

      const table = await fetchTable(debouncedValue || "", floors);
      const space = await fetchSpace(table.floorId);
      const order = await fetchTableOrder(table.id);

      return {
        table,
        space,
        isCurrent: currentTableId === table.id,
        hasActiveOrder: !!order && order.ordered.length > 0,
      };
    },
  });

  const status = useMemo(() => {
    if (!value || value !== debouncedValue) return "idle";
    if (isPending) return "pending";
    if (!isSuccess) return "not-found";
    if (data?.isCurrent) return "current";
    return "success";
  }, [value, debouncedValue, isPending, isSuccess, data?.isCurrent]);

  useEffect(() => {
    if (status === "success" && data) {
      onChange(data.table);
    } else {
      onChange(null);
    }
  }, [status, data, onChange]);

  useLayoutEffect(() => {
    if (isDeviceKeyboard) {
      inputRef.current?.focus();
    }
  }, [isDeviceKeyboard]);

  return (
    <div
      className={cn("bg-background-highlight w-full text-left", className)}
      {...props}
    >
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          className="placeholder:text-foreground-tertiary text-foreground w-full px-2.5 py-4 text-2xl uppercase outline-none placeholder:normal-case"
          placeholder="Type table..."
          value={value}
          onChange={(e) => setValue(e.target.value)}
          readOnly={!isDeviceKeyboard}
          inert={!isDeviceKeyboard}
        />
        <div className="absolute inset-0 flex items-center px-2.5 py-4" inert>
          {/* sets left offset for badges and spinner */}
          <span
            aria-hidden
            className="invisible inline-block text-2xl whitespace-pre-wrap uppercase before:content-[attr(data-value)]"
            data-value={value + " "}
          />
          <AnimatePresence>
            {status !== "idle" && status !== "pending" && (
              <motion.div
                className="flex items-center gap-2"
                initial={{ opacity: 0, y: 4 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.25, delay: 0.05 },
                }}
              >
                <Badge
                  size="sm"
                  variant={status === "success" ? "default" : "negative"}
                >
                  {data?.hasActiveOrder && status !== "current" && (
                    <CircleAlert />
                  )}
                  {status === "not-found" && <TriangleAlert />}
                  {status === "current" && <Ban />}
                  {status === "success" &&
                    data &&
                    `${data.space.name} · 4 seats`}
                  {status === "not-found" && "Not found"}
                  {status === "current" && "Current"}
                </Badge>
              </motion.div>
            )}
          </AnimatePresence>
          {status === "pending" && <Spinner className="ml-auto h-4 w-4" />}
        </div>
      </div>
      {!isDeviceKeyboard && (
        <NumericKeyboard
          className="pb-1.5"
          onKey={(key) => setValue((value) => value + key)}
          onBackspace={() => setValue((value) => value.slice(0, -1))}
          onSwitch={() => setIsDeviceKeyboard(true)}
        />
      )}
    </div>
  );
};
