import { MENUS } from "@/lib/mock-data/menu";
import { cn, inputStyle } from "@allo/ui";

export const PrototypeSettingsMenu = () => {
  const currentMenu = localStorage.getItem("menu") || Object.keys(MENUS)[0];

  const handleMenuChange = (newMenuId: string) => {
    localStorage.setItem("menu", newMenuId);
    window.location.pathname = "/";
  };

  return (
    <div className="bg-background-high absolute top-full z-50 mt-0.25 h-[calc(100vh-100%)] w-full p-4 text-sm">
      <h1 className="text-foreground font-medium">Prototype Settings</h1>
      <ul className="mt-2">
        <li>
          <div className="block py-3">
            <div className="flex items-center justify-between gap-12">
              <h2>Menu</h2>
              <select
                className={cn(
                  inputStyle(),
                  "0 w-auto appearance-none",
                  "appearance-none bg-[length:1em] bg-[position:right_--spacing(2)_center] bg-no-repeat pr-8",
                  'bg-[url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNiIgaGVpZ2h0PSIxNiIgdmlld0JveD0iMCAwIDE2IDE2Ij48cGF0aCBmaWxsPSJibGFjayIgZD0iTTMuNyA1LjNsNC4zIDQuMyA0LjMtNC4zLjcuNy01IDUtNS01eiIvPjwvc3ZnPg==")]'
                )}
                value={currentMenu}
                onChange={(e) => handleMenuChange(e.target.value)}
              >
                {Object.keys(MENUS).map((key) => {
                  return (
                    <option key={key} value={key}>
                      {key}
                    </option>
                  );
                })}
              </select>
            </div>
          </div>
        </li>
      </ul>
    </div>
  );
};
