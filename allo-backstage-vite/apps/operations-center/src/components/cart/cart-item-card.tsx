import { cn, Slot } from "@allo/ui";
import { ComponentPropsWithRef } from "react";

interface CartItemCardProps extends ComponentPropsWithRef<"div"> {
  asChild?: boolean;
}

export const CartItemCard = ({
  children,
  className,
  asChild,
  ...props
}: CartItemCardProps) => {
  const Component = asChild ? Slot : "div";
  return (
    <Component
      className={cn(
        "text-foreground border-border flex items-center gap-2.5 border-b p-2.5 text-sm",
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};

export const CartItemCardContent = ({
  children,
  className,
  asChild,
  ...props
}: CartItemCardProps) => {
  const Component = asChild ? Slot : "div";
  return (
    <Component className={cn("flex grow flex-col gap-3", className)} {...props}>
      {children}
    </Component>
  );
};

interface CartItemCardRowProps extends ComponentPropsWithRef<"div"> {
  asChild?: boolean;
}

export const CartItemCardRow = ({
  children,
  className,
  asChild,
  ...props
}: CartItemCardRowProps) => {
  const Component = asChild ? Slot : "div";
  return (
    <Component
      className={cn("flex items-center justify-between gap-2", className)}
      {...props}
    >
      {children}
    </Component>
  );
};

interface CartItemCardTitleProps extends ComponentPropsWithRef<"div"> {
  asChild?: boolean;
}

export const CartItemCardTitle = ({
  children,
  className,
  asChild,
  ...props
}: CartItemCardTitleProps) => {
  const Component = asChild ? Slot : "div";
  return (
    <Component className={cn("font-medium", className)} {...props}>
      {children}
    </Component>
  );
};

interface CartItemCardDetailsProps extends ComponentPropsWithRef<"div"> {
  asChild?: boolean;
}

export const CartItemCardDetails = ({
  children,
  className,
  asChild,
  ...props
}: CartItemCardDetailsProps) => {
  const Component = asChild ? Slot : "div";
  return (
    <Component
      className={cn(
        "relative pl-3.5",
        "before:bg-border-hard before:absolute before:top-0 before:left-0 before:h-full before:w-1 before:rounded-full",
        className
      )}
      {...props}
    >
      {children}
    </Component>
  );
};
