import { ComponentPropsWithRef, useMemo } from "react";
import { CartItem } from "@/lib/cart/types";
import { Badge, Price, cn, Slot } from "@allo/ui";
import { X } from "lucide-react";

interface CartItemPriceDetailsProps
  extends Omit<ComponentPropsWithRef<"ul">, "children"> {
  item: CartItem;
  asChild?: boolean;
}

export const CartItemPriceDetails = ({
  item,
  className,
  asChild,
  ...props
}: CartItemPriceDetailsProps) => {
  const Component = asChild ? Slot : "ul";

  const options = useMemo(() => [...item.options].reverse(), [item.options]);

  return (
    <Component className={cn("space-y-1.5", className)} {...props}>
      {options.map((option) => {
        const isPredefined = option.initialQuantity > 0;
        const isRemoved = isPredefined && option.quantity === 0;
        if (isPredefined && !isRemoved) return;

        return (
          <li key={option.id} className="flex items-center gap-1.5">
            <Badge
              size="xs"
              variant={isRemoved ? "default" : "positive"}
              className="text-foreground aspect-square justify-center p-0"
            >
              {isRemoved ? <X className="size-3" /> : option.quantity}
            </Badge>
            {option.name}
            <Price
              className="text-foreground-secondary ml-auto"
              amount={option.unitPrice * option.quantity}
            />
          </li>
        );
      })}
      {item.notes.length > 0 && (
        <li className="mt-2.5 space-y-0.5">
          <ul>
            {item.notes.map((note, index) => {
              return (
                <li key={`note-${index}`} className="break-words">
                  {note}
                </li>
              );
            })}
          </ul>
        </li>
      )}
    </Component>
  );
};
