import { cn } from "@allo/ui";
import {
  use,
  createContext,
  useState,
  useLayoutEffect,
  useCallback,
  type ReactNode,
  Fragment,
  type ComponentPropsWithRef,
} from "react";

type BottomBarContext = {
  slots: { id: string; children: ReactNode }[];
  registerSlot: (id: string, children: ReactNode) => void;
  theme: "light" | "dark";
  setTheme: (theme: BottomBarContext["theme"]) => void;
};

const BottomBarContext = createContext<BottomBarContext>({
  slots: [],
  registerSlot: () => {},
  theme: "light",
  setTheme: () => {},
});

// eslint-disable-next-line react-refresh/only-export-components
export const useBottomBar = () => {
  const context = use(BottomBarContext);

  if (!context) {
    throw new Error("useBottomBar must be used within a BottomBarProvider");
  }

  return context;
};

export const BottomBarProvider = ({ children }: { children: ReactNode }) => {
  const [theme, setTheme] = useState<BottomBarContext["theme"]>("light");
  const [slots, setSlots] = useState<BottomBarContext["slots"]>([]);

  const registerSlot = useCallback((id: string, children: ReactNode) => {
    setSlots((prev) => {
      const existingSlot = prev.find((slot) => slot.id === id);

      const newSlots = existingSlot
        ? prev.map((slot) => (slot.id === id ? { id, children } : slot))
        : [...prev, { id, children }];

      return newSlots.filter((slot) => slot.children !== null);
    });
  }, []);

  return (
    <BottomBarContext value={{ slots, registerSlot, theme, setTheme }}>
      {children}
    </BottomBarContext>
  );
};

export const BottomBar = ({ className }: ComponentPropsWithRef<"div">) => {
  const { slots, theme } = useBottomBar();
  const isEmpty = slots.length === 0;

  return (
    <>
      {!isEmpty && (
        <div
          className={cn(
            "bg-background-highlight text-foreground border-t-border relative z-20 flex w-full flex-col border-t pb-4",
            className
          )}
          data-theme={theme}
        >
          {slots.map((slot) => (
            <Fragment key={slot.id}>{slot.children}</Fragment>
          ))}
        </div>
      )}
    </>
  );
};

interface BottomBarPortalProps {
  id: string;
  children: ReactNode;
}

export const BottomBarPortal = ({ id, children }: BottomBarPortalProps) => {
  const { registerSlot } = useBottomBar();

  useLayoutEffect(() => {
    registerSlot(id, children);

    return () => {
      registerSlot(id, null);
    };
  }, [children, id, registerSlot]);

  return null;
};
