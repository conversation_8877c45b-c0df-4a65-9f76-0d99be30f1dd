import { Order } from "@/lib/mock-data/order";
import { Drawer, DrawerContent } from "@allo/ui";
import { createContext, use, useCallback, useState } from "react";
import { MoveOrder } from "./move-order";
import { CancelOrder } from "./cancel-order";
import { MoveItems, useCanMoveItems } from "./move-items";

type OrderAction = "move-order" | "cancel-order" | "move-items";

interface OrderActionsContextType {
  open: (action: OrderAction, order: Order) => void;
}

export const OrderActionsContext =
  createContext<OrderActionsContextType | null>(null);

export const OrderActionsProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [action, setAction] = useState<OrderAction | null>(null);

  const open = useCallback((action: OrderAction, order: Order) => {
    setOrder(order);
    setAction(action);
  }, []);

  return (
    <OrderActionsContext value={{ open }}>
      {children}
      <Drawer open={!!order} onOpenChange={() => setOrder(null)}>
        <DrawerContent>
          {order && (
            <>
              {action === "move-order" && (
                <MoveOrder order={order} onSuccess={() => setOrder(null)} />
              )}
              {action === "cancel-order" && (
                <CancelOrder order={order} onSuccess={() => setOrder(null)} />
              )}
              {action === "move-items" && (
                <MoveItems order={order} onSuccess={() => setOrder(null)} />
              )}
            </>
          )}
        </DrawerContent>
      </Drawer>
    </OrderActionsContext>
  );
};

const useCanExecuteAction = (action: OrderAction, order?: Order) => {
  switch (action) {
    case "move-items":
      return useCanMoveItems(order);
    default:
      return !!order;
  }
};

export const useOrderAction = (action: OrderAction, order?: Order) => {
  const context = use(OrderActionsContext);

  if (!context) {
    throw new Error(
      "useOrderActions must be used within a OrderActionsProvider"
    );
  }

  const { open } = context;
  const enabled = useCanExecuteAction(action, order);

  return {
    open: () => {
      if (order && enabled) {
        open(action, order);
      }
    },
    disabled: !enabled,
  };
};
