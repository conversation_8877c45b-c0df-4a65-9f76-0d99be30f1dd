import {
  But<PERSON>,
  <PERSON>,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
} from "@allo/ui";
import { useMemo } from "react";

import { calculateCartItemTotalPrice, mergeCartItems } from "@/lib/cart/utils";

import { useProductConfigurationContext } from "./context";

export const ProductConfigurationActions = () => {
  const { initialCartItem, cartItem, dispatch, submit, cancel, isPending } =
    useProductConfigurationContext();

  const hasChanged = useMemo(() => {
    return initialCartItem
      ? mergeCartItems([cartItem, initialCartItem]).length !== 1 ||
          initialCartItem.quantity !== cartItem.quantity
      : true;
  }, [cartItem, initialCartItem]);

  return (
    <>
      <QuantitySelector
        size="md"
        min={1}
        quantity={cartItem.quantity}
        onChange={(qty) => {
          dispatch({ type: "quantity", payload: qty });
        }}
        inert={isPending}
      >
        <QuantitySelectorDecrease />
        <QuantitySelectorQuantity className="text-sm font-medium" />
        <QuantitySelectorIncrease />
      </QuantitySelector>
      <Button
        size="md"
        variant="accent"
        className="grow"
        onClick={submit}
        isLoading={isPending}
        inert={isPending}
        disabled={!hasChanged}
      >
        <span>{initialCartItem ? "Update" : "Add"}</span>
        <span>·</span>
        <Price amount={calculateCartItemTotalPrice(cartItem)} />
      </Button>
      <Button size="md" onClick={cancel} inert={isPending}>
        Cancel
      </Button>
    </>
  );
};
