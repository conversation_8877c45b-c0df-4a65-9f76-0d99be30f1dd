import {
  Drawer,
  DrawerActions,
  DrawerContent,
  Drawer<PERSON>eader,
  DrawerTitle,
} from "@allo/ui";
import { createContext, use, useState } from "react";

import { isPromise } from "@/lib/utils/async";

import { ProductConfigurationActions } from "./actions";
import {
  ProductConfigurationProvider,
  ProductConfigurationProviderProps,
} from "./context";
import { ProductConfigurationNotes } from "./notes";
import { ProductConfigurationOptions } from "./options";

type DrawerConfigurationProps = Omit<
  ProductConfigurationProviderProps,
  "children"
>;

interface ProductConfigurationDrawerContext {
  open: (props: Omit<ProductConfigurationProviderProps, "children">) => void;
  close: () => void;
}

const ProductConfigurationDrawerContext =
  createContext<ProductConfigurationDrawerContext>({
    open: () => {},
    close: () => {},
  });

// eslint-disable-next-line react-refresh/only-export-components
export const useProductConfigurationDrawer = () => {
  const context = use(ProductConfigurationDrawerContext);

  if (!context) {
    throw new Error(
      "useProductConfigurationDrawer must be used within a ProductConfigurationDrawerProvider"
    );
  }

  return context;
};

export const ProductConfigurationDrawerProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [props, setProps] = useState<DrawerConfigurationProps | null>(null);

  return (
    <ProductConfigurationDrawerContext
      value={{ open: setProps, close: () => setProps(null) }}
    >
      {children}
      <Drawer
        open={!!props}
        onOpenChange={(isOpen) => {
          if (!isOpen) setProps(null);
        }}
      >
        <DrawerContent className="scroll-pt-16">
          {props && (
            <ProductConfigurationProvider
              {...props}
              onSubmit={(cartItem) => {
                const res = props.onSubmit(cartItem);

                if (isPromise(res)) {
                  res.then(() => setProps(null));
                } else {
                  setProps(null);
                }

                return res;
              }}
              onCancel={() => {
                setProps(null);
              }}
            >
              <DrawerHeader>
                <DrawerTitle>{props.product.name}</DrawerTitle>
              </DrawerHeader>
              <div className="mb-32 min-h-full space-y-2 overflow-hidden p-2 text-xs">
                <ProductConfigurationOptions />
                <ProductConfigurationNotes />
              </div>
              <DrawerActions>
                <ProductConfigurationActions />
              </DrawerActions>
            </ProductConfigurationProvider>
          )}
        </DrawerContent>
      </Drawer>
    </ProductConfigurationDrawerContext>
  );
};
