import { productOptionToCartItemOption } from "@/lib/cart/utils";
import { Product } from "@/lib/mock-data/menu";
import { useMemo, MouseEvent } from "react";
import { useProductConfigurationContext } from "./context";
import { Check, Plus, X } from "lucide-react";
import {
  Badge,
  Button,
  Checkbox,
  cn,
  Price,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorDelete,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
  Radio,
} from "@allo/ui";
import { isInteractiveElement } from "@/lib/utils/dom";
import { getCartItemGroupError } from "@/lib/cart/validation";

export const ProductConfigurationOptions = () => {
  const { product, liveValidationGroups } = useProductConfigurationContext();

  // TODO: add extras
  return (
    <>
      {product.options.map((group) => (
        <ProductOptionGroup
          key={group.id}
          group={group}
          shouldValidate={liveValidationGroups.has(group.id)}
        />
      ))}
    </>
  );
};

interface ProductOptionGroupProps {
  group: Product["configuration"][number];
  shouldValidate: boolean;
}

export const ProductOptionGroup = ({
  group,
  shouldValidate,
}: ProductOptionGroupProps) => {
  const { cartItem, isPending } = useProductConfigurationContext();

  const isRequired = group.min > 0;
  const isInCart = cartItem.options.some((o) => o.groupId === group.id);

  const error = useMemo(
    () => getCartItemGroupError(cartItem, group),
    [cartItem, group]
  );

  return (
    <div className="text-xs" id={group.id} inert={isPending}>
      <div
        className={cn(
          "box-content flex min-h-5 items-center gap-1.5 px-2 py-3",
          shouldValidate && error && "animate-shake"
        )}
      >
        <h2>{group.name}</h2>
        {shouldValidate && error && (
          <Badge size="xs" variant="negative">
            <X /> {error.message}
          </Badge>
        )}
        {!(shouldValidate && error) && isRequired && (
          <Badge
            size="xs"
            variant={isInCart && !error ? "positive" : "default"}
          >
            {isInCart && !error && <Check />} Required
          </Badge>
        )}
      </div>
      <div className="bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border">
        {group.items.map((option) => {
          const multi = option.max > 0 || option.qtd > 1;
          const type = multi
            ? option.max > 1
              ? "quantity"
              : "checkbox"
            : "radio";

          return (
            <div
              key={option.id}
              className="bg-background-highlight flex items-center gap-1.5 p-3"
            >
              <ProductOption groupId={group.id} option={option} type={type} />
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface ProductOptionProps {
  type: "radio" | "quantity";
  groupId: string;
  option: Product["configuration"][number]["options"][number];
}

export const ProductOption = ({
  type,
  groupId,
  option,
}: ProductOptionProps) => {
  const { cartItem, dispatch } = useProductConfigurationContext();
  const optionInCart = cartItem.options.find((o) => option.id === o.id);

  const handleContainerClick = ({ target }: MouseEvent<HTMLElement>) => {
    if (isInteractiveElement(target)) return;

    const currentQuantity = optionInCart ? optionInCart.quantity : 0;

    if (type === "quantity" && currentQuantity < (option.max || Infinity)) {
      dispatch({
        type: "set",
        payload: productOptionToCartItemOption(
          groupId,
          option,
          optionInCart ? optionInCart.quantity + 1 : 1
        ),
      });
    }
  };

  const Comp = type === "radio" ? "label" : "div";
  return (
    <Comp
      className="flex w-full items-center justify-between gap-1.5"
      onClick={handleContainerClick}
    >
      <div className="grow">
        <p>{option.name}</p>
        <p className="text-foreground-secondary">
          <Price amount={option.unitPrice} />
        </p>
      </div>
      <div className="shrink-0">
        {type === "checkbox" && (
          <Checkbox
            name={groupId}
            value={option.id}
            checked={optionInCart?.id === option.id}
            onChange={() => {
              if (optionInCart) {
                return dispatch({ type: "remove", payload: option.id });
              }

              dispatch({
                type: "set-in-group",
                payload: productOptionToCartItemOption(groupId, option, 1),
              });
            }}
          ></Checkbox>
        )}
        {type === "radio" && (
          <Radio
            name={groupId}
            value={option.id}
            checked={optionInCart?.id === option.id}
            onChange={() => {
              dispatch({
                type: "set-in-group",
                payload: productOptionToCartItemOption(groupId, option, 1),
              });
            }}
          />
        )}
        {type === "quantity" && (
          <>
            {optionInCart ? (
              <QuantitySelector
                size="sm"
                quantity={optionInCart.quantity}
                min={option.min}
                max={option.max || undefined}
                onChange={(qty) => {
                  // TODO: add stop propagation
                  dispatch({
                    type: "set",
                    payload: productOptionToCartItemOption(
                      groupId,
                      option,
                      qty
                    ),
                  });
                }}
              >
                {optionInCart.quantity === option.min + 1 ? (
                  <QuantitySelectorDelete
                    onClick={() => {
                      return optionInCart.initialQuantity
                        ? dispatch({
                            type: "set",
                            payload: productOptionToCartItemOption(
                              groupId,
                              option,
                              option.min
                            ),
                          })
                        : dispatch({ type: "remove", payload: option.id });
                    }}
                  />
                ) : (
                  <QuantitySelectorDecrease />
                )}
                <QuantitySelectorQuantity />
                <QuantitySelectorIncrease />
              </QuantitySelector>
            ) : (
              <Button
                size="sm"
                aria-label="Add"
                square
                onClick={() => {
                  dispatch({
                    type: "set",
                    payload: productOptionToCartItemOption(groupId, option, 1),
                  });
                }}
              >
                <Plus />
              </Button>
            )}
          </>
        )}
      </div>
    </Comp>
  );
};
