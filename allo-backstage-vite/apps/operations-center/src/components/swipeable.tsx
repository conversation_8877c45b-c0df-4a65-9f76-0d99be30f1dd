import { cn } from "@allo/ui";
import { motion, AnimatePresence } from "motion/react";
import { useSwipeable } from "react-swipeable";
import { ComponentPropsWithRef } from "react";

interface SwipeableProps extends ComponentPropsWithRef<"div"> {
  onSwipe: (direction: "left" | "right") => void;
}

export function Swipeable({
  children,
  onSwipe,
  className,
  ...props
}: SwipeableProps) {
  const { ref, onMouseDown } = useSwipeable({
    swipeDuration: 500,
    preventScrollOnSwipe: true,
    trackMouse: true,
    onSwipedLeft: () => onSwipe("left"),
    onSwipedRight: () => onSwipe("right"),
  });

  return (
    <div
      ref={ref}
      onMouseDown={onMouseDown}
      className={cn(
        "relative grid grid-cols-1 grid-rows-1 *:col-start-1 *:row-start-1",
        className
      )}
      {...props}
    >
      <AnimatePresence mode="popLayout" initial={false}>
        {children}
      </AnimatePresence>
    </div>
  );
}

interface SwipeableItemProps extends ComponentPropsWithRef<"div"> {
  direction: "left" | "right";
}

export const SwipeableItem = ({
  children,
  direction = "left",
}: SwipeableItemProps) => {
  return (
    <motion.div
      initial={{ x: direction === "left" ? "-100%" : "100%", opacity: 1 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: direction === "left" ? "-100%" : "100%", opacity: 0 }}
      transition={{ duration: 0.175, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
};
