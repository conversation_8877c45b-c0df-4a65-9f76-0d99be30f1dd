import { Checkbox, TextareaResize } from "@allo/ui";
import { useEffect, useState } from "react";
import { useStableCallback } from "@/lib/hooks/use-stable-callback";

const getNodeId = (value: string, presets: string[]) => {
  if (presets.includes(value)) {
    return presets.indexOf(value).toString();
  }

  return "custom";
};

interface NotesInputProps {
  customNotePlaceholder?: string;
  defaultValue?: string[];
  presets: string[];
  onChange: (notes: string[]) => void;
}

export const NotesInput = ({
  presets,
  defaultValue = [],
  customNotePlaceholder = "Type a custom note...",
  onChange,
}: NotesInputProps) => {
  const [notes, setNotes] = useState<{ id: string; value: string }[]>(
    defaultValue.map((n) => ({ id: getNodeId(n, presets), value: n }))
  );
  const stableOnChange = useStableCallback(onChange);

  useEffect(() => {
    stableOnChange(notes.map((n) => n.value));
  }, [notes, stableOnChange]);

  return (
    <div className="bg-border border-border flex flex-col gap-0.25 overflow-hidden rounded-xl border">
      <TextareaResize
        rows={1}
        placeholder={customNotePlaceholder}
        className="h-12 rounded-t-xl rounded-b-none border-0 px-3 py-3.5 text-sm !ring-transparent !ring-offset-transparent"
        defaultValue={notes.find((n) => n.id === "custom")?.value}
        onChange={(e) => {
          const noteId = getNodeId(e.target.value, presets);

          if (e.target.value) {
            setNotes((prev) => [
              ...prev.filter((n) => n.id !== noteId),
              { id: noteId, value: e.target.value },
            ]);
          } else {
            setNotes((prev) => prev.filter((n) => n.id !== noteId));
          }
        }}
      />
      {presets?.map?.((note) => {
        const noteId = getNodeId(note, presets);

        return (
          <label
            key={note}
            className="bg-background-highlight flex h-12 items-center justify-between gap-1.5 px-3"
          >
            <p>{note}</p>
            <Checkbox
              checked={notes.some((n) => n.value === note)}
              onChange={(e) => {
                if (e.target.checked) {
                  setNotes([...notes, { id: noteId, value: note }]);
                } else {
                  setNotes(notes.filter((n) => n.id !== noteId));
                }
              }}
            />
          </label>
        );
      })}
    </div>
  );
};
