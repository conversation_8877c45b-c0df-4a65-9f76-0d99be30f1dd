import { Button, cn } from "@allo/ui";
import { Delete, KeyboardIcon } from "lucide-react";
import { ComponentPropsWithRef } from "react";

const KEYBOARD_LAYOUT = [
  ["1", "2", "3"],
  ["4", "5", "6"],
  ["7", "8", "9"],
  ["switch", "0", "backspace"],
];

interface NumericKeyboardProps extends ComponentPropsWithRef<"div"> {
  onKey?: (key: string) => void;
  onBackspace?: () => void;
  onSwitch?: () => void;
}

export const NumericKeyboard = ({
  onKey,
  onBackspace,
  onSwitch,
  className,
  ...props
}: NumericKeyboardProps) => {
  const handleKey = (key: string) => {
    switch (key) {
      case "switch":
        return onSwitch?.();
      case "backspace":
        return onBackspace?.();
      default:
        return onKey?.(key);
    }
  };

  return (
    <div className={cn("flex flex-col gap-2", className)} {...props}>
      {KEYBOARD_LAYOUT.map((row, index) => (
        <div key={index} className="grid grid-cols-3 gap-2">
          {row.map((key) => (
            <Button key={key} onClick={() => handleKey(key)} aria-label={key}>
              {key === "switch" ? (
                <KeyboardIcon className="size-4" />
              ) : key === "backspace" ? (
                <Delete className="size-4" />
              ) : (
                <kbd>{key}</kbd>
              )}
            </Button>
          ))}
        </div>
      ))}
    </div>
  );
};
