import { Outlet, createRootRouteWithContext } from "@tanstack/react-router";
import { TopBar, TopBarProvider } from "@/components/top-bar";
import { BottomBar, BottomBarProvider } from "@/components/bottom-bar";
import { MotionConfig } from "motion/react";
import { Toaster } from "@/components/ui/toast";
import { QueryClient, QueryErrorResetBoundary } from "@tanstack/react-query";
import { ErrorBoundary } from "react-error-boundary";
import { Button } from "@allo/ui";
import {
  Error,
  ErrorTitle,
  ErrorDescription,
  ErrorActions,
} from "@/components/error";
import { OrderActionsProvider } from "@/components/order-actions";

export const Route = createRootRouteWithContext<{ queryClient: QueryClient }>()(
  { component: RootComponent }
);

function RootComponent() {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ resetErrorBoundary, error }) => (
            <div className="flex h-screen w-full flex-col items-center justify-center gap-2">
              <Error>
                <ErrorTitle>Something went wrong</ErrorTitle>
                <ErrorDescription>
                  {error.status ? `${error.status} - ` : ""}
                  {error.message}
                </ErrorDescription>
                <ErrorActions>
                  <Button onClick={() => resetErrorBoundary()} variant="accent">
                    Retry
                  </Button>
                  <Button asChild>
                    <a href="/">Back Home</a>
                  </Button>
                </ErrorActions>
              </Error>
            </div>
          )}
        >
          <MotionConfig reducedMotion="user">
            <OrderActionsProvider>
              <TopBarProvider>
                <BottomBarProvider>
                  <div className="flex h-screen flex-col">
                    <div className="relative">
                      <TopBar />
                      <Toaster data-theme="dark" />
                    </div>
                    <main className="flex-1 overflow-y-auto">
                      <Outlet />
                    </main>
                    <BottomBar />
                  </div>
                </BottomBarProvider>
              </TopBarProvider>
            </OrderActionsProvider>
          </MotionConfig>
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  );
}
