import {
  createFileRoute,
  <PERSON>,
  redirect,
  useNavigate,
} from "@tanstack/react-router";
import { Button, Field } from "@allo/ui";
import { Label } from "@allo/ui";
import { Input } from "@allo/ui";
import { schema, View } from "@/lib/user-config/schema";
import { useUserConfig, ViewConfigs } from "@/lib/user-config/store";
import { useRef, useState } from "react";

import { Actions } from "./-components/actions";
import { getUserConfigGroupFirstError } from "@/lib/user-config/schema/validation";
import { ConfigField } from "./-components/config-field";

export const Route = createFileRoute("/dine-in/settings/tables/$view/")({
  component: TablesViewPage,
  loader: async ({ params }) => {
    const tableViews = useUserConfig.getState().views.tables;
    const initialView = tableViews.find((view) => view.id === params.view);

    if (!initialView) {
      throw redirect({ to: "/dine-in/settings" });
    }

    return { initialValue: initialView };
  },
});

function TablesViewPage() {
  const views = useUserConfig((state) => state.views);
  const navigate = useNavigate();

  const { initialValue } = Route.useLoaderData();

  const ref = useRef<HTMLDivElement>(null);
  const [config, setConfig] = useState<ViewConfigs[View][number]>(initialValue);
  const [liveValidationFields, setLiveValidationFields] = useState<
    Map<string, number>
  >(new Map());

  const handleSaveViewConfig = () => {
    const error = getUserConfigGroupFirstError(schema.views.tables, config);

    if (error) {
      const item = ref.current?.querySelector(`#${error.fieldId}`);
      item?.scrollIntoView({ behavior: "smooth" });

      setLiveValidationFields((prev) => {
        prev.set(error.fieldId, Date.now());
        return new Map(prev);
      });
    } else {
      views.update("tables", config.id, config);
      navigate({ to: "/dine-in/settings" });
    }
  };

  return (
    <div ref={ref} className="flex h-full flex-col *:p-4">
      <div>
        <Field className="space-y-2">
          <Label>View name</Label>
          <Input
            placeholder="View name"
            value={config.title}
            onChange={(e) => {
              setConfig((prev) => ({ ...prev, title: e.target.value }));
            }}
          />
        </Field>
        <ul className="my-6 space-y-6">
          {schema.views.tables.map((field) => {
            return (
              <li key={field.id} className="space-y-2" id={field.id}>
                <ConfigField
                  key={liveValidationFields.get(field.id)}
                  shouldValidate={liveValidationFields.has(field.id)}
                  field={field}
                  value={config[field.id]}
                  onChange={(value) => {
                    setConfig((prev) => ({ ...prev, [field.id]: value }));
                  }}
                />
              </li>
            );
          })}
        </ul>
      </div>
      <Actions>
        <Button asChild>
          <Link to="/dine-in/settings">Cancel</Link>
        </Button>
        <Button
          variant="accent"
          className="grow"
          onClick={handleSaveViewConfig}
        >
          Save view
        </Button>
      </Actions>
    </div>
  );
}
