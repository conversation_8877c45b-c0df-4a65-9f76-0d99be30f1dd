import {
  UserConfigField,
  InferUserConfigFieldValue,
} from "@/lib/user-config/schema/types";
import { useMemo } from "react";
import { X } from "lucide-react";
import { Badge, cn } from "@allo/ui";
import { getUserConfigFieldError } from "@/lib/user-config/schema/validation";
import { SelectField } from "./select-field";
import { MultiSelectField } from "./multi-select-field";

type ConfigFieldProps = {
  field: UserConfigField;
  value: InferUserConfigFieldValue<UserConfigField>;
  onChange: (value: InferUserConfigFieldValue<UserConfigField>) => void;
  shouldValidate?: boolean;
};

export function ConfigField({
  field,
  value,
  onChange,
  shouldValidate,
}: ConfigFieldProps) {
  const error = useMemo(
    () => getUserConfigFieldError(field, value),
    [field, value]
  );

  return (
    <div>
      <div className="mb-3">
        <div
          className={cn(
            "flex flex-wrap items-center gap-2",
            shouldValidate && "animate-shake"
          )}
        >
          <h3>{field.title}</h3>
          {shouldValidate && error && (
            <Badge size="xs" variant="negative">
              <X /> {error.message}
            </Badge>
          )}
        </div>
        {field.description && (
          <p className="text-foreground-secondary mt-1 text-xs">
            {field.description}
          </p>
        )}
      </div>
      {field.type === "select" && (
        <SelectField
          field={field}
          value={value as string}
          onChange={(value) => onChange(value)}
        />
      )}

      {field.type === "multi-select" && (
        <MultiSelectField
          field={field}
          value={value as string[]}
          onChange={(newValue) => onChange(newValue)}
        />
      )}
    </div>
  );
}
