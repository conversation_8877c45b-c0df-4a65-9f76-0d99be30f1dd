import { UserConfigMultiSelectField } from "@/lib/user-config/schema/types";
import { Checkbox } from "@allo/ui";

type MultiSelectFieldProps = {
  value: string[];
  onChange: (value: string[]) => void;
  field: UserConfigMultiSelectField;
};

export const MultiSelectField = ({
  value,
  onChange,
  field,
}: MultiSelectFieldProps) => {
  return (
    <ul className="grid grid-cols-2 gap-1">
      {field.options.map((option) => (
        <li key={option.id}>
          <label className="flex items-center gap-2 py-1.5">
            <Checkbox
              checked={value.includes(option.id)}
              disabled={option.readOnly}
              readOnly={option.readOnly}
              onChange={(e) => {
                onChange(
                  e.target.checked
                    ? [...value, option.id]
                    : value.filter((value) => value !== option.id)
                );
              }}
            />
            <span>{option.title}</span>
          </label>
        </li>
      ))}
    </ul>
  );
};
