import { Radio } from "@allo/ui";

import { UserConfigSelectField } from "@/lib/user-config/schema/types";
import { RadioGroup } from "@allo/ui";

type SelectFieldProps = {
  field: UserConfigSelectField;
  value: string;
  onChange: (value: string) => void;
};

export const SelectField = ({ value, onChange, field }: SelectFieldProps) => {
  return (
    <RadioGroup>
      {field.options.map((option) => (
        <label className="flex items-center gap-2 py-1.5" key={option.id}>
          <Radio
            value={option.id}
            checked={value === option.id}
            disabled={option.readOnly}
            readOnly={option.readOnly}
            onChange={(checked) => {
              if (checked) onChange(option.id);
            }}
          />
          <span>
            <div>{option.title}</div>
            {option.description && (
              <div className="text-foreground-secondary">
                {option.description}
              </div>
            )}
          </span>
        </label>
      ))}
    </RadioGroup>
  );
};
