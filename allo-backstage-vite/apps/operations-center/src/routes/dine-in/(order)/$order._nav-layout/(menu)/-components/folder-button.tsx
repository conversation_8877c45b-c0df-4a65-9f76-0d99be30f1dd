import { Button, ButtonProps, cn } from "@allo/ui";
import { mixWithWhite } from "@/lib/utils/color";

interface FolderButtonProps extends ButtonProps {
  color?: string;
}

export const FolderButton = ({
  children,
  color,
  className,
  style,
  ...props
}: FolderButtonProps) => {
  return (
    <Button
      className={cn(
        "h-17 w-full px-2 text-center whitespace-normal",
        className
      )}
      style={{
        ...(color && {
          "--chunky-border-color": mixWithWhite(color, 0.25),
          "--chunky-shadow-bg-color": mixWithWhite(color, 0.25),
          "--chunky-shadow-border-color": mixWith<PERSON>hite(color, 0.35),
        }),
        ...(style ?? {}),
      }}
      {...props}
    >
      {children}
    </Button>
  );
};
