import {
  Product,
  Menu,
  MenuFolder,
  MenuSuperFolder,
} from "@/lib/mock-data/menu";
import { useMemo } from "react";

const extractProductsAndFolders = (folder: MenuFolder) => {
  const products: Product[] = [];
  const folders: MenuFolder[] = [];

  for (const item of folder.items) {
    if ("items" in item) {
      folders.push(item);
    } else {
      products.push(item);
    }
  }

  return { products, folders };
};

type PathItem = {
  id: string;
  name: string;
  emoji?: string;
};

export const useMenuFolder = (splat: string[], menu: Menu) => {
  return useMemo(() => {
    const [superFolderId, ...subPath] = splat;
    const path: PathItem[] = [];

    const superFolder = menu.find((f) => f.id === superFolderId);
    if (!superFolder) {
      return { superFolder: null, path, products: [], folders: [] };
    }

    path.push({
      id: superFolderId,
      name: superFolder.title,
      emoji: superFolder.emoji,
    });

    let folder: MenuFolder | MenuSuperFolder = superFolder;

    for (const id of subPath) {
      for (const child of folder.items) {
        if ("items" in child && child.id === id) {
          folder = child;
          path.push({ id: child.id, name: child.name });
        }
      }
    }

    return {
      superFolder,
      path,
      ...(folder
        ? extractProductsAndFolders(folder)
        : { products: [], folders: [] }),
    };
  }, [menu, splat]);
};
