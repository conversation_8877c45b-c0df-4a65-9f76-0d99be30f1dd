import {
  calculateCartItemTotalPrice,
  getCartItemProduct,
} from "@/lib/cart/utils";

import {
  <PERSON><PERSON>,
  Price,
  QuantitySelectorDelete,
  QuantitySelectorDecrease,
  QuantitySelectorQuantity,
  QuantitySelectorIncrease,
  QuantitySelector,
} from "@allo/ui";
import { useProductConfigurationDrawer } from "@/components/product-configuration/drawer";
import { useCartActions } from "@/lib/cart/store";
import { useQuery } from "@tanstack/react-query";
import { getVersionedMenus } from "@/lib/queries";
import { useMemo } from "react";
import { CartItem } from "@/lib/cart/types";
import {
  CartItemCard,
  CartItemCardContent,
  CartItemCardDetails,
  CartItemCardRow,
  CartItemCardTitle,
} from "@/components/cart/cart-item-card";
import { CartItemPriceDetails } from "@/components/cart/cart-item-price-details";

interface PendingCartItemProps {
  orderId: string;
  item: CartItem;
}

export const PendingCartItem = ({ orderId, item }: PendingCartItemProps) => {
  const { updateItem, removeItem } = useCartActions(orderId);
  const { open: openConfigurationDrawer } = useProductConfigurationDrawer();
  const versionedMenus = useQuery(getVersionedMenus());

  const product = useMemo(
    () => getCartItemProduct(item, versionedMenus.data.items ?? []),
    [item, versionedMenus.data]
  );

  return (
    <CartItemCard>
      <CartItemCardContent>
        <CartItemCardRow>
          <CartItemCardTitle>{product.name}</CartItemCardTitle>
          <QuantitySelector
            min={0}
            quantity={item.quantity}
            onChange={(qty: number) => {
              updateItem(item.id, { ...item, quantity: qty });
            }}
          >
            {item.quantity === 1 ? (
              <QuantitySelectorDelete
                onClick={() => removeItem(item.id)}
                size="md"
              />
            ) : (
              <QuantitySelectorDecrease size="md" />
            )}
            <QuantitySelectorQuantity />
            <QuantitySelectorIncrease />
          </QuantitySelector>
        </CartItemCardRow>
        <CartItemCardDetails>
          <CartItemPriceDetails item={item} />
        </CartItemCardDetails>
        <CartItemCardRow>
          <Button
            size="sm"
            onClick={() =>
              openConfigurationDrawer({
                initialCartItem: item,
                product,
                onSubmit: async (item) => updateItem(item.id, item),
              })
            }
          >
            Edit
          </Button>
          <Price amount={calculateCartItemTotalPrice(item)} />
        </CartItemCardRow>
      </CartItemCardContent>
    </CartItemCard>
  );
};
