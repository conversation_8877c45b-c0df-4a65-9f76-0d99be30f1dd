import { getVersionedMenus } from "@/lib/queries";
import { useQuery } from "@tanstack/react-query";
import { createFileRoute, Link } from "@tanstack/react-router";

import {
  SuperFolderButton,
  SuperFolderButtonEmoji,
  SuperFolderButtonLabel,
  SuperFolderButtonSkeleton,
} from "./-components/super-folder-button";
import { getSuperFolderColorByName } from "@/lib/mock-data/menus/colors";

export const Route = createFileRoute(
  "/dine-in/(order)/$order/_nav-layout/(menu)/"
)({
  component: MenuIndex,
});

function MenuIndex() {
  const versionedMenus = useQuery(getVersionedMenus());

  return (
    <ul className="grid grid-cols-3 gap-1.5 p-2.5 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-7">
      {versionedMenus.isPending &&
        Array.from({ length: 14 }).map((_, index) => (
          <li key={index}>
            <SuperFolderButtonSkeleton className="w-full" />
          </li>
        ))}
      {versionedMenus.isSuccess &&
        versionedMenus.data.items.map((item) => (
          <li key={item.id}>
            <SuperFolderButton
              asChild
              color={getSuperFolderColorByName(item.color as any)}
              className="w-full"
            >
              <Link from={Route.fullPath} to={item.id}>
                <SuperFolderButtonEmoji>{item.emoji}</SuperFolderButtonEmoji>
                <SuperFolderButtonLabel>{item.title}</SuperFolderButtonLabel>
              </Link>
            </SuperFolderButton>
          </li>
        ))}
    </ul>
  );
}
