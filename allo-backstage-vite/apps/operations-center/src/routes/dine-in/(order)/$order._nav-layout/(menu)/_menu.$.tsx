import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { ArrowLeftIcon } from "lucide-react";
import { Skeleton } from "@allo/ui";
import { useQuery } from "@tanstack/react-query";

import { useSplat } from "@/lib/hooks/use-splat";
import { getVersionedMenus } from "@/lib/queries";

import { useMenuFolder } from "./-hooks/use-menu-folder";
import { FolderButton } from "./-components/folder-button";
import { useRegisterBreadcrumb } from "@/components/ui/breadcrumbs";
import { MenuProductCard } from "@/components/menu/menu-product-card";
import { MenuProductCardGrid } from "@/components/menu/menu-product-card-grid";

export const Route = createFileRoute(
  "/dine-in/(order)/$order/_nav-layout/(menu)/_menu/$"
)({
  component: MenuSplat,
});

function MenuSplat() {
  const { order: orderId } = Route.useParams();
  const splat = useSplat();

  const menu = useQuery(getVersionedMenus());
  const { superFolder, path, products, folders } = useMenuFolder(
    splat,
    menu.data?.items || []
  );

  useRegisterBreadcrumb(
    path.map(({ name, emoji }, index) => ({
      id: `menu-${index}`,
      label: emoji ? `${emoji} ${name}` : name,
    }))
  );

  return (
    <div className="p-2.5">
      <ul className="grid grid-cols-2 gap-1.5 not-empty:mb-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
        {/* back button */}
        {splat.length > 1 && (
          <li>
            <FolderButton asChild>
              <Link to={splat.slice(0, -1).join("/")}>
                <ArrowLeftIcon className="size-4 stroke-1" />
                Back
              </Link>
            </FolderButton>
          </li>
        )}

        {/* folders */}
        {menu.isPending && <FoldersSkeleton />}
        {menu.isSuccess &&
          folders.map((folder) => (
            <li key={folder.id}>
              <FolderButton asChild color={superFolder?.color}>
                <Link to={[...splat, folder.id].join("/")}>{folder.name}</Link>
              </FolderButton>
            </li>
          ))}
      </ul>

      {/* products */}
      {menu.isSuccess && (
        <MenuProductCardGrid size="tight">
          {products.map((product) => {
            return (
              <MenuProductCard
                key={product.id}
                orderId={orderId}
                product={product}
              />
            );
          })}
        </MenuProductCardGrid>
      )}
    </div>
  );
}

function FoldersSkeleton() {
  return (
    <>
      {new Array(2).fill(null).map((_, index) => (
        <li key={index}>
          <Skeleton className="block h-18" />
        </li>
      ))}
    </>
  );
}
