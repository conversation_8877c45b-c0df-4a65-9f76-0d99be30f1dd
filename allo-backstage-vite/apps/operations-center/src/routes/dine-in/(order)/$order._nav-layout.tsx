import { TopBarActionsPortal } from "@/components/top-bar";
import { createFileRoute, Link, useLocation } from "@tanstack/react-router";
import { Outlet } from "@tanstack/react-router";
import { OrderActionsDropdown } from "./-components/order-actions-dropdown";
import { BottomBarPortal } from "@/components/bottom-bar";
import { Button } from "@allo/ui";
import { Search } from "lucide-react";
import { useMemo } from "react";
import { getOrderOptions } from "@/lib/queries";
import { useQuery } from "@tanstack/react-query";

export const Route = createFileRoute("/dine-in/(order)/$order/_nav-layout")({
  component: OrderNavLayout,
});

function OrderNavLayout() {
  const { order: orderId, ...params } = Route.useParams();
  const order = useQuery(getOrderOptions(orderId));

  const pathname = useLocation({ select: (location) => location.pathname });
  const isMenuRootRoute = useMemo(
    () => !Object.keys(params).length && !pathname.includes("cart"),
    [params, pathname]
  );

  return (
    <>
      <TopBarActionsPortal id="order-actions">
        <OrderActionsDropdown orderId={orderId} />
      </TopBarActionsPortal>
      <Outlet />
      <BottomBarPortal id="order-nav-bar">
        <div className="flex items-center gap-2 p-3">
          <Button asChild>
            <Link
              to="/dine-in/spaces/$space"
              params={{ space: order.data?.spaceId || "" }}
            >
              Tables
            </Link>
          </Button>
          <Button
            asChild
            disabled={isMenuRootRoute}
            className="ml-auto aria-[disabled]:pointer-events-none aria-[disabled]:opacity-60"
          >
            <Link from={Route.fullPath} to=".">
              Menu List
            </Link>
          </Button>
          <Button asChild square>
            <Link from={Route.fullPath} to="./search">
              <Search />
            </Link>
          </Button>
        </div>
      </BottomBarPortal>
    </>
  );
}
