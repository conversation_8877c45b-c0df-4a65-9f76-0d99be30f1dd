import { calculateCartTotalPrice } from "@/lib/cart/utils";
import { Order } from "@/lib/mock-data/order";
import { Table } from "@/lib/mock-data/tables";
import { useViewConfig } from "@/lib/user-config/store";
import { getSecondsSince } from "@/lib/utils/date";
import { useMemo } from "react";

interface UseTableDataOptions {
  order: Order;
  table: Table;
}

export const useTableData = ({ table, order }: UseTableDataOptions) => {
  const viewConfig = useViewConfig("tables");

  const assignee = useMemo(() => {
    const randomColor = ["#55A58A", "#5093D2", "#9D7ECC"][
      Number(
        String(order.assignedAt?.getTime() || 0)
          .split("")
          .pop()
      ) % 3
    ];

    return order.assignedTo !== null
      ? {
          name: order.assignedTo,
          color: randomColor,
        }
      : undefined;
  }, [order.assignedTo, order.assignedAt]);

  const properties = useMemo(() => {
    if (!assignee) return [];

    return [
      viewConfig.properties.includes("price") && {
        id: "price" as const,
        value: calculateCartTotalPrice(order.ordered),
      },
      // TEMP: fake properties
      viewConfig.properties.includes("time") && {
        id: "time" as const,
        value: getSecondsSince(order.assignedAt!),
        alert: table.code === "1",
      },
      viewConfig.properties.includes("drinks") && {
        id: "drinks" as const,
        value: ~~(order.ordered.length / 2),
      },
      viewConfig.properties.includes("status") && {
        id: "status" as const,
        value: "1/2",
      },
      viewConfig.properties.includes("courses") && {
        id: "courses" as const,
        value: "2/5",
      },
      viewConfig.properties.includes("customers") && {
        id: "customers" as const,
        value: "3",
      },
      viewConfig.properties.includes("items") && {
        id: "items" as const,
        value: order.ordered.length,
      },
    ].filter((property) => !!property);
  }, [
    assignee,
    viewConfig.properties,
    order.ordered,
    order.assignedAt,
    table.code,
  ]);

  return {
    properties,
    assignee,
  };
};
