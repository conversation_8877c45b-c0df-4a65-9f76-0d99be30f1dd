export const darkenColor = (hex: string, amount: number = 20): string => {
  if (!hex) {
    return hex;
  }

  // Remove the hash if it exists
  hex = hex.replace(/^#/, "");

  // Parse the hex values
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);

  // Calculate the darkened values
  r = Math.max(0, Math.floor(r * (1 - amount / 100)));
  g = Math.max(0, Math.floor(g * (1 - amount / 100)));
  b = Math.max(0, Math.floor(b * (1 - amount / 100)));

  // Convert back to hex and ensure 2 digits
  const darkHex =
    "#" +
    r.toString(16).padStart(2, "0") +
    g.toString(16).padStart(2, "0") +
    b.toString(16).padStart(2, "0");

  return darkHex;
};

export const mixWithWhite = (color: string, colorOpacity: number = 0.5) => {
  if (!color) {
    return color;
  }

  const colorWithoutHash = color.replace(/^#/, "");
  const r = parseInt(colorWithoutHash.substring(0, 2), 16);
  const g = parseInt(colorWithoutHash.substring(2, 4), 16);
  const b = parseInt(colorWithoutHash.substring(4, 6), 16);

  const mixedR = Math.round(r * colorOpacity + 255 * (1 - colorOpacity));
  const mixedG = Math.round(g * colorOpacity + 255 * (1 - colorOpacity));
  const mixedB = Math.round(b * colorOpacity + 255 * (1 - colorOpacity));

  return `#${mixedR.toString(16).padStart(2, "0")}${mixedG.toString(16).padStart(2, "0")}${mixedB.toString(16).padStart(2, "0")}`;
};
