type Vertices = "tl" | "tr" | "bl" | "br";

export const getGridCellVertices = (
  index: number,
  total: number,
  columns: number
): Vertices[] => {
  const vertices: Vertices[] = [];
  const isLastRow = index >= total - columns;
  const isLastRowFull = total % columns === 0;
  const isLastInSecondToLastRow =
    index >= total - columns && index < total - (total % columns || columns);

  if (index === 0) vertices.push("tl");
  if (index === total - 1) vertices.push("br");
  if (index === Math.min(columns - 1, total - 1)) vertices.push("tr");
  if (isLastRow && index % columns === 0) vertices.push("bl");
  if (isLastRow && index % columns === 0) vertices.push("bl");

  if (
    !isLastRowFull &&
    isLastInSecondToLastRow &&
    index % columns === columns - 1
  )
    vertices.push("br");

  return vertices;
};
