import { Menu } from "../menu";
import { getSuperFolderColor } from "./colors";

export const menu: Menu = [
  {
    id: "60485e22342c7c4108f525ae",
    name: "Hotpot togo",
    children: [
      {
        id: "60492225342c7c4108f525ea",
        name: "Hotpot Menu for 2 Person togo",
        unitPrice: 55.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60485e4c342c7c4108f525af",
            name: "Soup",
            min: 2,
            max: 2,
            options: [
              {
                id: "60485f17342c7c4108f525b0",
                name: "Super Spicy Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f31342c7c4108f525b1",
                name: "Spicy Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f41342c7c4108f525b2",
                name: "Mushroom Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f52342c7c4108f525b3",
                name: "Japan Curry Base Soup",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f62342c7c4108f525b4",
                name: "Miso Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f73342c7c4108f525b5",
                name: "Thai TomYam Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485f8e342c7c4108f525b6",
                name: "Vietnam Pho Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60485fa5342c7c4108f525b7",
                name: "Tomato Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60af80657684845d0d0ad28f",
                name: "Shacha Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60b344cc484a0865ae7875c1",
                name: "Thai Curry Soup Base",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "654e3de24881ea5c2bf7e193",
                name: "veggie Mushroom Soup Base",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "654e3e114e63642f2943c573",
                name: "veggie Japan Curry Soup Base",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "654e3e444881ea5c2bf7e195",
                name: "veggie Miso Soup Base",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "654e3e964e63642f2943c578",
                name: "Veggie Tomato Soup Base",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "654e3f144881ea5c2bf7e19c",
                name: "Veggie Vietnam Pho",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
          {
            id: "6048601c342c7c4108f525b8",
            name: "配菜 外卖",
            min: 1,
            max: 8,
            options: [
              {
                id: "6048607d342c7c4108f525b9",
                name: "Thinly Sliced ​​Lamb",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604860c1342c7c4108f525bc",
                name: "Pork Belly",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604860d1342c7c4108f525bd",
                name: "Beef",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604860e0342c7c4108f525be",
                name: "Chicken Breast Fillet",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604860f1342c7c4108f525bf",
                name: "Minced Beef in Ball",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486100342c7c4108f525c0",
                name: "Minced Chicken Meat in Ball",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048610f342c7c4108f525c1",
                name: "Luncheon Meat",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486123342c7c4108f525c2",
                name: "Tripe",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486136342c7c4108f525c3",
                name: "Big Shrimps",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048614a342c7c4108f525c4",
                name: "Squid Braids",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486160342c7c4108f525c5",
                name: "Scallop",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486177342c7c4108f525c6",
                name: "Pangasius",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604861bc342c7c4108f525c7",
                name: "Prawn Pasta",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604861cb342c7c4108f525c8",
                name: "Cuttlefish",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604861db342c7c4108f525c9",
                name: "Octopus",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604861ec342c7c4108f525ca",
                name: "Surimi",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604861fb342c7c4108f525cb",
                name: "Mussels",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486220342c7c4108f525cd",
                name: "Mixed Vegetables",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048622e342c7c4108f525ce",
                name: "Mixed Mushrooms",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048623e342c7c4108f525cf",
                name: "Mixed Tofu",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048624c342c7c4108f525d0",
                name: "Tofu Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048625c342c7c4108f525d1",
                name: "Morels",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486272342c7c4108f525d2",
                name: "Broccoli",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486280342c7c4108f525d3",
                name: "Potato",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486290342c7c4108f525d4",
                name: "Sweet Potato",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048629e342c7c4108f525d5",
                name: "Corn",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604862b0342c7c4108f525d6",
                name: "Glass Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604862c0342c7c4108f525d7",
                name: "Yuho Yam Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604862da342c7c4108f525d8",
                name: "Sweet Potato Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604862eb342c7c4108f525d9",
                name: "Rice",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486300342c7c4108f525da",
                name: "Hotpot Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486314342c7c4108f525db",
                name: "Thin Sweet Potato Noodle",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486324342c7c4108f525dc",
                name: "Lotus Root",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60486336342c7c4108f525dd",
                name: "Bamboo Top",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6077390f078ff40d843f59d8",
                name: "Thinly Sliced ​​Fatty Beef",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607740c4078ff40d843f59dd",
                name: "Ricestick",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6048608d342c7c4108f525ba",
                name: "Pork Pasta",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60b3434d484a0865ae7875bf",
                name: "Kimchi",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60b3446b484a0865ae7875c0",
                name: "Erdnüsse geröstet mit schwarzem Essig",
                min: 1,
                max: 3,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "65958e8f2a09525e7e863754",
                name: "Thinly Sliced Lean ​​Beef",
                min: 1,
                max: 2,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "659938512a09525e7e864a96",
                name: "Shrimp Black Tiger",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "659939aa07814b74995d1c96",
                name: "Fish Balls",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "607b3806774ddf47d43e7cb2",
        name: "Trinkgeld",
        unitPrice: 0,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6662f3b35652a54d350704bf",
        name: "Veggie Power Pot (Für 2 Personen - 19,95 € p.P.)",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6662f533982a26618a0c185d",
            name: "Zubehörkits für das Hot Pot-Erlebnis",
            min: 1,
            max: 1,
            options: [
              {
                id: "6662f57e982a26618a0c1864",
                name: "Einweg - Kohle Grill",
                unitPrice: 8,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "66646eb85652a54d350718e6",
                name: "Gasgrill+Gasflasche (Zum Ausleihen)",
                unitPrice: 52,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "668fdf4f4c427623ada28e6b",
                name: "Kein ZubehörKit notwendig",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "6662f495982a26618a0c1856",
        name: "Mixed Classic Pot (Für 2 Personen - 24,90 € p.P.)",
        unitPrice: 49.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6662f533982a26618a0c185d",
            name: "Zubehörkits für das Hot Pot-Erlebnis",
            min: 1,
            max: 1,
            options: [
              {
                id: "6662f57e982a26618a0c1864",
                name: "Einweg - Kohle Grill",
                unitPrice: 8,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "66646eb85652a54d350718e6",
                name: "Gasgrill+Gasflasche (Zum Ausleihen)",
                unitPrice: 52,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "668fdf4f4c427623ada28e6b",
                name: "Kein ZubehörKit notwendig",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "6662f4f5982a26618a0c1858",
        name: "Gourmet Deluxe Pot (Für 2 Personen - 29,90 € p.P.)",
        unitPrice: 59.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6662f533982a26618a0c185d",
            name: "Zubehörkits für das Hot Pot-Erlebnis",
            min: 1,
            max: 1,
            options: [
              {
                id: "6662f57e982a26618a0c1864",
                name: "Einweg - Kohle Grill",
                unitPrice: 8,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "66646eb85652a54d350718e6",
                name: "Gasgrill+Gasflasche (Zum Ausleihen)",
                unitPrice: 52,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "668fdf4f4c427623ada28e6b",
                name: "Kein ZubehörKit notwendig",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥘",
  },
  {
    id: "6098208e2dd0a031852a0565",
    name: "hotpot buffet Mo.-Do.",
    children: [
      {
        id: "609820d92dd0a031852a0566",
        name: "火锅自助堂食周一到周四",
        unitPrice: 36.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60af88f47684845d0d0ad292",
        name: "儿童 5 - 8 岁",
        unitPrice: 17,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60af89597684845d0d0ad293",
        name: "儿童 9 - 14 岁",
        unitPrice: 22,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥘",
  },
  {
    id: "61eed31bd371531605a00ebf",
    name: "HotPot Wochenende",
    children: [
      {
        id: "609ef5fe7c4c1f1c97b57a4c",
        name: "火锅自助堂食节假日",
        unitPrice: 36.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6192e39d8510490014353d3b",
        name: "火锅自助堂食新年  43,90",
        unitPrice: 43.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61eed398dcf6f30a863e4c89",
        name: "儿童 5 - 8 岁",
        unitPrice: 17,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61eed3a3dcf6f30a863e4c8a",
        name: "儿童 9 - 14 岁",
        unitPrice: 22,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67731f8563a914771fd257b9",
        name: "火锅自助堂食新年餐 39,9",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥘",
  },
  {
    id: "604f78bef3f7c341c8200ff3",
    name: "Rosé",
    children: [
      {
        id: "609d845b710fe122f60859b8",
        name: "Rosé Crémant No. 7",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d90e5710fe122f60859da",
        name: "Rossi Rosé (glas)",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9215710fe122f60859db",
        name: "Diva Rosé, bio",
        unitPrice: 40,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9309710fe122f60859dc",
        name: "AIX Rosé, Superstar aus Provence, zur Empfehlung",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9bc3710fe122f60859e9",
        name: "Rossi Rosé",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "609cf775710fe122f608598f",
    name: "Aperitif/Champagne",
    children: [
      {
        id: "61aa48f22a0e9a3e61a67050",
        name: "Fritz Müller Perlwein",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60bd0fc05951350c0d0ffee0",
        name: "Fritz Müller Perlwein",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8340710fe122f60859b6",
        name: "Weinschorle",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f779df3f7c341c8200ff0",
        name: "MeiJian Green Plum Wine",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d85b6710fe122f60859ba",
        name: "Ingwer Spritz",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d863f710fe122f60859bb",
        name: "Pflaumen Spritz",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8669710fe122f60859bc",
        name: "Tocco Rosso",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d86a3710fe122f60859bd",
        name: "Aperol Spritz",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d86d1710fe122f60859be",
        name: "Hugo",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8704710fe122f60859bf",
        name: "Lillet Hugo",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d871e710fe122f60859c0",
        name: "Lillet Rouge Tonic",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8734710fe122f60859c1",
        name: "Lillet Vive",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61d490df9d960814133d72cb",
        name: "Champagne Runiart",
        unitPrice: 99,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥂",
  },
  {
    id: "609d8784710fe122f60859c3",
    name: "Cocktails",
    children: [
      {
        id: "609d87c4710fe122f60859c4",
        name: "Asian Whiskey Sour",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8812710fe122f60859c5",
        name: "Peking Summer Night",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8842710fe122f60859c6",
        name: "Mai Tai",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8880710fe122f60859c7",
        name: "Mojito Mango",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d88a9710fe122f60859c8",
        name: "Munich Mule",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d88c9710fe122f60859c9",
        name: "Moskow Mule",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d88ef710fe122f60859ca",
        name: "Mojito Minze",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8920710fe122f60859cb",
        name: "Heart of Munich",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61956f3cbe7f30028a97717e",
        name: "Mojito alkoholfrei",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍹",
  },
  {
    id: "604f7328f3f7c341c8200fce",
    name: "White Wine",
    children: [
      {
        id: "63828c922d2e0e4ed43dde52",
        name: "Sauvignon Blanc Gunderloch",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "63828cca70d2c60b1ba08a57",
        name: "Sauvignon Blanc Gunderloch",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d97aa710fe122f60859e2",
        name: "Krebs weiss bio (glas)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9a46710fe122f60859e4",
        name: "Krebs weiss bio",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6182d32c7d04c82e01e704ae",
        name: "Chardonnay",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f76e3f3f7c341c8200fe5",
        name: "Chardonnay",
        unitPrice: 26,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f7768f3f7c341c8200fec",
        name: "Red Stone Riesling VDP",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9b79710fe122f60859e7",
        name: "Cuvee Reserve Österreich (glas)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9c9a710fe122f60859ea",
        name: "Scheurebe VDP Gutswein",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9d74710fe122f60859ec",
        name: "Riesling -Porphyr- VDP.Ortswein",
        unitPrice: 42,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9dc9710fe122f60859ed",
        name: "Sancerre AC, Sauvignon Blanc",
        unitPrice: 45,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ce161804eac002c2615dff",
        name: "Grüner Veltliner Reserve",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60d4abe13f07d8056c45bb95",
        name: "Lugana SANTI, DOC, Venetien, Italien",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60f700cf6a2c850c69690382",
        name: "Gelber Muskateller, Qualitätswein aus Niederösterreich",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6352a3cfd8be7c14754614fa",
        name: "Red Stone Riesling VDP",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥂",
  },
  {
    id: "60b1540e484a0865ae78756a",
    name: "White Wine",
    children: [
      {
        id: "63828b352d2e0e4ed43dde4c",
        name: "Weißburgunder Gunderloch Qualitätswein",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f7616f3f7c341c8200fda",
        name: "Weißburgunder Gunderloch Qualitätswein",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f7670f3f7c341c8200fdf",
        name: "Lugana Farina",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9acb710fe122f60859e5",
        name: "Custoza Farina (glas)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9ae6710fe122f60859e6",
        name: "Custoza Farina",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9e3d710fe122f60859ef",
        name: "Grauburgunder QbA",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9ed2710fe122f60859f0",
        name: "Blanc de Noir Gies Düppel",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9f1b710fe122f60859f1",
        name: "Lugana Monte del Fra, DOC (Zur Empfehlung)",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9fd9710fe122f60859f2",
        name: "Grüner Veltliner Dürnstein, Wachau, QW",
        unitPrice: 36,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609da056710fe122f60859f3",
        name: "Dönnhoff Stückfass, Chardonnay&Weissburgunder",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥂",
  },
  {
    id: "60bd09a65951350c0d0ffedd",
    name: "White Wine",
    children: [
      {
        id: "609d83c3710fe122f60859b7",
        name: "JEAN BAPTISTE RIESLING KABINETT",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "63828ea72d2e0e4ed43dde5b",
        name: "JEAN BAPTISTE RIESLING KABINETT",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9b95710fe122f60859e8",
        name: "Cuvee Reserve Österreich (zur Empfehlung)",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60bd0ccb5951350c0d0ffede",
        name: "Pinot Grigio -Porer- Alto Adige AC *bio",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥂",
  },
  {
    id: "609ba4e5710fe122f6085947",
    name: "Softdrinks/ Cold Drinks",
    children: [
      {
        id: "666ca1d75d3e9419f35521ef",
        name: "Glass SprudelWasser 0,4L",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60b3ec90484a0865ae7877e0",
        name: "Cola Zero",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f62f3f3f7c341c8200fa3",
        name: "Gefiltertes Sprudelwasser",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609ba58d710fe122f6085948",
        name: "Coca-Cola",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609ba5e1710fe122f6085949",
        name: "Sprudelwasser",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609ba635710fe122f608594a",
        name: "Stilles Wasser",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8a80710fe122f60859cd",
        name: "Aloe Vera",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669dd1765465b52c4cfdfa4",
        name: "Apfel Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669dd5365465b52c4cfdfa9",
        name: "Mango Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669dd9765465b52c4cfdfaf",
        name: "Maracuja Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669ddc265465b52c4cfdfb7",
        name: "Orange Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669dded65465b52c4cfdfbc",
        name: "Rhabarber Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669de1165465b52c4cfdfc4",
        name: "Johannisbeere Schorle",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669de3765465b52c4cfdfca",
        name: "Litchi Schorle",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669de7165465b52c4cfdfd0",
        name: "Oolong Ice Tea",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669de9300f7393756d351e3",
        name: "Calpis Milchdrink",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669decf00f7393756d351eb",
        name: "Limonade",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669def700f7393756d351f3",
        name: "Ramune Matcha Schorle",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6669e0e700f7393756d35225",
        name: "Paulaner Spezi",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66804de34763a2651ec15a0a",
        name: "Glass spudelwasser",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668050ef4763a2651ec15a44",
        name: "Glass Stilleswasser",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥤",
  },
  {
    id: "609ba874710fe122f6085951",
    name: "Bier",
    children: [
      {
        id: "609ba8be710fe122f6085952",
        name: "Helles",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf57d710fe122f608598a",
        name: "Weissbier",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "667067bf87205f2c2e21eaaf",
        name: "Weissbier",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668049404763a2651ec159bb",
        name: "Leicht Weissbier",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf38a710fe122f6085986",
        name: "Radler",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf631710fe122f608598d",
        name: "Russ bier",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf4c4710fe122f6085987",
        name: "Helles Alkoholfrei",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf5c1710fe122f608598b",
        name: "Weissbier Alkoholfrei",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf4f6710fe122f6085988",
        name: "Radler Alkoholfrei",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609cf665710fe122f608598e",
        name: "Russ Alkoholfrei",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60cf63ec04eac002c2615e48",
        name: "Pils",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60cf64e304eac002c2615e49",
        name: "pils alkoholfrei",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "627983b08238484f3aad436f",
        name: "Pils Alkoholfrei",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍺",
  },
  {
    id: "604e7ecc536f792212621b09",
    name: "Red Wine",
    children: [
      {
        id: "604f74caf3f7c341c8200fd3",
        name: "Heideboden Reserve",
        unitPrice: 25,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f755cf3f7c341c8200fd7",
        name: "Blaufränkisch (Top Angebot)",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d93af710fe122f60859dd",
        name: "Merlot QbA, zur Empfehlung, (glas)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9428710fe122f60859de",
        name: "Primitivo Salento (glas)",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d957f710fe122f60859e0",
        name: "Tempranillo Rioja Raposo DOCa",
        unitPrice: 48,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d960c710fe122f60859e1",
        name: "Priorat Petit Pissares",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a65ba71808343bd765400f",
        name: "Merlot QbA",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60bcffb45951350c0d0ffed8",
        name: "Primitivo Salento",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60bd00835951350c0d0ffed9",
        name: "Côtes du Rhône AC",
        unitPrice: 38,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60eb1d912bda596bd6358471",
        name: "Terre des Dames. Cuvee.",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "609d8c6d710fe122f60859d1",
    name: "Kaffee / Tee",
    children: [
      {
        id: "609d8d2a710fe122f60859d3",
        name: "Espresso",
        unitPrice: 2.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c22cf723621f60252bc3be",
        name: "Espresso  Macchiato",
        unitPrice: 2.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8d4f710fe122f60859d4",
        name: "Doppio Espresso",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8cca710fe122f60859d2",
        name: "Kaffee",
        unitPrice: 2.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8d6b710fe122f60859d5",
        name: "Cappuccino",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8db4710fe122f60859d6",
        name: "Latte Macchiato",
        unitPrice: 3.3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8df1710fe122f60859d7",
        name: "Goji Minze HonigTee",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8e36710fe122f60859d8",
        name: "Ingwer Minze Honig Tee",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d8e51710fe122f60859d9",
        name: "Pomelo Honig Tee",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60b3f37f484a0865ae7877f6",
        name: "Longjing Tee Blatt",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ef13c3a2fcdf6c1a87981c",
        name: "Schwarzer Tee Blatt",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60faefe1d96be0106c50bd38",
        name: "Jasmin Tee Blatt",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60faf0116a2c850c69690d51",
        name: "Grüner Tee Blatt",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61ddb7d82a0e9a3e61a6d993",
        name: "Oolong Tee Blatt (warm)",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "☕",
  },
  {
    id: "60c275a923621f60252bc3fc",
    name: "Spiritousen",
    children: [
      {
        id: "60c790f15af721149ad36c77",
        name: "Tonic Water",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ca1f9f2dcf4f7a114f1a67",
        name: "FEN CHIEW, Alk. 42%",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ca1f3a2dcf4f7a114f1a66",
        name: "FEN CHIEW 30Jahre (Flasche)",
        unitPrice: 150,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60d58a443f07d8056c45bce8",
        name: "52度 五粮液 （瓶装）",
        unitPrice: 280,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60d58abe3f07d8056c45bce9",
        name: "Kweichow Moutai, Alk. 53%",
        unitPrice: 520,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "646c9a124e875c0bb7e33482",
        name: "Wuliangchun (Flasche) ",
        unitPrice: 55,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "618d5ce9533bb80909412742",
        name: "Soju Chamisul Original",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "62c5a6bf2b5cac74fbe23800",
        name: "Chinesischer Schnaps Vol. 33% (Flasche)",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ca441f8b65453b0d033ef2",
        name: "Adler Berlin Dry Gin",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60b38c36484a0865ae7876c9",
        name: "Sake (Warm)",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60b38c59484a0865ae7876ca",
        name: "Sake (Kalt)",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c275e823621f60252bc3fe",
        name: "Absolut Vodka",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2773a23621f60252bc409",
        name: "Wisky Nikka",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2779723621f60252bc40a",
        name: "Akashi White Oak",
        unitPrice: 7,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c78b975af721149ad36c72",
        name: "The Duke Gin",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c78bcc5af721149ad36c73",
        name: "Jinzu",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c791155af721149ad36c78",
        name: "Sünner 260",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ca43d88b65453b0d033ef0",
        name: "Soul of Bavaria Gin",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60ca43f88b65453b0d033ef1",
        name: "Feel Munich",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610956188481813e3259dba7",
        name: "Berliner Brandstifter Gin",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610956298481813e3259dba8",
        name: "Elefant Dry Gin",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109564c8481813e3259dba9",
        name: "Diplome Dry Gin",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610956768481813e3259dbaa",
        name: "Bulldog London",
        unitPrice: 6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109569749d1e51b2a240983",
        name: "Hendricks",
        unitPrice: 6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610956bf8481813e3259dbab",
        name: "Cognac Chateau Montifaud",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109581e49d1e51b2a24098f",
        name: "Pircher Williams Birne",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109584a49d1e51b2a240990",
        name: "The Balvenie",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109587449d1e51b2a240992",
        name: "Big Peat",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610959158481813e3259dbb3",
        name: "Max& Daniels Ingwer",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "610959488481813e3259dbb6",
        name: "Togouchi Premium",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6109597b8481813e3259dbb8",
        name: "The Classic Laddie",
        unitPrice: 9.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61095a178481813e3259dbbc",
        name: "Floraison GVine",
        unitPrice: 7,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61095a398481813e3259dbc0",
        name: "Gin 27 Premium Appenzeller",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "620822022eddf2535ada742b",
        name: "Jägermeister",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥃",
  },
  {
    id: "60a115f67c4c1f1c97b57af5",
    name: "Nachtisch",
    children: [
      {
        id: "60a116a47c4c1f1c97b57af6",
        name: "Matcha Eis",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a116ee7c4c1f1c97b57af7",
        name: "Yuzu Eis",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1172c7c4c1f1c97b57af8",
        name: "Ingwer Eis",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a117577c4c1f1c97b57af9",
        name: "Sesam Eis",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a117847c4c1f1c97b57afb",
        name: "Mango Panna Cotta",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a117dc7c4c1f1c97b57afd",
        name: "Mochi Matcha",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a118097c4c1f1c97b57afe",
        name: "Mochi Mango",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67618e561f9e8d3ef045d333",
        name: "Mochi Matcha",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67683ce863a914771fd1ce3b",
        name: "芝麻汤圆",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67683ec063a914771fd1ce6f",
        name: "红豆沙汤圆",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍰",
  },
  {
    id: "60a139867c4c1f1c97b57b0e",
    name: "额外付费锅底",
    children: [
      {
        id: "60a139e37c4c1f1c97b57b10",
        name: "Chili-Brühe sehr sehr scharf",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13aac7c4c1f1c97b57b12",
        name: "Chili-Brühe scharf",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13aeb7c4c1f1c97b57b13",
        name: "Pilz-Brühe",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13b127c4c1f1c97b57b15",
        name: "Curry-Brühe",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13b297c4c1f1c97b57b16",
        name: "Miso-Brühe",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13b5a7c4c1f1c97b57b17",
        name: "Thai Tom Yam",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13b6b7c4c1f1c97b57b18",
        name: "Vietnam Pho   Vegan",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a13b897c4c1f1c97b57b19",
        name: "Tomaten-Brühe",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60af865e7684845d0d0ad291",
        name: "Shacha-Brühe",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60b346a2484a0865ae7875c3",
        name: "Thai Curry",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥘",
  },
  {
    id: "60a112047c4c1f1c97b57ae7",
    name: "Extrazutaten im Haus",
    children: [
      {
        id: "60a112507c4c1f1c97b57ae8",
        name: "Entenblut",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a112f87c4c1f1c97b57ae9",
        name: "卤肥肠",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1135e7c4c1f1c97b57aea",
        name: "毛肚",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a113a57c4c1f1c97b57aeb",
        name: "Krabbe",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a113f27c4c1f1c97b57aed",
        name: "Rindfleisch mit eingelegter Chillipasta",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1143d7c4c1f1c97b57aee",
        name: "Rindfleisch mit trockenem Chillipulver",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1147b7c4c1f1c97b57aef",
        name: "Fluss Aal",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a114c37c4c1f1c97b57af1",
        name: "Froschschenkel",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1151d7c4c1f1c97b57af2",
        name: "Riesengarnelen",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a1154c7c4c1f1c97b57af3",
        name: "Jakobsmuschel",
        unitPrice: 1.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍲",
  },
  {
    id: "666cabe55d3e9419f3552249",
    name: "Getränke Online",
    emoji: " ",
    children: [
      {
        id: "666cac625d3e9419f355224a",
        name: "Coca Cola",
        unitPrice: 3.57,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cade3222ef22bf8b11981",
        name: "Cola Zero",
        unitPrice: 3.57,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cadf75d3e9419f355224c",
        name: "Cola Light",
        unitPrice: 3.57,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cadfa222ef22bf8b11982",
        name: "Cola Mix",
        unitPrice: 3.57,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cadff222ef22bf8b11983",
        name: "Wasser Still",
        unitPrice: 6.72,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cae01222ef22bf8b11984",
        name: "Wasser Sprudel",
        unitPrice: 6.72,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cae045d3e9419f355224d",
        name: "Aloe Vera",
        unitPrice: 4.17,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb1485d3e9419f3552267",
        name: "Helles",
        unitPrice: 4.08,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb14a5d3e9419f3552268",
        name: "Leicht Weissbier",
        unitPrice: 4.08,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb14e5d3e9419f3552269",
        name: "Radler",
        unitPrice: 4.08,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb1505d3e9419f355226a",
        name: "Helles Alkoholfrei",
        unitPrice: 4.08,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb210222ef22bf8b119b8",
        name: "Weissbier Alkoholfrei",
        unitPrice: 4.08,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666cb2125d3e9419f355226f",
        name: "Radler Alkoholfrei",
        unitPrice: 3.83,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66804f443550a4279ef57819",
        name: "Stilles Wasser",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
  {
    id: "668fba794c427623ada28cce",
    name: "Weine and Soju",
    emoji: " ",
    children: [
      {
        id: "668fbaf14c427623ada28cd9",
        name: "Bio Hausrose 0.75l",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668fbb40581aeb2b115362f9",
        name: "Weißwein 0.75",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668fbb594c427623ada28cde",
        name: "Flasche Soju",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
  {
    id: "668fbaa94c427623ada28cd2",
    name: "Spritz und Bier",
    emoji: " ",
    children: [
      {
        id: "668fbb73581aeb2b11536302",
        name: "Aperol Spritz 0.75l",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668fbb8e4c427623ada28ce1",
        name: "Ingwer Spritz 0.75",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "668fbbc5581aeb2b1153630b",
        name: "Bier Oberbräu",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
  {
    id: "609d97e2710fe122f60859e3",
    name: "löschen",
    emoji: " ",
    children: [
      {
        id: "60c2762623621f60252bc404",
        name: "Gaoliang",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c5079237c2c7419fea7696",
        name: "Calpis Milchsäure",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f6022f3f7c341c8200fa2",
        name: "Goldmuskateller Pfefferer",
        unitPrice: 19,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f74f9f3f7c341c8200fd4",
        name: "Pinot Noir Reserve",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f75c9f3f7c341c8200fd8",
        name: "Primitivo Merlot",
        unitPrice: 19,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f7728f3f7c341c8200fe8",
        name: "Lemberger Rosé trocken",
        unitPrice: 27,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "604f791ef3f7c341c8200ff5",
        name: "Domaine ATTILON",
        unitPrice: 19,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d84e5710fe122f60859b9",
        name: "Champagne Brut Platine, Ier Cru",
        unitPrice: 69,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d94a7710fe122f60859df",
        name: "Spätburgunder Pfeffingen",
        unitPrice: 36,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9cf8710fe122f60859eb",
        name: "Gelber Muskateller DAC",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "609d9e2a710fe122f60859ee",
        name: "Grauburgunder QbA (Zur Empfehlung)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60a115997c4c1f1c97b57af4",
        name: "Rindfleisch in dünnen Scheiben",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60bd0d245951350c0d0ffedf",
        name: "Mâcon-Verzé AC *bio",
        unitPrice: 45,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60d4b0433f07d8056c45bbb9",
        name: "Chiaretto Bardolino",
        unitPrice: 19,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60eb1c5c2bda596bd6358467",
        name: "Sauvignon Blanc (Top Angebot)",
        unitPrice: 26,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "64d14fb4b9c3927e184f306a",
        name: "Diva Rosé, bio",
        unitPrice: 40,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
];
