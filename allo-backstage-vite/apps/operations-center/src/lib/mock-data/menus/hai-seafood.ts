import { Menu } from "../menu";
import { getSuperFolderColor } from "./colors";

export const menu: Menu = [
  {
    id: "666c36055d3e9419f3551baf",
    name: "<PERSON>dr<PERSON><PERSON>",
    children: [
      {
        id: "666f1672222ef22bf8b12b98",
        name: "<PERSON>ser ohne 0.75l",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb1",
        name: "<PERSON><PERSON> mit 0.75l",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666f25a4222ef22bf8b12c9c",
        name: "<PERSON>ser ohne 0,25l",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb0",
        name: "<PERSON>ser mit 0.25l",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb2",
        name: "Coca-Cola 0,33l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb3",
        name: "Coca-Cola Zero 0,33l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb4",
        name: "Sprite 0,33l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bb5",
        name: "Mezzo Mix 0,33l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bb6",
        name: "Apfelschorle 0,4l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bb7",
        name: "Mangoschorle 0,4l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bb8",
        name: "Lycheeschorle 0,4l",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "673e24d9f4f85d7fbbaee6de",
        name: "Dry Tonic 0,2l",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥤",
  },
  {
    id: "666c36055d3e9419f3551b9e",
    name: "Aperitif",
    children: [
      {
        id: "666c36055d3e9419f3551b9f",
        name: "Crémant de Loire Blanc Brut 0,1l",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba0",
        name: "Crémant de Loire Rosé Brut 0,1l",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba1",
        name: "Aperol Spritz",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba2",
        name: "Negroni",
        unitPrice: 9.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba5",
        name: "Gin Tonic alkoholfrei",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba6",
        name: "Ingwer Soda",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba7",
        name: "Yuzu Kumquats Soda",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "667451a412908a030da9f8a0",
        name: "Weinschorle 0,2l",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b2425fb50e199eeaa76b",
        name: "Campari Spritz",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b2645487ab2a0a551713",
        name: "Aperol Sour",
        unitPrice: 11.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b2935fb50e199eeaa772",
        name: "Hai Spritz",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "673765b09adf1d375ba4af17",
        name: "Portwein Tonic",
        unitPrice: 9.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67caf70b3e216e5701f4c50f",
        name: "Plaumenwein Tonic",
        unitPrice: 9.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67d2eecd749ae93348386171",
        name: "Yuzu Sgroppino",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍹",
  },
  {
    id: "666c36055d3e9419f3551b59",
    name: "Sparkling 0.1 L",
    children: [
      {
        id: "666c36055d3e9419f3551b5b",
        name: "Crémant de Loire Blanc Brut 0.1l",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b5c",
        name: "Crémant de Loire Rosé Brut 0.1l",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "666c36055d3e9419f3551b5f",
    name: "Weissweine 0.1 L",
    children: [
      {
        id: "666c36055d3e9419f3551b60",
        name: "Grauburgunder von der Mark Walter 0.1l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b62",
        name: 'Grüner Veltliner "Fliegen Gewicht" 0.1l',
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b63",
        name: "Sauvignon Blanc Pays d'Oc 0.1l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b64",
        name: "Chardonnay Pays d'Oc 0.1l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b65",
        name: 'Pinot Grigio "I Ciari" DOC 0.1l',
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b66",
        name: "Folàr Lugana DOC 0.1l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "679bb64cbfd79e0da0fbccbb",
        name: "Saar Riesling 0,1 l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "66c604352629f04760f16845",
    name: "limited time",
    children: [
      {
        id: "666c36055d3e9419f3551b7b",
        name: "Grauburgunder vom Löss 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c605592629f04760f16850",
        name: "Sauvignon Blanc vom Stein 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b7c",
        name: "Riesling by the glass 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c605a45d4ad94e101bfa88",
        name: "Les Amandiers Rose 0,75l",
        unitPrice: 30,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c604672629f04760f16846",
        name: "Sancerre Bailly-Reverdy 0.75l",
        unitPrice: 46,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c6054c2629f04760f1684f",
        name: "Grüner Veltliner Reserve 0,75l",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b8b",
        name: "Merlot Pays d'Oc 0.75l",
        unitPrice: 26,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b96",
        name: "Chianti Classico 0,75l",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551b3d",
    name: "Kaltes",
    children: [
      {
        id: "666c36055d3e9419f3551b3e",
        name: "毛豆 Edamame",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [
          {
            id: "666f267b5d3e9419f3553450",
            name: "schaf / nicht scharf",
            min: 1,
            max: 1,
            options: [
              {
                id: "666f292e222ef22bf8b12cca",
                name: "辣 scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "666f293e5d3e9419f355347d",
                name: "不辣 nicht scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "666c36055d3e9419f3551b3f",
        name: "拌黄瓜 Minigurkensalat",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b40",
        name: "虾皮小瓜 Zucchinisalat ",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b41",
        name: "油焖笋 Gebratene Bambusstäbe",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b42",
        name: "三文鱼刺身 Lachs Sashimi",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66ab63f96ccca01187532f8b",
        name: "辣油松子布拉塔 Burrata mit hausgemachter Chili-Pinien Pesto",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66ab6411a5ef7272c8e3376f",
        name: "泡菜布拉塔 kimchi Burrata",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66db0bc400295e1a3685b900",
        name: "吉娜朵 Gillardeau 1 Stk.",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc784d02a78018f436cca2",
        name: "金枪鱼 Thunfisch Sashimi",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc786c00295e1a3685ca53",
        name: "带子刺身 Scallop Sashimi",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "679a6371b839631b3b49e3ec",
        name: "𫚕鱼 Hamachi Sashimi ",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67cb38773e216e5701f4ceb5",
        name: "吉娜朵2个 Gillardeau 2 Stk.",
        unitPrice: 13,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍴",
  },
  {
    id: "666c36055d3e9419f3551b44",
    name: "Pan Fried",
    children: [
      {
        id: "666c36055d3e9419f3551b45",
        name: "炸鸡 Kaarage",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b46",
        name: "炸沙丁 Tempura Sardinen",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b47",
        name: "炸虾 frittierte Riesengarnelen",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b48",
        name: "香菇素饼 Pfannkuchen mit Shitake",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [
          {
            id: "666f267b5d3e9419f3553450",
            name: "schaf / nicht scharf",
            min: 1,
            max: 1,
            options: [
              {
                id: "666f292e222ef22bf8b12cca",
                name: "辣 scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "666f293e5d3e9419f355347d",
                name: "不辣 nicht scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "666c36055d3e9419f3551b49",
        name: "牛肉饼 Pfannkuchen mit Wagyu",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [
          {
            id: "666f267b5d3e9419f3553450",
            name: "schaf / nicht scharf",
            min: 1,
            max: 1,
            options: [
              {
                id: "666f292e222ef22bf8b12cca",
                name: "辣 scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "666f293e5d3e9419f355347d",
                name: "不辣 nicht scharf",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "666c36055d3e9419f3551b4a",
        name: "松露年糕虾球4个 gebratene Riesengarnelen  mit Trüffelöl & Klebreisküchlein ",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b4b",
        name: "荠菜松子虾球 4个 gebratene Riesengarnelen mit Asiakräuter & Pinienkern",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b4f",
        name: "藕酿虾 Gebratener Lotuswurzel gefüllt mit Garnelen",
        unitPrice: 15,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b50",
        name: "虾串 Garnelen Spieße mit Paprika",
        unitPrice: 15,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666feef4f9454b67e7f0635a",
        name: "香菇饭团 Onigiri Shiitake gebacken",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66df27a202a78018f436e462",
        name: "牛肉饭团 Onigiri Wagyu gebacken",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e5aa4bb77d9127f9e24982",
        name: "烤鳕鱼 Black Cod gebraten",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6713d8f0cbf5f655c7eab4cf",
        name: "椒盐青椒 Gebratene Pimientos",
        unitPrice: 10,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "671539d4cbf5f655c7eacab6",
        name: "紫菜蛋饼 Omelett mit Nori u. Tintenfisch ",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c634a1de12050fe1876260",
        name: "虾饺4个 Gyoza 4 Stk.",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🥘",
  },
  {
    id: "666c36055d3e9419f3551b4c",
    name: "Steamed Seefood",
    children: [
      {
        id: "666c36055d3e9419f3551b4d",
        name: "蒸鱿鱼筒 gedämpfte Tintenfisch",
        unitPrice: 15,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b4e",
        name: "黄椒蒸鳕鱼 gedämpfter Black Cod",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6715397987bf8d608aa410ea",
        name: "紫菜笔管 Calamari gefüllt mit Nori ",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🦐",
  },
  {
    id: "666c36055d3e9419f3551b51",
    name: "Boild Seefood",
    children: [
      {
        id: "66c087eeaf24540b97880049",
        name: "蒜香 Knoblauch",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "67d713321b6f2040105ee537",
            name: "wann",
            min: 1,
            max: 1,
            options: [
              {
                id: "67d713641b6f2040105ee547",
                name: "一起上 mit Vorspeise",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "67d713771b6f2040105ee54f",
                name: "等叫 2.gang",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "666c36055d3e9419f3551b53",
        name: "冬阴 Tom Yum",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "67d713321b6f2040105ee537",
            name: "wann",
            min: 1,
            max: 1,
            options: [
              {
                id: "67d713641b6f2040105ee547",
                name: "一起上 mit Vorspeise",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "67d713771b6f2040105ee54f",
                name: "等叫 2.gang",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "66c08860af24540b97880057",
        name: "香辣 Scharf Chinese Soul",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "67d713321b6f2040105ee537",
            name: "wann",
            min: 1,
            max: 1,
            options: [
              {
                id: "67d713641b6f2040105ee547",
                name: "一起上 mit Vorspeise",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "67d713771b6f2040105ee54f",
                name: "等叫 2.gang",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
      {
        id: "66c08a0c1a417c2ae13d3508",
        name: "沙茶 Shacha",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "67d713321b6f2040105ee537",
            name: "wann",
            min: 1,
            max: 1,
            options: [
              {
                id: "67d713641b6f2040105ee547",
                name: "一起上 mit Vorspeise",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
              {
                id: "67d713771b6f2040105ee54f",
                name: "等叫 2.gang",
                unitPrice: 0,
                initialQuantity: 0,
                min: 1,
                max: 1,
              },
            ],
          },
        ],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🦑",
  },
  {
    id: "666c36055d3e9419f3551b5d",
    name: "Roséweine 0.1 L",
    children: [
      {
        id: "666c36055d3e9419f3551b5e",
        name: 'Rosé "I Rosa" 0.1l',
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c6069a5d4ad94e101bfa94",
        name: "Lignes Rose 0,1l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "666c36055d3e9419f3551b67",
    name: "Rotweine 0.1 L",
    children: [
      {
        id: "666c36055d3e9419f3551b6a",
        name: "Pinot Noir Pays d'Oc 0.1l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b6b",
        name: "Nero d'Avola Sicilia 0.1l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b6c",
        name: "Primitivo Larinum Puglia 0.1l",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "666c36055d3e9419f3551b6d",
    name: "Drinks zum Dessert",
    children: [
      {
        id: "666c36055d3e9419f3551b6e",
        name: "Ruby Port 0,1l",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b6f",
        name: "Chateau Guiraud 2015",
        unitPrice: 25,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b7325487ab2a0a551731",
        name: "Plaumenwein 5cl",
        unitPrice: 6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b7475487ab2a0a551733",
        name: "Yuzu Sake 5cl",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍷",
  },
  {
    id: "666c36055d3e9419f3551b70",
    name: "Sparkling 0.75 L",
    children: [
      {
        id: "666c36055d3e9419f3551b71",
        name: "Crémant de Loire Blanc Brut 0.75l",
        unitPrice: 52,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b72",
        name: "Crémant de Loire Rosé Brut 0.75l",
        unitPrice: 58,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b73",
        name: "Champagne Théophile Brut 0.75l",
        unitPrice: 98,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b74",
        name: "Champagne Théophile Brut Rosé 0.75l",
        unitPrice: 110,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b75",
        name: "Blanc de Blanc extra Brut 0.75l",
        unitPrice: 58,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551b76",
    name: "Roséweine 0.75 L",
    children: [
      {
        id: "666c36055d3e9419f3551b77",
        name: 'Rosé "I Rosa" DOC 2021 0.75l',
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b78",
        name: "By.Ott Rosé Côtes de Provence 2021 0.75l",
        unitPrice: 46,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66c606fa5d4ad94e101bfa95",
        name: "Lignes Rose 0,75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551b79",
    name: "Weissweine 0.75 L",
    children: [
      {
        id: "666c36055d3e9419f3551b7a",
        name: "Grauburgunder QbA von der Mark Walter 0.75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b7d",
        name: "Scheurebe QW 0.75l",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b7e",
        name: "Riesling Johannisbrünnchen GG 0.75l",
        unitPrice: 65,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b7f",
        name: 'Grüner Veltliner "Fliegen Gewicht" 0.75l',
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b81",
        name: "Sauvignon Blanc Pays d'Oc 2021 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b82",
        name: "Chardonnay Pays d'Oc 2021 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b83",
        name: "Sancerre AC 0.75l",
        unitPrice: 55,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b84",
        name: 'Chablis "Saint Pierre" 0.75l',
        unitPrice: 60,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b85",
        name: 'Pouilly-Fuissé „Clos du Pavillon" 0.75l',
        unitPrice: 79,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b86",
        name: "Chablis Mont de Milieu Premier Cru 0.75l",
        unitPrice: 99,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b87",
        name: "Pinot Grigio „I Ciari” 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b88",
        name: "Folàr Lugana DOC 0.75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b89",
        name: "Rossj-Bass Langhe DOC 0.75l",
        unitPrice: 139,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "679bb68614b7f9739a765e29",
        name: "Saar Riesling 0,75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67ae35e4b4fbb946a99ad81d",
        name: "Sonnenuhr Riesling Auslese 0.75l",
        unitPrice: 119,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67ae361093269800849f66ac",
        name: "Roero Arneis 0.75l",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67ae3634b4fbb946a99ad839",
        name: "Sanmur Blanc 0.75l",
        unitPrice: 75,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551b8a",
    name: "Rotweine 0.75 L",
    children: [
      {
        id: "666c36055d3e9419f3551b8c",
        name: "Malbec Cépage Rare Pays d'Oc 0.75l",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b8d",
        name: "Pinot Noir Pays d'Oc 0.75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b8e",
        name: "Côtes du Rhône Rouge 0.75l",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b8f",
        name: "Grand Cru Saint Emilion 0.75l",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b90",
        name: "Echo de Lynch-Bages 0.75l",
        unitPrice: 120,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b91",
        name: "Château Lynch-Bages 5ème Cru Classé 0.75l",
        unitPrice: 260,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b92",
        name: "Tempranillo Rioja 0.75l",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b93",
        name: "Nero d'Avola Sicilia 0.75l",
        unitPrice: 29,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b94",
        name: "Primitivo Larinum Puglia 0.75l",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b97",
        name: "Amarone Torre d'Orti della Valpolicella 0,75l",
        unitPrice: 79,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b98",
        name: "Brunello di Montalcino DOCG 0,75l",
        unitPrice: 98,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b99",
        name: "Dagromis di Gaja Barolo 0.75l",
        unitPrice: 135,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b9a",
        name: "Barbaresco DOCG 2020 0.75l",
        unitPrice: 325,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551b9b",
    name: "Süßweine 0.75 L",
    children: [
      {
        id: "666c36055d3e9419f3551b9c",
        name: "Ruby Port, Vintage character 0.75l",
        unitPrice: 55,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551b9d",
        name: "Chateau Guiraud 2015 0.75l",
        unitPrice: 150,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍾",
  },
  {
    id: "666c36055d3e9419f3551ba8",
    name: "Bier",
    children: [
      {
        id: "67228f750b4fc338ef30c2a6",
        name: "mini Helles 0,3l",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551ba9",
        name: "Helles vom Fass 0.5l",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551baa",
        name: "Radler 0.5l",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bab",
        name: "Weißbier 0.5l",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bac",
        name: "Russen 0.5l",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bad",
        name: "Pils 0.3l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36055d3e9419f3551bae",
        name: "Pils Alkoholfrei 0.3l",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍻",
  },
  {
    id: "666c36065d3e9419f3551bb9",
    name: "Cocktails",
    children: [
      {
        id: "666c36065d3e9419f3551bba",
        name: "Moskow Mule",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bbb",
        name: "Munich Mule",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bbc",
        name: "Gin Fizz ",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bbd",
        name: "Matscha Baileys",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b3365487ab2a0a551718",
        name: "Ki No Bi Smash",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b3685487ab2a0a55171c",
        name: "Lychee Mojito",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67caf75071b80d56ef9d62da",
        name: "Espresso Baileys",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67caf7693e216e5701f4c513",
        name: "Espresso Martini",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍹",
  },
  {
    id: "666c36065d3e9419f3551bbe",
    name: "Kaffee",
    children: [
      {
        id: "666c36065d3e9419f3551bbf",
        name: "Kaffee",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc0",
        name: "Espresso",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc1",
        name: "Espresso Doppio",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc2",
        name: "Espresso Macchiato",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc3",
        name: "Cappuccino",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc4",
        name: "Latte Macchiato",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "☕",
  },
  {
    id: "666c36065d3e9419f3551bc5",
    name: "Tee",
    children: [
      {
        id: "666c36065d3e9419f3551bc6",
        name: "Schwarzer Tee",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc7",
        name: "Jasmin Tee",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc8",
        name: "Milky Oolong",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bc9",
        name: "Kamille Tee",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6737652b9adf1d375ba4af0c",
        name: "Ingwer Tee",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "673765406d7a10342c0abb9e",
        name: "1*Nachguss",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍵",
  },
  {
    id: "666c36065d3e9419f3551bca",
    name: "Dessert",
    children: [
      {
        id: "666c36065d3e9419f3551bcb",
        name: "Yuzu Sorbet",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bcc",
        name: "Seasam Eis",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666c36065d3e9419f3551bce",
        name: "Cheesecake Original",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e46a8ab77d9127f9e23c49",
        name: "Cheesecake Pistazie",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66ec76e5c634da541923c6d2",
        name: "Matscha Creme Brulee",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍰",
  },
  {
    id: "666f142e222ef22bf8b12b60",
    name: "Beilage",
    children: [
      {
        id: "666f1456222ef22bf8b12b67",
        name: "拉面 Ramen",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666f147c222ef22bf8b12b6f",
        name: "米饭 Reis",
        unitPrice: 2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666f14d0222ef22bf8b12b73",
        name: "粉丝 Glasnudeln",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66b8f0a2b9d99e3db046b366",
        name: "三文鱼150g Lachs",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66cf570fa32dbd46b0e323ad",
        name: "平菇 Austernpilze",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66db0c0100295e1a3685b901",
        name: "西兰花 Broccoli",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66db0c2202a78018f436bc9a",
        name: "龙虾尾 Lagustenschwanz",
        unitPrice: 50,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63939de12050fe187627b",
        name: "年糕 Klebreisküchlein",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63959a635fd31044aa6ff",
        name: "大虾3个",
        unitPrice: 11,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63975de12050fe187627c",
        name: "带子3个",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c6398fde12050fe187627d",
        name: "笔管3个",
        unitPrice: 7,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c639aca635fd31044aa700",
        name: "鱿鱼花 150g",
        unitPrice: 7,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63aa0a635fd31044aa701",
        name: "小墨鱼 150g",
        unitPrice: 7,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63acbde12050fe187627e",
        name: "青口贝3个",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63afbde12050fe187627f",
        name: "白贝 150g",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63b2dde12050fe1876280",
        name: "螃蟹 4块",
        unitPrice: 6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c63b55de12050fe1876281",
        name: "玉米 6块",
        unitPrice: 4,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍚",
  },
  {
    id: "666f18e65d3e9419f355334d",
    name: "Sake",
    children: [
      {
        id: "666f1936222ef22bf8b12bc5",
        name: "Rihaku Wandering Poet 0,3l",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6675dd9f6c733f7915490e1c",
        name: "Dassai 23 Jumai Daiginjo 0,3l",
        unitPrice: 99,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66899ecea9ee203c0b2a64e8",
        name: "Ume Rose Nanbu Bijin 0,3l",
        unitPrice: 49,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6738f7709adf1d375ba4d7ae",
        name: "Sake Fukuju 0,5l",
        unitPrice: 69,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍶",
  },
  {
    id: "666fecfd87205f2c2e21e6c1",
    name: "Lunch ",
    children: [
      {
        id: "666fed50f9454b67e7f06356",
        name: "M1 Thai Street Seafood Ramen Soup 泰国海鲜面",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666fed7c87205f2c2e21e6c2",
        name: "M2 Ramen Soup mit Karaage 日式炸鸡面",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666feda787205f2c2e21e6c3",
        name: "M3 Vegan Ramen Soup 素汤面",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666fee60f9454b67e7f06357",
        name: "M4 Non-Soup DanDan Garnelen Ramen 担担虾拌面",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666fee9ef9454b67e7f06358",
        name: "M5 Non-Soup Beef Ramen 牛肉拌面",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "666feecff9454b67e7f06359",
        name: "Reisbowl Seafood Mix 海鲜盖饭",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dec38d00295e1a3685ddc8",
        name: " Reisbowl Rindfleisch 牛肉盖饭",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dec3aa02a78018f436df7c",
        name: "Reisbowl Hühnerfleisch 鸡肉盖饭",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dec3c802a78018f436df7f",
        name: "Reisbowl Aubergine 茄子盖饭",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6797e360bab35f4a622d8ff8",
        name: "Reisbowl Lachs 三文鱼盖饭",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6797fb9bbab35f4a622d90eb",
        name: "Miso Suppe 味增汤",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍜",
  },
  {
    id: "66882d5ea9ee203c0b2a5795",
    name: "Mitarbeiter",
    children: [
      {
        id: "66882d95a9ee203c0b2a579b",
        name: " Limonade Pali",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6730fc9f73f2412035e17ace",
        name: "Cola 0,5l",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: " ",
  },
  {
    id: "66dc78c800295e1a3685ca64",
    name: "offene 0,2 L",
    children: [
      {
        id: "66dc790f02a78018f436ccbd",
        name: "Grauburgunder Walter 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc793900295e1a3685ca6d",
        name: "Red Stone Riesling 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc796602a78018f436ccd1",
        name: "Grüner Veltliner 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc798502a78018f436ccd6",
        name: "Sauvignon Blanc 0,2l",
        unitPrice: 8.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc79a900295e1a3685ca78",
        name: "Chardonnay 0,2l",
        unitPrice: 8.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc79be00295e1a3685ca7a",
        name: "Pino Grigio 0,2l",
        unitPrice: 8.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc79d802a78018f436ccdf",
        name: "Lugana 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc79f902a78018f436cceb",
        name: "Rosé I Rosa 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc7a1700295e1a3685ca81",
        name: "Lignes Rosé 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc7a3502a78018f436ccf8",
        name: "Pinot Noir 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc7a5000295e1a3685ca83",
        name: "Nero d‘Avola 0,2l",
        unitPrice: 8.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc7a6d02a78018f436cd00",
        name: "Primitivo 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "679bb663bfd79e0da0fbccc3",
        name: "Saar Riesling 0,2l",
        unitPrice: 9.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: " ",
  },
  {
    id: "66f6b43b5fb50e199eeaa783",
    name: "Gin 4cl",
    emoji: " ",
    children: [
      {
        id: "66f6b4555487ab2a0a55171e",
        name: "Tanqueray",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b4735fb50e199eeaa785",
        name: "Malfy",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b48c5487ab2a0a55171f",
        name: "Monkey 47",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b4a25487ab2a0a551720",
        name: "Ki No Bi",
        unitPrice: 10.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
  {
    id: "66f6b61e5487ab2a0a551727",
    name: "Japan Whisky 4cl",
    emoji: " ",
    children: [
      {
        id: "66f6b6325487ab2a0a551728",
        name: "The Chita 4cl",
        unitPrice: 10,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b64a5fb50e199eeaa796",
        name: "Yoichi 4cl",
        unitPrice: 12,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b6765487ab2a0a55172b",
        name: "Hibiki 4cl",
        unitPrice: 16,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b6a85487ab2a0a55172e",
        name: "The Hakushu 4cl",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66f6b6ba5fb50e199eeaa79b",
        name: "Yamazaki 12",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
  },
  {
    id: "67c1e59aded30f37a7cc97f6",
    name: "Raw & Refined",
    children: [
      {
        id: "666c36055d3e9419f3551b42",
        name: "三文鱼刺身 Lachs Sashimi",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66db0bc400295e1a3685b900",
        name: "吉娜朵 Gillardeau 1 Stk.",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66dc784d02a78018f436cca2",
        name: "金枪鱼 Thunfisch Sashimi",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "679a6371b839631b3b49e3ec",
        name: "𫚕鱼 Hamachi Sashimi ",
        unitPrice: 18,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c1e6b3cd48fc1edc83da46",
        name: "吉娜朵 3个 Gillardeau 3 Stk.",
        unitPrice: 19.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c1e7aeded30f37a7cc985a",
        name: "Sashimi Platte 9 Stk. 刺身拼盘 9片",
        unitPrice: 36,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67c1e7efded30f37a7cc9860",
        name: "Sashimi Platte 12 Stk. 刺身拼盘 12片",
        unitPrice: 46,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67cb38773e216e5701f4ceb5",
        name: "吉娜朵2个 Gillardeau 2 Stk.",
        unitPrice: 13,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: " ",
  },
  {
    id: "67c6011688350865affd27f4",
    name: "Abrufen",
    children: [
      {
        id: "67c6016090d8d47d868f3fa2",
        name: "上咖",
        unitPrice: 0,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: " ",
  },
  {
    id: "67d84f8b1b6f2040105ef33b",
    name: "Boild Seafood to go",
    children: [
      {
        id: "67d84ff51b6f2040105ef346",
        name: "蒜香",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67d851ee0f523a7cabb6133f",
        name: "冬阴功",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67d852231b6f2040105ef395",
        name: "沙茶",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67d852620f523a7cabb61354",
        name: "香辣",
        unitPrice: 39.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
    color: getSuperFolderColor(),
    emoji: "🍲",
  },
  {
    id: "668fff1e4c427623ada28f30",
    name: "Lagerverwaltung",
    children: [],
    color: getSuperFolderColor(),
    emoji: " ",
  },
];
