import { Menu } from "../menu";

export const menu: Menu = [
  {
    id: "starters",
    emoji: "🥖",
    color: "#44947A",
    name: "Start<PERSON>",
    children: [
      {
        id: "cold-starters",
        name: "Cold Starters",
        children: [
          {
            id: "0001",
            name: "Bread Basket",
            unitPrice: 4.5,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0002",
            name: "Marinated Olives",
            unitPrice: 3.5,
            configuration: [],
            suggestedNotes: [],
            isSoldOut: true,
          },
          {
            id: "0004",
            name: "<PERSON><PERSON><PERSON><PERSON>",
            unitPrice: 6,
            configuration: [
              {
                id: "bread-type",
                name: "Bread Type",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "sourdough",
                    name: "Sourdough",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "ciabatta",
                    name: "<PERSON><PERSON><PERSON><PERSON>",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra garlic", "No onions", "Light on olive oil"],
          },
        ],
      },
      {
        id: "hot-starters",
        name: "Hot Starters",
        children: [
          {
            id: "0003",
            name: "Soup of the Day",
            unitPrice: 5.5,
            configuration: [
              {
                id: "soup-size",
                name: "Size",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "regular",
                    name: "Regular",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "large",
                    name: "Large",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra hot", "Light on salt", "Bread on side"],
          },
          {
            id: "0005",
            name: "Fried Calamari",
            unitPrice: 8.5,
            configuration: [],
            suggestedNotes: [],
          },
        ],
      },
    ],
  },
  {
    id: "drinks",
    emoji: "🍺",
    color: "#4995AB",
    name: "Drinks",
    children: [
      {
        id: "beer",
        name: "Beer",
        children: [
          {
            id: "0006",
            name: "Lager",
            unitPrice: 2,
            configuration: [
              {
                id: "beer-size",
                name: "Size",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "half-pint",
                    name: "Half Pint",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "pint",
                    name: "Pint",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra cold", "Chilled glass"],
          },
          {
            id: "0007",
            name: "Ale",
            unitPrice: 6,
            configuration: [],
            suggestedNotes: [],
          },
        ],
      },
      {
        id: "wines",
        name: "Wine",
        children: [
          {
            id: "sparkling",
            name: "Sparkling",
            children: [
              {
                id: "0008",
                name: "Rose Sparkling",
                unitPrice: 3,
                configuration: [],
                suggestedNotes: [],
              },
              {
                id: "0009",
                name: "White Sparkling",
                unitPrice: 3,
                configuration: [],
                suggestedNotes: [],
              },
            ],
          },
          {
            id: "red",
            name: "Red",
            children: [
              {
                id: "0010",
                name: "Red Wine",
                unitPrice: 10,
                configuration: [
                  {
                    id: "wine-size",
                    name: "Serving",
                    min: 1,
                    max: 1,
                    options: [
                      {
                        id: "glass",
                        name: "Glass (175ml)",
                        min: 0,
                        max: 1,
                        initialQuantity: 0,
                        unitPrice: 0,
                      },
                      {
                        id: "large-glass",
                        name: "Large Glass (250ml)",
                        min: 0,
                        max: 1,
                        initialQuantity: 0,
                        unitPrice: 3,
                      },
                      {
                        id: "bottle",
                        name: "Bottle (750ml)",
                        min: 0,
                        max: 1,
                        initialQuantity: 0,
                        unitPrice: 20,
                      },
                    ],
                  },
                ],
                suggestedNotes: ["Room temperature", "Decanted"],
              },
            ],
          },
          {
            id: "white",
            name: "White",
            children: [
              {
                id: "0011",
                name: "White Wine",
                unitPrice: 10,
                configuration: [],
                suggestedNotes: [],
              },
            ],
          },
          {
            id: "rose",
            name: "Rose",
            children: [
              {
                id: "0012",
                name: "Rose Wine",
                unitPrice: 10,
                configuration: [],
                suggestedNotes: [],
              },
            ],
          },
        ],
      },
      {
        id: "water",
        name: "Water",
        children: [
          {
            id: "0013",
            name: "Mineral Water",
            unitPrice: 2.5,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0014",
            name: "Sparkling Water",
            unitPrice: 3,
            configuration: [],
            suggestedNotes: [],
          },
        ],
      },
      {
        id: "soft-drinks",
        name: "Soft Drinks",
        children: [
          {
            id: "0015",
            name: "Soft Drinks",
            unitPrice: 3,
            configuration: [],
            suggestedNotes: [],
          },
        ],
      },
      {
        id: "hot-drinks",
        name: "Hot Drinks",
        children: [
          {
            id: "0016",
            name: "Coffee",
            unitPrice: 3.5,
            configuration: [
              {
                id: "coffee-type",
                name: "Type",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "espresso",
                    name: "Espresso",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "americano",
                    name: "Americano",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "cappuccino",
                    name: "Cappuccino",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "latte",
                    name: "Latte",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                ],
              },
              {
                id: "coffee-extras",
                name: "Extras",
                min: 0,
                max: 3,
                options: [
                  {
                    id: "extra-shot",
                    name: "Extra Shot",
                    min: 0,
                    max: 2,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "syrup",
                    name: "Flavored Syrup",
                    min: 0,
                    max: 2,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "whipped-cream",
                    name: "Whipped Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Extra hot",
              "Light foam",
              "Sugar free syrup available",
            ],
          },
          {
            id: "0017",
            name: "Tea",
            unitPrice: 3,
            configuration: [
              {
                id: "tea-type",
                name: "Type",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "english-breakfast",
                    name: "English Breakfast",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "earl-grey",
                    name: "Earl Grey",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "green",
                    name: "Green Tea",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "chamomile",
                    name: "Chamomile",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
              {
                id: "tea-extras",
                name: "Extras",
                min: 0,
                max: 2,
                options: [
                  {
                    id: "milk",
                    name: "Milk",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "honey",
                    name: "Honey",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "lemon",
                    name: "Lemon",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra hot", "Milk on side", "Strong brew"],
          },
        ],
      },
      {
        id: "juices",
        name: "Juices",
        children: [
          {
            id: "0018",
            name: "Fresh Juice",
            unitPrice: 4.5,
            configuration: [
              {
                id: "juice-type",
                name: "Type",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "orange",
                    name: "Orange",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "apple",
                    name: "Apple",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "carrot",
                    name: "Carrot",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "mixed",
                    name: "Mixed",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                ],
              },
              {
                id: "juice-extras",
                name: "Extras",
                min: 0,
                max: 2,
                options: [
                  {
                    id: "ginger",
                    name: "Ginger Shot",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                  {
                    id: "ice",
                    name: "Ice",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: ["No pulp", "Extra ice", "Not too sweet"],
          },
        ],
      },
    ],
  },
  {
    id: "main-fish",
    emoji: "🐟",
    color: "#5676AE",
    name: "Main Fish",
    children: [
      {
        id: "grilled-fish",
        name: "Grilled Fish",
        children: [
          {
            id: "0019",
            name: "Grilled Salmon",
            unitPrice: 18.5,
            configuration: [
              {
                id: "cooking",
                name: "Cooking Preference",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "salmon-rare",
                    name: "Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "salmon-medium",
                    name: "Medium",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "salmon-well",
                    name: "Well Done",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
              {
                id: "sides",
                name: "Choose your side",
                min: 1,
                max: 2,
                options: [
                  {
                    id: "rice",
                    name: "Steamed Rice",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "potatoes",
                    name: "Roasted Potatoes",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "vegetables",
                    name: "Grilled Vegetables",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: ["No sauce", "Extra lemon", "Gluten free"],
          },
          {
            id: "0020",
            name: "Seared Tuna Steak",
            unitPrice: 19,
            configuration: [
              {
                id: "tuna-cooking",
                name: "Cooking Preference",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "tuna-rare",
                    name: "Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "tuna-medium-rare",
                    name: "Medium Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Sesame crust",
              "No wasabi",
              "Gluten free soy sauce",
            ],
          },
        ],
      },
      {
        id: "other-fish",
        name: "Other Fish",
        children: [
          {
            id: "0021",
            name: "Baked Cod",
            unitPrice: 16.5,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0022",
            name: "Sea Bass Fillet",
            unitPrice: 21,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0023",
            name: "Fish & Chips",
            unitPrice: 14.5,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0024",
            name: "Shrimp Scampi",
            unitPrice: 17.5,
            configuration: [],
            suggestedNotes: [],
          },
          {
            id: "0025",
            name: "Lobster Tail",
            unitPrice: 28,
            configuration: [],
            suggestedNotes: [],
          },
        ],
      },
    ],
  },
  {
    id: "vegetarian",
    emoji: "🥑",
    color: "#5CAD71",
    name: "Vegetarian",
    children: [
      {
        id: "pasta-risotto",
        name: "Pasta & Risotto",
        children: [
          {
            id: "0034",
            name: "Mushroom Risotto",
            unitPrice: 14.5,
            configuration: [
              {
                id: "mushroom-type",
                name: "Mushroom Selection",
                min: 1,
                max: 2,
                options: [
                  {
                    id: "portobello",
                    name: "Portobello",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "shiitake",
                    name: "Shiitake",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                  {
                    id: "wild-mix",
                    name: "Wild Mushroom Mix",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 3,
                  },
                ],
              },
              {
                id: "cheese",
                name: "Extra Cheese",
                min: 0,
                max: 1,
                options: [
                  {
                    id: "parmesan",
                    name: "Extra Parmesan",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1.5,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Gluten free",
              "Dairy free option",
              "Extra truffle oil",
            ],
          },
          {
            id: "0037",
            name: "Wild Mushroom Pasta",
            unitPrice: 14,
            configuration: [
              {
                id: "pasta-type",
                name: "Pasta Type",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "spaghetti",
                    name: "Spaghetti",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "penne",
                    name: "Penne",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "gluten-free",
                    name: "Gluten Free Pasta",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra garlic", "Spicy", "No cream"],
          },
        ],
      },
      {
        id: "bowls-plates",
        name: "Bowls & Plates",
        children: [
          {
            id: "0035",
            name: "Buddha Bowl",
            unitPrice: 13.5,
            configuration: [
              {
                id: "base",
                name: "Choose Base",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "quinoa",
                    name: "Quinoa",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "brown-rice",
                    name: "Brown Rice",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "cauliflower-rice",
                    name: "Cauliflower Rice",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                ],
              },
              {
                id: "protein",
                name: "Protein",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "tofu",
                    name: "Grilled Tofu",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "tempeh",
                    name: "Marinated Tempeh",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra avocado", "Spicy sauce on side", "Vegan"],
          },
          {
            id: "0036",
            name: "Eggplant Parmigiana",
            unitPrice: 15,
            configuration: [
              {
                id: "side-salad",
                name: "Side Salad",
                min: 0,
                max: 1,
                options: [
                  {
                    id: "mixed-greens",
                    name: "Mixed Greens",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 3,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Extra sauce",
              "Light on cheese",
              "Gluten free option",
            ],
          },
        ],
      },
    ],
  },
  {
    id: "main-meat",
    emoji: "🥩",
    color: "#D25054",
    name: "Main Meat",
    children: [
      {
        id: "steaks",
        name: "Steaks",
        children: [
          {
            id: "0026",
            name: "Ribeye Steak",
            unitPrice: 24,
            configuration: [
              {
                id: "steak-cooking",
                name: "Cooking Preference",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "rare",
                    name: "Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "medium-rare",
                    name: "Medium Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "medium",
                    name: "Medium",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "medium-well",
                    name: "Medium Well",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "well-done",
                    name: "Well Done",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
              {
                id: "steak-sauce",
                name: "Sauce Selection",
                min: 0,
                max: 2,
                options: [
                  {
                    id: "peppercorn",
                    name: "Peppercorn Sauce",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                  {
                    id: "bearnaise",
                    name: "Béarnaise Sauce",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                  {
                    id: "mushroom",
                    name: "Mushroom Sauce",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Blue rare available",
              "Served with compound butter",
              "Let rest for 5 minutes",
            ],
          },
        ],
      },
      {
        id: "other-meat",
        name: "Other Meat",
        children: [
          {
            id: "0027",
            name: "Roast Chicken",
            unitPrice: 16.5,
            configuration: [
              {
                id: "chicken-part",
                name: "Choose your cut",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "breast",
                    name: "Breast",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "leg",
                    name: "Leg Quarter",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "whole",
                    name: "Half Chicken",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 3,
                  },
                ],
              },
            ],
            suggestedNotes: ["Crispy skin", "No herbs", "Extra gravy"],
          },
          {
            id: "0028",
            name: "Lamb Chops",
            unitPrice: 22,
            configuration: [
              {
                id: "lamb-cooking",
                name: "Cooking Preference",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "lamb-medium-rare",
                    name: "Medium Rare",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "lamb-medium",
                    name: "Medium",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                  {
                    id: "lamb-well",
                    name: "Well Done",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Mint sauce on side",
              "Extra rosemary",
              "French cut",
            ],
          },
        ],
      },
    ],
  },
  {
    id: "desserts",
    emoji: "🍰",
    color: "#8669B0",
    name: "Desserts",
    children: [
      {
        id: "hot-desserts",
        name: "Hot Desserts",
        children: [
          {
            id: "0029",
            name: "Chocolate Fondant",
            unitPrice: 8.5,
            configuration: [
              {
                id: "ice-cream",
                name: "Ice Cream Selection",
                min: 0,
                max: 1,
                options: [
                  {
                    id: "vanilla",
                    name: "Vanilla Ice Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "salted-caramel",
                    name: "Salted Caramel Ice Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Allow 15 minutes",
              "Contains nuts",
              "Served warm",
            ],
          },
        ],
      },
      {
        id: "cold-desserts",
        name: "Cold Desserts",
        children: [
          {
            id: "0030",
            name: "Crème Brûlée",
            unitPrice: 7.5,
            configuration: [
              {
                id: "flavor",
                name: "Flavor",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "vanilla",
                    name: "Classic Vanilla",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "lavender",
                    name: "Lavender",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                ],
              },
            ],
            suggestedNotes: ["Extra crispy top", "Room temperature"],
          },
          {
            id: "0033",
            name: "Ice Cream Selection",
            unitPrice: 6,
            configuration: [
              {
                id: "scoops",
                name: "Number of Scoops",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "two",
                    name: "Two Scoops",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "three",
                    name: "Three Scoops",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 2,
                  },
                ],
              },
              {
                id: "toppings",
                name: "Toppings",
                min: 0,
                max: 3,
                options: [
                  {
                    id: "chocolate-sauce",
                    name: "Chocolate Sauce",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "nuts",
                    name: "Chopped Nuts",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                  {
                    id: "whipped-cream",
                    name: "Whipped Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 0.5,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Ask for flavors",
              "Sugar cone available",
              "Dairy free options",
            ],
          },
        ],
      },
      {
        id: "cheese-desserts",
        name: "Cheese & Desserts",
        children: [
          {
            id: "0031",
            name: "Cheese Board",
            unitPrice: 12,
            configuration: [
              {
                id: "size",
                name: "Size",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "small",
                    name: "Small (3 cheeses)",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "large",
                    name: "Large (5 cheeses)",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 6,
                  },
                ],
              },
              {
                id: "extras",
                name: "Extras",
                min: 0,
                max: 2,
                options: [
                  {
                    id: "honey",
                    name: "Honey",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                  {
                    id: "nuts",
                    name: "Mixed Nuts",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1.5,
                  },
                ],
              },
            ],
            suggestedNotes: [
              "Served at room temperature",
              "Extra crackers",
              "No blue cheese",
            ],
          },
          {
            id: "0032",
            name: "Apple Tart",
            unitPrice: 7,
            configuration: [
              {
                id: "serving",
                name: "Serving",
                min: 1,
                max: 1,
                options: [
                  {
                    id: "plain",
                    name: "Plain",
                    min: 0,
                    max: 1,
                    initialQuantity: 1,
                    unitPrice: 0,
                  },
                  {
                    id: "cream",
                    name: "With Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1,
                  },
                  {
                    id: "ice-cream",
                    name: "With Ice Cream",
                    min: 0,
                    max: 1,
                    initialQuantity: 0,
                    unitPrice: 1.5,
                  },
                ],
              },
            ],
            suggestedNotes: ["Served warm", "Extra caramel sauce"],
          },
        ],
      },
    ],
  },
];
