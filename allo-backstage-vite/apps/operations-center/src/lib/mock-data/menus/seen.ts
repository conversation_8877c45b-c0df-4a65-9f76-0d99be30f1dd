import { Menu } from "../menu";
import { getSuperFolderColor } from "./colors";

export const menu: Menu = [
  {
    id: "5f21e0e25d001a28b7deb149",
    name: "Aperitif",
    emoji: "🍹",
    color: getSuperFolderColor(), // SALMON_500
    children: [
      {
        id: "5f21e0e25d001a28b7deb169",
        name: "<PERSON>",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14a",
        name: "<PERSON>g<PERSON> <PERSON><PERSON><PERSON>",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14b",
        name: "<PERSON><PERSON><PERSON><PERSON>ritz",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14c",
        name: "<PERSON><PERSON><PERSON> Fizz",
        unitPrice: 9.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14d",
        name: "<PERSON><PERSON>",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14e",
        name: "Aperol Spritz",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb14f",
        name: "Hollunderspritz",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb150",
        name: "Lillet Vive",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb151",
        name: "Lillet Hugo",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb152",
        name: "Lillet Rouge Tonic",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb174",
        name: "Campari Soda",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb175",
        name: "Campari Orange",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb176",
        name: "Gin Tonic",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "637674fb70d2c60b1ba06205",
            name: "Gin",
            min: 1,
            max: 1,
            options: [
              {
                id: "637675d570d2c60b1ba0620d",
                name: "Municher The Duke",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "63767a3d2d2e0e4ed43db6c1",
                name: "Berliner Brandstifter",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "6376764870d2c60b1ba06212",
                name: "Japanese Suntory RoKu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "6376776870d2c60b1ba06221",
                name: "London Bulldog",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "637679932d2e0e4ed43db6b9",
                name: "Colombian Ortodoxy",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 3,
              },
              {
                id: "63767be470d2c60b1ba06256",
                name: "Scotland Hendricks",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
              {
                id: "63767da22d2e0e4ed43db6da",
                name: "Schwarzwald Monkey 47",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb168",
    name: "Sparkling Wine",
    emoji: "🥂",
    color: getSuperFolderColor(), // YELLOW_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb169",
        name: "Fritz Müller Perlwein",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "624f58a446f52e0c201e3a2a",
        name: "Fritz Müller Perlwein",
        unitPrice: 22,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2080d23621f60252bc37b",
        name: "Fritz Müller Perlwein alkoholfrei",
        unitPrice: 22,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb16a",
        name: "Moscato D'Asti DOCG",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2088223621f60252bc37c",
        name: "`La Rosé N°7´ Cremant de Limoux Rosé",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb16b",
        name: "Champagne ANDRE-CLOUET Grande Reserve Bouzy Grand Cru Flaschengärung",
        unitPrice: 59,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6161d3bda2bd26034981793f",
        name: "Champagne ANDRE-CLOUET brut Rosé Flaschengärung",
        unitPrice: 69,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "60c209d323621f60252bc37d",
    name: "Rosé wine",
    emoji: "🍷",
    color: getSuperFolderColor(), // SALMON_500
    children: [
      {
        id: "60c1f6d023621f60252bc375",
        name: "Rossi QbA",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20a4323621f60252bc37f",
        name: "Rossi QbA",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20b7523621f60252bc387",
        name: "Aix Rosé",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "640fba4e1d66f75694142f1c",
    name: "New Year limited dishes",
    emoji: "🥣",
    color: getSuperFolderColor(), // SALMON_500
    children: [
      {
        id: "5f21e0e25d001a28b7deb0f1",
        name: "süße knusprige Ente mit Knochen (half)",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "62798de8017ce86795353584",
        name: "Rindersehnen",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698dad63a914771fd1dd05",
        name: "braised ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698e8eca7b281e6c8a065a",
        name: "steamed oyster (4 pcs.)",
        unitPrice: 20,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698fb863a914771fd1dd29",
        name: "braised pork belly with chestnuts",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6769904463a914771fd1dd3d",
        name: "steamed oyster (1 pcs.)",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb153",
    name: "White Wine",
    emoji: "🥂",
    color: getSuperFolderColor(), // YELLOW_700
    children: [
      {
        id: "60c1f5cf23621f60252bc372",
        name: "Krebs Riesling 2023",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb155",
        name: "Grauburgunder 2023",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb156",
        name: "Sauvignon Blanc 2023",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c1f70023621f60252bc376",
        name: "Verdicchio di Castelli Jesi Classico 2023",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb158",
        name: "Krebs Riesling 2023",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c1f63323621f60252bc373",
        name: "Grauburgunder 2023",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c1f6ca23621f60252bc374",
        name: "Sauvignon Blanc 2023",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb157",
        name: "Verdicchio di Castelli Jesi Classico 2023",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb15c",
        name: "Lugana DOC 2024",
        unitPrice: 32,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb159",
        name: "Riesling -Tonschiefer- VDP. 2023",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb15a",
        name: "Chablis AC 2023",
        unitPrice: 39,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb15b",
        name: "Saar Riesling Feinherb fruchtsüß 2023",
        unitPrice: 36,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f79fcf86253b927c653320e",
        name: "White Wine Spritzer",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f4bef13f31ec0508a0e41ca",
    name: "Lunch Menu",
    emoji: "🍲",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "5f4bef13f31ec0508a0e41cb",
        name: "Wolfsbarsch-Reisnudeltopf",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41cc",
        name: "Sichuan-Reisnudeltopf mit Rindfleisch",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "612fef5d50116b113995646a",
            name: "select type",
            min: 1,
            max: 1,
            options: [
              {
                id: "612ff02450116b113995646b",
                name: "Standard",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "612ff04350116b113995646c",
                name: "Schweinedarm dazu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 5,
              },
            ],
          },
        ],
      },
      {
        id: "6298939a55ca6670a7e3b13b",
        name: "Sichuan-Reisnudeltopf",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "612fef5d50116b113995646a",
            name: "select type",
            min: 1,
            max: 1,
            options: [
              {
                id: "612ff02450116b113995646b",
                name: "Standard",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "612ff04350116b113995646c",
                name: "Schweinedarm dazu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 5,
              },
            ],
          },
        ],
      },
      {
        id: "5f4bef13f31ec0508a0e41cd",
        name: "WanTan-Reisnudeltopf",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41ce",
        name: "Crispy Chicken",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41cf",
        name: "Korean Rice Dish",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d0",
        name: "Eggplant",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d1",
        name: "Mapo Tofu",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d2",
        name: "Curry",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "607ea6586bfec30b02cf6b36",
            name: "Select Type Curry",
            min: 1,
            max: 1,
            options: [
              {
                id: "607ea83b6bfec30b02cf6b38",
                name: "Seasonal Vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eaa516bfec30b02cf6b3a",
                name: "Chicken Breast",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eaaa26bfec30b02cf6b3b",
                name: "Beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "607eaad26bfec30b02cf6b3c",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 3,
              },
            ],
          },
        ],
      },
      {
        id: "5f4bef13f31ec0508a0e41d6",
        name: "coconut peanut",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "607ea6586bfec30b02cf6b36",
            name: "Select Type Curry",
            min: 1,
            max: 1,
            options: [
              {
                id: "607ea83b6bfec30b02cf6b38",
                name: "Seasonal Vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eaa516bfec30b02cf6b3a",
                name: "Chicken Breast",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eaaa26bfec30b02cf6b3b",
                name: "Beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "607eaad26bfec30b02cf6b3c",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 3,
              },
            ],
          },
        ],
      },
      {
        id: "5f4bef13f31ec0508a0e41de",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "607ee8686bfec30b02cf6b58",
            name: "Choose filling",
            min: 1,
            max: 1,
            options: [
              {
                id: "607ee9456bfec30b02cf6b59",
                name: "mixed plate",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eeaf46bfec30b02cf6b5a",
                name: "Asian seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eebbe6bfec30b02cf6b5b",
                name: "minced chicken",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eec956bfec30b02cf6b5c",
                name: "minced pork mushrooms",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eed636bfec30b02cf6b5d",
                name: "organic minced pork shrimp",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eedf66bfec30b02cf6b5e",
                name: "organic minced beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "60c20f3723621f60252bc39e",
    name: "SEEN´s Refrescher",
    emoji: "🍸",
    color: getSuperFolderColor(), // PURPLE_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb13c",
        name: "Kumquats – Limette",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb13d",
        name: "Ingwer – Limette",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb13e",
        name: "Passionsfrucht – Limette",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb13f",
        name: "Yuzu – Mint",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb140",
        name: "Strawberry - Basil",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb141",
        name: "Mango Lassi",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f76288df71e5e219333de58",
        name: "Strawberry Ice Bubble Milk Tea",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f76288df71e5e219333de59",
        name: "Mango Ice Olong Tea",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb0b0",
    name: "Appetizer",
    emoji: "🥣",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "5f21e0e25d001a28b7deb0b1",
        name: "Mung Beans",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b4",
        name: "Chicken Cull Meat",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b3",
        name: "Broccoli Leaves & Smoked Tofu",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b5",
        name: "Tribe Salad",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b8",
        name: "Thousend-year Eggs",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ba",
        name: "Cucumber",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0bb",
        name: "Chinese Cabbage",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0bc",
        name: "Chicken Cull Meat",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0bd",
        name: "Duck Thigh",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41de",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "607ee8686bfec30b02cf6b58",
            name: "Choose filling",
            min: 1,
            max: 1,
            options: [
              {
                id: "607ee9456bfec30b02cf6b59",
                name: "mixed plate",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eeaf46bfec30b02cf6b5a",
                name: "Asian seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eebbe6bfec30b02cf6b5b",
                name: "minced chicken",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eec956bfec30b02cf6b5c",
                name: "minced pork mushrooms",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607eed636bfec30b02cf6b5d",
                name: "organic minced pork shrimp",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "607eedf66bfec30b02cf6b5e",
                name: "organic minced beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb0c2",
    name: "Main Dishes",
    emoji: "🍲",
    color: getSuperFolderColor(), // BLUE_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb0f1",
        name: "süße knusprige Ente mit Knochen (half)",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "62798de8017ce86795353584",
        name: "Rindersehnen",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698dad63a914771fd1dd05",
        name: "braised ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698e8eca7b281e6c8a065a",
        name: "steamed oyster (4 pcs.)",
        unitPrice: 20,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "67698fb863a914771fd1dd29",
        name: "braised pork belly with chestnuts",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6769904463a914771fd1dd3d",
        name: "steamed oyster (1 pcs.)",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c9",
        name: "Filmy Beef Slices",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60c6fd935af721149ad36c65",
            name: "select meat",
            min: 1,
            max: 1,
            options: [
              {
                id: "60c6fdf75af721149ad36c66",
                name: "rinderfiletscheiben",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "60c6fe4d5af721149ad36c67",
                name: "Hauchdünne Rindfleischscheiben mit Fettanteil",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0df",
        name: "Beef",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6276c8129f90cb496e6dc850",
        name: "Maishähnchen Eintopf mit Pfefferlinger",
        unitPrice: 24.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e19494b77d9127f9e21ed4",
        name: "Kaninchen gebraten mit eingelegter Chili und Ingwer🌶️🌶️🌶️",
        unitPrice: 23.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e194f4b77d9127f9e21ede",
        name: "Rinderhack gebraten mit eingelegter Kohl, Ingwer und Chili",
        unitPrice: 18.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e195cdb77d9127f9e21ee6",
        name: "萝卜薄荷羊肉粉汤锅",
        unitPrice: 25.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "66e195d5b77d9127f9e21ee7",
        name: "Black Angus gebraten mit Steinpilze und Lauch",
        unitPrice: 21.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb101",
        name: "Beef",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb103",
        name: "Beef & Silk Tofu",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb105",
        name: "Beef",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c7",
        name: "Beef",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f4faef4fcbb5628be6bb6",
        name: "Veal cutlet",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6280a3e28c9a1e5ae4d34e07",
        name: "农家炒鲜笋l🌶️",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0cb",
        name: "Meat of Your Choice",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6045103157a5053e3396e94b",
            name: "Select Meat",
            min: 1,
            max: 1,
            options: [
              {
                id: "60451e0d57a5053e3396e966",
                name: "Pork Ribs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604520fb57a5053e3396e967",
                name: "Chicken Cull Meat",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045212257a5053e3396e968",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "604522f057a5053e3396e969",
                name: "Mixed",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 1,
              },
              {
                id: "6045233c57a5053e3396e96a",
                name: "Frog Legs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0d7",
        name: "King Prawns",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "641061af1d66f75694142ffd",
        name: "家常豆腐🌶️",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d1",
        name: "Sea Bass",
        unitPrice: 22.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f5",
        name: "Sea Bass",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "64d22decc6de96329a5426c5",
        name: "steamed Sea bass",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ef",
        name: "River Eel",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f0",
        name: "Frog Legs",
        unitPrice: 24.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fe",
        name: "Sea Bass",
        unitPrice: 22.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fb",
        name: "Sea Bass",
        unitPrice: 26.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452dbf57a5053e3396e981",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452df057a5053e3396e982",
                name: "with chili and sichuan pepper, lotus root, seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452e6557a5053e3396e983",
                name: "with fermented soybeans, lotus root, seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452e8a57a5053e3396e984",
                name: "with fermented chili, lotus root, seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "607f53d7f4fcbb5628be6bb7",
        name: "Shrimps",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f54c0f4fcbb5628be6bb8",
        name: "seafood with tofu",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d0",
        name: "Pork Ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "64d235d9c6de96329a542707",
        name: "crispy pork ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d8",
        name: "Lamb",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ec",
        name: "Chicken Cull Meat on the Bone",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f56def4fcbb5628be6bb9",
        name: "lamb rip",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e0",
        name: "Chicken Cull Meat",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d5",
        name: "Tofu",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0cf",
        name: "Tofu",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c218bd23621f60252bc3b1",
        name: "Tofu",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ed",
        name: "Clams",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452a0b57a5053e3396e974",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452a7457a5053e3396e975",
                name: "With garlic & peppers",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452aad57a5053e3396e976",
                name: "With chili & ginger",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0d9",
        name: "Prawns",
        unitPrice: 22.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb107",
        name: "Egg Plant",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f9",
        name: "Crabs",
        unitPrice: 28.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452ce457a5053e3396e97e",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452d2557a5053e3396e97f",
                name: "with spring onions, ginger",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452d8b57a5053e3396e980",
                name: "with chili, spring onions",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0c5",
        name: "Gebratenes Schweinebauch",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f6",
        name: "Pork & Yeast dumpling",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb108",
        name: "Pork Fillet",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f8",
        name: "Offals",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60958ddd58ffcc4acdbf058b",
            name: "Select type",
            min: 1,
            max: 1,
            options: [
              {
                id: "607f5b1ef4fcbb5628be6bbd",
                name: "ohne Schweinedarm",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "607f5b47f4fcbb5628be6bbe",
                name: "mit Schweinedarm",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 3,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0eb",
        name: "Crispy Pork Belly",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb106",
        name: "Pig Intestins",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6410603a35b9f00612eb60c5",
        name: "Kung Pao Prawns",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "64105fb11d66f75694142ff0",
        name: "干煸鳝鱼",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "641060d31d66f75694142ffa",
        name: "Schweinemagen Paprika Fermentierte Soja",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c3",
        name: "Chicken Giblets",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6650bf6dda48d458d3a56769",
        name: "Dried Tofu and Pork Belly Stew",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6650c256da48d458d3a567af",
        name: "stir-fried Chicken, Meretrix",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb10b",
    name: "Rice & Vegetables",
    emoji: "🥦",
    color: getSuperFolderColor(), // GREEN_600
    children: [
      {
        id: "5f21e0e25d001a28b7deb10c",
        name: "Potatoes",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb10d",
        name: "Seasonal Vegetables (Depends on Availability)",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6098ea8c2dd0a031852a05a4",
            name: "select typ",
            min: 1,
            max: 1,
            options: [
              {
                id: "6098eb342dd0a031852a05a5",
                name: "water spinach",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6098ec892dd0a031852a05a6",
                name: "Bok Choi",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6098f1182dd0a031852a05a7",
                name: "cabbage",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "61783dd77598f72b7774d01a",
                name: "菜芯",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb10e",
        name: "Cauliflower",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ca",
        name: "Eggplant",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d6",
        name: "Pole Beans",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb100",
        name: "Turkish Peppers",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6280a3e28c9a1e5ae4d34e07",
        name: "农家炒鲜笋l🌶️",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6410622b1d66f75694142ffe",
        name: "花生椰奶南瓜",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "641061af1d66f75694142ffd",
        name: "家常豆腐🌶️",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "623b2f67ba180c7f5683bc34",
        name: "Rice (1 Portion)",
        unitPrice: 1.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb10f",
    name: "Desserts",
    emoji: "🍰",
    color: getSuperFolderColor(), // YELLOW_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb110",
        name: "Steamed Coconut Dumpling",
        unitPrice: 7.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb119",
        name: "Mango Panacotta",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb112",
        name: "Sticky Rice Cake",
        unitPrice: 8.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb114",
        name: "Motchi",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452ec957a5053e3396e985",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452f0057a5053e3396e986",
                name: "Mango",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452f2257a5053e3396e987",
                name: "Matcha",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb117",
        name: "Scoop of Ice",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452f9d57a5053e3396e98b",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452fbf57a5053e3396e98c",
                name: "Matcha",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452ff257a5053e3396e98d",
                name: "Sesame",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "61672f042ecb343050ca5fdd",
                name: "Yu Zu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb116",
        name: "Popsicle",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452f4157a5053e3396e988",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452f5957a5053e3396e989",
                name: "Durian",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452f7957a5053e3396e98a",
                name: "Red Bean",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "627cf83a8c9a1e5ae4d3414b",
        name: "Sichuan Bingfen (Ice Jelly)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb11a",
    name: "Beverages",
    emoji: "🥤",
    color: getSuperFolderColor(), // PURPLE_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb11b",
        name: "sparkling water (small)",
        unitPrice: 2.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656911e57410bc298a0eb50a",
        name: "sparkling water (large)",
        unitPrice: 7.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569104dd01597027ec14db5",
        name: "still water (small)",
        unitPrice: 2.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656912f97410bc298a0eb50c",
        name: "still water (large)",
        unitPrice: 7.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f492af4cf74b337f4833386",
        name: "Aloe Vera",
        unitPrice: 4.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "614f084f85c929227dcacb30",
        name: "Japanischer Oolong Eistee (0.40l)",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656917cfd01597027ec14de7",
        name: "Lychee Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb11f",
        name: "Apple Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569160ad01597027ec14ddf",
        name: "Mango Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569163dd01597027ec14de2",
        name: "Passionfruit Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569166ad01597027ec14de3",
        name: "Black Currant Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656916e9d01597027ec14de4",
        name: "Rhubarb Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569177ed01597027ec14de6",
        name: "Orange Spritzer",
        unitPrice: 4.4,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569271f09d741493f8d4265",
        name: "Orange Juice",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "653aaafe7a6af71900826108",
        name: "Apple Juice",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656924c87410bc298a0eb559",
        name: "Passionfruit Nectar",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656925e976148b4aaba9d8bf",
        name: "Black Currant Nectar",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656926d809d741493f8d4264",
        name: "Rhubarb Nectar",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569249cd01597027ec14df7",
        name: "Mango Juice",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569293f76148b4aaba9d8c2",
        name: "Lychee Juice",
        unitPrice: 4.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12b",
        name: "Coca Cola (small)",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "65691ee27410bc298a0eb54a",
        name: "Coca Cola (large)",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "65691f86d01597027ec14dee",
        name: "Cola Zero (small)",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6569188b7410bc298a0eb53e",
        name: "Cola Zero (large)",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12f",
        name: "Mezzo Mix (small)",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "65692017d01597027ec14def",
        name: "Mezzo Mix (large)",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "65692214d01597027ec14df6",
        name: "Bionade Ginger-Orange",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656922377410bc298a0eb558",
        name: "Bionade Herbs",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb134",
        name: "Bionade Elderflower",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb131",
        name: "Ginger Ale",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656920aad01597027ec14df0",
        name: "Bitter Lemon",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb142",
    name: "Beer",
    emoji: "🍺",
    color: getSuperFolderColor(), // YELLOW_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb143",
        name: "Pils",
        unitPrice: 4.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb144",
        name: "Helles Oberbräu",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb145",
        name: "Helles Alkoholfrei",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb146",
        name: "Weißbier",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb147",
        name: "Weißbier Alkoholfrei",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb148",
        name: "Radler",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f79fb7a6253b927c6533200",
        name: "Russ",
        unitPrice: 4.5,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "60c1ef4623621f60252bc371",
    name: "Red wine",
    emoji: "🍷",
    color: getSuperFolderColor(), // SALMON_500
    children: [
      {
        id: "5f79fcf86253b927c653320f",
        name: "Red Wine Spritzer",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20ce923621f60252bc393",
        name: "Merlot QbA 2019",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20d3523621f60252bc395",
        name: "Primitivo Salento IGP 2019",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb154",
        name: "Merlot QbA 2019",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20d7623621f60252bc398",
        name: "Primitivo Salento IGP 2019",
        unitPrice: 24,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20dac23621f60252bc39a",
        name: "La Buena Vid Rioja DOCa 2017",
        unitPrice: 42,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c20db023621f60252bc39b",
        name: "Cotes du Rhone AC 2018",
        unitPrice: 38,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb166",
        name: "Chateau LA CROIX 2013",
        unitPrice: 69,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb16e",
    name: "Longdrinks",
    emoji: "🍸",
    color: getSuperFolderColor(), // SALMON_500
    children: [
      {
        id: "5f21e0e25d001a28b7deb170",
        name: "Moscow Mule",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb171",
        name: "Munich Mule",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb172",
        name: "Cuba Libre",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb173",
        name: "Japanese Oldman",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb174",
        name: "Campari Soda",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb175",
        name: "Campari Orange",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb176",
        name: "Gin Tonic",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "637674fb70d2c60b1ba06205",
            name: "Gin",
            min: 1,
            max: 1,
            options: [
              {
                id: "637675d570d2c60b1ba0620d",
                name: "Municher The Duke",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "63767a3d2d2e0e4ed43db6c1",
                name: "Berliner Brandstifter",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "6376764870d2c60b1ba06212",
                name: "Japanese Suntory RoKu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "6376776870d2c60b1ba06221",
                name: "London Bulldog",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "637679932d2e0e4ed43db6b9",
                name: "Colombian Ortodoxy",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 3,
              },
              {
                id: "63767be470d2c60b1ba06256",
                name: "Scotland Hendricks",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
              {
                id: "63767da22d2e0e4ed43db6da",
                name: "Schwarzwald Monkey 47",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb177",
    name: "Spirits",
    emoji: "🥃",
    color: getSuperFolderColor(), // GREY_800
    children: [
      {
        id: "5f21e0e25d001a28b7deb15f",
        name: "Wuliangye 52% vol. (Flasche)",
        unitPrice: 380,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb160",
        name: "FEN CHIEW 30Jahre (Flasche)",
        unitPrice: 280,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb162",
        name: "FEN CHIEW 4 cl",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb165",
        name: "贵州飞天茅台 53%",
        unitPrice: 680,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb178",
        name: "Partisan Vodka 40% / 伏特加",
        unitPrice: 5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb179",
        name: "Hennessy Cognac / 轩尼诗",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17a",
        name: "Pircher Williamsbirne / 梨子酒",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17b",
        name: "Max&Daniels Ingwer Liqueur / 姜味酒",
        unitPrice: 6.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17c",
        name: "Akashi White Oak Japanese Blended Whisky 40% / 日本威士忌",
        unitPrice: 9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17d",
        name: "Togouchi Premium Japanese Blended Whisky 40% / 日本威士忌",
        unitPrice: 10,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17e",
        name: "Nikka from the Barrel Double Matured Japanese Blended Whisky 51.4% / 日本威士忌",
        unitPrice: 11,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb17f",
        name: "The Balvenie Single Malt Scotch Whisky DoubleWood, 12 Jahre 40° / 苏格兰威士忌",
        unitPrice: 10.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb180",
        name: "The Classic Laddie Bruichladdich Scottish Barley, 50° / 苏格兰威士忌",
        unitPrice: 10.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb181",
        name: "BIG PEAT Blended Malt Whisky 46°, aus Schottland von Douglas Laing / 苏格兰威士忌",
        unitPrice: 14,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb182",
        name: "Sake / 日本清酒",
        unitPrice: 8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "614770a885c929227dcab9bd",
        name: "FEN CHIEW 10Jahre (Flasche)",
        unitPrice: 160,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "5f21e0e25d001a28b7deb183",
    name: "Hot Beverages",
    emoji: "☕",
    color: getSuperFolderColor(), // BLUE_700
    children: [
      {
        id: "5f21e0e25d001a28b7deb184",
        name: "Coffee",
        unitPrice: 2.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb185",
        name: "Espresso",
        unitPrice: 2.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb186",
        name: "Espresso Doppio",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb187",
        name: "Espresso Macciato",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb188",
        name: "Cappuccino",
        unitPrice: 3.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb189",
        name: "Latte Macchiato",
        unitPrice: 3.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb18a",
        name: "Ginger Minzer Honey Tea",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb18b",
        name: "Oolong Tea (0.30l)",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb18c",
        name: "Jasmine Tea (0.30l)",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb18d",
        name: "Longjing Tea (0.30l)",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb18e",
        name: "Puer Tea (0.30l)",
        unitPrice: 5.2,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "604bd472536f792212621ae4",
    name: "our specials",
    emoji: "🍲",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "5f21e0e25d001a28b7deb0c9",
        name: "Filmy Beef Slices",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60c6fd935af721149ad36c65",
            name: "select meat",
            min: 1,
            max: 1,
            options: [
              {
                id: "60c6fdf75af721149ad36c66",
                name: "rinderfiletscheiben",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "60c6fe4d5af721149ad36c67",
                name: "Hauchdünne Rindfleischscheiben mit Fettanteil",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb101",
        name: "Beef",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb103",
        name: "Beef & Silk Tofu",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb105",
        name: "Beef",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f4faef4fcbb5628be6bb6",
        name: "Veal cutlet",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0cb",
        name: "Meat of Your Choice",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6045103157a5053e3396e94b",
            name: "Select Meat",
            min: 1,
            max: 1,
            options: [
              {
                id: "60451e0d57a5053e3396e966",
                name: "Pork Ribs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604520fb57a5053e3396e967",
                name: "Chicken Cull Meat",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045212257a5053e3396e968",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 2,
              },
              {
                id: "604522f057a5053e3396e969",
                name: "Mixed",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045233c57a5053e3396e96a",
                name: "Frog Legs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 4,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0d7",
        name: "King Prawns",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d1",
        name: "Sea Bass",
        unitPrice: 22.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f5",
        name: "Sea Bass",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ef",
        name: "River Eel",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fe",
        name: "Sea Bass",
        unitPrice: 22.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fb",
        name: "Sea Bass",
        unitPrice: 26.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452dbf57a5053e3396e981",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452df057a5053e3396e982",
                name: "with chili and sichuan pepper, lotus root, seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452e6557a5053e3396e983",
                name: "with fermented soybeans, lotus root, seasonal vegetables",
                min: 1,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452e8a57a5053e3396e984",
                name: "with fermented chili, lotus root, seasonal vegetables",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "607f53d7f4fcbb5628be6bb7",
        name: "Shrimps",
        unitPrice: 23.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f54c0f4fcbb5628be6bb8",
        name: "seafood with tofu",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d0",
        name: "Pork Ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "64d235d9c6de96329a542707",
        name: "crispy pork ribs",
        unitPrice: 17.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d8",
        name: "Lamb",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ec",
        name: "Chicken Cull Meat on the Bone",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f56def4fcbb5628be6bb9",
        name: "lamb rip",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "612ff40f50116b113995646f",
    name: "Others",
    emoji: "🍽️",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "5f21e0e25d001a28b7deb18f",
        name: "Hot Water (0.30l)",
        unitPrice: 1.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f58f7f4fcbb5628be6bba",
        name: "Fried Rice",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61be36072a0e9a3e61a69a2e",
        name: "荷叶饼1个",
        unitPrice: 1,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61be36822a0e9a3e61a69a32",
        name: "Prosecco(0.10L)",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "61e1c53b9d960814133d8fb4",
        name: "开瓶费用",
        unitPrice: 15,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "621ca6872eddf2535adaa74b",
        name: "Business Menü",
        unitPrice: 30,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "63c68b18b6dd995b5eaf1c2c",
        name: "Gutschein",
        unitPrice: 50,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656a66c63e6af2552ae2f4a7",
        name: "stilles Wasser Mitarbeiter",
        unitPrice: 0,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "656a67333e6af2552ae2f4a8",
        name: "Sprudelwasser Mitarbeiter",
        unitPrice: 0,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5fe3545cbabd9942d4b4253b",
        name: "双人套餐",
        unitPrice: 125,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5fe3556bbabd9942d4b4253c",
        name: "家庭套餐",
        unitPrice: 211,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "63cc346c0373301918542ca4",
        name: "双人套餐堂食",
        unitPrice: 141,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "63cc34950373301918542ca6",
        name: "家庭套餐堂食",
        unitPrice: 239,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "6412e5d8ba62583188da2c1a",
    name: "Cutlery",
    emoji: "🥢",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "6412e614ba62583188da2c1b",
        name: "Do you need cutlery?",
        unitPrice: 0,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
  {
    id: "60c2039323621f60252bc377",
    name: "Achievierung",
    emoji: "🏆",
    color: getSuperFolderColor(), // GREY_600
    children: [
      {
        id: "62798e678238484f3aad4391",
        name: "Filmy Wagyu Beef Slices",
        unitPrice: 29.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "629bb532cf2a6a437c9c3a9c",
        name: "Sichuan-Reisnudeltopf mit Hautdünne Wagyu Rindfleischscheiben (5A 200 g)",
        unitPrice: 49.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5fe33d3fbabd9942d4b42539",
        name: "Gedämpfter Jakobsmuschel（Stück）",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "62b61694de042e735979106b",
        name: "Primitivo Salento IGP 2019",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c218f423621f60252bc3b2",
        name: "Green Asparagus",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c8",
        name: "Beef Goulash",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2191c23621f60252bc3b3",
        name: "Kokosmilch Topping with Tofu",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c2192323621f60252bc3b4",
        name: "red curry Topping with Tofu",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "623b311089994150af3eb446",
        name: "Tofu",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "65691d7ad01597027ec14deb",
        name: "Mezzo Mix (large)",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d2",
        name: "Seeteufel",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d4",
        name: "Minced Meat",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e2",
        name: "Topping of Your Choice",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "604510c657a5053e3396e94d",
            name: "Select Type",
            min: 1,
            max: 1,
            options: [
              {
                id: "604528c057a5053e3396e970",
                name: "Tofu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045291657a5053e3396e971",
                name: "Chicken Breast",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045295257a5053e3396e972",
                name: "Beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604529b557a5053e3396e973",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0e6",
        name: "Topping of Your Choice",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "604510c657a5053e3396e94d",
            name: "Select Type",
            min: 1,
            max: 1,
            options: [
              {
                id: "604528c057a5053e3396e970",
                name: "Tofu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045291657a5053e3396e971",
                name: "Chicken Breast",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045295257a5053e3396e972",
                name: "Beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604529b557a5053e3396e973",
                name: "Shrimps",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0ea",
        name: "Crispy Chicken",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb10a",
        name: "Egg Plant",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0d3",
        name: "Green Asparagus",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e1",
        name: "Schweinemagen Knoblauch Rettich",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "620c09432eddf2535ada7e02",
        name: "Gebratenes Schweinebauch",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "641060d31d66f75694142ffa",
        name: "Schweinemagen Paprika Fermentierte Soja",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0db",
        name: "Meat of Your Choice",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "6045108657a5053e3396e94c",
            name: "Select Meat",
            min: 1,
            max: 1,
            options: [
              {
                id: "6045254a57a5053e3396e96b",
                name: "All 3 kinds of meat",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045258857a5053e3396e96d",
                name: "Pork Belly",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604525d157a5053e3396e96e",
                name: "Beef",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045260b57a5053e3396e96f",
                name: "Pork Ribs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0c4",
        name: "Pig kidney",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb104",
        name: "Tribe",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ff",
        name: "Cattle Rips",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b2",
        name: "Chicken Cull Meat",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b6",
        name: "Chicken Cull Meat",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b7",
        name: "Morels",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0b9",
        name: "Chicken Thigh & Duck Gizzard",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0be",
        name: "Soup of The Day: Duck",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0bf",
        name: "Soup of The Day",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "60450fe157a5053e3396e949",
            name: "Select Soup",
            min: 1,
            max: 1,
            options: [
              {
                id: "6045122157a5053e3396e94f",
                name: "Pork Ribs",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045131e57a5053e3396e951",
                name: "Chicken",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "653aa6777a6af719008260e4",
                name: "Maracuja",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb0c0",
        name: "Soup of The Day: Pork Ribs",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c1",
        name: "Soup of The Day: Chicken",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0c6",
        name: "Beef",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0cc",
        name: "Meat of Your Choice: Pork Ribs",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0cd",
        name: "Meat of Your Choice: Chicken Cull Meat",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ce",
        name: "Meat of Your Choice: Frog Legs",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0da",
        name: "Beef",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0dc",
        name: "Meat of Your Choice: Beef",
        unitPrice: 15.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0dd",
        name: "Meat of Your Choice: Pork Ribs",
        unitPrice: 14.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0de",
        name: "Meat of Your Choice: all 3 kinds of meat",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e3",
        name: "Topping of Your Choice: Tofu",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e4",
        name: "Topping of Your Choice: Shrimps",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e5",
        name: "Topping of Your Choice: Beef",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e7",
        name: "Topping of Your Choice: Tofu",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e8",
        name: "Topping of Your Choice: Shrimps",
        unitPrice: 13.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0e9",
        name: "Topping of Your Choice: Beef",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0ee",
        name: "Clams",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f2",
        name: "Crayfish",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f3",
        name: "Crayfish",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f4",
        name: "Sea Bass",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0f7",
        name: "Pig Intestins",
        unitPrice: 21.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fa",
        name: "Crabs",
        unitPrice: 26.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fc",
        name: "Sea Bass",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb0fd",
        name: "Sea Bass",
        unitPrice: 19.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb102",
        name: "Lamb Goulash",
        unitPrice: 18.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb109",
        name: "Silk Tofu in Batter",
        unitPrice: 11.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb111",
        name: "Ginger Chocolate Mousse",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb113",
        name: "Motchi",
        unitPrice: 2.5,
        suggestedNotes: [],
        configuration: [
          {
            id: "60452ec957a5053e3396e985",
            name: "Select Taste",
            min: 1,
            max: 1,
            options: [
              {
                id: "60452f0057a5053e3396e986",
                name: "Mango",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60452f2257a5053e3396e987",
                name: "Matcha",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb115",
        name: "Popsicle (Durian)",
        unitPrice: 3,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb118",
        name: "Scoop of Ice (Sesame)",
        unitPrice: 2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb11c",
        name: "Still Water",
        unitPrice: 6.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb11d",
        name: "Sparkling Water",
        unitPrice: 2.6,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb11e",
        name: "Sparkling Water",
        unitPrice: 6.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb120",
        name: "Mango Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb121",
        name: "Apple",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [
          {
            id: "604531fe57a5053e3396e994",
            name: "Select Type",
            min: 1,
            max: 1,
            options: [
              {
                id: "6045323a57a5053e3396e995",
                name: "Juice(0,2l)",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045327357a5053e3396e996",
                name: "Spritzer(0,4l)",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "60ce38e304eac002c2615e04",
                name: "Saft(0,4l)",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb122",
        name: "Apple Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb123",
        name: "Orange Juice",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb124",
        name: "Orange Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb125",
        name: "Currant Juice",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb126",
        name: "Currant Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb127",
        name: "Rhubarb Juice",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb128",
        name: "Rhubarb Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb129",
        name: "Litchi Juice",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12a",
        name: "Litchi Spritzer",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12c",
        name: "Coca Cola",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12d",
        name: "Coca Cola Zero",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb12e",
        name: "Coca Cola Zero",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb130",
        name: "Mezzo Mix",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb132",
        name: "Goldberg Ginger Ale",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb133",
        name: "Goldberg Tonic Water",
        unitPrice: 2.8,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb135",
        name: "Bionade Ginger-Orange",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb136",
        name: "Bionade Holunder",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb137",
        name: "Chari Tea",
        unitPrice: 3.8,
        suggestedNotes: [],
        configuration: [
          {
            id: "6045354a57a5053e3396e9a5",
            name: "Select Flavour",
            min: 1,
            max: 1,
            options: [
              {
                id: "6045357f57a5053e3396e9a6",
                name: "Black",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604535a957a5053e3396e9a7",
                name: "Green",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "604535bd57a5053e3396e9a8",
                name: "Red",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb13a",
        name: "Ramune",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [
          {
            id: "604535e057a5053e3396e9a9",
            name: "Select Flavour",
            min: 1,
            max: 1,
            options: [
              {
                id: "6045360757a5053e3396e9aa",
                name: "Matcha",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "6045361d57a5053e3396e9ab",
                name: "Litchi",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "614f0ab46ca5864685f02b72",
                name: "Yuzu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f21e0e25d001a28b7deb13b",
        name: "Ramune (Japanese Limonade Litchi)",
        unitPrice: 3.2,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb15d",
        name: "F",
        unitPrice: 28,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb15e",
        name: "F",
        unitPrice: 30,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb161",
        name: "F",
        unitPrice: 26,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb163",
        name: "F",
        unitPrice: 5.5,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb164",
        name: "F",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb167",
        name: "ST. DANIEL. Riserva 2016",
        unitPrice: 35,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb16c",
        name: "r",
        unitPrice: 79,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb16d",
        name: "r",
        unitPrice: 95,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f21e0e25d001a28b7deb16f",
        name: "Pimms Cup",
        unitPrice: 9.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f492af4cf74b337f4833387",
        name: "Aloe Vera",
        unitPrice: 3.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d3",
        name: "Chicken Breast",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d4",
        name: "Beef",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d5",
        name: "Shrimp",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d7",
        name: "Chicken Breast",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d8",
        name: "Shrimp",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41d9",
        name: "Beef",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41da",
        name: "Large Rice Noodle Soup",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41db",
        name: "Pangasius Filet in Thai Curry",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41dc",
        name: "Noodle Salad",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [
          {
            id: "612fef5d50116b113995646a",
            name: "select type",
            min: 1,
            max: 1,
            options: [
              {
                id: "612ff02450116b113995646b",
                name: "Standard",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
              {
                id: "612ff04350116b113995646c",
                name: "Schweinedarm dazu",
                min: 0,
                max: 1,
                initialQuantity: 0,
                unitPrice: 0,
              },
            ],
          },
        ],
      },
      {
        id: "5f4bef13f31ec0508a0e41dd",
        name: "Noodle Salad",
        unitPrice: 8.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41df",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e0",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e1",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e2",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e3",
        name: "Handmade Dumplings (10 Pieces)",
        unitPrice: 7.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e4",
        name: "Chinese Cucumber Salad",
        unitPrice: 4.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e5",
        name: "Chicken Legs in Rice Wine",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e6",
        name: "Silk Tofu",
        unitPrice: 5.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f4bef13f31ec0508a0e41e7",
        name: "Silk Tofu",
        unitPrice: 6.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "5f75c0fef71e5e219333dd05",
        name: "Meat of Your Choice: Mixed",
        unitPrice: 16.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "607f59b2f4fcbb5628be6bbb",
        name: "Fried Noodles",
        unitPrice: 12.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "60c6fd225af721149ad36c64",
        name: "lamb rip",
        unitPrice: 20.9,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "6161ec4ea2bd2603498179e6",
        name: "Verpackung",
        unitPrice: 1,
        suggestedNotes: [],
        configuration: [],
      },
      {
        id: "623b306bba180c7f5683bc38",
        name: "荷叶饼1个",
        unitPrice: 1,
        suggestedNotes: [],
        configuration: [],
      },
    ],
  },
];
