import { spaces, Space } from "@/lib/mock-data/spaces";
import { Table } from "@/lib/mock-data/tables";
import { state } from "@/lib/mock-data/server-state";
import { Order } from "@/lib/mock-data/order";
import {
  getMenu,
  getProducts,
  <PERSON>u,
  MenuId,
  Product,
} from "@/lib/mock-data/menu";
import { OrderItem } from "@/lib/mock-data/order";
import { useAuthStore } from "../auth/store";
import { MenuItem } from "@/types/menu";
import { versionedFloors, versionedMenus } from "./menus/dev";

const DEFAULT_POSSIBLE_ERROR_PROBABILITY = 0;

// const SLEEP_MIN = import.meta.env.DEV ? 8 : 32;
// const SLEEP_MAX = import.meta.env.DEV ? 64 : 1024;

// const SLEEP_MIN = 0;
// const SLEEP_MAX = 0;

// simulate a response delay in the API
// const sleep = (fixed: number | null = null, min: number = SLEEP_MIN, max: number = SLEEP_MAX) => {
//   return new Promise<void>((resolve) => {
//     const delay = Math.random() * (max - min) + min;
//     setTimeout(() => resolve(), fixed || delay);
//   });
// };

interface Error {
  status?: number;
}

const error = (code: number, message: string) => {
  const error = new Error(message) as Error;
  error.status = code;

  throw error;
};

// simulate a possible error in the API
const possibleError = ({
  probability = DEFAULT_POSSIBLE_ERROR_PROBABILITY,
  message = "Simulated error",
}: {
  probability?: number;
  message?: string;
}) => {
  return true;

  if (Math.random() < probability) {
    throw error(500, message);
  }
};

const getHeaders = (token: string | null) => {
  return {
    Accept: "application/json,text/plain, */*",
    "Content-Type": "application/json",
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    "content-language": "en",
  };
};

export const fetchSpaces = async (): Promise<Space[]> => {
  // await sleep(0);
  const data = spaces;
  possibleError({ message: "Failed to fetch dine-in spaces" });

  if (!data) {
    throw error(500, "Failed to fetch dine-in spaces");
  }

  if (data.length === 0) {
    throw error(404, "No dine-in spaces found");
  }

  return data;
};

export const fetchSpace = async (spaceId: string): Promise<Space> => {
  // await sleep(0);
  possibleError({ message: `Failed to fetch space '${spaceId}'` });

  const data = spaces.find(
    (space) => space.id.toLowerCase() === spaceId.toLowerCase()
  );

  if (!data) {
    throw error(404, `Space '${spaceId}' not found`);
  }

  return data;
};

export const fetchVersionedFloors = async () => {
  if (import.meta.env.DEV) {
    return versionedFloors;
  }

  const { token, restaurantId } = useAuthStore.getState().getAuth();

  const response = await fetch(
    `
    https://app-dev.allo.restaurant/restaurant-api/restaurants/${restaurantId}/versioned-floors
  `,
    {
      headers: {
        ...getHeaders(token),
      },
    }
  );

  const data = await response.json();

  return data;
};

export const fetchTable = async (
  tableId: string,
  spaces: Space[]
): Promise<Table> => {
  possibleError({ message: `Failed to fetch table '${tableId}'` });

  const data = spaces
    .flat()
    .find(
      (table: Space | Table) => table.id.toLowerCase() === tableId.toLowerCase()
    ) as unknown as Table;

  if (!data) {
    throw error(404, `Table '${tableId}' not found`);
  }

  return data;
};

export const fetchOrders = async (): Promise<Order[]> => {
  // await sleep(0);
  possibleError({ message: "Failed to fetch dine-in orders" });

  return Object.values(state.orders);
};

export const fetchOrder = async (orderId: string): Promise<Order> => {
  // await sleep(0);
  possibleError({ message: `Order '${orderId}' not found` });

  const order = state.orders[orderId];

  if (!order) {
    throw error(404, `Order '${orderId}' not found`);
  }

  return order;
};

export const fetchTableOrder = async (
  tableId: string
): Promise<Order | null> => {
  // await sleep(0);
  possibleError({ message: `Failed to fetch table order '${tableId}'` });

  const order = Object.values(state.orders).find(
    (order) => order.tableId.toLowerCase() === tableId.toLowerCase()
  );

  return order || null;
};

export const fetchMenu = async (): Promise<Menu> => {
  // await sleep(0);
  possibleError({ message: "Failed to fetch menu" });

  const key = localStorage.getItem("menu") || "basic";
  const menu = getMenu(key as MenuId);

  if (!menu) {
    throw new Error("Menu not found");
  }

  return menu;
};

export const fetchVersionedMenus = async (): Promise<{
  menuVersion: number;
  items: MenuItem[];
}> => {
  if (import.meta.env.DEV) {
    return versionedMenus;
  }

  const { token, restaurantId } = useAuthStore.getState().getAuth();

  const response = await fetch(
    `
    https://app-dev.allo.restaurant/restaurant-api/restaurants/${restaurantId}/versioned-menus
  `,
    {
      headers: {
        ...getHeaders(token),
      },
    }
  );

  const data = await response.json();

  return data;
};

export const fetchProducts = async (query: string): Promise<Product[]> => {
  // await sleep(0);
  possibleError({ message: "Failed to fetch products" });

  if (!query || query.length < 1) {
    return [];
  }

  const key = localStorage.getItem("menu") || "basic";
  const allProducts = getProducts(key as MenuId);

  return allProducts.filter((product) => {
    return (
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.id.toLowerCase().includes(query.toLowerCase())
    );
  });
};

export const postCreateOrder = async (table: Table): Promise<Order> => {
  // await sleep(0);
  possibleError({
    message: `Failed to create order for table "${table.id}"`,
  });

  const order = state.createOrder(table);
  return order;
};

export const postAssignOrder = async (
  orderId: string,
  assignee: string
): Promise<Order> => {
  // await sleep(0);
  possibleError({ message: "Failed to assign order" });

  return state.assignOrder(orderId, assignee);
};

export const postMoveOrder = async (
  orderId: string,
  destinationTable: Table
): Promise<void> => {
  // await sleep(0);
  possibleError({ message: "Failed to move order" });

  return state.moveOrder(orderId, destinationTable);
};

export const postCancelOrder = async (orderId: string): Promise<void> => {
  // await sleep(0);
  possibleError({ message: "Failed to cancel order" });

  return state.cancelOrder(orderId);
};

export const postOrderItems = async (
  orderId: string,
  items: Omit<OrderItem, "createdAt" | "isCancelled">[]
): Promise<Order> => {
  // await sleep(0);
  possibleError({ message: "Failed to order items" });

  return state.orderItems(orderId, items);
};

export const postCancelItem = async (
  orderId: string,
  itemId: string,
  quantity: number,
  reasons: string[]
): Promise<void> => {
  // await sleep(0);
  possibleError({ message: "Failed to cancel item" });

  return state.cancelItem(orderId, itemId, quantity, reasons);
};

export const fetchUserPreferencies = async () => {
  const response = await fetch(
    `
    https://app-dev.allo.restaurant/user-service/users/_me/account/preferences
  `,
    {
      credentials: "include",
      headers: {
        ...getHeaders(null),
      },
    }
  );

  const data = await response.json();

  return data;
};
