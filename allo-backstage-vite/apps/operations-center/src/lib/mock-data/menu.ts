import { menu as basicMenu } from "./menus/basic";
import { menu as choisMenu } from "./menus/chois";
import { menu as seenMenu } from "./menus/seen";
import { menu as haiSeafoodMenu } from "./menus/hai-seafood";

export type ProductOption = {
  id: string;
  name: string;
  min: number;
  max: number | null;
  initialQuantity: number;
  unitPrice: number;
};

export type ProductConfiguration = {
  id: string;
  name: string;
  min: number;
  max: number | null;
  options: ProductOption[];
};

export type Product = {
  id: string;
  name: string;
  unitPrice: number;
  configuration: ProductConfiguration[];
  suggestedNotes: string[];
  isSoldOut?: boolean;
};

export type MenuFolder = {
  id: string;
  name: string;
  children: MenuFolder[] | Product[];
};

export type MenuSuperFolder = MenuFolder & {
  emoji: string;
  color: `#${string}`;
};

export type Menu = MenuSuperFolder[];

export const MENUS = {
  basic: basicMenu,
  chois: choisMenu,
  seen: seenMenu,
  haiSeafood: haiSeafoodMenu,
};

export type MenuId = keyof typeof MENUS;

export const getMenu = (id: MenuId = "basic") => {
  return MENUS[id];
};

export const getProductById = (
  id: string,
  menuId: MenuId = "basic"
): Product => {
  const findProduct = (items: MenuFolder[] | Product[]): Product | null => {
    for (const item of items) {
      if ("unitPrice" in item && item.id === id) {
        return item;
      }
      if ("children" in item) {
        const found = findProduct(item.children);
        if (found) return found;
      }
    }
    return null;
  };

  const product = findProduct(getMenu(menuId));
  if (!product) {
    throw new Error(`Product with id ${id} not found`);
  }
  return product;
};

export const getProducts = (menuId: MenuId = "basic"): Product[] => {
  const menu = getMenu(menuId);

  const extractProducts = (items: MenuFolder[] | Product[]): Product[] => {
    const products: Product[] = [];

    for (const item of items) {
      if ("unitPrice" in item) {
        products.push(item as Product);
      } else if ("children" in item) {
        products.push(...extractProducts(item.children));
      }
    }

    return products;
  };

  return extractProducts(menu);
};
