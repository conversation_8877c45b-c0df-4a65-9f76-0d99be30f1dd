import { useState, useEffect, useCallback } from "react";
import { debounce } from "@/lib/utils/async";

export function useDebouncedValue<T>(value: T, delay = 500): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const updateDebouncedValue = useCallback(
    debounce((value: T) => setDebouncedValue(value), delay),
    [delay]
  );

  useEffect(() => {
    updateDebouncedValue(value);
  }, [value, updateDebouncedValue]);

  return debouncedValue;
}
