import { MouseEvent, TouchEvent } from "react";
import { useCallback, useRef } from "react";

type UseLongPressOptions = {
  onClick?: (e: MouseEvent | TouchEvent) => void;
  onLongPress: () => void;
  threshold?: number;
};

type LongPressResult = {
  onMouseDown: () => void;
  onMouseUp: (e: MouseEvent) => void;
  onMouseLeave: () => void;
  onTouchStart: () => void;
  onTouchEnd: (e: TouchEvent) => void;
};

export const useLongPress = ({
  onClick,
  onLongPress,
  threshold = 400,
}: UseLongPressOptions): LongPressResult => {
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const isLongPress = useRef(false);

  const startPressTimer = useCallback(() => {
    isLongPress.current = false;
    timerRef.current = setTimeout(() => {
      isLongPress.current = true;
      onLongPress();
    }, threshold);
  }, [onLongPress, threshold]);

  const handleOnClick = useCallback(
    (e: MouseEvent | TouchEvent) => {
      if (isLongPress.current) {
        return;
      }

      onClick?.(e);
    },
    [onClick]
  );

  const clearTimer = useCallback(() => {
    if (timerRef.current) clearTimeout(timerRef.current);
  }, []);

  const onMouseDown = useCallback(() => {
    startPressTimer();
  }, [startPressTimer]);

  const onMouseUp = useCallback(
    (e: MouseEvent) => {
      clearTimer();

      handleOnClick(e);
    },
    [clearTimer, handleOnClick]
  );

  const onMouseLeave = useCallback(() => {
    clearTimer();
  }, [clearTimer]);

  const onTouchStart = useCallback(() => {
    startPressTimer();
  }, [startPressTimer]);

  const onTouchEnd = useCallback(
    (e: TouchEvent) => {
      clearTimer();
      handleOnClick(e);
    },
    [clearTimer, handleOnClick]
  );

  return {
    onMouseDown,
    onMouseUp,
    onMouseLeave,
    onTouchStart,
    onTouchEnd,
  };
};
