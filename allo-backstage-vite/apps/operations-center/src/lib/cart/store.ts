import { create } from "zustand";
import { CartItem } from "./types";
import { mergeCartItems } from "./utils";

interface CartStore {
  carts: Record<string, CartItem[]>;
}

interface CartActions {
  createCart: (orderId: string) => CartItem[];
  getCart(orderId: string, throwOnMissing?: true): CartItem[];
  getCart(orderId: string, throwOnMissing: false): CartItem[] | undefined;
  clearCart: (orderId: string) => void;
  deleteCart: (orderId: string) => void;
  mergeCarts: (fromOrderId: string, toOrderId: string) => void;
  addItem: (orderId: string, item: CartItem) => void;
  updateItem: (
    orderId: string,
    itemId: string,
    item: Partial<Omit<CartItem, "id">>
  ) => void;
  removeItem: (orderId: string, itemId: string) => void;
  moveItems: (
    fromOrderId: string,
    toOrderId: string,
    items: CartItem[]
  ) => void;
}

export const useCartStore = create<CartStore & CartActions>((set, get) => ({
  carts: {},
  createCart: (orderId) => {
    set((state) => {
      return {
        carts: { ...state.carts, [orderId]: [] },
      };
    });

    return get().carts[orderId];
  },
  getCart: (orderId: string, throwOnMissing = true) => {
    const cart = get().carts[orderId];

    if (throwOnMissing && !cart) {
      throw new Error(`Cart for order ${orderId} not found`);
    }

    return cart;
  },
  clearCart: (orderId) => {
    set((state) => {
      return {
        carts: { ...state.carts, [orderId]: [] },
      };
    });
  },
  deleteCart: (orderId) => {
    set((state) => {
      const transformedCarts = { ...state.carts };
      delete transformedCarts[orderId];

      return {
        carts: transformedCarts,
      };
    });
  },
  mergeCarts: (fromOrderId, toOrderId) => {
    set((state) => {
      const fromCart = state.getCart(fromOrderId);
      const toCart = state.getCart(toOrderId);

      const transformedCarts = {
        ...state.carts,
        [toOrderId]: mergeCartItems([...toCart, ...fromCart]),
      };

      delete transformedCarts[fromOrderId];

      return {
        carts: transformedCarts,
      };
    });
  },
  addItem: (orderId, item) => {
    set((state) => {
      const cart = state.getCart(orderId);

      return {
        carts: {
          ...state.carts,
          [orderId]: mergeCartItems([...cart, item]),
        },
      };
    });
  },
  updateItem: (orderId, itemId, item) => {
    set((state) => {
      const cart = state.getCart(orderId);

      const updatedCart = cart.map((existingItem) => {
        return existingItem.id === itemId
          ? { ...existingItem, ...item }
          : existingItem;
      });

      return {
        carts: {
          ...state.carts,
          [orderId]: updatedCart,
        },
      };
    });
  },
  removeItem: (orderId, itemId) => {
    set((state) => {
      const cart = state.getCart(orderId);

      return {
        carts: {
          ...state.carts,
          [orderId]: cart.filter((item) => item.id !== itemId),
        },
      };
    });
  },
  moveItems: (originOrderId, destinationOrderId, itemsToMove) => {
    set((state) => {
      const originCart = state.getCart(originOrderId);
      const destinationCart =
        state.getCart(destinationOrderId, false) ||
        state.createCart(destinationOrderId);

      return {
        carts: {
          ...state.carts,
          [destinationOrderId]: mergeCartItems([
            ...destinationCart!,
            ...itemsToMove,
          ]),
          [originOrderId]: originCart
            .map((item) => {
              const movingItem = itemsToMove.find(({ id }) => id === item.id);

              return movingItem
                ? { ...item, quantity: item.quantity - movingItem.quantity }
                : item;
            })
            .filter((item) => item.quantity > 0),
        },
      };
    });
  },
}));

export const useCart = (orderId: string) => {
  return useCartStore((state) => state.getCart(orderId));
};

export const useCartActions = (orderId: string) => {
  const clearCart = useCartStore((state) => state.clearCart);
  const addItem = useCartStore((state) => state.addItem);
  const updateItem = useCartStore((state) => state.updateItem);
  const removeItem = useCartStore((state) => state.removeItem);

  return {
    addItem: (item: CartItem) => addItem(orderId, item),
    updateItem: (itemId: string, item: Partial<Omit<CartItem, "id">>) =>
      updateItem(orderId, itemId, item),
    removeItem: (itemId: string) => removeItem(orderId, itemId),
    clearCart: () => clearCart(orderId),
  };
};
