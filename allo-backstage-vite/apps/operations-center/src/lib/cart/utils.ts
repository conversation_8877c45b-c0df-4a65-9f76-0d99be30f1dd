import { <PERSON>u, MenuFolder, Product, ProductOption } from "@/lib/mock-data/menu";
import { CartItem, CartItemOption } from "./types";
import { uuid } from "@/lib/utils/string";

export const productOptionToCartItemOption = (
  optionGroupId: string,
  productOption: ProductOption,
  quantity: number
): CartItemOption => {
  return {
    groupId: optionGroupId,
    id: productOption.id,
    name: productOption.name,
    min: productOption.min,
    max: productOption.max,
    unitPrice: productOption.unitPrice,
    initialQuantity: productOption.initialQuantity,
    quantity,
  };
};

export const productToCartItem = (product: Product): CartItem => {
  return {
    id: uuid(),
    productId: product.id,
    name: product.name,
    quantity: 1,
    baseUnitPrice: product.unitPrice,
    // TODO: add extras
    options: product.options.reduce<CartItemOption[]>((options, group) => {
      const selectedOptions = group.items.filter(
        (option) => option.initialQuantity > 0
      );

      return [
        ...options,
        ...selectedOptions.map((option) =>
          productOptionToCartItemOption(
            group.id,
            option,
            option.initialQuantity
          )
        ),
      ];
    }, []),
    notes: [],
  };
};

const generateUniqueCartItemKey = (item: CartItem): string => {
  const base = item.productId;
  const options = item.options.map((o) => `${o.groupId}-${o.id}`).join("-");
  const notes = item.notes.join("-");

  return `${base}-${options}-${notes}`;
};

export const mergeCartItems = (items: CartItem[]): CartItem[] => {
  const mergedItems = new Map<string, CartItem>();

  for (const item of items) {
    const key = generateUniqueCartItemKey(item);
    const existingItem = mergedItems.get(key);

    if (existingItem) {
      mergedItems.set(key, {
        ...existingItem,
        id: item.id, // overwrite the old item id with the new one
        quantity: existingItem.quantity + item.quantity,
      });
    } else {
      mergedItems.set(key, item);
    }
  }

  return Array.from(mergedItems.values());
};

export const calculateCartItemOptionTotalPrice = (option: {
  initialQuantity: number;
  unitPrice: number;
  quantity: number;
}): number => {
  return (
    Math.max(0, option.quantity - (option.initialQuantity || 0)) *
    option.unitPrice
  );
};

export const calculateCartItemTotalPrice = (cartItem: CartItem): number => {
  const optionsPrice = cartItem.options.reduce(
    (total, option) => total + calculateCartItemOptionTotalPrice(option),
    0
  );

  return (cartItem.baseUnitPrice + optionsPrice) * cartItem.quantity;
};

export const calculateCartTotalPrice = (items: CartItem[]): number => {
  return items
    .filter((item) => !("isCancelled" in item && item.isCancelled))
    .reduce((total, item) => total + calculateCartItemTotalPrice(item), 0);
};

export const calculateProductQuantityInCart = (
  productId: string,
  items: CartItem[]
): number => {
  return items.reduce((acc, item) => {
    if (item.productId !== productId) return acc;
    if ("isCancelled" in item && item.isCancelled) return acc;
    return acc + item.quantity;
  }, 0);
};

export const calculateCartTotalItemQuantity = (items: CartItem[]): number => {
  return items.reduce((acc, item) => acc + item.quantity, 0);
};

export const getCartItemProduct = (cartItem: CartItem, menu: Menu): Product => {
  const product = findProductInMenu(menu, cartItem.productId);

  if (!product) {
    throw new Error(`Product with id ${cartItem.productId} not found`);
  }

  return product;
};

const findProductInMenu = (
  items: MenuFolder[] | Product[],
  productId: string
): Product | null => {
  for (const item of items) {
    if ("unitPrice" in item && item.id === productId) {
      return item;
    }
    if ("items" in item) {
      const found = findProductInMenu(item.items, productId);
      if (found) return found;
    }
  }
  return null;
};
