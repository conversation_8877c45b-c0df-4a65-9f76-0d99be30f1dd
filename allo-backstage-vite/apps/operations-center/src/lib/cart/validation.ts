import { CartItem } from "./types";
import { Product, ProductConfiguration } from "@/lib/mock-data/menu";

export type CartItemGroupError = {
  code:
    | "TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR"
    | "TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR";
  message: string;
  groupId: string;
};

// returns only one error at a time
export const getCartItemGroupError = (
  cartItem: CartItem,
  group: ProductConfiguration
): CartItemGroupError | undefined => {
  const selectedGroupOptions = cartItem.options.filter((option) =>
    group.items.some((groupOption) => groupOption.id === option.id)
  );
  const selectedGroupOptionsCount = selectedGroupOptions.reduce(
    (total, option) => total + option.quantity,
    0
  );

  // Check if the group is required and the user has selected the minimum amount of options
  if (group.min > 0) {
    if (selectedGroupOptionsCount < group.min) {
      return {
        code: "TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",
        message: `Select at least ${group.min}`,
        groupId: group.id,
      };
    }
  }

  // Check if the user has selected more options than allowed
  if (group.max && selectedGroupOptionsCount > group.max) {
    return {
      code: "TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",
      message: `Select up to ${group.max}`,
      groupId: group.id,
    };
  }
};

// get the first group error from a cart item
export const getCartItemFirstGroupError = (
  cartItem: CartItem,
  product: Product
) => {
  // TODO: add extras
  for (const group of product.options) {
    const error = getCartItemGroupError(cartItem, group);

    if (error) return error;
  }
};
