import {
  InferUserConfigFieldValue,
  InferUserConfigFieldGroupValue,
  UserConfigField,
  UserConfigFieldGroup,
} from "./types";

export interface UserConfigError {
  code:
    | "TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR"
    | "TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR";
  message: string;
  fieldId: string;
}

export const getUserConfigFieldError = <T extends UserConfigField>(
  field: T,
  value: InferUserConfigFieldValue<T>
): UserConfigError | undefined => {
  switch (field.type) {
    case "select":
      if (!value) {
        return {
          code: "TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",
          message: "Select at least one option",
          fieldId: field.id,
        };
      }

      break;
    case "multi-select":
      if (field.min && value.length < field.min) {
        return {
          code: "TOO_FEW_OPTIONS_SELECTED_IN_GROUP_ERROR",
          message: `Select at least ${field.min} options`,
          fieldId: field.id,
        };
      }

      if (field.max && value.length > field.max) {
        return {
          code: "TOO_MANY_OPTIONS_SELECTED_IN_GROUP_ERROR",
          message: `Select up to ${field.max} options`,
          fieldId: field.id,
        };
      }

      break;
  }

  return;
};

export const getUserConfigGroupFirstError = <T extends UserConfigFieldGroup>(
  group: T,
  value: InferUserConfigFieldGroupValue<T>
): UserConfigError | undefined => {
  for (const field of group) {
    const fieldId = field.id as keyof typeof value;
    const error = getUserConfigFieldError(field, value[fieldId]);

    if (error) return error;
  }

  return;
};
