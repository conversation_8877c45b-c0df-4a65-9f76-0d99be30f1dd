export type UserConfigFieldMetadata = {
  id: string;
  title: string;
  description?: string;
};

export type UserConfigFieldOption = {
  id: string;
  title: string;
  description?: string;
  default?: boolean;
  readOnly?: boolean;
};

export type UserConfigSelectField = UserConfigFieldMetadata & {
  type: "select";
  options: UserConfigFieldOption[];
};

// select field
export type InferUserConfigSelectFieldValue<T extends UserConfigSelectField> =
  T["options"][number]["id"];

export type UserConfigMultiSelectField = UserConfigFieldMetadata & {
  type: "multi-select";
  min?: number;
  max?: number;
  options: UserConfigFieldOption[];
};

// multi-select
export type InferUserConfigMultiSelectFieldValue<
  T extends UserConfigMultiSelectField,
> = T["options"][number]["id"][];

export type UserConfigField =
  | UserConfigSelectField
  | UserConfigMultiSelectField;

export type UserConfigFieldGroup = UserConfigField[];

export type InferUserConfigFieldValue<T extends UserConfigField> =
  T extends UserConfigSelectField
    ? InferUserConfigSelectFieldValue<T>
    : T extends UserConfigMultiSelectField
      ? InferUserConfigMultiSelectFieldValue<T>
      : never;

export type InferUserConfigFieldGroupValue<T extends UserConfigFieldGroup> = {
  [K in T[number]["id"]]: InferUserConfigFieldValue<
    Extract<T[number], { id: K }>
  >;
};
