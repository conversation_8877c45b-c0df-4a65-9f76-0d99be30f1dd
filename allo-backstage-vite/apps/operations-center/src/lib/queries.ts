import { queryOptions } from "@tanstack/react-query";
import {
  fetchMenu,
  fetchOrder,
  fetchOrders,
  fetchSpaces,
  fetchVersionedFloors,
  fetchVersionedMenus,
} from "@/lib/mock-data/api";

export const getSpacesOptions = () => {
  return queryOptions({
    queryKey: ["spaces"],
    queryFn: () => fetchSpaces(),
    staleTime: 1000 * 60 * 60 * 24, // 1 day
  });
};

export const getVersionedFloors = () => {
  return queryOptions({
    queryKey: ["versioned-floors"],
    queryFn: () => fetchVersionedFloors(),
    staleTime: 0,
  });
};

// FUTURE: we should be fetching the specific space/floor
export const getTablesOptions = (spaceId: string) => {
  return {
    ...getVersionedFloors(),
    // @ts-expect-error - FIXME: missing types
    select: (data) => data.items.find((space) => space.id === spaceId)?.tables,
  };
};

export const getOrdersOptions = () => {
  return queryOptions({
    queryKey: ["orders"],
    queryFn: () => fetchOrders(),
    // staleTime: 1000 * 60 * 1, // 1 minute
  });
};

export const getOrderOptions = (orderId: string) => {
  return queryOptions({
    queryKey: ["orders", orderId],
    queryFn: () => fetchOrder(orderId),
    staleTime: 1000 * 60 * 1, // 1 minute
  });
};

export const getMenuOptions = () => {
  return queryOptions({
    queryKey: ["menu"],
    throwOnError: true,
    queryFn: () => fetchMenu(),
    staleTime: 1000 * 60 * 60 * 24, // 1 day
  });
};

export const getVersionedMenus = () => {
  return queryOptions({
    queryKey: ["versioned-menus"],
    throwOnError: true,
    queryFn: () => fetchVersionedMenus(),
    staleTime: 60 * 1000,
  });
};
