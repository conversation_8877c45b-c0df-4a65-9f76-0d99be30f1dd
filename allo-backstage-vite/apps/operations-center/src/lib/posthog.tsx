import { PostHogProvider } from "posthog-js/react";
import { ReactNode } from "react";

const POSTHOG_KEY = "phc_9dPYTVbWPYq8okSMKiBD7BBA6gx0HappOjdTF3Gxp58";
const POSTHOG_HOST = "https://eu.i.posthog.com";

export const PHProvider = ({ children }: { children: ReactNode }) => {
  if (import.meta.env.DEV) {
    return children;
  }

  return (
    <PostHogProvider
      apiKey={POSTHOG_KEY}
      options={{
        api_host: POSTHOG_HOST,
      }}
    >
      {children}
    </PostHogProvider>
  );
};
