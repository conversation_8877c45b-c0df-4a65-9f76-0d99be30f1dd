import { RouterProvider, createRouter } from "@tanstack/react-router";
import { routeTree } from "./routeTree.gen";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { PHProvider } from "@/lib/posthog";

import { useEffect } from "react";
import { useAuth, useAuthStore } from "./lib/auth/store";

import "./globals.css";
import "@fontsource-variable/inter";
import "@fontsource-variable/bricolage-grotesque";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      throwOnError: true,
      retry: false,
    },
  },
});

// Set up a Router instance
const router = createRouter({
  context: { queryClient },
  routeTree,
  basepath: "/operations",
});

// Register things for typesafety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

function App() {
  const auth = useAuth();

  window.addEventListener("message", async (event) => {
    const { token, restaurantId } = event.data || {};

    if (token) {
      useAuthStore.getState().setAuth({ token, restaurantId });
    }
  });

  console.log("hello dine-in v2", location.origin, document.cookie);

  useEffect(() => {
    setTimeout(
      () => [window.parent.postMessage({ type: "request-token" }, "*")],
      0
    );
  }, []);

  if (!import.meta.env.DEV && !auth.restaurantId) {
    return null;
  }

  return (
    <PHProvider>
      <QueryClientProvider client={queryClient}>
        <RouterProvider router={router} />
      </QueryClientProvider>
    </PHProvider>
  );
}

export default App;
