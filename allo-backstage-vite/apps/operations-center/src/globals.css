@import "@allo/ui/tailwind";

@source "../node_modules/@allo/ui/dist";
@source "../../../node_modules/@allo/ui/dist";

body {
  background-color: var(--color-background-low);
}

@property --angle {
  syntax: "<angle>";
  inherits: false;
  initial-value: 0deg;
}

@theme {
  --animate-pulse-opacity: pulse-opacity 0.33s linear infinite alternate;
  --animate-pulse-scale: pulse-scale 0.33s linear infinite alternate;
  --animate-spin-conic-gradient: spin-conic-gradient 1s linear infinite;

  @keyframes pulse-opacity {
    from {
      opacity: 0.36;
    }
    to {
      opacity: 0.72;
    }
  }

  @keyframes pulse-scale {
    from {
      transform: scale(1);
    }
    to {
      transform: scale(0.925);
    }
  }

  @keyframes spin-conic-gradient {
    from {
      --angle: 0deg;

      background-image: conic-gradient(
        from var(--angle),
        transparent 0%,
        var(--color) 50%,
        transparent 100%
      );
    }
    to {
      --angle: 360deg;

      background-image: conic-gradient(
        from var(--angle),
        transparent 0%,
        var(--color) 50%,
        transparent 100%
      );
    }
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* TEMP: override primary button colors for dark theme */
[data-theme="dark"] .chunky:not([class*="--chunky-border-color"]) {
  --chunky-bg-color: #2b2a26;
  --chunky-shadow-bg-color: #23221f;
  --chunky-border-color: #3c3b39;
  --chunky-shadow-border-color: #3c3b39;
}

[data-theme="dark"] input:not([class*="--chunky-border-color"]) {
  background-color: #2b2a26;

  &:not(:focus) {
    border-color: #3c3b39;
  }
}
