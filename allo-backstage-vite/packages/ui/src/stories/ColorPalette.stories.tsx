import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { ColorPalette } from "./ColorPalette";
import { colorValues, getAllColors, getColorsByCategory } from "./colors";

const meta: Meta<typeof ColorPalette> = {
  title: "Design System/Color Palette",
  component: ColorPalette,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
This component showcases the default Color Palette with interactive swatches. 
You can switch between different themes using the theme selector in the toolbar.

## Features

- **Interactive Color Swatches**: Click any color to copy its hex value to clipboard
- **Theme Support**: Switch between Light, Dark, and Custom themes
- **Responsive Grid**: Adapts to different screen sizes
- **Complete Palette**: Shows all major Tailwind color families

## Usage

The color palette is organized by color families (Gray, Blue, Green, Red, Yellow, Purple) 
with shades from 50 (lightest) to 900 (darkest).

## Themes

- **Light**: Default light theme with standard colors
- **Dark**: Dark theme with inverted background
- **Custom**: Purple-themed custom variant

Click on any color swatch to copy the hex value to your clipboard for use in your designs.
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    theme: {
      control: "select",
      options: ["light", "dark", "custom"],
      description: "Theme variant for the color palette",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// export const Default: Story = {
//   args: {
//     theme: "light",
//   },
// };

// export const LightTheme: Story = {
//   args: {
//     theme: "light",
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "The default light theme showing all Tailwind colors with a clean white background.",
//       },
//     },
//   },
// };

// export const DarkTheme: Story = {
//   args: {
//     theme: "dark",
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Dark theme variant with a dark background, perfect for dark mode interfaces.",
//       },
//     },
//   },
// };

// export const CustomTheme: Story = {
//   args: {
//     theme: "custom",
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Custom purple-themed variant demonstrating how to create custom theme variations.",
//       },
//     },
//   },
// };

// Interactive story that responds to global theme changes
export const Interactive: Story = {
  render: (args, { globals }) => {
    const theme = globals.theme || args.theme || "light";
    return <ColorPalette {...args} theme={theme} />;
  },
  args: {
    theme: "light",
  },
  parameters: {
    docs: {
      description: {
        story:
          "This story automatically responds to the global theme selector in the toolbar. Change the theme using the paintbrush icon in the toolbar to see the palette adapt.",
      },
    },
  },
};

// Story demonstrating the extracted color values object
export const ColorValuesDemo: Story = {
  render: () => {
    const allColors = getAllColors();
    const categorizedColors = getColorsByCategory();

    return (
      <div className="space-y-8 p-6">
        <div>
          <h2 className="mb-4 text-2xl font-bold">
            Extracted Color Values Object
          </h2>
          <p className="mb-4 text-gray-600">
            All colors are now available as a typed object with slugified keys:
          </p>
          <pre className="overflow-x-auto rounded-lg bg-gray-100 p-4 text-sm">
            <code>{JSON.stringify(colorValues, null, 2)}</code>
          </pre>
        </div>

        <div>
          <h3 className="mb-4 text-xl font-semibold">Usage Examples</h3>
          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">Direct access:</h4>
              <pre className="rounded bg-gray-100 p-3 text-sm">
                <code>{`import { colorValues } from './colors';
const primaryText = colorValues['primary-text']; // "${colorValues["primary-text"]}"`}</code>
              </pre>
            </div>

            <div>
              <h4 className="mb-2 font-medium">All colors count:</h4>
              <p className="text-gray-700">
                Total colors available: <strong>{allColors.length}</strong>
              </p>
            </div>

            <div>
              <h4 className="mb-2 font-medium">Colors by category:</h4>
              <ul className="space-y-1 text-gray-700">
                <li>
                  Text colors: <strong>{categorizedColors.text.length}</strong>
                </li>
                <li>
                  Background colors:{" "}
                  <strong>{categorizedColors.background.length}</strong>
                </li>
                <li>
                  Surface colors:{" "}
                  <strong>{categorizedColors.surface.length}</strong>
                </li>
                <li>
                  Outline colors:{" "}
                  <strong>{categorizedColors.outline.length}</strong>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  },
  parameters: {
    docs: {
      description: {
        story:
          "This story demonstrates the extracted color values object and helper functions. All colors are now available with slugified keys for easy programmatic access.",
      },
    },
  },
};
