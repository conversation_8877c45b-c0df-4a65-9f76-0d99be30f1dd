import React from "react";
import {
  typographyStyles,
  getTypographyStylesBySize,
  type TypographyStyle,
} from "./typography-styles";

interface TypographySwatchProps {
  style: TypographyStyle;
  sampleText?: string;
}

const TypographySwatch: React.FC<TypographySwatchProps> = ({
  style,
  sampleText = "The quick brown fox jumps over the lazy dog",
}) => {
  return (
    <div className="space-y-4 rounded-lg border border-gray-200 p-6">
      {/* Sample Text */}
      <div className={`text-primary ${style.tailwindClasses}`}>
        {sampleText}
      </div>

      {/* Style Information */}
      <div className="space-y-2 border-t border-gray-100 pt-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-semibold text-gray-900">{style.name}</h4>
          <code className="rounded bg-blue-50 px-2 py-1 text-xs text-blue-700">
            {style.className}
          </code>
        </div>

        <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
          <div>
            <span className="font-medium">Size:</span> {style.fontSize}
          </div>
          <div>
            <span className="font-medium">Weight:</span> {style.fontWeight}
          </div>
          <div>
            <span className="font-medium">Line Height:</span> {style.lineHeight}
          </div>
          <div>
            <span className="font-medium">Letter Spacing:</span>{" "}
            {style.letterSpacing}
          </div>
        </div>

        {(style.uppercase || style.tabularNums) && (
          <div className="flex gap-2 pt-2">
            {style.uppercase && (
              <span className="rounded bg-purple-50 px-2 py-1 text-xs text-purple-700">
                UPPERCASE
              </span>
            )}
            {style.tabularNums && (
              <span className="rounded bg-green-50 px-2 py-1 text-xs text-green-700">
                TABULAR NUMS
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

interface TypographySectionProps {
  size: string;
  styles: TypographyStyle[];
  sampleText?: string;
}

const TypographySection: React.FC<TypographySectionProps> = ({
  size,
  styles,
  sampleText,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="border-b border-gray-200 pb-2 text-xl font-semibold text-gray-900">
        {size} Styles
      </h3>
      <div className="grid gap-6">
        {styles.map((style) => (
          <TypographySwatch
            key={style.name}
            style={style}
            sampleText={sampleText}
          />
        ))}
      </div>
    </div>
  );
};

export interface TypographyProps {
  sampleText?: string;
  showNumbers?: boolean;
}

export const Typography: React.FC<TypographyProps> = ({
  sampleText = "The quick brown fox jumps over the lazy dog",
  showNumbers = false,
}) => {
  const stylesBySize = getTypographyStylesBySize();
  const displayText = showNumbers ? "0123456789" : sampleText;

  return (
    <div className="mx-auto max-w-7xl space-y-8 p-6">
      {/* Header */}
      <div className="space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Typography System</h1>
        <div className="space-y-2">
          <p className="text-gray-600">
            Our typography system uses <strong>Bricolage Grotesque</strong> as
            the primary font family. All text styles are designed to provide
            consistent hierarchy and readability across the application.
          </p>
          <div className="flex gap-4 text-sm text-gray-500">
            <span>Font Family: Bricolage Grotesque Variable</span>
            <span>•</span>
            <span>Total Styles: {typographyStyles.length}</span>
            <span>•</span>
            <span>Size Variants: {stylesBySize.length}</span>
          </div>
        </div>
      </div>

      {/* Font Information */}
      <div className="space-y-4 rounded-lg bg-gray-50 p-6">
        <h2 className="text-lg font-semibold text-gray-900">
          Font Information
        </h2>
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <h4 className="mb-2 font-medium text-gray-900">Font Family</h4>
            <p className="mb-4 text-sm text-gray-600">
              Bricolage Grotesque Variable
            </p>

            <h4 className="mb-2 font-medium text-gray-900">
              Available Weights
            </h4>
            <div className="space-y-1 text-sm text-gray-600">
              <div>400 - Normal</div>
              <div>500 - Medium</div>
            </div>
          </div>
        </div>
      </div>

      {/* Typography Styles */}
      <div className="space-y-12">
        {stylesBySize.map(({ size, styles }) => (
          <TypographySection
            key={size}
            size={size}
            styles={styles}
            sampleText={displayText}
          />
        ))}
      </div>
    </div>
  );
};
