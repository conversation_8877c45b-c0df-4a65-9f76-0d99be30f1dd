import React from "react";
import { colorValuesByTheme } from "./colors";

interface ColorSwatchProps {
  name: string;
  value: string;
  className: string;
}

const ColorSwatch: React.FC<ColorSwatchProps> = ({
  name,
  value,
  className,
}) => {
  const copyToClipboard = () => {
    navigator.clipboard.writeText(value);
  };

  return (
    <div
      className="group cursor-pointer rounded-lg border border-gray-200 p-3 transition-all hover:shadow-md"
      onClick={copyToClipboard}
      title={`Click to copy ${value}`}
    >
      <div
        className={`mb-2 h-16 w-full ${className} rounded-md`}
        style={{ backgroundColor: value }}
      />
      <div className="text-sm font-medium text-gray-900">{name}</div>
      <div className="font-mono text-xs text-gray-500">{value}</div>
      <div className="mt-1 font-mono text-xs text-blue-600">{className}</div>
    </div>
  );
};

interface ColorGroupProps {
  title: string;
  colors: Array<{
    name: string;
    value: string;
    className: string;
  }>;
}

const ColorGroup: React.FC<ColorGroupProps> = ({ title, colors }) => {
  return (
    <div className="mb-8">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">{title}</h3>
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6">
        {colors.map((color) => (
          <ColorSwatch
            key={color.name}
            name={color.name}
            value={color.value}
            className={color.className}
          />
        ))}
      </div>
    </div>
  );
};

export interface ColorPaletteProps {
  theme?: "light" | "dark" | "custom";
}

export const ColorPalette: React.FC<ColorPaletteProps> = ({
  theme = "light",
}) => {
  const currentPallete = colorValuesByTheme[theme];

  const colorGroups = [
    {
      title: "Text",
      colors: [
        {
          name: "Primary Text",
          value: currentPallete["primary-text"],
          className: "text-primary",
        },
        {
          name: "Secondary Text",
          value: currentPallete["secondary-text"],
          className: "text-secondary",
        },
        {
          name: "Disabled Text",
          value: currentPallete["disabled-text"],
          className: "text-disabled",
        },
        {
          name: "High Contrast Text",
          value: currentPallete["high-contrast-text"],
          className: "text-high-contrast",
        },
        {
          name: "Alert Text",
          value: currentPallete["alert-text"],
          className: "text-alert",
        },
        {
          name: "Positive Text",
          value: currentPallete["positive-text"],
          className: "text-positive",
        },
      ],
    },
    {
      title: "Backgrounds & Surfaces",
      colors: [
        {
          name: "Primary Background",
          value: currentPallete["background"],
          className: "bg-background",
        },
        {
          name: "Section Background",
          value: currentPallete["section"],
          className: "bg-section",
        },
        {
          name: "Card Background",
          value: currentPallete["card"],
          className: "bg-card",
        },
        {
          name: "Floating Background",
          value: currentPallete["floating"],
          className: "bg-floating",
        },
        {
          name: "Branded Surface",
          value: currentPallete["branded-surface"],
          className: "bg-branded-surface",
        },
        {
          name: "Branded Light Surface",
          value: currentPallete["branded-light-surface"],
          className: "bg-branded-light-surface",
        },
        {
          name: "Positive Surface",
          value: currentPallete["positive-surface"],
          className: "bg-positive-surface",
        },
        {
          name: "Primary Surface",
          value: currentPallete["primary-surface"],
          className: "bg-primary-surface",
        },
        {
          name: "Secondary Surface",
          value: currentPallete["secondary-surface"],
          className: "bg-secondary-surface",
        },
        {
          name: "Tertiary Surface",
          value: currentPallete["tertiary-surface"],
          className: "bg-tertiary-surface",
        },
        {
          name: "Light Transparent Surface",
          value: currentPallete["light-transparent-surface"],
          className: "bg-light-transparent-surface",
        },
        {
          name: "Dark Transparent Surface",
          value: currentPallete["dark-transparent-surface"],
          className: "bg-dark-transparent-surface",
        },
      ],
    },
    {
      title: "Outlines & Dividers",
      colors: [
        {
          name: "Background Divider",
          value: currentPallete["background-divider"],
          className: "border-background-divider",
        },
        {
          name: "Focus Outline",
          value: currentPallete["focus-outline"],
          className: "border-focus-outline",
        },
        {
          name: "Card Outline",
          value: currentPallete["card-outline"],
          className: "border-card-outline",
        },
        {
          name: "Branded Outline",
          value: currentPallete["branded-outline"],
          className: "border-branded-outline",
        },
        {
          name: "Branded Light Outline",
          value: currentPallete["branded-light-outline"],
          className: "border-branded-light-outline",
        },
        {
          name: "Positive Outline",
          value: currentPallete["positive-outline"],
          className: "border-positive-outline",
        },
      ],
    },
  ];

  return (
    <div className="mx-auto max-w-7xl p-6">
      <div className="mb-8">
        <h1 className="mb-2 text-3xl font-bold text-gray-900">Color Palette</h1>
        <p className="text-gray-600">
          Click on any color swatch to copy its hex value to clipboard. Current
          theme: <span className="font-semibold">{theme}</span>
        </p>
      </div>

      {colorGroups.map((group) => (
        <ColorGroup
          key={group.title}
          title={group.title}
          colors={group.colors}
        />
      ))}
    </div>
  );
};
