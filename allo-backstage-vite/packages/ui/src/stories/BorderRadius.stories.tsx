import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { BorderRadius } from "./BorderRadiusComponent";

const meta: Meta<typeof BorderRadius> = {
  title: "Design System/Border Radius",
  component: BorderRadius,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
Our border radius system provides a consistent set of corner rounding values that create visual hierarchy and maintain design consistency across all interface elements.

## Border Radius Scale

The system includes 10 distinct radius values:

### Minimal Rounding (0-4px)
- **None (0px)**
- **XS (2px)**
- **S (4px)**

### Standard Rounding (6-12px)
- **M (6px)**
- **L (8px)**
- **XL (12px)**

### Large Rounding (16-32px)
- **2XL (16px)**
- **3XL (24px)**
- **4XL (32px)**

### Special Cases
- **Full (100%)**: Fully rounded for circles, pills, and avatars

### CSS Implementation
Use the provided utility classes for consistent application:
\`\`\`css
.rounded-none   /* 0px */
.rounded-xs     /* 2px */
.rounded-s      /* 4px */
.rounded-m      /* 6px */
.rounded-l      /* 8px */
.rounded-xl     /* 12px */
.rounded-2xl    /* 16px */
.rounded-3xl    /* 24px */
.rounded-4xl    /* 32px */
.rounded-full   /* 100% */
\`\`\`
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    showComparison: {
      control: "boolean",
      description: "Show size comparison section",
    },
    showCategories: {
      control: "boolean",
      description: "Show categorized border radius groups",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const AllStyles: Story = {
  args: {
    showComparison: true,
    showCategories: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete overview of all border radius styles in the design system with visual examples and usage guidelines.",
      },
    },
  },
};

// export const ComparisonOnly: Story = {
//   args: {
//     showComparison: true,
//     showCategories: false,
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Side-by-side comparison of all border radius values to see the visual differences and relationships.",
//       },
//     },
//   },
// };

// export const CategoriesOnly: Story = {
//   args: {
//     showComparison: false,
//     showCategories: true,
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Border radius styles organized by categories (Minimal, Standard, Large, Special) with detailed information.",
//       },
//     },
//   },
// };

// export const MinimalView: Story = {
//   args: {
//     showComparison: false,
//     showCategories: false,
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Minimal view showing just the border radius overview and usage guidelines.",
//       },
//     },
//   },
// };

export const QuickReference: Story = {
  render: () => (
    <div className="space-y-6 p-6">
      <h1 className="mb-6 text-2xl font-bold">Border Radius Quick Reference</h1>

      {/* Visual Reference Table */}
      <div className="bg-section rounded-l p-6">
        <h3 className="text-primary mb-4 text-lg font-semibold">
          Visual Reference
        </h3>
        <div className="mb-6 grid grid-cols-5 gap-4 md:grid-cols-10">
          {[
            { name: "None", value: "0", class: "rounded-none" },
            { name: "XS", value: "2px", class: "rounded-xs" },
            { name: "S", value: "4px", class: "rounded-s" },
            { name: "M", value: "6px", class: "rounded-m" },
            { name: "L", value: "8px", class: "rounded-l" },
            { name: "XL", value: "12px", class: "rounded-xl" },
            { name: "2XL", value: "16px", class: "rounded-2xl" },
            { name: "3XL", value: "24px", class: "rounded-3xl" },
            { name: "4XL", value: "32px", class: "rounded-4xl" },
            { name: "Full", value: "100%", class: "rounded-full" },
          ].map((item) => (
            <div key={item.name} className="text-center">
              <div
                className={`bg-branded-surface mx-auto mb-2 h-12 w-12 ${item.class}`}
              ></div>
              <div className="text-primary text-xs font-medium">
                {item.name}
              </div>
              <div className="text-secondary text-xs">{item.value}</div>
            </div>
          ))}
        </div>

        {/* CSS Classes Table */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-background-divider border-b">
                <th className="text-primary py-2 text-left font-medium">
                  Name
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  Value
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  Pixels
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  CSS Class
                </th>
              </tr>
            </thead>
            <tbody>
              {[
                {
                  name: "None",
                  value: "0",
                  pixels: "0px",
                  class: ".rounded-none",
                },
                {
                  name: "XS",
                  value: "0.125rem",
                  pixels: "2px",
                  class: ".rounded-xs",
                },
                {
                  name: "S",
                  value: "0.25rem",
                  pixels: "4px",
                  class: ".rounded-s",
                },
                {
                  name: "M",
                  value: "0.375rem",
                  pixels: "6px",
                  class: ".rounded-m",
                },
                {
                  name: "L",
                  value: "0.5rem",
                  pixels: "8px",
                  class: ".rounded-l",
                },
                {
                  name: "XL",
                  value: "0.75rem",
                  pixels: "12px",
                  class: ".rounded-xl",
                },
                {
                  name: "2XL",
                  value: "1rem",
                  pixels: "16px",
                  class: ".rounded-2xl",
                },
                {
                  name: "3XL",
                  value: "1.5rem",
                  pixels: "24px",
                  class: ".rounded-3xl",
                },
                {
                  name: "4XL",
                  value: "2rem",
                  pixels: "32px",
                  class: ".rounded-4xl",
                },
                {
                  name: "Full",
                  value: "100%",
                  pixels: "100%",
                  class: ".rounded-full",
                },
              ].map((row, index) => (
                <tr key={row.name} className={index % 2 === 0 ? "bg-card" : ""}>
                  <td className="text-primary py-2 font-medium">{row.name}</td>
                  <td className="text-secondary py-2 font-mono text-xs">
                    {row.value}
                  </td>
                  <td className="text-secondary py-2">{row.pixels}</td>
                  <td className="text-primary bg-primary-surface rounded px-2 py-2 font-mono text-xs">
                    {row.class}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Quick reference table showing all border radius values, their CSS classes, and recommended use cases.",
      },
    },
  },
};
