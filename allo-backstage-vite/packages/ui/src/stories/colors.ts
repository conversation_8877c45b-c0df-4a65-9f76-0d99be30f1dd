export const colorValues = {
  "primary-text": "#151413",
  "secondary-text": "#66625E",
  "disabled-text": "#B8B4B1",
  "high-contrast-text": "#FFFFFF",
  "alert-text": "#AA4D37",
  "positive-text": "#1D6E53",

  background: "#F6F4F2",
  section: "#ECE9E7",
  card: "#F6F4F2",
  floating: "#1F1E1C",

  "branded-surface": "#FF7452",
  "branded-light-surface": "#FFC5B7",
  "positive-surface": "#32BD8D",
  "primary-surface": "#ECE9E7",
  "secondary-surface": "#E1DFDC",
  "tertiary-surface": "#D7D4D1",
  "light-transparent-surface": "#FFFFFF",
  "dark-transparent-surface": "#000000",

  "background-divider": "#E1DFDC",
  "focus-outline": "#B8B4B1",
  "card-outline": "#E1DFDC",
  "branded-outline": "#BF573E",
  "branded-light-outline": "#FFA28C",
  "positive-outline": "#258E6A",
} as const;

export const colorValuesDark = {
  "primary-text": "#F6F4F2",
  "secondary-text": "#A39F9B",
  "disabled-text": "#66625E",
  "high-contrast-text": "#FFFFFF",
  "alert-text": "#FF8060",
  "positive-text": "#54C8A0",

  background: "#292826",
  section: "#33312F",
  card: "#484542",
  floating: "#1F1E1C",

  "branded-surface": "#FF7452",
  "branded-light-surface": "#FF977D",
  "positive-surface": "#32BD8D",
  "primary-surface": "#33312F",
  "secondary-surface": "#3D3B39",
  "tertiary-surface": "#484542",
  "light-transparent-surface": "#FFFFFF",
  "dark-transparent-surface": "#000000",

  "background-divider": "#3D3B39",
  "focus-outline": "#66625E",
  "card-outline": "#5C5855",
  "branded-outline": "#FF8B6F",
  "branded-light-outline": "#FFB9A8",
  "positive-outline": "#54C8A0",
} as const;

export const colorValuesCustom = {
  "primary-text": "#151741",
  "secondary-text": "#252858",
  "disabled-text": "#6A6C9E",
  "high-contrast-text": "#FFFFFF",
  "alert-text": "#D14BC4",
  "positive-text": "#2EB8B3",

  background: "#9C9EC4",
  section: "#888AB5",
  card: "#B0B2D3",
  floating: "#040529",

  "branded-surface": "#D14BC4",
  "branded-light-surface": "#FF977D",
  "positive-surface": "#2EB8B3",
  "primary-surface": "#A6A8CB",
  "secondary-surface": "#9C9EC4",
  "tertiary-surface": "#9294BC",
  "light-transparent-surface": "#FFFFFF",
  "dark-transparent-surface": "#000000",

  "background-divider": "#7E80AD",
  "focus-outline": "#6A6C9E",
  "card-outline": "#7E80AD",
  "branded-outline": "#852F7D",
  "branded-light-outline": "#EC74E1",
  "positive-outline": "#299E9A",
} as const;

export const colorValuesByTheme = {
  light: colorValues,
  dark: colorValuesDark,
  custom: colorValuesCustom,
};

export type ColorKey = keyof typeof colorValues;

export const getColor = (key: ColorKey): string => {
  return colorValues[key];
};

export const getAllColors = () => {
  return Object.entries(colorValues).map(([key, value]) => ({
    key,
    value,
    name: key
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" "),
  }));
};

export const getColorsByCategory = () => {
  const categories = {
    text: Object.entries(colorValues).filter(([key]) => key.includes("text")),
    background: Object.entries(colorValues).filter(([key]) =>
      key.includes("background")
    ),
    surface: Object.entries(colorValues).filter(([key]) =>
      key.includes("surface")
    ),
    outline: Object.entries(colorValues).filter(
      ([key]) => key.includes("outline") || key.includes("divider")
    ),
  };

  return categories;
};
