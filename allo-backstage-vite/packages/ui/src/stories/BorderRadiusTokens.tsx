import React from "react";

export interface BorderRadiusTokensProps {
  showCSSVariables?: boolean;
  showUtilityClasses?: boolean;
}

/**
 * Component demonstrating CSS custom properties for border radius
 */
export const BorderRadiusTokens: React.FC<BorderRadiusTokensProps> = ({
  showCSSVariables = true,
  showUtilityClasses = true,
}) => {
  const radiusTokens = [
    {
      name: "None",
      variable: "--radius-none",
      value: "0",
      pixels: "0px",
      class: "rounded-none",
    },
    {
      name: "XS",
      variable: "--radius-xs",
      value: "0.125rem",
      pixels: "2px",
      class: "rounded-xs",
    },
    {
      name: "S",
      variable: "--radius-s",
      value: "0.25rem",
      pixels: "4px",
      class: "rounded-s",
    },
    {
      name: "M",
      variable: "--radius-m",
      value: "0.375rem",
      pixels: "6px",
      class: "rounded-m",
    },
    {
      name: "L",
      variable: "--radius-l",
      value: "0.5rem",
      pixels: "8px",
      class: "rounded-l",
    },
    {
      name: "XL",
      variable: "--radius-xl",
      value: "0.75rem",
      pixels: "12px",
      class: "rounded-xl",
    },
    {
      name: "2XL",
      variable: "--radius-2xl",
      value: "1rem",
      pixels: "16px",
      class: "rounded-2xl",
    },
    {
      name: "3XL",
      variable: "--radius-3xl",
      value: "1.5rem",
      pixels: "24px",
      class: "rounded-3xl",
    },
    {
      name: "4XL",
      variable: "--radius-4xl",
      value: "2rem",
      pixels: "32px",
      class: "rounded-4xl",
    },
    {
      name: "Full",
      variable: "--radius-full",
      value: "100%",
      pixels: "100%",
      class: "rounded-full",
    },
  ];

  return (
    <div className="mx-auto max-w-7xl space-y-8 p-6">
      {/* Header */}
      <div className="space-y-4">
        <h1 className="text-primary text-3xl font-bold">
          Border Radius Design Tokens
        </h1>
        <p className="text-secondary">
          Border radius values are now available as CSS custom properties
          (design tokens) that can be used directly in your CSS or through
          utility classes. These tokens ensure consistency and make it easy to
          update values globally.
        </p>
      </div>

      {/* CSS Custom Properties */}
      {showCSSVariables && (
        <div className="space-y-4">
          <h2 className="text-primary text-xl font-semibold">
            CSS Custom Properties
          </h2>
          <div className="bg-section rounded-l p-6">
            <p className="text-secondary mb-4">
              Use these CSS custom properties directly in your stylesheets:
            </p>

            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h4 className="text-primary mb-3 font-medium">
                  Available Tokens
                </h4>
                <div className="space-y-2 font-mono text-sm">
                  {radiusTokens.map((token) => (
                    <div
                      key={token.name}
                      className="flex items-center justify-between py-1"
                    >
                      <code className="text-primary bg-card rounded px-2 py-1">
                        var({token.variable})
                      </code>
                      <span className="text-secondary text-xs">
                        {token.pixels}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-primary mb-3 font-medium">
                  Usage Examples
                </h4>
                <div className="space-y-3">
                  <div className="bg-card border-card-outline rounded border p-4">
                    <h5 className="text-primary mb-2 text-sm font-medium">
                      CSS
                    </h5>
                    <pre className="text-secondary font-mono text-xs">
                      {`.my-component {
  border-radius: var(--radius-m);
}

.my-button {
  border-radius: var(--radius-s);
}

.my-avatar {
  border-radius: var(--radius-full);
}`}
                    </pre>
                  </div>

                  <div className="bg-card border-card-outline rounded border p-4">
                    <h5 className="text-primary mb-2 text-sm font-medium">
                      Inline Styles
                    </h5>
                    <pre className="text-secondary font-mono text-xs">
                      {`<div style={{
  borderRadius: 'var(--radius-l)'
}}>
  Content
</div>`}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Utility Classes */}
      {showUtilityClasses && (
        <div className="space-y-4">
          <h2 className="text-primary text-xl font-semibold">
            Utility Classes
          </h2>
          <div className="bg-section rounded-l p-6">
            <p className="text-secondary mb-4">
              Pre-built utility classes for quick application:
            </p>

            <div className="grid gap-6 md:grid-cols-2">
              <div>
                <h4 className="text-primary mb-3 font-medium">
                  Available Classes
                </h4>
                <div className="space-y-2">
                  {radiusTokens.map((token) => (
                    <div
                      key={token.name}
                      className="flex items-center justify-between py-1"
                    >
                      <code className="text-primary bg-card rounded px-2 py-1 font-mono text-sm">
                        .{token.class}
                      </code>
                      <span className="text-secondary text-xs">
                        {token.pixels}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="text-primary mb-3 font-medium">HTML Examples</h4>
                <div className="space-y-3">
                  <div className="bg-card border-card-outline rounded border p-4">
                    <pre className="text-secondary font-mono text-xs">
                      {`<div class="rounded-m bg-card p-4">
  Standard card
</div>

<button class="rounded-s px-4 py-2">
  Button
</button>

<img class="rounded-full w-12 h-12">
  Avatar
</img>`}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Visual Examples */}
      <div className="space-y-4">
        <h2 className="text-primary text-xl font-semibold">Visual Examples</h2>
        <div className="bg-section rounded-l p-6">
          <div className="grid grid-cols-2 gap-4 md:grid-cols-5 lg:grid-cols-10">
            {radiusTokens.map((token) => (
              <div key={token.name} className="text-center">
                <div
                  className="bg-branded-surface mx-auto mb-2 h-12 w-12"
                  style={{ borderRadius: `var(${token.variable})` }}
                />
                <div className="text-primary text-xs font-medium">
                  {token.name}
                </div>
                <div className="text-secondary text-xs">{token.pixels}</div>
                <code className="text-disabled mt-1 block font-mono text-xs">
                  {token.variable}
                </code>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
