export interface TypographyStyle {
  name: string;
  fontSize: string;
  fontWeight: number;
  lineHeight: string;
  letterSpacing: string;
  uppercase?: boolean;
  tabularNums?: boolean;
  className: string;
  tailwindClasses: string;
}

export const typographyStyles: TypographyStyle[] = [
  // 3XL Styles
  {
    name: "3XL",
    fontSize: "1.668rem", // 26.7px
    fontWeight: 400,
    lineHeight: "2.25rem", // 36px
    letterSpacing: "-1%",
    className: "text-3xl",
    tailwindClasses:
      "font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)]",
  },
  {
    name: "3XL Uppercase",
    fontSize: "1.668rem", // 26.7px
    fontWeight: 400,
    lineHeight: "2.25rem", // 36px
    letterSpacing: "-1%",
    uppercase: true,
    className: "text-3xl",
    tailwindClasses:
      "font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)] uppercase",
  },
  {
    name: "3XL Tabular Numbers",
    fontSize: "1.668rem", // 26.7px
    fontWeight: 400,
    lineHeight: "2.25rem", // 36px
    letterSpacing: "-1%",
    tabularNums: true,
    className: "text-3xl",
    tailwindClasses:
      "font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)] tabular-nums",
  },

  // 2XL Styles
  {
    name: "2XL",
    fontSize: "1.5rem", // 24px
    fontWeight: 400,
    lineHeight: "2rem", // 32px
    letterSpacing: "-1%",
    className: "text-2xl",
    tailwindClasses:
      "font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)]",
  },
  {
    name: "2XL Uppercase",
    fontSize: "1.5rem", // 24px
    fontWeight: 400,
    lineHeight: "2rem", // 32px
    letterSpacing: "-1%",
    uppercase: true,
    className: "text-2xl",
    tailwindClasses:
      "font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)] uppercase",
  },
  {
    name: "2XL Tabular Numbers",
    fontSize: "1.5rem", // 24px
    fontWeight: 400,
    lineHeight: "2rem", // 32px
    letterSpacing: "-1%",
    tabularNums: true,
    className: "text-2xl",
    tailwindClasses:
      "font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)] tabular-nums",
  },

  // XL Styles
  {
    name: "XL",
    fontSize: "1.313rem", // 21px
    fontWeight: 400,
    lineHeight: "1.75rem", // 28px
    letterSpacing: "-1%",
    className: "text-xl",
    tailwindClasses:
      "font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)]",
  },
  {
    name: "XL Uppercase",
    fontSize: "1.313rem", // 21px
    fontWeight: 400,
    lineHeight: "1.75rem", // 28px
    letterSpacing: "-1%",
    uppercase: true,
    className: "text-xl",
    tailwindClasses:
      "font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)] uppercase",
  },
  {
    name: "XL Tabular Numbers",
    fontSize: "1.313rem", // 21px
    fontWeight: 400,
    lineHeight: "1.75rem", // 28px
    letterSpacing: "-1%",
    tabularNums: true,
    className: "text-xl",
    tailwindClasses:
      "font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)] tabular-nums",
  },

  // L Styles
  {
    name: "L",
    fontSize: "1.125rem", // 18px
    fontWeight: 500,
    lineHeight: "1.5rem", // 24px
    letterSpacing: "0%",
    className: "text-l",
    tailwindClasses:
      "font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)]",
  },
  {
    name: "L Uppercase",
    fontSize: "1.125rem", // 18px
    fontWeight: 500,
    lineHeight: "1.5rem", // 24px
    letterSpacing: "0%",
    uppercase: true,
    className: "text-l",
    tailwindClasses:
      "font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)] uppercase",
  },
  {
    name: "L Tabular Numbers",
    fontSize: "1.125rem", // 18px
    fontWeight: 500,
    lineHeight: "1.5rem", // 24px
    letterSpacing: "0%",
    tabularNums: true,
    className: "text-l",
    tailwindClasses:
      "font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)] tabular-nums",
  },

  // M Styles
  {
    name: "M",
    fontSize: "0.938rem", // 15px
    fontWeight: 400,
    lineHeight: "1.25rem", // 20px
    letterSpacing: "0%",
    className: "text-m",
    tailwindClasses:
      "font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)]",
  },
  {
    name: "M Uppercase",
    fontSize: "0.938rem", // 15px
    fontWeight: 400,
    lineHeight: "1.25rem", // 20px
    letterSpacing: "0%",
    uppercase: true,
    className: "text-m",
    tailwindClasses:
      "font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)] uppercase",
  },
  {
    name: "M Tabular Numbers",
    fontSize: "0.938rem", // 15px
    fontWeight: 400,
    lineHeight: "1.25rem", // 20px
    letterSpacing: "0%",
    tabularNums: true,
    className: "text-m",
    tailwindClasses:
      "font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)] tabular-nums",
  },

  // S Styles
  {
    name: "S",
    fontSize: "0.75rem", // 12px
    fontWeight: 500,
    lineHeight: "1rem", // 16px
    letterSpacing: "0%",
    className: "text-s",
    tailwindClasses:
      "font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)]",
  },
  {
    name: "S Uppercase",
    fontSize: "0.75rem", // 12px
    fontWeight: 500,
    lineHeight: "1rem", // 16px
    letterSpacing: "0%",
    uppercase: true,
    className: "text-s",
    tailwindClasses:
      "font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)] uppercase",
  },
  {
    name: "S Tabular Numbers",
    fontSize: "0.75rem", // 12px
    fontWeight: 500,
    lineHeight: "1rem", // 16px
    letterSpacing: "0%",
    tabularNums: true,
    className: "text-s",
    tailwindClasses:
      "font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)] tabular-nums",
  },

  // XS Styles
  {
    name: "XS",
    fontSize: "0.563rem", // 9px
    fontWeight: 400,
    lineHeight: "0.75rem", // 12px
    letterSpacing: "0%",
    className: "text-xs",
    tailwindClasses:
      "font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)]",
  },
  {
    name: "XS Uppercase",
    fontSize: "0.563rem", // 9px
    fontWeight: 400,
    lineHeight: "0.75rem", // 12px
    letterSpacing: "0%",
    uppercase: true,
    className: "text-xs",
    tailwindClasses:
      "font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)] uppercase",
  },
  {
    name: "XS Tabular Numbers",
    fontSize: "0.563rem", // 9px
    fontWeight: 400,
    lineHeight: "0.75rem", // 12px
    letterSpacing: "0%",
    tabularNums: true,
    className: "text-xs",
    tailwindClasses:
      "font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)] tabular-nums",
  },
];

/**
 * Helper function to get typography style by name
 */
export const getTypographyStyle = (
  name: string
): TypographyStyle | undefined => {
  return typographyStyles.find((style) => style.name === name);
};

/**
 * Helper function to get all typography styles by size
 */
export const getTypographyStylesBySize = () => {
  const sizes = ["3XL", "2XL", "XL", "L", "M", "S", "XS"];
  return sizes.map((size) => ({
    size,
    styles: typographyStyles.filter((style) => style.name.startsWith(size)),
  }));
};
