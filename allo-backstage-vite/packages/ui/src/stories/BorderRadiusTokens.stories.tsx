import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { BorderRadiusTokens } from "./BorderRadiusTokens";

const meta: Meta<typeof BorderRadiusTokens> = {
  title: "Design System/Border Radius Tokens",
  component: BorderRadiusTokens,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
Border radius values are now implemented as CSS custom properties (design tokens) that provide a consistent, 
maintainable, and theme-aware approach to corner rounding throughout the application.

## What are Design Tokens?

Design tokens are named entities that store visual design attributes. They're the single source of truth for 
design decisions and help maintain consistency across products and platforms.

## CSS Custom Properties

All border radius values are available as CSS custom properties:

\`\`\`css
/* Available tokens */
var(--radius-none)   /* 0 */
var(--radius-xs)     /* 0.125rem / 2px */
var(--radius-s)      /* 0.25rem / 4px */
var(--radius-m)      /* 0.375rem / 6px */
var(--radius-l)      /* 0.5rem / 8px */
var(--radius-xl)     /* 0.75rem / 12px */
var(--radius-2xl)    /* 1rem / 16px */
var(--radius-3xl)    /* 1.5rem / 24px */
var(--radius-4xl)    /* 2rem / 32px */
var(--radius-full)   /* 100% */
\`\`\`

## Usage Examples

### In CSS
\`\`\`css
.my-component {
  border-radius: var(--radius-m);
}

.my-button {
  border-radius: var(--radius-s);
}
\`\`\`

### In React (inline styles)
\`\`\`jsx
<div style={{ borderRadius: 'var(--radius-l)' }}>
  Content
</div>
\`\`\`

### With Utility Classes
\`\`\`html
<div class="rounded-m">Standard card</div>
<button class="rounded-s">Button</button>
<img class="rounded-full" />
\`\`\`

## Benefits

1. **Consistency**: Single source of truth for all radius values
2. **Maintainability**: Update values globally from one location
3. **Theme Support**: Can be overridden per theme
4. **Performance**: CSS custom properties are efficient
5. **Developer Experience**: Clear, semantic naming

## Theme Integration

Border radius tokens are defined in the main theme and inherited by all theme variants:

\`\`\`css
@theme {
  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-s: 0.25rem;
  /* ... */
}
\`\`\`

Different themes can override these values if needed for brand-specific requirements.
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    showCSSVariables: {
      control: "boolean",
      description: "Show CSS custom properties documentation",
    },
    showUtilityClasses: {
      control: "boolean",
      description: "Show utility classes documentation",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Complete: Story = {
  args: {
    showCSSVariables: true,
    showUtilityClasses: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete documentation of border radius design tokens including CSS custom properties and utility classes.",
      },
    },
  },
};

// export const CSSVariablesOnly: Story = {
//   args: {
//     showCSSVariables: true,
//     showUtilityClasses: false,
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Focus on CSS custom properties (design tokens) for border radius values.",
//       },
//     },
//   },
// };

// export const UtilityClassesOnly: Story = {
//   args: {
//     showCSSVariables: false,
//     showUtilityClasses: true,
//   },
//   parameters: {
//     docs: {
//       description: {
//         story:
//           "Focus on utility classes for quick application of border radius values.",
//       },
//     },
//   },
// };

export const TokensReference: Story = {
  render: () => (
    <div className="space-y-6 p-6">
      <h1 className="text-primary mb-6 text-2xl font-bold">
        Border Radius Tokens Quick Reference
      </h1>

      {/* Token Table */}
      <div className="bg-section rounded-l p-6">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-divider border-b">
              <th className="text-primary py-3 text-left font-medium">
                Token Name
              </th>
              <th className="text-primary py-3 text-left font-medium">
                CSS Variable
              </th>
              <th className="text-primary py-3 text-left font-medium">Value</th>
              <th className="text-primary py-3 text-left font-medium">
                Pixels
              </th>
              <th className="text-primary py-3 text-left font-medium">
                Utility Class
              </th>
              <th className="text-primary py-3 text-left font-medium">
                Preview
              </th>
            </tr>
          </thead>
          <tbody>
            {[
              {
                name: "None",
                variable: "--radius-none",
                value: "0",
                pixels: "0px",
                class: ".rounded-none",
              },
              {
                name: "XS",
                variable: "--radius-xs",
                value: "0.125rem",
                pixels: "2px",
                class: ".rounded-xs",
              },
              {
                name: "S",
                variable: "--radius-s",
                value: "0.25rem",
                pixels: "4px",
                class: ".rounded-s",
              },
              {
                name: "M",
                variable: "--radius-m",
                value: "0.375rem",
                pixels: "6px",
                class: ".rounded-m",
              },
              {
                name: "L",
                variable: "--radius-l",
                value: "0.5rem",
                pixels: "8px",
                class: ".rounded-l",
              },
              {
                name: "XL",
                variable: "--radius-xl",
                value: "0.75rem",
                pixels: "12px",
                class: ".rounded-xl",
              },
              {
                name: "2XL",
                variable: "--radius-2xl",
                value: "1rem",
                pixels: "16px",
                class: ".rounded-2xl",
              },
              {
                name: "3XL",
                variable: "--radius-3xl",
                value: "1.5rem",
                pixels: "24px",
                class: ".rounded-3xl",
              },
              {
                name: "4XL",
                variable: "--radius-4xl",
                value: "2rem",
                pixels: "32px",
                class: ".rounded-4xl",
              },
              {
                name: "Full",
                variable: "--radius-full",
                value: "100%",
                pixels: "100%",
                class: ".rounded-full",
              },
            ].map((token, index) => (
              <tr key={token.name} className={index % 2 === 0 ? "bg-card" : ""}>
                <td className="text-primary py-3 font-medium">{token.name}</td>
                <td className="py-3">
                  <code className="text-primary rounded px-2 py-1 font-mono text-xs">
                    {token.variable}
                  </code>
                </td>
                <td className="text-secondary py-3 font-mono text-xs">
                  {token.value}
                </td>
                <td className="text-secondary py-3">{token.pixels}</td>
                <td className="py-3">
                  <code className="text-primary rounded px-2 py-1 font-mono text-xs">
                    {token.class}
                  </code>
                </td>
                <td className="py-3">
                  <div
                    className="bg-branded h-8 w-8"
                    style={{ borderRadius: `var(${token.variable})` }}
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Usage Examples */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-card border-card rounded-l border p-4">
          <h3 className="text-primary mb-3 font-semibold">CSS Usage</h3>
          <pre className="text-secondary bg-section rounded p-3 font-mono text-xs">
            {`.component {
  border-radius: var(--radius-m);
}

.button {
  border-radius: var(--radius-s);
}

.avatar {
  border-radius: var(--radius-full);
}`}
          </pre>
        </div>

        <div className="bg-card border-card rounded-l border p-4">
          <h3 className="text-primary mb-3 font-semibold">HTML Usage</h3>
          <pre className="text-secondary bg-section rounded p-3 font-mono text-xs">
            {`<div class="rounded-m">
  Card content
</div>

<button class="rounded-s">
  Button
</button>

<img class="rounded-full" />`}
          </pre>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Quick reference table showing all border radius tokens with their CSS variables, values, and usage examples.",
      },
    },
  },
};
