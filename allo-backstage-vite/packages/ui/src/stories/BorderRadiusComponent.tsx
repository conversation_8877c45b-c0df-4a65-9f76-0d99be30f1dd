import React from "react";
import {
  borderRadiusStyles,
  borderRadiusCategories,
  type BorderRadiusStyle,
} from "./border-radius-styles";

interface BorderRadiusSwatchProps {
  style: BorderRadiusStyle;
  size?: "small" | "medium" | "large";
}

const BorderRadiusSwatch: React.FC<BorderRadiusSwatchProps> = ({
  style,
  size = "medium",
}) => {
  const sizeClasses = {
    small: "w-12 h-12",
    medium: "w-16 h-16",
    large: "w-20 h-20",
  };

  const swatchStyle = {
    borderRadius: style.value,
  };

  return (
    <div className="flex flex-col items-center space-y-3 p-4">
      {/* Visual Swatch */}
      <div className="relative">
        <div
          className={`${sizeClasses[size]} bg-branded-surface border-none`}
          style={swatchStyle}
        />
        {/* Corner indicator for very small radius */}
        {style.name === "None" && (
          <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full border-none border-white bg-red-500" />
        )}
      </div>

      {/* Style Information */}
      <div className="space-y-1 text-center">
        <h4 className="text-primary text-sm font-semibold">{style.name}</h4>
        <div className="text-secondary space-y-0.5 text-xs">
          <div>{style.value}</div>
          <div className="text-disabled">({style.pixelValue})</div>
        </div>
        <code className="bg-section text-primary block rounded px-2 py-1 text-xs">
          {style.className}
        </code>
      </div>
    </div>
  );
};

interface BorderRadiusCategoryProps {
  title: string;
  styles: BorderRadiusStyle[];
  description?: string;
}

const BorderRadiusCategory: React.FC<BorderRadiusCategoryProps> = ({
  title,
  styles,
  description,
}) => {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-primary text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-secondary mt-1 text-sm">{description}</p>
        )}
      </div>
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5">
        {styles.map((style) => (
          <BorderRadiusSwatch key={style.name} style={style} />
        ))}
      </div>
    </div>
  );
};

interface BorderRadiusComparisonProps {
  styles: BorderRadiusStyle[];
}

const BorderRadiusComparison: React.FC<BorderRadiusComparisonProps> = ({
  styles,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-primary text-lg font-semibold">Size Comparison</h3>
      <div className="bg-section rounded-lg p-6">
        <div className="flex flex-wrap items-end justify-center gap-6">
          {styles.map((style) => {
            const size =
              style.name === "Full"
                ? "w-16 h-16"
                : parseInt(style.pixelValue) >= 16
                  ? "w-20 h-20"
                  : parseInt(style.pixelValue) >= 8
                    ? "w-16 h-16"
                    : "w-12 h-12";

            return (
              <div
                key={style.name}
                className="flex flex-col items-center space-y-2"
              >
                <div
                  className={`${size} bg-branded-surface border-card-outline border`}
                  style={{ borderRadius: style.value }}
                />
                <div className="text-center">
                  <div className="text-primary text-xs font-medium">
                    {style.name}
                  </div>
                  <div className="text-disabled text-xs">
                    {style.pixelValue}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export interface BorderRadiusProps {
  showComparison?: boolean;
  showCategories?: boolean;
}

export const BorderRadius: React.FC<BorderRadiusProps> = ({
  showComparison = true,
  showCategories = true,
}) => {
  return (
    <div className="mx-auto max-w-7xl space-y-8 p-6">
      {/* Header */}
      <div className="space-y-4">
        <h1 className="text-primary text-3xl font-bold">
          Border Radius System
        </h1>
        <div className="space-y-2">
          <p className="text-secondary">
            Our border radius system provides consistent corner rounding across
            all interface elements. From sharp corners to fully rounded
            elements, these values ensure visual harmony and hierarchy.
          </p>
          <div className="text-disabled flex gap-4 text-sm">
            <span>Total Styles: {borderRadiusStyles.length}</span>
            <span>•</span>
            <span>Range: 0px - 32px + Full</span>
            <span>•</span>
            <span>Categories: 4</span>
          </div>
        </div>
      </div>

      {/* All Styles Overview */}
      <div className="space-y-4">
        <h2 className="text-primary text-xl font-semibold">
          All Border Radius Styles
        </h2>
        <div className="grid grid-cols-2 gap-4 md:grid-cols-5 lg:grid-cols-10">
          {borderRadiusStyles.map((style) => (
            <BorderRadiusSwatch key={style.name} style={style} size="small" />
          ))}
        </div>
      </div>

      {/* Size Comparison */}
      {showComparison && <BorderRadiusComparison styles={borderRadiusStyles} />}

      {/* Categories */}
      {showCategories && (
        <div className="space-y-8">
          <h2 className="text-primary text-xl font-semibold">
            Border Radius Categories
          </h2>

          <BorderRadiusCategory
            title="Minimal Rounding"
            styles={borderRadiusCategories.minimal}
            description="Subtle or no rounding for clean, sharp interfaces"
          />

          <BorderRadiusCategory
            title="Standard Rounding"
            styles={borderRadiusCategories.standard}
            description="Most commonly used radius values for cards, buttons, and containers"
          />

          <BorderRadiusCategory
            title="Large Rounding"
            styles={borderRadiusCategories.large}
            description="Prominent rounding for hero sections and special elements"
          />

          <BorderRadiusCategory
            title="Special Cases"
            styles={borderRadiusCategories.special}
            description="Fully rounded elements for circles, pills, and avatars"
          />
        </div>
      )}
    </div>
  );
};
