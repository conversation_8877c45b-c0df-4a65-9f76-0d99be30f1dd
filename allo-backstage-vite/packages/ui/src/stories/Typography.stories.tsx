import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Typography } from "./TypographyComponent";

const meta: Meta<typeof Typography> = {
  title: "Design System/Typography",
  component: Typography,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component: `
Our typography system is built around **Bricolage Grotesque**, a modern and versatile font family that provides excellent readability across all digital interfaces.

## Font Family

**Bricolage Grotesque Variable** is used as the primary font family throughout the application. This variable font provides:

- Consistent character spacing and proportions
- Excellent readability at all sizes
- Support for tabular numbers
- Optimized for digital interfaces

## Available Styles

### Size Hierarchy
- **3XL-2XL**: Large headings and hero text
- **XL-L**: Section headings and subheadings  
- **M**: Body text and paragraphs
- **S-XS**: Small text and captions

## Font Weights
- **400 (Normal)**: Default weight for most text
- **500 (Medium)**: For emphasis and headings
        `,
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    sampleText: {
      control: "text",
      description: "Custom sample text to display in all typography styles",
    },
    showNumbers: {
      control: "boolean",
      description:
        "Show numbers instead of sample text to demonstrate tabular number styles",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const AllStyles: Story = {
  args: {
    sampleText: "The quick brown fox jumps over the lazy dog",
    showNumbers: false,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Complete overview of all typography styles in the design system with sample text.",
      },
    },
  },
};

export const WithNumbers: Story = {
  args: {
    sampleText: "0123456789",
    showNumbers: true,
  },
  parameters: {
    docs: {
      description: {
        story:
          "Typography styles displayed with numbers to showcase tabular number variants and numeric readability.",
      },
    },
  },
};

// Individual size demonstrations
export const HeadingSizes: Story = {
  render: () => (
    <div className="space-y-8 p-6">
      <h1 className="mb-6 text-2xl font-bold">Heading Sizes (3XL - XL)</h1>

      <div className="space-y-6">
        <div>
          <p className="mb-2 text-sm text-gray-600">3XL - Page Headlines</p>
          <h1 className="font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)]">
            Page Headline
          </h1>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">2XL - Section Headlines</p>
          <h2 className="font-bricolage text-2xl font-normal tracking-[var(--letter-spacing-tight)]">
            Section Headline
          </h2>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">
            XL - Subsection Headlines
          </p>
          <h3 className="font-bricolage text-xl font-normal tracking-[var(--letter-spacing-tight)]">
            Subsection Headline
          </h3>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstration of the largest typography sizes used for headlines and hero content.",
      },
    },
  },
};

export const BodyTextSizes: Story = {
  render: () => (
    <div className="space-y-8 p-6">
      <h1 className="mb-6 text-2xl font-bold">Body Text Sizes (L - XS)</h1>

      <div className="space-y-6">
        <div>
          <p className="mb-2 text-sm text-gray-600">L - Large Body Text</p>
          <p className="font-bricolage text-l font-medium">
            This is large body text used for important paragraphs and
            introductory content.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">M - Regular Body Text</p>
          <p className="font-bricolage text-m font-normal">
            This is regular body text used for most paragraph content and
            general reading.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">S - Small Body Text</p>
          <p className="font-bricolage text-s font-medium">
            This is small body text used for secondary information and
            supporting content.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">XS - Caption Text</p>
          <p className="font-bricolage text-xs font-normal">
            This is caption text used for image captions and supplementary
            information.
          </p>
        </div>
      </div>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story:
          "Demonstration of body text sizes used for content, captions, and fine print.",
      },
    },
  },
};
