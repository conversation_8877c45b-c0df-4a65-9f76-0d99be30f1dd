export interface BorderRadiusStyle {
  name: string;
  value: string;
  pixelValue: string;
  className: string;
  description: string;
}

export const borderRadiusStyles: BorderRadiusStyle[] = [
  {
    name: "None",
    value: "var(--radius-none)",
    pixelValue: "0px",
    className: "rounded-none",
    description: "No border radius - sharp corners",
  },
  {
    name: "XS",
    value: "var(--radius-xs)",
    pixelValue: "2px",
    className: "rounded-xs",
    description: "Extra small radius for subtle rounding",
  },
  {
    name: "S",
    value: "var(--radius-s)",
    pixelValue: "4px",
    className: "rounded-s",
    description: "Small radius for buttons and small elements",
  },
  {
    name: "M",
    value: "var(--radius-m)",
    pixelValue: "6px",
    className: "rounded-m",
    description: "Medium radius for cards and containers",
  },
  {
    name: "L",
    value: "var(--radius-l)",
    pixelValue: "8px",
    className: "rounded-l",
    description: "Large radius for prominent elements",
  },
  {
    name: "XL",
    value: "var(--radius-xl)",
    pixelValue: "12px",
    className: "rounded-xl",
    description: "Extra large radius for special containers",
  },
  {
    name: "2XL",
    value: "var(--radius-2xl)",
    pixelValue: "16px",
    className: "rounded-2xl",
    description: "Very large radius for hero sections",
  },
  {
    name: "3XL",
    value: "var(--radius-3xl)",
    pixelValue: "24px",
    className: "rounded-3xl",
    description: "Extremely large radius for special cases",
  },
  {
    name: "4XL",
    value: "var(--radius-4xl)",
    pixelValue: "32px",
    className: "rounded-4xl",
    description: "Maximum radius for unique design elements",
  },
  {
    name: "Full",
    value: "var(--radius-full)",
    pixelValue: "100%",
    className: "rounded-full",
    description: "Fully rounded - creates circles and pills",
  },
];

/**
 * Helper function to get border radius style by name
 */
export const getBorderRadiusStyle = (
  name: string
): BorderRadiusStyle | undefined => {
  return borderRadiusStyles.find((style) => style.name === name);
};

/**
 * Helper function to get border radius value by className
 */
export const getBorderRadiusByClassName = (
  className: string
): BorderRadiusStyle | undefined => {
  return borderRadiusStyles.find((style) => style.className === className);
};

/**
 * Border radius categories for organization
 */
export const borderRadiusCategories = {
  minimal: borderRadiusStyles.slice(0, 3), // None, XS, S
  standard: borderRadiusStyles.slice(3, 6), // M, L, XL
  large: borderRadiusStyles.slice(6, 9), // 2XL, 3XL, 4XL
  special: [borderRadiusStyles[9]], // Full
};
