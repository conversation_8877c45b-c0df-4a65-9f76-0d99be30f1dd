@import url("https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:opsz,wght@12..96,200..800&display=swap");

@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
@import "tailwindcss/utilities.css" layer(utilities);

@theme {
  --font-primary:
    "Bricolage Grotesque Variable", "Bricolage Grotesque", sans-serif;

  --text-3xl: 1.668rem;
  --text-2xl: 1.5rem;
  --text-xl: 1.313rem;
  --text-l: 1.125rem;
  --text-m: 0.938rem;
  --text-s: 0.75rem;
  --text-xs: 0.563rem;

  --text-3xl--line-height: 2.25rem;
  --text-2xl--line-height: 2rem;
  --text-xl--line-height: 1.75rem;
  --text-l--line-height: 1.5rem;
  --text-m--line-height: 1.25rem;
  --text-s--line-height: 1rem;
  --text-xs--line-height: 0.75rem;

  --letter-spacing-tight: -0.01em;
  --letter-spacing-normal: 0em;

  --font-weight-normal: 400;
  --font-weight-medium: 500;

  --color-primary: #151413;
  --color-secondary: #66625e;
  --color-disabled: #b8b4b1;
  --color-high-contrast: #ffffff;
  --color-alert: #aa4d37;
  --color-positive: #1d6e53;

  --color-background: #f6f4f2;
  --color-section: #ece9e7;
  --color-card: #f6f4f2;
  --color-floating: #1f1e1c;

  --color-branded-surface: #ff7452;
  --color-branded-light-surface: #ffc5b7;
  --color-positive-surface: #32bd8d;
  --color-primary-surface: #ece9e7;
  --color-secondary-surface: #e1dfdc;
  --color-tertiary-surface: #d7d4d1;
  --color-light-transparent-surface: #ffffff;
  --color-dark-transparent-surface: #000000;

  --color-background-divider: #e1dfdc;
  --color-focus-outline: #b8b4b1;
  --color-card-outline: #e1dfdc;
  --color-branded-outline: #bf573e;
  --color-branded-light-outline: #ffa28c;
  --color-positive-outline: #258e6a;

  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-s: 0.25rem;
  --radius-m: 0.375rem;
  --radius-l: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 100%;
}

/* Dark theme overrides */
[data-theme="dark"] {
  --color-background: var(--color-gray-900);
  --color-foreground: var(--color-gray-100);

  /* Design System Colors - Dark Theme */
  --color-primary: #f6f4f2;
  --color-secondary: #a39f9b;
  --color-disabled: #66625e;
  --color-high-contrast: #ffffff;
  --color-alert: #ff8060;
  --color-positive: #54c8a0;

  --color-background: #292826;
  --color-section: #33312f;
  --color-card: #484542;
  --color-floating: #1f1e1c;

  --color-branded-surface: #ff7452;
  --color-branded-light-surface: #ff977d;
  --color-positive-surface: #32bd8d;
  --color-primary-surface: #33312f;
  --color-secondary-surface: #3d3b39;
  --color-tertiary-surface: #484542;
  --color-light-transparent-surface: #ffffff;
  --color-dark-transparent-surface: #000000;

  --color-background-divider: #3d3b39;
  --color-focus-outline: #66625e;
  --color-card-outline: #5c5855;
  --color-branded-outline: #ff8b6f;
  --color-branded-light-outline: #ffb9a8;
  --color-positive-outline: #54c8a0;
}

/* Light theme (default) */
[data-theme="light"] {
  --color-background: var(--color-white);
  --color-foreground: var(--color-gray-900);
}

/* Custom theme */
[data-theme="custom"] {
  --color-background: var(--color-purple-900);
  --color-foreground: var(--color-purple-100);

  /* Design System Colors - Custom Theme */
  --color-primary: #151741;
  --color-secondary: #252858;
  --color-disabled: #6a6c9e;
  --color-high-contrast: #ffffff;
  --color-alert: #d14bc4;
  --color-positive: #2eb8b3;

  --color-background: #9c9ec4;
  --color-section: #888ab5;
  --color-card: #b0b2d3;
  --color-floating: #040529;

  --color-branded-surface: #d14bc4;
  --color-branded-light-surface: #ff977d;
  --color-positive-surface: #2eb8b3;
  --color-primary-surface: #a6a8cb;
  --color-secondary-surface: #9c9ec4;
  --color-tertiary-surface: #9294bc;
  --color-light-transparent-surface: #ffffff;
  --color-dark-transparent-surface: #000000;

  --color-background-divider: #7e80ad;
  --color-focus-outline: #6a6c9e;
  --color-card-outline: #7e80ad;
  --color-branded-outline: #852f7d;
  --color-branded-light-outline: #ec74e1;
  --color-positive-outline: #299e9a;
}

/* Base styles */
body {
  background-color: var(--color-white);
  color: var(--color-foreground, black);
  font-family: var(--font-primary);
  border: initial;
  margin: initial;
  border: initial;
  box-sizing: border-box;
}

.font-bricolage {
  font-family: var(--font-primary);
}

.rounded-none {
  border-radius: var(--radius-none);
}

.rounded-xs {
  border-radius: var(--radius-xs); /* 2px */
}

.rounded-s {
  border-radius: var(--radius-s); /* 4px */
}

.rounded-m {
  border-radius: var(--radius-m); /* 6px */
}

.rounded-l {
  border-radius: var(--radius-l); /* 8px */
}

.rounded-xl {
  border-radius: var(--radius-xl); /* 12px */
}

.rounded-2xl {
  border-radius: var(--radius-2xl); /* 16px */
}

.rounded-3xl {
  border-radius: var(--radius-3xl); /* 24px */
}

.rounded-4xl {
  border-radius: var(--radius-4xl); /* 32px */
}

.rounded-full {
  border-radius: var(--radius-full);
}
