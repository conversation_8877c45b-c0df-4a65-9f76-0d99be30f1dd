{"name": "@monorepo/ui", "version": "0.0.1", "private": true, "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "./tailwind": "./dist/tailwind.css"}, "files": ["dist"], "scripts": {"build": "tsc --emitDeclarationOnly && vite build", "dev": "vite build --watch --mode storybook", "storybook": "storybook dev -p 6006 --mode storybook", "storybook:build": "storybook build --mode storybook", "typecheck": "tsc --noEmit"}, "dependencies": {"@fontsource-variable/bricolage-grotesque": "^5.2.5", "@fontsource-variable/inter": "^5.2.5", "@tailwindcss/vite": "4.0.17", "lucide-react": "^0.476.0", "motion": "^12.5.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "4.0.17"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@storybook/addon-a11y": "^9.0.17", "@storybook/addon-docs": "^9.0.17", "@storybook/addon-onboarding": "^9.0.17", "@storybook/addon-vitest": "^9.0.17", "@storybook/react-vite": "^9.0.17", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "chroma-js": "^3.1.2", "eslint-plugin-storybook": "^9.0.17", "storybook": "^9.0.17", "typescript": "~5.7.2", "vite": "^6.2.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}