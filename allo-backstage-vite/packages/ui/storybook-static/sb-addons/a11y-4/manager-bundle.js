try{
(()=>{var kn=Object.defineProperty;var Se=(e,t)=>()=>(e&&(t=e(e=0)),t);var Nn=(e,t)=>{for(var a in t)kn(e,a,{get:t[a],enumerable:!0})};var T=Se(()=>{});var _=Se(()=>{});var C=Se(()=>{});var mt={};Nn(mt,{A:()=>Mn,ActionBar:()=>Ln,AddonPanel:()=>Fn,Badge:()=>le,Bar:()=>Hn,Blockquote:()=>Un,Button:()=>re,ClipboardCode:()=>Gn,Code:()=>Bn,DL:()=>zn,Div:()=>Wn,DocumentWrapper:()=>jn,EmptyTabContent:()=>Ce,ErrorFormatter:()=>Yn,FlexBar:()=>Vn,Form:()=>qn,H1:()=>Kn,H2:()=>Qn,H3:()=>Xn,H4:()=>Zn,H5:()=>Jn,H6:()=>ea,HR:()=>ta,IconButton:()=>ie,Img:()=>na,LI:()=>aa,Link:()=>Ie,ListItem:()=>ra,Loader:()=>oa,Modal:()=>la,OL:()=>ia,P:()=>ca,Placeholder:()=>sa,Pre:()=>ua,ProgressSpinner:()=>da,ResetWrapper:()=>ma,ScrollArea:()=>Oe,Separator:()=>pa,Spaced:()=>fa,Span:()=>ha,StorybookIcon:()=>ya,StorybookLogo:()=>ba,SyntaxHighlighter:()=>we,TT:()=>ga,TabBar:()=>Sa,TabButton:()=>va,TabWrapper:()=>Ea,Table:()=>Ia,Tabs:()=>xa,TabsState:()=>Ra,TooltipLinkList:()=>ke,TooltipMessage:()=>Aa,TooltipNote:()=>fe,UL:()=>$a,WithTooltip:()=>de,WithTooltipPure:()=>Ta,Zoom:()=>_a,codeCommon:()=>Ca,components:()=>Oa,createCopyToClipboardFunction:()=>wa,default:()=>Dn,getStoryHref:()=>ka,interleaveSeparators:()=>Na,nameSpaceClassNames:()=>Pa,resetComponents:()=>Da,withReset:()=>Ma});var Dn,Mn,Ln,Fn,le,Hn,Un,re,Gn,Bn,zn,Wn,jn,Ce,Yn,Vn,qn,Kn,Qn,Xn,Zn,Jn,ea,ta,ie,na,aa,Ie,ra,oa,la,ia,ca,sa,ua,da,ma,Oe,pa,fa,ha,ya,ba,we,ga,Sa,va,Ea,Ia,xa,Ra,ke,Aa,fe,$a,de,Ta,_a,Ca,Oa,wa,ka,Na,Pa,Da,Ma,Ne=Se(()=>{T();_();C();Dn=__STORYBOOK_COMPONENTS__,{A:Mn,ActionBar:Ln,AddonPanel:Fn,Badge:le,Bar:Hn,Blockquote:Un,Button:re,ClipboardCode:Gn,Code:Bn,DL:zn,Div:Wn,DocumentWrapper:jn,EmptyTabContent:Ce,ErrorFormatter:Yn,FlexBar:Vn,Form:qn,H1:Kn,H2:Qn,H3:Xn,H4:Zn,H5:Jn,H6:ea,HR:ta,IconButton:ie,Img:na,LI:aa,Link:Ie,ListItem:ra,Loader:oa,Modal:la,OL:ia,P:ca,Placeholder:sa,Pre:ua,ProgressSpinner:da,ResetWrapper:ma,ScrollArea:Oe,Separator:pa,Spaced:fa,Span:ha,StorybookIcon:ya,StorybookLogo:ba,SyntaxHighlighter:we,TT:ga,TabBar:Sa,TabButton:va,TabWrapper:Ea,Table:Ia,Tabs:xa,TabsState:Ra,TooltipLinkList:ke,TooltipMessage:Aa,TooltipNote:fe,UL:$a,WithTooltip:de,WithTooltipPure:Ta,Zoom:_a,codeCommon:Ca,components:Oa,createCopyToClipboardFunction:wa,getStoryHref:ka,interleaveSeparators:Na,nameSpaceClassNames:Pa,resetComponents:Da,withReset:Ma}=__STORYBOOK_COMPONENTS__});T();_();C();T();_();C();T();_();C();T();_();C();var o=__REACT__,{Children:P,Component:ri,Fragment:me,Profiler:oi,PureComponent:ct,StrictMode:li,Suspense:ii,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:ci,act:si,cloneElement:V,createContext:pe,createElement:m,createFactory:ui,createRef:st,forwardRef:L,isValidElement:U,lazy:di,memo:mi,startTransition:pi,unstable_act:fi,useCallback:R,useContext:ve,useDebugValue:hi,useDeferredValue:yi,useEffect:G,useId:ut,useImperativeHandle:bi,useInsertionEffect:gi,useLayoutEffect:Ee,useMemo:j,useReducer:dt,useRef:z,useState:H,useSyncExternalStore:Si,useTransition:vi,version:Ei}=__REACT__;Ne();T();_();C();var _i=__STORYBOOK_API__,{ActiveTabs:Ci,Consumer:Oi,ManagerContext:wi,Provider:ki,RequestResponseError:Ni,addons:xe,combineParameters:Pi,controlOrMetaKey:Di,controlOrMetaSymbol:Mi,eventMatchesShortcut:Li,eventToShortcut:Fi,experimental_MockUniversalStore:Hi,experimental_UniversalStore:Ui,experimental_getStatusStore:pt,experimental_getTestProviderStore:Gi,experimental_requestResponse:Bi,experimental_useStatusStore:ft,experimental_useTestProviderStore:zi,experimental_useUniversalStore:Wi,internal_fullStatusStore:ji,internal_fullTestProviderStore:Yi,internal_universalStatusStore:Vi,internal_universalTestProviderStore:qi,isMacLike:Ki,isShortcutTaken:Qi,keyToSymbol:Xi,merge:Zi,mockChannel:Ji,optionOrAltSymbol:ec,shortcutMatchesShortcut:tc,shortcutToHumanString:nc,types:Pe,useAddonState:De,useArgTypes:ac,useArgs:rc,useChannel:ht,useGlobalTypes:oc,useGlobals:yt,useParameter:bt,useSharedState:lc,useStoryPrepared:ic,useStorybookApi:Re,useStorybookState:gt}=__STORYBOOK_API__;T();_();C();var mc=__STORYBOOK_ICONS__,{AccessibilityAltIcon:pc,AccessibilityIcon:St,AccessibilityIgnoredIcon:fc,AddIcon:hc,AdminIcon:yc,AlertAltIcon:bc,AlertIcon:gc,AlignLeftIcon:Sc,AlignRightIcon:vc,AppleIcon:Ec,ArrowBottomLeftIcon:Ic,ArrowBottomRightIcon:xc,ArrowDownIcon:Rc,ArrowLeftIcon:Ac,ArrowRightIcon:$c,ArrowSolidDownIcon:Tc,ArrowSolidLeftIcon:_c,ArrowSolidRightIcon:Cc,ArrowSolidUpIcon:Oc,ArrowTopLeftIcon:wc,ArrowTopRightIcon:kc,ArrowUpIcon:Nc,AzureDevOpsIcon:Pc,BackIcon:Dc,BasketIcon:Mc,BatchAcceptIcon:Lc,BatchDenyIcon:Fc,BeakerIcon:Hc,BellIcon:Uc,BitbucketIcon:Gc,BoldIcon:Bc,BookIcon:zc,BookmarkHollowIcon:Wc,BookmarkIcon:jc,BottomBarIcon:Yc,BottomBarToggleIcon:Vc,BoxIcon:qc,BranchIcon:Kc,BrowserIcon:Qc,ButtonIcon:Xc,CPUIcon:Zc,CalendarIcon:Jc,CameraIcon:es,CameraStabilizeIcon:ts,CategoryIcon:ns,CertificateIcon:as,ChangedIcon:rs,ChatIcon:os,CheckIcon:vt,ChevronDownIcon:ls,ChevronLeftIcon:is,ChevronRightIcon:cs,ChevronSmallDownIcon:Et,ChevronSmallLeftIcon:ss,ChevronSmallRightIcon:us,ChevronSmallUpIcon:ds,ChevronUpIcon:ms,ChromaticIcon:ps,ChromeIcon:fs,CircleHollowIcon:hs,CircleIcon:ys,ClearIcon:bs,CloseAltIcon:gs,CloseIcon:Ss,CloudHollowIcon:vs,CloudIcon:Es,CogIcon:Is,CollapseIcon:It,CommandIcon:xs,CommentAddIcon:Rs,CommentIcon:As,CommentsIcon:$s,CommitIcon:Ts,CompassIcon:_s,ComponentDrivenIcon:Cs,ComponentIcon:Os,ContrastIcon:ws,ContrastIgnoredIcon:ks,ControlsIcon:Ns,CopyIcon:xt,CreditIcon:Ps,CrossIcon:Ds,DashboardIcon:Ms,DatabaseIcon:Ls,DeleteIcon:Fs,DiamondIcon:Hs,DirectionIcon:Us,DiscordIcon:Gs,DocChartIcon:Bs,DocListIcon:zs,DocumentIcon:Ws,DownloadIcon:js,DragIcon:Ys,EditIcon:Vs,EllipsisIcon:qs,EmailIcon:Ks,ExpandAltIcon:Rt,ExpandIcon:Qs,EyeCloseIcon:At,EyeIcon:$t,FaceHappyIcon:Xs,FaceNeutralIcon:Zs,FaceSadIcon:Js,FacebookIcon:eu,FailedIcon:tu,FastForwardIcon:nu,FigmaIcon:au,FilterIcon:ru,FlagIcon:ou,FolderIcon:lu,FormIcon:iu,GDriveIcon:cu,GithubIcon:su,GitlabIcon:uu,GlobeIcon:du,GoogleIcon:mu,GraphBarIcon:pu,GraphLineIcon:fu,GraphqlIcon:hu,GridAltIcon:yu,GridIcon:bu,GrowIcon:gu,HeartHollowIcon:Su,HeartIcon:vu,HomeIcon:Eu,HourglassIcon:Iu,InfoIcon:xu,ItalicIcon:Ru,JumpToIcon:Au,KeyIcon:$u,LightningIcon:Tu,LightningOffIcon:_u,LinkBrokenIcon:Cu,LinkIcon:Ou,LinkedinIcon:wu,LinuxIcon:ku,ListOrderedIcon:Nu,ListUnorderedIcon:Pu,LocationIcon:Tt,LockIcon:Du,MarkdownIcon:Mu,MarkupIcon:Lu,MediumIcon:Fu,MemoryIcon:Hu,MenuIcon:Uu,MergeIcon:Gu,MirrorIcon:Bu,MobileIcon:zu,MoonIcon:Wu,NutIcon:ju,OutboxIcon:Yu,OutlineIcon:Vu,PaintBrushIcon:qu,PaperClipIcon:Ku,ParagraphIcon:Qu,PassedIcon:Xu,PhoneIcon:Zu,PhotoDragIcon:Ju,PhotoIcon:ed,PhotoStabilizeIcon:td,PinAltIcon:nd,PinIcon:ad,PlayAllHollowIcon:rd,PlayBackIcon:od,PlayHollowIcon:ld,PlayIcon:id,PlayNextIcon:cd,PlusIcon:sd,PointerDefaultIcon:ud,PointerHandIcon:dd,PowerIcon:md,PrintIcon:pd,ProceedIcon:fd,ProfileIcon:hd,PullRequestIcon:yd,QuestionIcon:bd,RSSIcon:gd,RedirectIcon:Sd,ReduxIcon:vd,RefreshIcon:Ed,ReplyIcon:Id,RepoIcon:xd,RequestChangeIcon:Rd,RewindIcon:Ad,RulerIcon:$d,SaveIcon:Td,SearchIcon:_d,ShareAltIcon:Cd,ShareIcon:Od,ShieldIcon:wd,SideBySideIcon:kd,SidebarAltIcon:Nd,SidebarAltToggleIcon:Pd,SidebarIcon:Dd,SidebarToggleIcon:Md,SpeakerIcon:Ld,StackedIcon:Fd,StarHollowIcon:Hd,StarIcon:Ud,StatusFailIcon:Gd,StatusIcon:Bd,StatusPassIcon:zd,StatusWarnIcon:Wd,StickerIcon:jd,StopAltHollowIcon:Yd,StopAltIcon:Vd,StopIcon:qd,StorybookIcon:Kd,StructureIcon:Qd,SubtractIcon:Xd,SunIcon:Zd,SupportIcon:Jd,SweepIcon:em,SwitchAltIcon:tm,SyncIcon:Me,TabletIcon:nm,ThumbsUpIcon:am,TimeIcon:rm,TimerIcon:om,TransferIcon:lm,TrashIcon:im,TwitterIcon:cm,TypeIcon:sm,UbuntuIcon:um,UndoIcon:dm,UnfoldIcon:mm,UnlockIcon:pm,UnpinIcon:fm,UploadIcon:hm,UserAddIcon:ym,UserAltIcon:bm,UserIcon:gm,UsersIcon:Sm,VSCodeIcon:vm,VerifiedIcon:Em,VideoIcon:Im,WandIcon:xm,WatchIcon:Rm,WindowsIcon:Am,WrenchIcon:$m,XIcon:Tm,YoutubeIcon:_m,ZoomIcon:Cm,ZoomOutIcon:Om,ZoomResetIcon:wm,iconList:km}=__STORYBOOK_ICONS__;T();_();C();var Lm=__STORYBOOK_THEMING__,{CacheProvider:Fm,ClassNames:Hm,Global:_t,ThemeProvider:Um,background:Gm,color:Bm,convert:Ct,create:zm,createCache:Wm,createGlobal:jm,createReset:Ym,css:Vm,darken:qm,ensure:Km,ignoreSsrWarning:Qm,isPropValid:Xm,jsx:Zm,keyframes:Jm,lighten:ep,styled:E,themes:Ot,typography:tp,useTheme:np,withTheme:ap}=__STORYBOOK_THEMING__;T();_();C();var cp=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:sp,ARGTYPES_INFO_RESPONSE:up,CHANNEL_CREATED:dp,CHANNEL_WS_DISCONNECT:mp,CONFIG_ERROR:pp,CREATE_NEW_STORYFILE_REQUEST:fp,CREATE_NEW_STORYFILE_RESPONSE:hp,CURRENT_STORY_WAS_SET:yp,DOCS_PREPARED:bp,DOCS_RENDERED:gp,FILE_COMPONENT_SEARCH_REQUEST:Sp,FILE_COMPONENT_SEARCH_RESPONSE:vp,FORCE_REMOUNT:Ep,FORCE_RE_RENDER:Ip,GLOBALS_UPDATED:xp,NAVIGATE_URL:Rp,PLAY_FUNCTION_THREW_EXCEPTION:Ap,PRELOAD_ENTRIES:$p,PREVIEW_BUILDER_PROGRESS:Tp,PREVIEW_KEYDOWN:_p,REGISTER_SUBSCRIPTION:Cp,REQUEST_WHATS_NEW_DATA:Op,RESET_STORY_ARGS:wp,RESULT_WHATS_NEW_DATA:kp,SAVE_STORY_REQUEST:Np,SAVE_STORY_RESPONSE:Pp,SELECT_STORY:Dp,SET_CONFIG:Mp,SET_CURRENT_STORY:Lp,SET_FILTER:Fp,SET_GLOBALS:Hp,SET_INDEX:Up,SET_STORIES:Gp,SET_WHATS_NEW_CACHE:Bp,SHARED_STATE_CHANGED:zp,SHARED_STATE_SET:Wp,STORIES_COLLAPSE_ALL:jp,STORIES_EXPAND_ALL:Yp,STORY_ARGS_UPDATED:Vp,STORY_CHANGED:wt,STORY_ERRORED:qp,STORY_FINISHED:kt,STORY_HOT_UPDATED:Nt,STORY_INDEX_INVALIDATED:Kp,STORY_MISSING:Qp,STORY_PREPARED:Xp,STORY_RENDERED:Zp,STORY_RENDER_PHASE_CHANGED:Pt,STORY_SPECIFIED:Jp,STORY_THREW_EXCEPTION:ef,STORY_UNCHANGED:tf,TELEMETRY_ERROR:nf,TOGGLE_WHATS_NEW_NOTIFICATIONS:af,UNHANDLED_ERRORS_WHILE_PLAYING:rf,UPDATE_GLOBALS:of,UPDATE_QUERY_PARAMS:lf,UPDATE_STORY_ARGS:cf}=__STORYBOOK_CORE_EVENTS__;T();_();C();var Ae="storybook/highlight",Le=`${Ae}/add`,Fe=`${Ae}/remove`,pf=`${Ae}/reset`,Dt=`${Ae}/scroll-into-view`;T();_();C();var gf=__REACT_DOM__,{__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Sf,createPortal:vf,createRoot:Ef,findDOMNode:Mt,flushSync:Lt,hydrate:If,hydrateRoot:xf,render:Rf,unmountComponentAtNode:Af,unstable_batchedUpdates:$f,unstable_renderSubtreeIntoContainer:Tf,version:_f}=__REACT_DOM__;var W={VIOLATION:"violations",PASS:"passes",INCOMPLETION:"incomplete"},La={"area-alt":{title:"<area> alt text",axeSummary:"Ensure <area> elements of image maps have alternative text",friendlySummary:"Add alt text to all <area> elements of image maps."},"aria-allowed-attr":{title:"Supported ARIA attributes",axeSummary:"Ensure an element's role supports its ARIA attributes",friendlySummary:"Only use ARIA attributes that are permitted for the element's role."},"aria-braille-equivalent":{title:"Braille equivalent",axeSummary:"Ensure aria-braillelabel and aria-brailleroledescription have a non-braille equivalent",friendlySummary:"If you use braille ARIA labels, also provide a matching non-braille label."},"aria-command-name":{title:"ARIA command name",axeSummary:"Ensure every ARIA button, link and menuitem has an accessible name",friendlySummary:"Every ARIA button, link, or menuitem needs a label or accessible name."},"aria-conditional-attr":{title:"ARIA attribute valid for role",axeSummary:"Ensure ARIA attributes are used as described in the specification of the element's role",friendlySummary:"Follow the element role's specification when using ARIA attributes."},"aria-deprecated-role":{title:"Deprecated ARIA role",axeSummary:"Ensure elements do not use deprecated roles",friendlySummary:"Don't use deprecated ARIA roles on elements."},"aria-hidden-body":{title:"Hidden body",axeSummary:'Ensure aria-hidden="true" is not present on the document <body>',friendlySummary:'Never set aria-hidden="true" on the <body> element.'},"aria-hidden-focus":{title:"Hidden element focus",axeSummary:"Ensure aria-hidden elements are not focusable nor contain focusable elements",friendlySummary:"Elements marked hidden (aria-hidden) should not be focusable or contain focusable items."},"aria-input-field-name":{title:"ARIA input field name",axeSummary:"Ensure every ARIA input field has an accessible name",friendlySummary:"Give each ARIA text input or field a label or accessible name."},"aria-meter-name":{title:"ARIA meter name",axeSummary:"Ensure every ARIA meter node has an accessible name",friendlySummary:'Give each element with role="meter" a label or accessible name.'},"aria-progressbar-name":{title:"ARIA progressbar name",axeSummary:"Ensure every ARIA progressbar node has an accessible name",friendlySummary:'Give each element with role="progressbar" a label or accessible name.'},"aria-prohibited-attr":{title:"ARIA prohibited attributes",axeSummary:"Ensure ARIA attributes are not prohibited for an element's role",friendlySummary:"Don't use ARIA attributes that are forbidden for that element's role."},"aria-required-attr":{title:"ARIA required attributes",axeSummary:"Ensure elements with ARIA roles have all required ARIA attributes",friendlySummary:"Include all required ARIA attributes for elements with that ARIA role."},"aria-required-children":{title:"ARIA required children",axeSummary:"Ensure elements with an ARIA role that require child roles contain them",friendlySummary:"If an ARIA role requires specific child roles, include those child elements."},"aria-required-parent":{title:"ARIA required parent",axeSummary:"Ensure elements with an ARIA role that require parent roles are contained by them",friendlySummary:"Place elements with certain ARIA roles inside the required parent role element."},"aria-roles":{title:"ARIA role value",axeSummary:"Ensure all elements with a role attribute use a valid value",friendlySummary:"Use only valid values in the role attribute (no typos or invalid roles)."},"aria-toggle-field-name":{title:"ARIA toggle field name",axeSummary:"Ensure every ARIA toggle field has an accessible name",friendlySummary:"Every ARIA toggle field (elements with the checkbox, radio, or switch roles) needs an accessible name."},"aria-tooltip-name":{title:"ARIA tooltip name",axeSummary:"Ensure every ARIA tooltip node has an accessible name",friendlySummary:'Give each element with role="tooltip" a descriptive accessible name.'},"aria-valid-attr-value":{title:"ARIA attribute values valid",axeSummary:"Ensure all ARIA attributes have valid values",friendlySummary:"Use only valid values for ARIA attributes (no typos or invalid values)."},"aria-valid-attr":{title:"ARIA attribute valid",axeSummary:"Ensure attributes that begin with aria- are valid ARIA attributes",friendlySummary:"Use only valid aria-* attributes (make sure the attribute name is correct)."},blink:{title:"<blink> element",axeSummary:"Ensure <blink> elements are not used",friendlySummary:"Don't use the deprecated <blink> element."},"button-name":{title:"Button name",axeSummary:"Ensure buttons have discernible text",friendlySummary:"Every <button> needs a visible label or accessible name."},bypass:{title:"Navigation bypass",axeSummary:"Ensure each page has at least one mechanism to bypass navigation and jump to content",friendlySummary:'Provide a way to skip repetitive navigation (e.g. a "Skip to content" link).'},"color-contrast":{title:"Color contrast",axeSummary:"Ensure the contrast between foreground and background text meets WCAG 2 AA minimum thresholds",friendlySummary:"The color contrast between text and its background meets WCAG AA contrast ratio."},"definition-list":{title:"Definition list structure",axeSummary:"Ensure <dl> elements are structured correctly",friendlySummary:"Definition lists (<dl>) should directly contain <dt> and <dd> elements in order."},dlitem:{title:"Definition list items",axeSummary:"Ensure <dt> and <dd> elements are contained by a <dl>",friendlySummary:"Ensure <dt> and <dd> elements are contained by a <dl>"},"document-title":{title:"Document title",axeSummary:"Ensure each HTML document contains a non-empty <title> element",friendlySummary:"Include a non-empty <title> element for every page."},"duplicate-id-aria":{title:"Unique id",axeSummary:"Ensure every id attribute value used in ARIA and in labels is unique",friendlySummary:"Every id used for ARIA or form labels should be unique on the page."},"form-field-multiple-labels":{title:"Multiple form field labels",axeSummary:"Ensure a form field does not have multiple <label> elements",friendlySummary:"Don't give a single form field more than one <label>."},"frame-focusable-content":{title:"Focusable frames",axeSummary:'Ensure <frame> and <iframe> with focusable content do not have tabindex="-1"',friendlySummary:`Don't set tabindex="-1" on a <frame> or <iframe> that contains focusable elements.`},"frame-title-unique":{title:"Unique frame title",axeSummary:"Ensure <iframe> and <frame> elements contain a unique title attribute",friendlySummary:"Use a unique title attribute for each <frame> or <iframe> on the page."},"frame-title":{title:"Frame title",axeSummary:"Ensure <iframe> and <frame> elements have an accessible name",friendlySummary:"Every <frame> and <iframe> needs a title or accessible name."},"html-has-lang":{title:"<html> has lang",axeSummary:"Ensure every HTML document has a lang attribute",friendlySummary:"Add a lang attribute to the <html> element."},"html-lang-valid":{title:"<html> lang valid",axeSummary:"Ensure the <html lang> attribute has a valid value",friendlySummary:"Use a valid language code in the <html lang> attribute."},"html-xml-lang-mismatch":{title:"HTML and XML lang mismatch",axeSummary:"Ensure that HTML elements with both lang and xml:lang agree on the page's language",friendlySummary:"If using both lang and xml:lang on <html>, make sure they are the same language."},"image-alt":{title:"Image alt text",axeSummary:"Ensure <img> elements have alternative text or a role of none/presentation",friendlySummary:'Give every image alt text or mark it as decorative with alt="".'},"input-button-name":{title:"Input button name",axeSummary:"Ensure input buttons have discernible text",friendlySummary:'Give each <input type="button"> or similar a clear label (text or aria-label).'},"input-image-alt":{title:"Input image alt",axeSummary:'Ensure <input type="image"> elements have alternative text',friendlySummary:'<input type="image"> must have alt text describing its image.'},label:{title:"Form label",axeSummary:"Ensure every form element has a label",friendlySummary:"Every form field needs an associated label."},"link-in-text-block":{title:"Identifiable links",axeSummary:"Ensure links are distinguishable from surrounding text without relying on color",friendlySummary:"Make sure links are obviously identifiable without relying only on color."},"link-name":{title:"Link name",axeSummary:"Ensure links have discernible text",friendlySummary:"Give each link meaningful text or an aria-label so its purpose is clear."},list:{title:"List structure",axeSummary:"Ensure that lists are structured correctly",friendlySummary:"Use proper list structure. Only use <li> inside <ul> or <ol>."},listitem:{title:"List item",axeSummary:"Ensure <li> elements are used semantically",friendlySummary:"Only use <li> tags inside <ul> or <ol> lists."},marquee:{title:"<marquee> element",axeSummary:"Ensure <marquee> elements are not used",friendlySummary:"Don't use the deprecated <marquee> element."},"meta-refresh":{title:"<meta> refresh",axeSummary:'Ensure <meta http-equiv="refresh"> is not used for delayed refresh',friendlySummary:'Avoid auto-refreshing or redirecting pages using <meta http-equiv="refresh">.'},"meta-viewport":{title:"<meta> viewport scaling",axeSummary:'Ensure <meta name="viewport"> does not disable text scaling and zooming',friendlySummary:`Don't disable user zooming in <meta name="viewport"> to allow scaling.`},"nested-interactive":{title:"Nested interactive controls",axeSummary:"Ensure interactive controls are not nested (nesting causes screen reader/focus issues)",friendlySummary:"Do not nest interactive elements; it can confuse screen readers and keyboard focus."},"no-autoplay-audio":{title:"Autoplaying video",axeSummary:"Ensure <video> or <audio> do not autoplay audio > 3 seconds without a control to stop/mute",friendlySummary:"Don't autoplay audio for more than 3 seconds without providing a way to stop or mute it."},"object-alt":{title:"<object> alt text",axeSummary:"Ensure <object> elements have alternative text",friendlySummary:"Provide alternative text or content for <object> elements."},"role-img-alt":{title:'role="img" alt text',axeSummary:'Ensure elements with role="img" have alternative text',friendlySummary:'Any element with role="img" needs alt text.'},"scrollable-region-focusable":{title:"Scrollable element focusable",axeSummary:"Ensure elements with scrollable content are keyboard-accessible",friendlySummary:"If an area can scroll, ensure it can be focused and scrolled via keyboard."},"select-name":{title:"<select> name",axeSummary:"Ensure <select> elements have an accessible name",friendlySummary:"Give each <select> field a label or other accessible name."},"server-side-image-map":{title:"Server-side image map",axeSummary:"Ensure that server-side image maps are not used",friendlySummary:"Don't use server-side image maps."},"svg-img-alt":{title:"SVG image alt text",axeSummary:"Ensure <svg> images/graphics have accessible text",friendlySummary:'SVG images with role="img" or similar need a text description.'},"td-headers-attr":{title:"Table headers attribute",axeSummary:"Ensure each cell in a table using headers only refers to <th> in that table",friendlySummary:"In tables using the headers attribute, only reference other cells in the same table."},"th-has-data-cells":{title:"<th> has data cell",axeSummary:"Ensure <th> (or header role) elements have data cells they describe",friendlySummary:"Every table header (<th> or header role) should correspond to at least one data cell."},"valid-lang":{title:"Valid lang",axeSummary:"Ensure lang attributes have valid values",friendlySummary:"Use valid language codes in all lang attributes."},"video-caption":{title:"<video> captions",axeSummary:"Ensure <video> elements have captions",friendlySummary:"Provide captions for all <video> content."}},Fa={"autocomplete-valid":{title:"autocomplete attribute valid",axeSummary:"Ensure the autocomplete attribute is correct and suitable for the form field",friendlySummary:"Use valid autocomplete values that match the form field's purpose."},"avoid-inline-spacing":{title:"Forced inline spacing",axeSummary:"Ensure that text spacing set via inline styles can be adjusted with custom CSS",friendlySummary:"Don't lock in text spacing with forced (!important) inline styles\u2014allow user CSS to adjust text spacing."}},Ha={"target-size":{title:"Touch target size",axeSummary:"Ensure touch targets have sufficient size and space",friendlySummary:"Make sure interactive elements are big enough and not too close together for touch."}},Ua={accesskeys:{title:"Unique accesskey",axeSummary:"Ensure every accesskey attribute value is unique",friendlySummary:"Use unique values for all accesskey attributes."},"aria-allowed-role":{title:"Appropriate role value",axeSummary:"Ensure the role attribute has an appropriate value for the element",friendlySummary:"ARIA roles should have a valid value for the element."},"aria-dialog-name":{title:"ARIA dialog name",axeSummary:"Ensure every ARIA dialog and alertdialog has an accessible name",friendlySummary:"Give each ARIA dialog or alertdialog a title or accessible name."},"aria-text":{title:'ARIA role="text"',axeSummary:'Ensure role="text" is used on elements with no focusable descendants',friendlySummary:`Only use role="text" on elements that don't contain focusable elements.`},"aria-treeitem-name":{title:"ARIA treeitem name",axeSummary:"Ensure every ARIA treeitem node has an accessible name",friendlySummary:"Give each ARIA treeitem a label or accessible name."},"empty-heading":{title:"Empty heading",axeSummary:"Ensure headings have discernible text",friendlySummary:"Don't leave heading elements empty or hide them."},"empty-table-header":{title:"Empty table header",axeSummary:"Ensure table headers have discernible text",friendlySummary:"Make sure table header cells have visible text."},"frame-tested":{title:"Test all frames",axeSummary:"Ensure <iframe> and <frame> elements contain the axe-core script",friendlySummary:"Make sure axe-core is injected into all frames or iframes so they are tested."},"heading-order":{title:"Heading order",axeSummary:"Ensure the order of headings is semantically correct (no skipping levels)",friendlySummary:"Use proper heading order (don't skip heading levels)."},"image-redundant-alt":{title:"Redundant image alt text",axeSummary:"Ensure image alternative text is not repeated as nearby text",friendlySummary:"Avoid repeating the same information in both an image's alt text and nearby text."},"label-title-only":{title:"Visible form element label",axeSummary:"Ensure each form element has a visible label (not only title/ARIA)",friendlySummary:"Every form input needs a visible label (not only a title attribute or hidden text)."},"landmark-banner-is-top-level":{title:"Top-level landmark banner",axeSummary:"Ensure the banner landmark is at top level (not nested)",friendlySummary:"Use the banner landmark (e.g. site header) only at the top level of the page, not inside another landmark."},"landmark-complementary-is-top-level":{title:"Top-level <aside>",axeSummary:"Ensure the complementary landmark (<aside>) is top level",friendlySummary:'The complementary landmark <aside> or role="complementary" should be a top-level region, not nested in another landmark.'},"landmark-contentinfo-is-top-level":{title:"Top-level contentinfo",axeSummary:"Ensure the contentinfo landmark (footer) is top level",friendlySummary:"Make sure the contentinfo landmark (footer) is at the top level of the page and not contained in another landmark."},"landmark-main-is-top-level":{title:"Top-level main",axeSummary:"Ensure the main landmark is at top level",friendlySummary:"The main landmark should be a top-level element and not nested inside another landmark."},"landmark-no-duplicate-banner":{title:"Duplicate banner landmark",axeSummary:"Ensure the document has at most one banner landmark",friendlySummary:'Have only one role="banner" or <header> on a page.'},"landmark-no-duplicate-contentinfo":{title:"Duplicate contentinfo",axeSummary:"Ensure the document has at most one contentinfo landmark",friendlySummary:'Have only one role="contentinfo" or <footer> on a page.'},"landmark-no-duplicate-main":{title:"Duplicate main",axeSummary:"Ensure the document has at most one main landmark",friendlySummary:'Have only one role="main" or <main> on a page.'},"landmark-one-main":{title:"main landmark",axeSummary:"Ensure the document has a main landmark",friendlySummary:'Include a main landmark on each page using a <main> region or role="main".'},"landmark-unique":{title:"Unique landmark",axeSummary:"Ensure landmarks have a unique role or role/label combination",friendlySummary:"If you use multiple landmarks of the same type, give them unique labels (names)."},"meta-viewport-large":{title:"Significant viewport scaling",axeSummary:'Ensure <meta name="viewport"> can scale a significant amount (e.g. 500%)',friendlySummary:'<meta name="viewport"> should allow users to significantly scale content.'},"page-has-heading-one":{title:"Has <h1>",axeSummary:"Ensure the page (or at least one frame) contains a level-one heading",friendlySummary:"Every page or frame should have at least one <h1> heading."},"presentation-role-conflict":{title:"Presentational content",axeSummary:'Ensure elements with role="presentation"/"none" have no ARIA or tabindex',friendlySummary:`Don't give elements with role="none"/"presentation" any ARIA attributes or a tabindex.`},region:{title:"Landmark regions",axeSummary:"Ensure all page content is contained by landmarks",friendlySummary:"Wrap all page content in appropriate landmark regions (<header>, <main>, <footer>, etc.)."},"scope-attr-valid":{title:"scope attribute",axeSummary:"Ensure the scope attribute is used correctly on tables",friendlySummary:"Use the scope attribute only on <th> elements, with proper values (col, row, etc.)."},"skip-link":{title:"Skip link",axeSummary:'Ensure all "skip" links have a focusable target',friendlySummary:'Make sure any "skip to content" link targets an existing, focusable element.'},tabindex:{title:"tabindex values",axeSummary:"Ensure tabindex attribute values are not greater than 0",friendlySummary:"Don't use tabindex values greater than 0."},"table-duplicate-name":{title:"Duplicate names for table",axeSummary:"Ensure the <caption> does not duplicate the summary attribute text",friendlySummary:"Don't use the same text in both a table's <caption> and its summary attribute."}},Ga={"color-contrast-enhanced":{title:"Enhanced color contrast",axeSummary:"Ensure contrast between text and background meets WCAG 2 AAA enhanced contrast thresholds",friendlySummary:"Use extra-high contrast for text and background to meet WCAG AAA level."},"identical-links-same-purpose":{title:"Same link name, same purpose",axeSummary:"Ensure links with the same accessible name serve a similar purpose",friendlySummary:"If two links have the same text, they should do the same thing (lead to the same content)."},"meta-refresh-no-exceptions":{title:'No <meta http-equiv="refresh">',axeSummary:'Ensure <meta http-equiv="refresh"> is not used for delayed refresh (no exceptions)',friendlySummary:`Don't auto-refresh or redirect pages using <meta http-equiv="refresh"> even with a delay.`}},Ba={"css-orientation-lock":{title:"CSS orientation lock",axeSummary:"Ensure content is not locked to a specific display orientation (works in all orientations)",friendlySummary:"Don't lock content to one screen orientation; support both portrait and landscape modes."},"focus-order-semantics":{title:"Focus order semantic role",axeSummary:"Ensure elements in the tab order have a role appropriate for interactive content",friendlySummary:"Ensure elements in the tab order have a role appropriate for interactive content"},"hidden-content":{title:"Hidden content",axeSummary:"Informs users about hidden content",friendlySummary:"Display hidden content on the page for test analysis."},"label-content-name-mismatch":{title:"Content name mismatch",axeSummary:"Ensure elements labeled by their content include that text in their accessible name",friendlySummary:"If an element's visible text serves as its label, include that text in its accessible name."},"p-as-heading":{title:"No <p> headings",axeSummary:"Ensure <p> elements aren't styled to look like headings (use real headings)",friendlySummary:"Don't just style a <p> to look like a heading \u2013 use an actual heading tag for headings."},"table-fake-caption":{title:"Table caption",axeSummary:"Ensure that tables with a caption use the <caption> element",friendlySummary:"Use a <caption> element for table captions instead of just styled text."},"td-has-header":{title:"<td> has header",axeSummary:"Ensure each non-empty data cell in large tables (3\xD73+) has one or more headers",friendlySummary:"Every data cell in large tables should be associated with at least one header cell."}},za={"aria-roledescription":{title:"aria-roledescription",axeSummary:"Ensure aria-roledescription is only used on elements with an implicit or explicit role",friendlySummary:"Only use aria-roledescription on elements that already have a defined role."}},en={...La,...Fa,...Ha,...Ga,...Ua,...Ba,...za},je=e=>en[e.id]?.title||e.id,Ye=e=>en[e.id]?.friendlySummary||e.description,B="storybook/a11y",Ve=`${B}/panel`,Wa="a11y",ja=`${B}/result`,Ya=`${B}/request`,Va=`${B}/running`,qa=`${B}/error`,Ka=`${B}/manual`,Qa=`${B}/select`,Xa="writing-tests/accessibility-testing",Za=`${Xa}#why-are-my-tests-failing-in-different-environments`,ce={RESULT:ja,REQUEST:Ya,RUNNING:Va,ERROR:qa,MANUAL:Ka,SELECT:Qa},Ft="storybook/component-test",Ja="storybook/a11y",er=["html","body","main"],He=Ct(Ot.light),Ue={[W.VIOLATION]:He.color.negative,[W.PASS]:He.color.positive,[W.INCOMPLETION]:He.color.warning},tn=pe({parameters:{},results:void 0,highlighted:!1,toggleHighlight:()=>{},tab:W.VIOLATION,handleCopyLink:()=>{},setTab:()=>{},setStatus:()=>{},status:"initial",error:void 0,handleManual:()=>{},discrepancy:null,selectedItems:new Map,allExpanded:!1,toggleOpen:()=>{},handleCollapseAll:()=>{},handleExpandAll:()=>{},handleJumpToElement:()=>{},handleSelectionChange:()=>{}}),tr=e=>{let t=bt("a11y",{}),[a]=yt()??[],n=Re(),r=R((y=!1)=>y?"manual":"initial",[]),i=j(()=>a?.a11y?.manual??!1,[a?.a11y?.manual]),l=j(()=>{let y=n.getQueryParam("a11ySelection");return y&&n.setQueryParams({a11ySelection:""}),y},[n]),[c,d]=De(B),[s,p]=H(()=>{let[y]=l?.split(".")??[];return y&&Object.values(W).includes(y)?y:W.VIOLATION}),[u,f]=H(void 0),[b,h]=H(r(i)),[I,v]=H(!!l),{storyId:g}=gt(),A=ft(y=>y[g]?.[Ja]?.value);G(()=>pt("storybook/component-test").onAllStatusChange((y,O)=>{let S=y[g]?.[Ft],w=O[g]?.[Ft];S?.value==="status-value:error"&&w?.value!=="status-value:error"&&h("component-test-error")}),[g]);let Y=R(()=>v(y=>!y),[]),[k,D]=H(()=>{let y=new Map;if(l&&/^[a-z]+.[a-z-]+.[0-9]+$/.test(l)){let[O,S]=l.split(".");y.set(`${O}.${S}`,l)}return y}),$=j(()=>c?.[s]?.every(y=>k.has(`${s}.${y.id}`))??!1,[c,k,s]),x=R((y,O,S)=>{y.stopPropagation();let w=`${O}.${S.id}`;D(M=>new Map(M.delete(w)?M:M.set(w,`${w}.1`)))},[]),F=R(()=>{D(new Map)},[]),q=R(()=>{D(y=>new Map(c?.[s]?.map(O=>{let S=`${s}.${O.id}`;return[S,y.get(S)??`${S}.1`]})??[]))},[c,s]),te=R(y=>{let[O,S]=y.split(".");D(w=>new Map(w.set(`${O}.${S}`,y)))},[]),ne=R(y=>{h("error"),f(y)},[]),ae=R((y,O)=>{g===O&&(h("ran"),d(y),setTimeout(()=>{if(b==="ran"&&h("ready"),k.size===1){let[S]=k.values();document.getElementById(S)?.scrollIntoView({behavior:"smooth",block:"center"})}},900))},[d,b,g,k]),se=R((y,O)=>{let[S,w]=y.split("."),{helpUrl:M,nodes:ye}=c?.[S]?.find(ue=>ue.id===w)||{},be=M&&window.open(M,"_blank","noopener,noreferrer");if(ye&&!be){let ue=ye.findIndex(ge=>O.selectors.some(wn=>wn===String(ge.target)))??-1;if(ue!==-1){let ge=`${S}.${w}.${ue+1}`;D(new Map([[`${S}.${w}`,ge]])),setTimeout(()=>{document.getElementById(ge)?.scrollIntoView({behavior:"smooth",block:"center"})},100)}}},[c]),K=R(({reporters:y})=>{let O=y.find(S=>S.type==="a11y");O&&("error"in O.result?ne(O.result.error):ae(O.result,g))},[ne,ae,g]),it=R(({newPhase:y})=>{y==="loading"&&(d(void 0),h(i?"manual":"initial")),y==="afterEach"&&!i&&h("running")},[i,d]),Q=ht({[ce.RESULT]:ae,[ce.ERROR]:ne,[ce.SELECT]:se,[wt]:()=>D(new Map),[Pt]:it,[kt]:K,[Nt]:()=>{h("running"),Q(ce.MANUAL,g,t)}},[it,K,se,ne,ae,t,g]),Tn=R(()=>{h("running"),Q(ce.MANUAL,g,t)},[Q,t,g]),_n=R(async y=>{let{createCopyToClipboardFunction:O}=await Promise.resolve().then(()=>(Ne(),mt));await O()(`${window.location.origin}${y}`)},[]),Cn=R(y=>Q(Dt,y),[Q]);G(()=>{h(r(i))},[r,i]),G(()=>{if(Q(Fe,`${B}/selected`),Q(Fe,`${B}/others`),!I)return;let y=Array.from(k.values()).flatMap(S=>{let[w,M,ye]=S.split(".");if(w!==s)return[];let be=c?.[w]?.find(ue=>ue.id===M)?.nodes[Number(ye)-1]?.target;return be?[String(be)]:[]});Q(Le,{id:`${B}/selected`,priority:1,selectors:y,styles:{outline:`1px solid color-mix(in srgb, ${Ue[s]}, transparent 30%)`,backgroundColor:"transparent"},hoverStyles:{outlineWidth:"2px"},focusStyles:{backgroundColor:"transparent"},menu:c?.[s].map(S=>{let w=S.nodes.flatMap(M=>M.target).map(String).filter(M=>y.includes(M));return[{id:`${s}.${S.id}:info`,title:je(S),description:Ye(S),selectors:w},{id:`${s}.${S.id}`,iconLeft:"info",iconRight:"shareAlt",title:"Learn how to resolve this violation",clickEvent:ce.SELECT,selectors:w}]})});let O=c?.[s].flatMap(S=>S.nodes.flatMap(w=>w.target).map(String)).filter(S=>![...er,...y].includes(S));Q(Le,{id:`${B}/others`,selectors:O,styles:{outline:`1px solid color-mix(in srgb, ${Ue[s]}, transparent 30%)`,backgroundColor:`color-mix(in srgb, ${Ue[s]}, transparent 60%)`},hoverStyles:{outlineWidth:"2px"},focusStyles:{backgroundColor:"transparent"},menu:c?.[s].map(S=>{let w=S.nodes.flatMap(M=>M.target).map(String).filter(M=>!y.includes(M));return[{id:`${s}.${S.id}:info`,title:je(S),description:Ye(S),selectors:w},{id:`${s}.${S.id}`,iconLeft:"info",iconRight:"shareAlt",title:"Learn how to resolve this violation",clickEvent:ce.SELECT,selectors:w}]})})},[Q,I,c,s,k]);let On=j(()=>{if(!A)return null;if(A==="status-value:success"&&c?.violations.length)return"cliPassedBrowserFailed";if(A==="status-value:error"&&!c?.violations.length){if(b==="ready"||b==="ran")return"browserPassedCliFailed";if(b==="manual")return"cliFailedButModeManual"}return null},[c?.violations.length,b,A]);return o.createElement(tn.Provider,{value:{parameters:t,results:c,highlighted:I,toggleHighlight:Y,tab:s,setTab:p,handleCopyLink:_n,status:b,setStatus:h,error:u,handleManual:Tn,discrepancy:On,selectedItems:k,toggleOpen:x,allExpanded:$,handleCollapseAll:F,handleExpandAll:q,handleJumpToElement:Cn,handleSelectionChange:te},...e})},nt=()=>ve(tn);function N(){return N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)({}).hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e},N.apply(null,arguments)}function ee(e,t,{checkForDefaultPrevented:a=!0}={}){return function(n){if(e?.(n),a===!1||!n.defaultPrevented)return t?.(n)}}function at(e,t=[]){let a=[];function n(i,l){let c=pe(l),d=a.length;a=[...a,l];function s(u){let{scope:f,children:b,...h}=u,I=f?.[e][d]||c,v=j(()=>h,Object.values(h));return m(I.Provider,{value:v},b)}function p(u,f){let b=f?.[e][d]||c,h=ve(b);if(h)return h;if(l!==void 0)return l;throw new Error(`\`${u}\` must be used within \`${i}\``)}return s.displayName=i+"Provider",[s,p]}let r=()=>{let i=a.map(l=>pe(l));return function(l){let c=l?.[e]||i;return j(()=>({[`__scope${e}`]:{...l,[e]:c}}),[l,c])}};return r.scopeName=e,[n,nr(r,...t)]}function nr(...e){let t=e[0];if(e.length===1)return t;let a=()=>{let n=e.map(r=>({useScope:r(),scopeName:r.scopeName}));return function(r){let i=n.reduce((l,{useScope:c,scopeName:d})=>{let s=c(r)[`__scope${d}`];return{...l,...s}},{});return j(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return a.scopeName=t.scopeName,a}function ar(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function nn(...e){return t=>e.forEach(a=>ar(a,t))}function Ht(...e){return R(nn(...e),e)}var qe=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(or);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Ke,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Ke,N({},n,{ref:t}),a)});qe.displayName="Slot";var Ke=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...lr(n,a.props),ref:t?nn(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Ke.displayName="SlotClone";var rr=({children:e})=>m(me,null,e);function or(e){return U(e)&&e.type===rr}function lr(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}function ir(e){let t=e+"CollectionProvider",[a,n]=at(t),[r,i]=a(t,{collectionRef:{current:null},itemMap:new Map}),l=b=>{let{scope:h,children:I}=b,v=o.useRef(null),g=o.useRef(new Map).current;return o.createElement(r,{scope:h,itemMap:g,collectionRef:v},I)},c=e+"CollectionSlot",d=o.forwardRef((b,h)=>{let{scope:I,children:v}=b,g=i(c,I),A=Ht(h,g.collectionRef);return o.createElement(qe,{ref:A},v)}),s=e+"CollectionItemSlot",p="data-radix-collection-item",u=o.forwardRef((b,h)=>{let{scope:I,children:v,...g}=b,A=o.useRef(null),Y=Ht(h,A),k=i(s,I);return o.useEffect(()=>(k.itemMap.set(A,{ref:A,...g}),()=>void k.itemMap.delete(A))),o.createElement(qe,{[p]:"",ref:Y},v)});function f(b){let h=i(e+"CollectionConsumer",b);return o.useCallback(()=>{let I=h.collectionRef.current;if(!I)return[];let v=Array.from(I.querySelectorAll(`[${p}]`));return Array.from(h.itemMap.values()).sort((g,A)=>v.indexOf(g.ref.current)-v.indexOf(A.ref.current))},[h.collectionRef,h.itemMap])}return[{Provider:l,Slot:d,ItemSlot:u},f,n]}function cr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function an(...e){return t=>e.forEach(a=>cr(a,t))}function sr(...e){return R(an(...e),e)}var ur=globalThis?.document?Ee:()=>{},dr=ut||(()=>{}),mr=0;function rn(e){let[t,a]=H(dr());return ur(()=>{e||a(n=>n??String(mr++))},[e]),e||(t?`radix-${t}`:"")}var on=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(fr);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Qe,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Qe,N({},n,{ref:t}),a)});on.displayName="Slot";var Qe=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...hr(n,a.props),ref:t?an(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Qe.displayName="SlotClone";var pr=({children:e})=>m(me,null,e);function fr(e){return U(e)&&e.type===pr}function hr(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}var yr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ln=yr.reduce((e,t)=>{let a=L((n,r)=>{let{asChild:i,...l}=n,c=i?on:t;return G(()=>{window[Symbol.for("radix-ui")]=!0},[]),m(c,N({},l,{ref:r}))});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function br(e){let t=z(e);return G(()=>{t.current=e}),j(()=>(...a)=>{var n;return(n=t.current)===null||n===void 0?void 0:n.call(t,...a)},[])}function cn(e){let t=z(e);return G(()=>{t.current=e}),j(()=>(...a)=>{var n;return(n=t.current)===null||n===void 0?void 0:n.call(t,...a)},[])}function sn({prop:e,defaultProp:t,onChange:a=()=>{}}){let[n,r]=gr({defaultProp:t,onChange:a}),i=e!==void 0,l=i?e:n,c=cn(a),d=R(s=>{if(i){let p=typeof s=="function"?s(e):s;p!==e&&c(p)}else r(s)},[i,e,r,c]);return[l,d]}function gr({defaultProp:e,onChange:t}){let a=H(e),[n]=a,r=z(n),i=cn(t);return G(()=>{r.current!==n&&(i(n),r.current=n)},[n,r,i]),a}var Sr=pe(void 0);function un(e){let t=ve(Sr);return e||t||"ltr"}var Ge="rovingFocusGroup.onEntryFocus",vr={bubbles:!1,cancelable:!0},rt="RovingFocusGroup",[Xe,dn,Er]=ir(rt),[Ir,mn]=at(rt,[Er]),[xr,Rr]=Ir(rt),Ar=L((e,t)=>m(Xe.Provider,{scope:e.__scopeRovingFocusGroup},m(Xe.Slot,{scope:e.__scopeRovingFocusGroup},m($r,N({},e,{ref:t}))))),$r=L((e,t)=>{let{__scopeRovingFocusGroup:a,orientation:n,loop:r=!1,dir:i,currentTabStopId:l,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:d,onEntryFocus:s,...p}=e,u=z(null),f=sr(t,u),b=un(i),[h=null,I]=sn({prop:l,defaultProp:c,onChange:d}),[v,g]=H(!1),A=br(s),Y=dn(a),k=z(!1),[D,$]=H(0);return G(()=>{let x=u.current;if(x)return x.addEventListener(Ge,A),()=>x.removeEventListener(Ge,A)},[A]),m(xr,{scope:a,orientation:n,dir:b,loop:r,currentTabStopId:h,onItemFocus:R(x=>I(x),[I]),onItemShiftTab:R(()=>g(!0),[]),onFocusableItemAdd:R(()=>$(x=>x+1),[]),onFocusableItemRemove:R(()=>$(x=>x-1),[])},m(ln.div,N({tabIndex:v||D===0?-1:0,"data-orientation":n},p,{ref:f,style:{outline:"none",...e.style},onMouseDown:ee(e.onMouseDown,()=>{k.current=!0}),onFocus:ee(e.onFocus,x=>{let F=!k.current;if(x.target===x.currentTarget&&F&&!v){let q=new CustomEvent(Ge,vr);if(x.currentTarget.dispatchEvent(q),!q.defaultPrevented){let te=Y().filter(K=>K.focusable),ne=te.find(K=>K.active),ae=te.find(K=>K.id===h),se=[ne,ae,...te].filter(Boolean).map(K=>K.ref.current);pn(se)}}k.current=!1}),onBlur:ee(e.onBlur,()=>g(!1))})))}),Tr="RovingFocusGroupItem",_r=L((e,t)=>{let{__scopeRovingFocusGroup:a,focusable:n=!0,active:r=!1,tabStopId:i,...l}=e,c=rn(),d=i||c,s=Rr(Tr,a),p=s.currentTabStopId===d,u=dn(a),{onFocusableItemAdd:f,onFocusableItemRemove:b}=s;return G(()=>{if(n)return f(),()=>b()},[n,f,b]),m(Xe.ItemSlot,{scope:a,id:d,focusable:n,active:r},m(ln.span,N({tabIndex:p?0:-1,"data-orientation":s.orientation},l,{ref:t,onMouseDown:ee(e.onMouseDown,h=>{n?s.onItemFocus(d):h.preventDefault()}),onFocus:ee(e.onFocus,()=>s.onItemFocus(d)),onKeyDown:ee(e.onKeyDown,h=>{if(h.key==="Tab"&&h.shiftKey){s.onItemShiftTab();return}if(h.target!==h.currentTarget)return;let I=wr(h,s.orientation,s.dir);if(I!==void 0){h.preventDefault();let v=u().filter(g=>g.focusable).map(g=>g.ref.current);if(I==="last")v.reverse();else if(I==="prev"||I==="next"){I==="prev"&&v.reverse();let g=v.indexOf(h.currentTarget);v=s.loop?kr(v,g+1):v.slice(g+1)}setTimeout(()=>pn(v))}})})))}),Cr={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function Or(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function wr(e,t,a){let n=Or(e.key,a);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(n))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(n)))return Cr[n]}function pn(e){let t=document.activeElement;for(let a of e)if(a===t||(a.focus(),document.activeElement!==t))return}function kr(e,t){return e.map((a,n)=>e[(t+n)%e.length])}var Nr=Ar,Pr=_r;function Dr(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Mr(...e){return t=>e.forEach(a=>Dr(a,t))}function Lr(...e){return R(Mr(...e),e)}var Ut=globalThis?.document?Ee:()=>{};function Fr(e,t){return dt((a,n)=>t[a][n]??a,e)}var fn=e=>{let{present:t,children:a}=e,n=Hr(t),r=typeof a=="function"?a({present:n.isPresent}):P.only(a),i=Lr(n.ref,r.ref);return typeof a=="function"||n.isPresent?V(r,{ref:i}):null};fn.displayName="Presence";function Hr(e){let[t,a]=H(),n=z({}),r=z(e),i=z("none"),l=e?"mounted":"unmounted",[c,d]=Fr(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return G(()=>{let s=$e(n.current);i.current=c==="mounted"?s:"none"},[c]),Ut(()=>{let s=n.current,p=r.current;if(p!==e){let u=i.current,f=$e(s);e?d("MOUNT"):f==="none"||s?.display==="none"?d("UNMOUNT"):d(p&&u!==f?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,d]),Ut(()=>{if(t){let s=u=>{let f=$e(n.current).includes(u.animationName);u.target===t&&f&&Lt(()=>d("ANIMATION_END"))},p=u=>{u.target===t&&(i.current=$e(n.current))};return t.addEventListener("animationstart",p),t.addEventListener("animationcancel",s),t.addEventListener("animationend",s),()=>{t.removeEventListener("animationstart",p),t.removeEventListener("animationcancel",s),t.removeEventListener("animationend",s)}}else d("ANIMATION_END")},[t,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:R(s=>{s&&(n.current=getComputedStyle(s)),a(s)},[])}}function $e(e){return e?.animationName||"none"}function Ur(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function Gr(...e){return t=>e.forEach(a=>Ur(a,t))}var hn=L((e,t)=>{let{children:a,...n}=e,r=P.toArray(a),i=r.find(zr);if(i){let l=i.props.children,c=r.map(d=>d===i?P.count(l)>1?P.only(null):U(l)?l.props.children:null:d);return m(Ze,N({},n,{ref:t}),U(l)?V(l,void 0,c):null)}return m(Ze,N({},n,{ref:t}),a)});hn.displayName="Slot";var Ze=L((e,t)=>{let{children:a,...n}=e;return U(a)?V(a,{...Wr(n,a.props),ref:t?Gr(t,a.ref):a.ref}):P.count(a)>1?P.only(null):null});Ze.displayName="SlotClone";var Br=({children:e})=>m(me,null,e);function zr(e){return U(e)&&e.type===Br}function Wr(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...l)=>{i(...l),r(...l)}:r&&(a[n]=r):n==="style"?a[n]={...r,...i}:n==="className"&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}var jr=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],_e=jr.reduce((e,t)=>{let a=L((n,r)=>{let{asChild:i,...l}=n,c=i?hn:t;return G(()=>{window[Symbol.for("radix-ui")]=!0},[]),m(c,N({},l,{ref:r}))});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),yn="Tabs",[Yr,Gf]=at(yn,[mn]),bn=mn(),[Vr,ot]=Yr(yn),qr=L((e,t)=>{let{__scopeTabs:a,value:n,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:c,activationMode:d="automatic",...s}=e,p=un(c),[u,f]=sn({prop:n,onChange:r,defaultProp:i});return m(Vr,{scope:a,baseId:rn(),value:u,onValueChange:f,orientation:l,dir:p,activationMode:d},m(_e.div,N({dir:p,"data-orientation":l},s,{ref:t})))}),Kr="TabsList",Qr=L((e,t)=>{let{__scopeTabs:a,loop:n=!0,...r}=e,i=ot(Kr,a),l=bn(a);return m(Nr,N({asChild:!0},l,{orientation:i.orientation,dir:i.dir,loop:n}),m(_e.div,N({role:"tablist","aria-orientation":i.orientation},r,{ref:t})))}),Xr="TabsTrigger",Zr=L((e,t)=>{let{__scopeTabs:a,value:n,disabled:r=!1,...i}=e,l=ot(Xr,a),c=bn(a),d=gn(l.baseId,n),s=Sn(l.baseId,n),p=n===l.value;return m(Pr,N({asChild:!0},c,{focusable:!r,active:p}),m(_e.button,N({type:"button",role:"tab","aria-selected":p,"aria-controls":s,"data-state":p?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:d},i,{ref:t,onMouseDown:ee(e.onMouseDown,u=>{!r&&u.button===0&&u.ctrlKey===!1?l.onValueChange(n):u.preventDefault()}),onKeyDown:ee(e.onKeyDown,u=>{[" ","Enter"].includes(u.key)&&l.onValueChange(n)}),onFocus:ee(e.onFocus,()=>{let u=l.activationMode!=="manual";!p&&!r&&u&&l.onValueChange(n)})})))}),Jr="TabsContent",eo=L((e,t)=>{let{__scopeTabs:a,value:n,forceMount:r,children:i,...l}=e,c=ot(Jr,a),d=gn(c.baseId,n),s=Sn(c.baseId,n),p=n===c.value,u=z(p);return G(()=>{let f=requestAnimationFrame(()=>u.current=!1);return()=>cancelAnimationFrame(f)},[]),m(fn,{present:r||p},({present:f})=>m(_e.div,N({"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!f,id:s,tabIndex:0},l,{ref:t,style:{...e.style,animationDuration:u.current?"0s":void 0}}),f&&i))});function gn(e,t){return`${e}-trigger-${t}`}function Sn(e,t){return`${e}-content-${t}`}var to=qr,no=Qr,ao=Zr,Gt=eo,Bt=E(we)(({theme:e})=>({fontSize:e.typography.size.s1}),({language:e})=>e==="css"&&{".selector ~ span:nth-last-of-type(-n+3)":{display:"none"}}),ro=E.div({display:"flex",flexDirection:"column"}),oo=E.div(({theme:e})=>({display:"block",color:e.textMutedColor,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,marginTop:-8,marginBottom:12,"@container (min-width: 800px)":{display:"none"}})),lo=E.p({margin:0}),io=E.div({display:"flex",flexDirection:"column",padding:"0 15px 20px 15px",gap:20}),co=E.div({gap:15,"@container (min-width: 800px)":{display:"grid",gridTemplateColumns:"50% 50%"}}),zt=E.div(({theme:e,side:t})=>({display:t==="left"?"flex":"none",flexDirection:"column",gap:15,margin:t==="left"?"15px 0":0,padding:t==="left"?"0 15px":0,borderLeft:t==="left"?`1px solid ${e.color.border}`:"none","&:focus-visible":{outline:"none",borderRadius:4,boxShadow:`0 0 0 1px inset ${e.color.secondary}`},"@container (min-width: 800px)":{display:t==="left"?"none":"flex"}})),so=E(re)(({theme:e})=>({fontFamily:e.typography.fonts.mono,fontWeight:e.typography.weight.regular,color:e.textMutedColor,height:40,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block",width:"100%",textAlign:"left",padding:"0 12px",'&[data-state="active"]':{color:e.color.secondary,backgroundColor:e.background.hoverable}})),uo=E.div({display:"flex",flexDirection:"column",gap:10}),mo=E.div({display:"flex",gap:10}),po=({onClick:e})=>{let[t,a]=H(!1),n=R(()=>{e(),a(!0);let r=setTimeout(()=>a(!1),2e3);return()=>clearTimeout(r)},[e]);return o.createElement(re,{onClick:n},t?o.createElement(vt,null):o.createElement(xt,null)," ",t?"Copied":"Copy link")},fo=({id:e,item:t,type:a,selection:n,handleSelectionChange:r})=>o.createElement(io,{id:e},o.createElement(ro,null,o.createElement(oo,null,t.id),o.createElement(lo,null,Ye(t)," ",o.createElement(Ie,{href:t.helpUrl,target:"_blank",rel:"noopener noreferrer",withArrow:!0},"Learn how to resolve this violation"))),o.createElement(to,{defaultValue:n,orientation:"vertical",value:n,onValueChange:r,asChild:!0},o.createElement(co,null,o.createElement(no,{"aria-label":a},t.nodes.map((i,l)=>{let c=`${a}.${t.id}.${l+1}`;return o.createElement(me,{key:c},o.createElement(ao,{value:c,asChild:!0},o.createElement(so,{variant:"ghost",size:"medium",id:c},l+1,". ",i.html)),o.createElement(Gt,{value:c,asChild:!0},o.createElement(zt,{side:"left"},Wt(i))))})),t.nodes.map((i,l)=>{let c=`${a}.${t.id}.${l+1}`;return o.createElement(Gt,{key:c,value:c,asChild:!0},o.createElement(zt,{side:"right"},Wt(i)))}))));function Wt(e){let{handleCopyLink:t,handleJumpToElement:a}=nt(),{any:n,all:r,none:i,html:l,target:c}=e,d=[...n,...r,...i];return o.createElement(o.Fragment,null,o.createElement(uo,null,d.map(s=>o.createElement("div",{key:s.id},`${s.message}${/(\.|: [^.]+\.*)$/.test(s.message)?"":"."}`))),o.createElement(mo,null,o.createElement(re,{onClick:()=>a(e.target.toString())},o.createElement(Tt,null)," Jump to element"),o.createElement(po,{onClick:()=>t(e.linkPath)})),o.createElement(Bt,{language:"jsx",wrapLongLines:!0},`/* element */
${l}`),o.createElement(Bt,{language:"css",wrapLongLines:!0},`/* selector */
${c} {}`))}var ho={minor:"neutral",moderate:"warning",serious:"negative",critical:"critical"},yo={minor:"Minor",moderate:"Moderate",serious:"Serious",critical:"Critical"},bo=E.div(({theme:e})=>({display:"flex",flexDirection:"column",width:"100%",borderBottom:`1px solid ${e.appBorderColor}`,containerType:"inline-size",fontSize:e.typography.size.s2})),go=E(Et)({transition:"transform 0.1s ease-in-out"}),So=E.div(({theme:e})=>({display:"flex",justifyContent:"space-between",alignItems:"center",gap:6,padding:"6px 10px 6px 15px",minHeight:40,background:"none",color:"inherit",textAlign:"left",cursor:"pointer",width:"100%","&:hover":{color:e.color.secondary}})),vo=E.div(({theme:e})=>({display:"flex",alignItems:"baseline",flexGrow:1,fontSize:e.typography.size.s2,gap:8})),Eo=E.div(({theme:e})=>({display:"none",color:e.textMutedColor,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,"@container (min-width: 800px)":{display:"block"}})),Io=E.div(({theme:e})=>({display:"flex",alignItems:"center",justifyContent:"center",color:e.textMutedColor,width:28,height:28})),Be=({items:e,empty:t,type:a,handleSelectionChange:n,selectedItems:r,toggleOpen:i})=>o.createElement(o.Fragment,null,e&&e.length?e.map(l=>{let c=`${a}.${l.id}`,d=`details:${c}`,s=r.get(c),p=je(l);return o.createElement(bo,{key:c},o.createElement(So,{onClick:u=>i(u,a,l),"data-active":!!s},o.createElement(vo,null,o.createElement("strong",null,p),o.createElement(Eo,null,l.id)),l.impact&&o.createElement(le,{status:a===W.PASS?"neutral":ho[l.impact]},yo[l.impact]),o.createElement(Io,null,l.nodes.length),o.createElement(ie,{onClick:u=>i(u,a,l),"aria-label":`${s?"Collapse":"Expand"} details for ${p}`,"aria-expanded":!!s,"aria-controls":d},o.createElement(go,{style:{transform:`rotate(${s?-180:0}deg)`}}))),s?o.createElement(fo,{id:d,item:l,type:a,selection:s,handleSelectionChange:n}):o.createElement("div",{id:d}))}):o.createElement(Ce,{title:t})),Je=function(e,t){return Je=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,n){a.__proto__=n}||function(a,n){for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(a[r]=n[r])},Je(e,t)};function xo(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");Je(e,t);function a(){this.constructor=e}e.prototype=t===null?Object.create(t):(a.prototype=t.prototype,new a)}var et=function(){return et=Object.assign||function(e){for(var t,a=1,n=arguments.length;a<n;a++){t=arguments[a];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},et.apply(this,arguments)};function Ro(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(a[n[r]]=e[n[r]]);return a}var Te=typeof globalThis<"u"?globalThis:typeof window<"u"||typeof window<"u"?window:typeof self<"u"?self:{};function Ao(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}var lt=Ao,$o=typeof Te=="object"&&Te&&Te.Object===Object&&Te,To=$o,_o=To,Co=typeof self=="object"&&self&&self.Object===Object&&self,Oo=_o||Co||Function("return this")(),vn=Oo,wo=vn,ko=function(){return wo.Date.now()},No=ko,Po=/\s/;function Do(e){for(var t=e.length;t--&&Po.test(e.charAt(t)););return t}var Mo=Do,Lo=Mo,Fo=/^\s+/;function Ho(e){return e&&e.slice(0,Lo(e)+1).replace(Fo,"")}var Uo=Ho,Go=vn,Bo=Go.Symbol,En=Bo,jt=En,In=Object.prototype,zo=In.hasOwnProperty,Wo=In.toString,he=jt?jt.toStringTag:void 0;function jo(e){var t=zo.call(e,he),a=e[he];try{e[he]=void 0;var n=!0}catch{}var r=Wo.call(e);return n&&(t?e[he]=a:delete e[he]),r}var Yo=jo,Vo=Object.prototype,qo=Vo.toString;function Ko(e){return qo.call(e)}var Qo=Ko,Yt=En,Xo=Yo,Zo=Qo,Jo="[object Null]",el="[object Undefined]",Vt=Yt?Yt.toStringTag:void 0;function tl(e){return e==null?e===void 0?el:Jo:Vt&&Vt in Object(e)?Xo(e):Zo(e)}var nl=tl;function al(e){return e!=null&&typeof e=="object"}var rl=al,ol=nl,ll=rl,il="[object Symbol]";function cl(e){return typeof e=="symbol"||ll(e)&&ol(e)==il}var sl=cl,ul=Uo,qt=lt,dl=sl,Kt=NaN,ml=/^[-+]0x[0-9a-f]+$/i,pl=/^0b[01]+$/i,fl=/^0o[0-7]+$/i,hl=parseInt;function yl(e){if(typeof e=="number")return e;if(dl(e))return Kt;if(qt(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=qt(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ul(e);var a=pl.test(e);return a||fl.test(e)?hl(e.slice(2),a?2:8):ml.test(e)?Kt:+e}var bl=yl,gl=lt,ze=No,Qt=bl,Sl="Expected a function",vl=Math.max,El=Math.min;function Il(e,t,a){var n,r,i,l,c,d,s=0,p=!1,u=!1,f=!0;if(typeof e!="function")throw new TypeError(Sl);t=Qt(t)||0,gl(a)&&(p=!!a.leading,u="maxWait"in a,i=u?vl(Qt(a.maxWait)||0,t):i,f="trailing"in a?!!a.trailing:f);function b($){var x=n,F=r;return n=r=void 0,s=$,l=e.apply(F,x),l}function h($){return s=$,c=setTimeout(g,t),p?b($):l}function I($){var x=$-d,F=$-s,q=t-x;return u?El(q,i-F):q}function v($){var x=$-d,F=$-s;return d===void 0||x>=t||x<0||u&&F>=i}function g(){var $=ze();if(v($))return A($);c=setTimeout(g,I($))}function A($){return c=void 0,f&&n?b($):(n=r=void 0,l)}function Y(){c!==void 0&&clearTimeout(c),s=0,n=d=r=c=void 0}function k(){return c===void 0?l:A(ze())}function D(){var $=ze(),x=v($);if(n=arguments,r=this,d=$,x){if(c===void 0)return h(d);if(u)return clearTimeout(c),c=setTimeout(g,t),b(d)}return c===void 0&&(c=setTimeout(g,t)),l}return D.cancel=Y,D.flush=k,D}var xn=Il,xl=xn,Rl=lt,Al="Expected a function";function $l(e,t,a){var n=!0,r=!0;if(typeof e!="function")throw new TypeError(Al);return Rl(a)&&(n="leading"in a?!!a.leading:n,r="trailing"in a?!!a.trailing:r),xl(e,t,{leading:n,maxWait:t,trailing:r})}var Tl=$l,Rn=function(e,t,a,n){switch(t){case"debounce":return xn(e,a,n);case"throttle":return Tl(e,a,n);default:return e}},tt=function(e){return typeof e=="function"},oe=function(){return typeof window>"u"},Xt=function(e){return e instanceof Element||e instanceof HTMLDocument},An=function(e,t,a,n){return function(r){var i=r.width,l=r.height;t(function(c){return c.width===i&&c.height===l||c.width===i&&!n||c.height===l&&!a?c:(e&&tt(e)&&e(i,l),{width:i,height:l})})}};(function(e){xo(t,e);function t(a){var n=e.call(this,a)||this;n.cancelHandler=function(){n.resizeHandler&&n.resizeHandler.cancel&&(n.resizeHandler.cancel(),n.resizeHandler=null)},n.attachObserver=function(){var s=n.props,p=s.targetRef,u=s.observerOptions;if(!oe()){p&&p.current&&(n.targetRef.current=p.current);var f=n.getElement();f&&(n.observableElement&&n.observableElement===f||(n.observableElement=f,n.resizeObserver.observe(f,u)))}},n.getElement=function(){var s=n.props,p=s.querySelector,u=s.targetDomEl;if(oe())return null;if(p)return document.querySelector(p);if(u&&Xt(u))return u;if(n.targetRef&&Xt(n.targetRef.current))return n.targetRef.current;var f=Mt(n);if(!f)return null;var b=n.getRenderType();switch(b){case"renderProp":return f;case"childFunction":return f;case"child":return f;case"childArray":return f;default:return f.parentElement}},n.createResizeHandler=function(s){var p=n.props,u=p.handleWidth,f=u===void 0?!0:u,b=p.handleHeight,h=b===void 0?!0:b,I=p.onResize;if(!(!f&&!h)){var v=An(I,n.setState.bind(n),f,h);s.forEach(function(g){var A=g&&g.contentRect||{},Y=A.width,k=A.height,D=!n.skipOnMount&&!oe();D&&v({width:Y,height:k}),n.skipOnMount=!1})}},n.getRenderType=function(){var s=n.props,p=s.render,u=s.children;return tt(p)?"renderProp":tt(u)?"childFunction":U(u)?"child":Array.isArray(u)?"childArray":"parent"};var r=a.skipOnMount,i=a.refreshMode,l=a.refreshRate,c=l===void 0?1e3:l,d=a.refreshOptions;return n.state={width:void 0,height:void 0},n.skipOnMount=r,n.targetRef=st(),n.observableElement=null,oe()||(n.resizeHandler=Rn(n.createResizeHandler,i,c,d),n.resizeObserver=new window.ResizeObserver(n.resizeHandler)),n}return t.prototype.componentDidMount=function(){this.attachObserver()},t.prototype.componentDidUpdate=function(){this.attachObserver()},t.prototype.componentWillUnmount=function(){oe()||(this.observableElement=null,this.resizeObserver.disconnect(),this.cancelHandler())},t.prototype.render=function(){var a=this.props,n=a.render,r=a.children,i=a.nodeType,l=i===void 0?"div":i,c=this.state,d=c.width,s=c.height,p={width:d,height:s,targetRef:this.targetRef},u=this.getRenderType(),f;switch(u){case"renderProp":return n&&n(p);case"childFunction":return f=r,f(p);case"child":if(f=r,f.type&&typeof f.type=="string"){p.targetRef;var b=Ro(p,["targetRef"]);return V(f,b)}return V(f,p);case"childArray":return f=r,f.map(function(h){return!!h&&V(h,p)});default:return m(l,null)}},t})(ct);var _l=oe()?G:Ee;function Cl(e){e===void 0&&(e={});var t=e.skipOnMount,a=t===void 0?!1:t,n=e.refreshMode,r=e.refreshRate,i=r===void 0?1e3:r,l=e.refreshOptions,c=e.handleWidth,d=c===void 0?!0:c,s=e.handleHeight,p=s===void 0?!0:s,u=e.targetRef,f=e.observerOptions,b=e.onResize,h=z(a),I=z(null),v=u??I,g=z(),A=H({width:void 0,height:void 0}),Y=A[0],k=A[1];return _l(function(){if(!oe()){var D=An(b,k,d,p),$=function(F){!d&&!p||F.forEach(function(q){var te=q&&q.contentRect||{},ne=te.width,ae=te.height,se=!h.current&&!oe();se&&D({width:ne,height:ae}),h.current=!1})};g.current=Rn($,n,i,l);var x=new window.ResizeObserver(g.current);return v.current&&x.observe(v.current,f),function(){x.disconnect();var F=g.current;F&&F.cancel&&F.cancel()}}},[n,i,l,d,p,b,f,v.current]),et({ref:v},Y)}var Ol=E.div({width:"100%",position:"relative",minHeight:"100%"}),wl=E.button(({theme:e})=>({textDecoration:"none",padding:"10px 15px",cursor:"pointer",color:e.textMutedColor,fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,lineHeight:1,height:40,border:"none",borderBottom:"3px solid transparent",background:"transparent","&:focus":{outline:"0 none",borderColor:e.color.secondary}}),({active:e,theme:t})=>e?{opacity:1,color:t.color.secondary,borderColor:t.color.secondary}:{}),kl=E.div(({theme:e})=>({boxShadow:`${e.appBorderColor} 0 -1px 0 0 inset`,background:e.background.app,position:"sticky",top:0,zIndex:1,display:"flex",alignItems:"center",whiteSpace:"nowrap",overflow:"auto",paddingRight:10,gap:6,scrollbarColor:`${e.barTextColor} ${e.background.app}`,scrollbarWidth:"thin"})),Nl=E.div({}),Pl=E.div({display:"flex",flexBasis:"100%",justifyContent:"flex-end",containerType:"inline-size",minWidth:96,gap:6}),Dl=E(ie)({"@container (max-width: 193px)":{span:{display:"none"}}}),Ml=({tabs:e})=>{let{ref:t}=Cl({refreshMode:"debounce",handleHeight:!1,handleWidth:!0}),{tab:a,setTab:n,toggleHighlight:r,highlighted:i,handleManual:l,allExpanded:c,handleCollapseAll:d,handleExpandAll:s}=nt(),p=R(u=>{n(u.currentTarget.getAttribute("data-type"))},[n]);return m(Ol,{ref:t},m(kl,null,m(Nl,{role:"tablist"},e.map((u,f)=>m(wl,{role:"tab",key:f,"data-type":u.type,"data-active":a===u.type,"aria-selected":a===u.type,active:a===u.type,onClick:p},u.label))),m(Pl,null,m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(fe,{note:"Highlight elements with accessibility violations"}),trigger:"hover"},m(Dl,{onClick:r,active:i},i?m(At,null):m($t,null),m("span",null,i?"Hide highlights":"Show highlights"))),m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(fe,{note:c?"Collapse all":"Expand all"}),trigger:"hover"},m(ie,{onClick:c?d:s,"aria-label":c?"Collapse all":"Expand all"},c?m(It,null):m(Rt,null))),m(de,{as:"div",hasChrome:!1,placement:"top",tooltip:m(fe,{note:"Rerun the accessibility scan"}),trigger:"hover"},m(ie,{onClick:l,"aria-label":"Rerun accessibility scan"},m(Me,null))))),m(Oe,{vertical:!0,horizontal:!0},e.find(u=>u.type===a)?.panel))},Ll=E.div(({theme:{color:e,typography:t,background:a}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:a.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative",code:{fontSize:`${t.size.s1-1}px`,color:"inherit",margin:"0 0.2em",padding:"0 0.2em",background:"rgba(255, 255, 255, 0.8)",borderRadius:"2px",boxShadow:"0 0 0 1px rgba(0, 0, 0, 0.1)"}})),Fl=({discrepancy:e})=>{let t=Re().getDocsUrl({subpath:Za,versioned:!0,renderer:!0}),a=j(()=>{switch(e){case"browserPassedCliFailed":return"Accessibility checks passed in this browser but failed in the CLI.";case"cliPassedBrowserFailed":return"Accessibility checks passed in the CLI but failed in this browser.";case"cliFailedButModeManual":return"Accessibility checks failed in the CLI. Run the tests manually to see the results.";default:return null}},[e]);return a?o.createElement(Ll,null,a," ",o.createElement(Ie,{href:t,target:"_blank",withArrow:!0},"Learn what could cause this")):null},Zt=E(Me)(({theme:e})=>({animation:`${e.animation.rotate360} 1s linear infinite;`,margin:4})),We=E.div({display:"flex",alignItems:"center",gap:6}),Jt=E.span(({theme:e})=>({display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",textAlign:"center",fontSize:e.typography.size.s2,height:"100%",gap:24,div:{display:"flex",flexDirection:"column",alignItems:"center",gap:8},p:{margin:0,color:e.textMutedColor},code:{display:"inline-block",fontSize:e.typography.size.s2-1,backgroundColor:e.background.app,border:`1px solid ${e.color.border}`,borderRadius:4,padding:"2px 3px"}})),Hl=()=>{let{parameters:e,tab:t,results:a,status:n,handleManual:r,error:i,discrepancy:l,handleSelectionChange:c,selectedItems:d,toggleOpen:s}=nt(),p=j(()=>{let{passes:u,incomplete:f,violations:b}=a??{passes:[],incomplete:[],violations:[]};return[{label:o.createElement(We,null,"Violations",o.createElement(le,{compact:!0,status:t==="violations"?"active":"neutral"},b.length)),panel:o.createElement(Be,{items:b,type:W.VIOLATION,empty:"No accessibility violations found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:b,type:W.VIOLATION},{label:o.createElement(We,null,"Passes",o.createElement(le,{compact:!0,status:t==="passes"?"active":"neutral"},u.length)),panel:o.createElement(Be,{items:u,type:W.PASS,empty:"No passing accessibility checks found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:u,type:W.PASS},{label:o.createElement(We,null,"Inconclusive",o.createElement(le,{compact:!0,status:t==="incomplete"?"active":"neutral"},f.length)),panel:o.createElement(Be,{items:f,type:W.INCOMPLETION,empty:"No inconclusive accessibility checks found.",handleSelectionChange:c,selectedItems:d,toggleOpen:s}),items:f,type:W.INCOMPLETION}]},[t,a,c,d,s]);return e.disable||e.test==="off"?o.createElement(Jt,null,o.createElement("div",null,o.createElement("strong",null,"Accessibility tests are disabled for this story"),o.createElement("p",null,"Update"," ",o.createElement("code",null,e.disable?"parameters.a11y.disable":"parameters.a11y.test")," ","to enable accessibility tests."))):o.createElement(o.Fragment,null,l&&o.createElement(Fl,{discrepancy:l}),n==="ready"||n==="ran"?o.createElement(Ml,{key:"tabs",tabs:p}):o.createElement(Jt,{style:{marginTop:l?"1em":0}},n==="initial"&&o.createElement("div",null,o.createElement(Zt,{size:12}),o.createElement("strong",null,"Preparing accessibility scan"),o.createElement("p",null,"Please wait while the addon is initializing...")),n==="manual"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"Accessibility tests run manually for this story"),o.createElement("p",null,"Results will not show when using the testing module. You can still run accessibility tests manually.")),o.createElement(re,{size:"medium",onClick:r},"Run accessibility scan"),o.createElement("p",null,"Update ",o.createElement("code",null,"globals.a11y.manual")," to disable manual mode.")),n==="running"&&o.createElement("div",null,o.createElement(Zt,{size:12}),o.createElement("strong",null,"Accessibility scan in progress"),o.createElement("p",null,"Please wait while the accessibility scan is running...")),n==="error"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"The accessibility scan encountered an error"),o.createElement("p",null,typeof i=="string"?i:i instanceof Error?i.toString():JSON.stringify(i,null,2))),o.createElement(re,{size:"medium",onClick:r},"Rerun accessibility scan")),n==="component-test-error"&&o.createElement(o.Fragment,null,o.createElement("div",null,o.createElement("strong",null,"This story's component tests failed"),o.createElement("p",null,"Automated accessibility tests will not run until this is resolved. You can still test manually.")),o.createElement(re,{size:"medium",onClick:r},"Run accessibility scan"))))},Ul=e=>m("svg",{...e},m("defs",null,m("filter",{id:"protanopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.567, 0.433, 0, 0, 0 0.558, 0.442, 0, 0, 0 0, 0.242, 0.758, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"protanomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.817, 0.183, 0, 0, 0 0.333, 0.667, 0, 0, 0 0, 0.125, 0.875, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"deuteranopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.625, 0.375, 0, 0, 0 0.7, 0.3, 0, 0, 0 0, 0.3, 0.7, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"deuteranomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.8, 0.2, 0, 0, 0 0.258, 0.742, 0, 0, 0 0, 0.142, 0.858, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"tritanopia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.95, 0.05,  0, 0, 0 0,  0.433, 0.567, 0, 0 0,  0.475, 0.525, 0, 0 0,  0, 0, 1, 0"})),m("filter",{id:"tritanomaly"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.967, 0.033, 0, 0, 0 0, 0.733, 0.267, 0, 0 0, 0.183, 0.817, 0, 0 0, 0, 0, 1, 0"})),m("filter",{id:"achromatopsia"},m("feColorMatrix",{in:"SourceGraphic",type:"matrix",values:"0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0.299, 0.587, 0.114, 0, 0 0, 0, 0, 1, 0"})))),Gl="storybook-preview-iframe",Bl=[{name:"blurred vision",percentage:22.9},{name:"deuteranomaly",percentage:2.7},{name:"deuteranopia",percentage:.56},{name:"protanomaly",percentage:.66},{name:"protanopia",percentage:.59},{name:"tritanomaly",percentage:.01},{name:"tritanopia",percentage:.016},{name:"achromatopsia",percentage:1e-4},{name:"grayscale"}],$n=e=>e?e==="blurred vision"?"blur(2px)":e==="grayscale"?"grayscale(100%)":`url('#${e}')`:"none",zl=E.div({"&, & svg":{position:"absolute",width:0,height:0}}),Wl=E.span({background:"linear-gradient(to right, #F44336, #FF9800, #FFEB3B, #8BC34A, #2196F3, #9C27B0)",borderRadius:"1rem",display:"block",height:"1rem",width:"1rem"},({filter:e})=>({filter:$n(e)}),({theme:e})=>({boxShadow:`${e.appBorderColor} 0 0 0 1px inset`})),jl=E.span({display:"flex",flexDirection:"column"}),Yl=E.span({textTransform:"capitalize"}),Vl=E.span(({theme:e})=>({fontSize:11,color:e.textMutedColor})),ql=(e,t)=>[...e!==null?[{id:"reset",title:"Reset color filter",onClick:()=>{t(null)},right:void 0,active:!1}]:[],...Bl.map(a=>{let n=a.percentage!==void 0?`${a.percentage}% of users`:void 0;return{id:a.name,title:o.createElement(jl,null,o.createElement(Yl,null,a.name),n&&o.createElement(Vl,null,n)),onClick:()=>{t(a)},right:o.createElement(Wl,{filter:a.name}),active:e===a}})],Kl=()=>{let[e,t]=H(null);return o.createElement(o.Fragment,null,e&&o.createElement(_t,{styles:{[`#${Gl}`]:{filter:$n(e.name)}}}),o.createElement(de,{placement:"top",tooltip:({onHide:a})=>{let n=ql(e,r=>{t(r),a()});return o.createElement(ke,{links:n})},closeOnOutsideClick:!0,onDoubleClick:()=>t(null)},o.createElement(ie,{key:"filter",active:!!e,title:"Vision simulator"},o.createElement(St,null))),o.createElement(zl,null,o.createElement(Ul,null)))},Ql=()=>{let e=Re().getSelectedPanel(),[t]=De(B),a=t?.violations?.length||0,n=t?.incomplete?.length||0,r=a+n;return o.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},o.createElement("span",null,"Accessibility"),r===0?null:o.createElement(le,{compact:!0,status:e===Ve?"active":"neutral"},r))};xe.register(B,e=>{xe.add(Ve,{title:"",type:Pe.TOOL,match:({viewMode:t,tabId:a})=>t==="story"&&!a,render:()=>o.createElement(Kl,null)}),xe.add(Ve,{title:Ql,type:Pe.PANEL,render:({active:t=!0})=>o.createElement(tr,null,t?o.createElement(Hl,null):null),paramKey:Wa})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
