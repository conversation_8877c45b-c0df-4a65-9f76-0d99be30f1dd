try{
(()=>{var Oc=Object.defineProperty;var st=(e=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof require<"u"?require:t)[r]}):e)(function(e){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')});var sr=(e,t)=>()=>(e&&(t=e(e=0)),t);var Tc=(e,t)=>{for(var r in t)Oc(e,r,{get:t[r],enumerable:!0})};var ee=sr(()=>{});var te=sr(()=>{});var re=sr(()=>{});var fo={};Tc(fo,{A:()=>Dc,ActionBar:()=>Mt,AddonPanel:()=>Ut,Badge:()=>ct,Bar:()=>$t,Blockquote:()=>Bc,Button:()=>ge,ClipboardCode:()=>_c,Code:()=>Rc,DL:()=>Nc,Div:()=>Fc,DocumentWrapper:()=>Lc,EmptyTabContent:()=>Ht,ErrorFormatter:()=>Pc,FlexBar:()=>mn,Form:()=>de,H1:()=>jc,H2:()=>Mc,H3:()=>Uc,H4:()=>$c,H5:()=>Hc,H6:()=>Vc,HR:()=>zc,IconButton:()=>G,Img:()=>qc,LI:()=>Gc,Link:()=>be,ListItem:()=>Wc,Loader:()=>Kc,Modal:()=>He,OL:()=>Yc,P:()=>fn,Placeholder:()=>Jc,Pre:()=>Xc,ProgressSpinner:()=>Zc,ResetWrapper:()=>hn,ScrollArea:()=>gn,Separator:()=>bn,Spaced:()=>Qc,Span:()=>ed,StorybookIcon:()=>td,StorybookLogo:()=>rd,SyntaxHighlighter:()=>Vt,TT:()=>nd,TabBar:()=>ad,TabButton:()=>od,TabWrapper:()=>ld,Table:()=>id,Tabs:()=>ud,TabsState:()=>sd,TooltipLinkList:()=>zt,TooltipMessage:()=>cd,TooltipNote:()=>Ne,UL:()=>dd,WithTooltip:()=>oe,WithTooltipPure:()=>yn,Zoom:()=>En,codeCommon:()=>Xe,components:()=>pd,createCopyToClipboardFunction:()=>md,default:()=>Ic,getStoryHref:()=>fd,interleaveSeparators:()=>hd,nameSpaceClassNames:()=>gd,resetComponents:()=>bd,withReset:()=>Ze});var Ic,Dc,Mt,Ut,ct,$t,Bc,ge,_c,Rc,Nc,Fc,Lc,Ht,Pc,mn,de,jc,Mc,Uc,$c,Hc,Vc,zc,G,qc,Gc,be,Wc,Kc,He,Yc,fn,Jc,Xc,Zc,hn,gn,bn,Qc,ed,td,rd,Vt,nd,ad,od,ld,id,ud,sd,zt,cd,Ne,dd,oe,yn,En,Xe,pd,md,fd,hd,gd,bd,Ze,U=sr(()=>{ee();te();re();Ic=__STORYBOOK_COMPONENTS__,{A:Dc,ActionBar:Mt,AddonPanel:Ut,Badge:ct,Bar:$t,Blockquote:Bc,Button:ge,ClipboardCode:_c,Code:Rc,DL:Nc,Div:Fc,DocumentWrapper:Lc,EmptyTabContent:Ht,ErrorFormatter:Pc,FlexBar:mn,Form:de,H1:jc,H2:Mc,H3:Uc,H4:$c,H5:Hc,H6:Vc,HR:zc,IconButton:G,Img:qc,LI:Gc,Link:be,ListItem:Wc,Loader:Kc,Modal:He,OL:Yc,P:fn,Placeholder:Jc,Pre:Xc,ProgressSpinner:Zc,ResetWrapper:hn,ScrollArea:gn,Separator:bn,Spaced:Qc,Span:ed,StorybookIcon:td,StorybookLogo:rd,SyntaxHighlighter:Vt,TT:nd,TabBar:ad,TabButton:od,TabWrapper:ld,Table:id,Tabs:ud,TabsState:sd,TooltipLinkList:zt,TooltipMessage:cd,TooltipNote:Ne,UL:dd,WithTooltip:oe,WithTooltipPure:yn,Zoom:En,codeCommon:Xe,components:pd,createCopyToClipboardFunction:md,getStoryHref:fd,interleaveSeparators:hd,nameSpaceClassNames:gd,resetComponents:bd,withReset:Ze}=__STORYBOOK_COMPONENTS__});ee();te();re();ee();te();re();ee();te();re();var n=__REACT__,{Children:cr,Component:Re,Fragment:Ie,Profiler:gb,PureComponent:bb,StrictMode:yb,Suspense:co,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:Eb,act:vb,cloneElement:ae,createContext:Ct,createElement:P,createFactory:xb,createRef:Ab,forwardRef:po,isValidElement:Cb,lazy:mo,memo:he,startTransition:Sb,unstable_act:wb,useCallback:$,useContext:dr,useDebugValue:kb,useDeferredValue:Ob,useEffect:j,useId:Tb,useImperativeHandle:Ib,useInsertionEffect:Db,useLayoutEffect:pr,useMemo:ce,useReducer:Bb,useRef:X,useState:R,useSyncExternalStore:_b,useTransition:Rb,version:Nb}=__REACT__;U();ee();te();re();var $b=__STORYBOOK_ICONS__,{AccessibilityAltIcon:Hb,AccessibilityIcon:Vb,AccessibilityIgnoredIcon:zb,AddIcon:fr,AdminIcon:qb,AlertAltIcon:Gb,AlertIcon:Wb,AlignLeftIcon:Kb,AlignRightIcon:Yb,AppleIcon:Jb,ArrowBottomLeftIcon:Xb,ArrowBottomRightIcon:Zb,ArrowDownIcon:Qb,ArrowLeftIcon:e1,ArrowRightIcon:t1,ArrowSolidDownIcon:r1,ArrowSolidLeftIcon:n1,ArrowSolidRightIcon:a1,ArrowSolidUpIcon:o1,ArrowTopLeftIcon:l1,ArrowTopRightIcon:i1,ArrowUpIcon:u1,AzureDevOpsIcon:s1,BackIcon:c1,BasketIcon:d1,BatchAcceptIcon:p1,BatchDenyIcon:m1,BeakerIcon:f1,BellIcon:h1,BitbucketIcon:g1,BoldIcon:b1,BookIcon:y1,BookmarkHollowIcon:E1,BookmarkIcon:v1,BottomBarIcon:x1,BottomBarToggleIcon:A1,BoxIcon:C1,BranchIcon:S1,BrowserIcon:ho,ButtonIcon:w1,CPUIcon:k1,CalendarIcon:O1,CameraIcon:T1,CameraStabilizeIcon:I1,CategoryIcon:D1,CertificateIcon:B1,ChangedIcon:_1,ChatIcon:R1,CheckIcon:hr,ChevronDownIcon:go,ChevronLeftIcon:N1,ChevronRightIcon:bo,ChevronSmallDownIcon:gr,ChevronSmallLeftIcon:F1,ChevronSmallRightIcon:L1,ChevronSmallUpIcon:yo,ChevronUpIcon:P1,ChromaticIcon:j1,ChromeIcon:M1,CircleHollowIcon:U1,CircleIcon:br,ClearIcon:$1,CloseAltIcon:H1,CloseIcon:V1,CloudHollowIcon:z1,CloudIcon:q1,CogIcon:G1,CollapseIcon:W1,CommandIcon:K1,CommentAddIcon:Y1,CommentIcon:J1,CommentsIcon:X1,CommitIcon:Z1,CompassIcon:Q1,ComponentDrivenIcon:ey,ComponentIcon:ty,ContrastIcon:ry,ContrastIgnoredIcon:ny,ControlsIcon:ay,CopyIcon:oy,CreditIcon:ly,CrossIcon:iy,DashboardIcon:uy,DatabaseIcon:sy,DeleteIcon:cy,DiamondIcon:dy,DirectionIcon:py,DiscordIcon:my,DocChartIcon:fy,DocListIcon:hy,DocumentIcon:dt,DownloadIcon:gy,DragIcon:by,EditIcon:yy,EllipsisIcon:Ey,EmailIcon:vy,ExpandAltIcon:xy,ExpandIcon:Ay,EyeCloseIcon:Eo,EyeIcon:vo,FaceHappyIcon:Cy,FaceNeutralIcon:Sy,FaceSadIcon:wy,FacebookIcon:ky,FailedIcon:xo,FastForwardIcon:Ao,FigmaIcon:Oy,FilterIcon:Ty,FlagIcon:Iy,FolderIcon:Dy,FormIcon:By,GDriveIcon:_y,GithubIcon:Ry,GitlabIcon:Ny,GlobeIcon:Fy,GoogleIcon:Ly,GraphBarIcon:Py,GraphLineIcon:jy,GraphqlIcon:My,GridAltIcon:Uy,GridIcon:Co,GrowIcon:So,HeartHollowIcon:$y,HeartIcon:Hy,HomeIcon:Vy,HourglassIcon:zy,InfoIcon:qy,ItalicIcon:Gy,JumpToIcon:Wy,KeyIcon:Ky,LightningIcon:Yy,LightningOffIcon:Jy,LinkBrokenIcon:Xy,LinkIcon:Zy,LinkedinIcon:Qy,LinuxIcon:eE,ListOrderedIcon:tE,ListUnorderedIcon:wo,LocationIcon:rE,LockIcon:nE,MarkdownIcon:aE,MarkupIcon:ko,MediumIcon:oE,MemoryIcon:lE,MenuIcon:iE,MergeIcon:uE,MirrorIcon:sE,MobileIcon:Oo,MoonIcon:cE,NutIcon:dE,OutboxIcon:pE,OutlineIcon:To,PaintBrushIcon:mE,PaperClipIcon:fE,ParagraphIcon:hE,PassedIcon:vn,PhoneIcon:gE,PhotoDragIcon:bE,PhotoIcon:Io,PhotoStabilizeIcon:yE,PinAltIcon:EE,PinIcon:vE,PlayAllHollowIcon:xE,PlayBackIcon:Do,PlayHollowIcon:AE,PlayIcon:Bo,PlayNextIcon:_o,PlusIcon:CE,PointerDefaultIcon:SE,PointerHandIcon:wE,PowerIcon:kE,PrintIcon:OE,ProceedIcon:TE,ProfileIcon:IE,PullRequestIcon:DE,QuestionIcon:BE,RSSIcon:_E,RedirectIcon:RE,ReduxIcon:NE,RefreshIcon:yr,ReplyIcon:FE,RepoIcon:LE,RequestChangeIcon:PE,RewindIcon:Ro,RulerIcon:No,SaveIcon:jE,SearchIcon:ME,ShareAltIcon:UE,ShareIcon:$E,ShieldIcon:HE,SideBySideIcon:VE,SidebarAltIcon:zE,SidebarAltToggleIcon:qE,SidebarIcon:GE,SidebarToggleIcon:WE,SpeakerIcon:KE,StackedIcon:YE,StarHollowIcon:JE,StarIcon:XE,StatusFailIcon:ZE,StatusIcon:QE,StatusPassIcon:ev,StatusWarnIcon:tv,StickerIcon:rv,StopAltHollowIcon:nv,StopAltIcon:Fo,StopIcon:av,StorybookIcon:ov,StructureIcon:lv,SubtractIcon:Lo,SunIcon:iv,SupportIcon:uv,SweepIcon:sv,SwitchAltIcon:cv,SyncIcon:Po,TabletIcon:jo,ThumbsUpIcon:dv,TimeIcon:pv,TimerIcon:mv,TransferIcon:Mo,TrashIcon:fv,TwitterIcon:hv,TypeIcon:gv,UbuntuIcon:bv,UndoIcon:Er,UnfoldIcon:yv,UnlockIcon:Ev,UnpinIcon:vv,UploadIcon:xv,UserAddIcon:Av,UserAltIcon:Cv,UserIcon:Sv,UsersIcon:wv,VSCodeIcon:kv,VerifiedIcon:Ov,VideoIcon:Tv,WandIcon:Iv,WatchIcon:Dv,WindowsIcon:Bv,WrenchIcon:_v,XIcon:Rv,YoutubeIcon:Nv,ZoomIcon:Uo,ZoomOutIcon:$o,ZoomResetIcon:Ho,iconList:Fv}=__STORYBOOK_ICONS__;ee();te();re();var Uv=__STORYBOOK_THEMING__,{CacheProvider:$v,ClassNames:Hv,Global:Vo,ThemeProvider:zo,background:Vv,color:vr,convert:qo,create:zv,createCache:qv,createGlobal:Gv,createReset:Wv,css:Kv,darken:Yv,ensure:Jv,ignoreSsrWarning:Go,isPropValid:Xv,jsx:Zv,keyframes:xn,lighten:Qv,styled:b,themes:An,typography:Fe,useTheme:ye,withTheme:Wo}=__STORYBOOK_THEMING__;ee();te();re();var Qe=(()=>{let e;return typeof window<"u"?e=window:typeof globalThis<"u"?e=globalThis:typeof window<"u"?e=window:typeof self<"u"?e=self:e={},e})();ee();te();re();var ux=__STORYBOOK_API__,{ActiveTabs:sx,Consumer:Ko,ManagerContext:cx,Provider:dx,RequestResponseError:px,addons:Z,combineParameters:mx,controlOrMetaKey:fx,controlOrMetaSymbol:hx,eventMatchesShortcut:gx,eventToShortcut:bx,experimental_MockUniversalStore:yx,experimental_UniversalStore:Ex,experimental_getStatusStore:vx,experimental_getTestProviderStore:xx,experimental_requestResponse:Cn,experimental_useStatusStore:Yo,experimental_useTestProviderStore:Ax,experimental_useUniversalStore:Cx,internal_fullStatusStore:Sx,internal_fullTestProviderStore:wx,internal_universalStatusStore:kx,internal_universalTestProviderStore:Ox,isMacLike:Tx,isShortcutTaken:Ix,keyToSymbol:Dx,merge:Bx,mockChannel:_x,optionOrAltSymbol:Rx,shortcutMatchesShortcut:Nx,shortcutToHumanString:Fx,types:Ee,useAddonState:St,useArgTypes:xr,useArgs:Jo,useChannel:Ar,useGlobalTypes:Lx,useGlobals:Ve,useParameter:et,useSharedState:Px,useStoryPrepared:jx,useStorybookApi:ve,useStorybookState:Xo}=__STORYBOOK_API__;U();ee();te();re();var Vx=__STORYBOOK_CORE_EVENTS__,{ARGTYPES_INFO_REQUEST:zx,ARGTYPES_INFO_RESPONSE:qx,CHANNEL_CREATED:Gx,CHANNEL_WS_DISCONNECT:Wx,CONFIG_ERROR:Kx,CREATE_NEW_STORYFILE_REQUEST:Yx,CREATE_NEW_STORYFILE_RESPONSE:Jx,CURRENT_STORY_WAS_SET:Xx,DOCS_PREPARED:Zx,DOCS_RENDERED:Qx,FILE_COMPONENT_SEARCH_REQUEST:eA,FILE_COMPONENT_SEARCH_RESPONSE:tA,FORCE_REMOUNT:Zo,FORCE_RE_RENDER:rA,GLOBALS_UPDATED:nA,NAVIGATE_URL:aA,PLAY_FUNCTION_THREW_EXCEPTION:Qo,PRELOAD_ENTRIES:oA,PREVIEW_BUILDER_PROGRESS:lA,PREVIEW_KEYDOWN:iA,REGISTER_SUBSCRIPTION:uA,REQUEST_WHATS_NEW_DATA:sA,RESET_STORY_ARGS:cA,RESULT_WHATS_NEW_DATA:dA,SAVE_STORY_REQUEST:Sn,SAVE_STORY_RESPONSE:Cr,SELECT_STORY:pA,SET_CONFIG:mA,SET_CURRENT_STORY:fA,SET_FILTER:hA,SET_GLOBALS:gA,SET_INDEX:bA,SET_STORIES:yA,SET_WHATS_NEW_CACHE:EA,SHARED_STATE_CHANGED:vA,SHARED_STATE_SET:xA,STORIES_COLLAPSE_ALL:AA,STORIES_EXPAND_ALL:CA,STORY_ARGS_UPDATED:SA,STORY_CHANGED:qt,STORY_ERRORED:wA,STORY_FINISHED:kA,STORY_HOT_UPDATED:OA,STORY_INDEX_INVALIDATED:TA,STORY_MISSING:IA,STORY_PREPARED:DA,STORY_RENDERED:BA,STORY_RENDER_PHASE_CHANGED:el,STORY_SPECIFIED:_A,STORY_THREW_EXCEPTION:tl,STORY_UNCHANGED:RA,TELEMETRY_ERROR:NA,TOGGLE_WHATS_NEW_NOTIFICATIONS:FA,UNHANDLED_ERRORS_WHILE_PLAYING:rl,UPDATE_GLOBALS:LA,UPDATE_QUERY_PARAMS:PA,UPDATE_STORY_ARGS:jA}=__STORYBOOK_CORE_EVENTS__;ee();te();re();var VA=__STORYBOOK_CLIENT_LOGGER__,{deprecate:zA,logger:tt,once:nl,pretty:qA}=__STORYBOOK_CLIENT_LOGGER__;U();ee();te();re();var yd=Object.create,kn=Object.defineProperty,Ed=Object.getOwnPropertyDescriptor,vd=Object.getOwnPropertyNames,xd=Object.getPrototypeOf,Ad=Object.prototype.hasOwnProperty,pe=(e,t)=>kn(e,"name",{value:t,configurable:!0}),Cd=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Sd=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of vd(t))!Ad.call(e,o)&&o!==r&&kn(e,o,{get:()=>t[o],enumerable:!(a=Ed(t,o))||a.enumerable});return e},wd=(e,t,r)=>(r=e!=null?yd(xd(e)):{},Sd(t||!e||!e.__esModule?kn(r,"default",{value:e,enumerable:!0}):r,e)),kd=Cd(e=>{Object.defineProperty(e,"__esModule",{value:!0}),e.isEqual=function(){var t=Object.prototype.toString,r=Object.getPrototypeOf,a=Object.getOwnPropertySymbols?function(o){return Object.keys(o).concat(Object.getOwnPropertySymbols(o))}:Object.keys;return function(o,c){return pe(function l(u,s,d){var m,f,p,h=t.call(u),g=t.call(s);if(u===s)return!0;if(u==null||s==null)return!1;if(d.indexOf(u)>-1&&d.indexOf(s)>-1)return!0;if(d.push(u,s),h!=g||(m=a(u),f=a(s),m.length!=f.length||m.some(function(E){return!l(u[E],s[E],d)})))return!1;switch(h.slice(8,-1)){case"Symbol":return u.valueOf()==s.valueOf();case"Date":case"Number":return+u==+s||+u!=+u&&+s!=+s;case"RegExp":case"Function":case"String":case"Boolean":return""+u==""+s;case"Set":case"Map":m=u.entries(),f=s.entries();do if(!l((p=m.next()).value,f.next().value,d))return!1;while(!p.done);return!0;case"ArrayBuffer":u=new Uint8Array(u),s=new Uint8Array(s);case"DataView":u=new Uint8Array(u.buffer),s=new Uint8Array(s.buffer);case"Float32Array":case"Float64Array":case"Int8Array":case"Int16Array":case"Int32Array":case"Uint8Array":case"Uint16Array":case"Uint32Array":case"Uint8ClampedArray":case"Arguments":case"Array":if(u.length!=s.length)return!1;for(p=0;p<u.length;p++)if((p in u||p in s)&&(p in u!=p in s||!l(u[p],s[p],d)))return!1;return!0;case"Object":return l(r(u),r(s),d);default:return!1}},"n")(o,c,[])}}()});function ll(e){return e.replace(/_/g," ").replace(/-/g," ").replace(/\./g," ").replace(/([^\n])([A-Z])([a-z])/g,(t,r,a,o)=>`${r} ${a}${o}`).replace(/([a-z])([A-Z])/g,(t,r,a)=>`${r} ${a}`).replace(/([a-z])([0-9])/gi,(t,r,a)=>`${r} ${a}`).replace(/([0-9])([a-z])/gi,(t,r,a)=>`${r} ${a}`).replace(/(\s|^)(\w)/g,(t,r,a)=>`${r}${a.toUpperCase()}`).replace(/ +/g," ").trim()}pe(ll,"toStartCaseStr");var al=wd(kd(),1),il=pe(e=>e.map(t=>typeof t<"u").filter(Boolean).length,"count"),Od=pe((e,t)=>{let{exists:r,eq:a,neq:o,truthy:c}=e;if(il([r,a,o,c])>1)throw new Error(`Invalid conditional test ${JSON.stringify({exists:r,eq:a,neq:o})}`);if(typeof a<"u")return(0,al.isEqual)(t,a);if(typeof o<"u")return!(0,al.isEqual)(t,o);if(typeof r<"u"){let l=typeof t<"u";return r?l:!l}return typeof c>"u"||c?!!t:!t},"testValue"),ul=pe((e,t,r)=>{if(!e.if)return!0;let{arg:a,global:o}=e.if;if(il([a,o])!==1)throw new Error(`Invalid conditional value ${JSON.stringify({arg:a,global:o})}`);let c=a?t[a]:r[o];return Od(e.if,c)},"includeConditionalArg");function Td(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Preview"}pe(Td,"isPreview");function Id(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Meta"}pe(Id,"isMeta");function Dd(e){return e!=null&&typeof e=="object"&&"_tag"in e&&e?._tag==="Story"}pe(Dd,"isStory");var Bd=pe(e=>e.toLowerCase().replace(/[ ’–—―′¿'`~!@#$%^&*()_|+\-=?;:'",.<>\{\}\[\]\\\/]/gi,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,""),"sanitize"),ol=pe((e,t)=>{let r=Bd(e);if(r==="")throw new Error(`Invalid ${t} '${e}', must include alphanumeric characters`);return r},"sanitizeSafe"),JA=pe((e,t)=>`${ol(e,"kind")}${t?`--${ol(t,"name")}`:""}`,"toId"),XA=pe(e=>ll(e),"storyNameFromExport");function wn(e,t){return Array.isArray(t)?t.includes(e):e.match(t)}pe(wn,"matches");function _d(e,{includeStories:t,excludeStories:r}){return e!=="__esModule"&&(!t||wn(e,t))&&(!r||!wn(e,r))}pe(_d,"isExportStory");var ZA=pe((e,{rootSeparator:t,groupSeparator:r})=>{let[a,o]=e.split(t,2),c=(o||e).split(r).filter(l=>!!l);return{root:o?a:null,groups:c}},"parseKind"),QA=pe((...e)=>{let t=e.reduce((r,a)=>(a.startsWith("!")?r.delete(a.slice(1)):r.add(a),r),new Set);return Array.from(t)},"combineTags");U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();U();var Rd=Object.create,rn=Object.defineProperty,Nd=Object.getOwnPropertyDescriptor,Fd=Object.getOwnPropertyNames,Ld=Object.getPrototypeOf,Pd=Object.prototype.hasOwnProperty,i=(e,t)=>rn(e,"name",{value:t,configurable:!0}),Sr=(e=>typeof st<"u"?st:typeof Proxy<"u"?new Proxy(e,{get:(t,r)=>(typeof st<"u"?st:t)[r]}):e)(function(e){if(typeof st<"u")return st.apply(this,arguments);throw Error('Dynamic require of "'+e+'" is not supported')}),q=(e,t)=>()=>(e&&(t=e(e=0)),t),Y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),jd=(e,t)=>{for(var r in t)rn(e,r,{get:t[r],enumerable:!0})},Md=(e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Fd(t))!Pd.call(e,o)&&o!==r&&rn(e,o,{get:()=>t[o],enumerable:!(a=Nd(t,o))||a.enumerable});return e},Se=(e,t,r)=>(r=e!=null?Rd(Ld(e)):{},Md(t||!e||!e.__esModule?rn(r,"default",{value:e,enumerable:!0}):r,e));function Oa(e){return typeof e=="symbol"||e instanceof Symbol}var Oi=q(()=>{i(Oa,"isSymbol")});function Ti(e){return Oa(e)?NaN:Number(e)}var Ud=q(()=>{Oi(),i(Ti,"toNumber")});function Ii(e){return e?(e=Ti(e),e===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e===e?e:0):e===0?e:0}var $d=q(()=>{Ud(),i(Ii,"toFinite")});function Di(e){let t=Ii(e),r=t%1;return r?t-r:t}var Hd=q(()=>{$d(),i(Di,"toInteger")});function Bi(e){return Array.from(new Set(e))}var Vd=q(()=>{i(Bi,"uniq")});function _i(e){return e==null||typeof e!="object"&&typeof e!="function"}var zd=q(()=>{i(_i,"isPrimitive")});function Ta(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}var Ri=q(()=>{i(Ta,"isTypedArray")});function Ia(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}var Ni=q(()=>{i(Ia,"getSymbols")});function Fi(e){return e==null?e===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var qd=q(()=>{i(Fi,"getTag")}),Li,Da,Ba,_a,Ra,Pi,ji,Mi,Ui,$i,Hi,Vi,zi,qi,Gi,Wi,Ki,Yi,Ji,Xi,Zi,Qi,eu=q(()=>{Li="[object RegExp]",Da="[object String]",Ba="[object Number]",_a="[object Boolean]",Ra="[object Arguments]",Pi="[object Symbol]",ji="[object Date]",Mi="[object Map]",Ui="[object Set]",$i="[object Array]",Hi="[object ArrayBuffer]",Vi="[object Object]",zi="[object DataView]",qi="[object Uint8Array]",Gi="[object Uint8ClampedArray]",Wi="[object Uint16Array]",Ki="[object Uint32Array]",Yi="[object Int8Array]",Ji="[object Int16Array]",Xi="[object Int32Array]",Zi="[object Float32Array]",Qi="[object Float64Array]"});function tu(e,t){return gt(e,void 0,e,new Map,t)}function gt(e,t,r,a=new Map,o=void 0){let c=o?.(e,t,r,a);if(c!=null)return c;if(_i(e))return e;if(a.has(e))return a.get(e);if(Array.isArray(e)){let l=new Array(e.length);a.set(e,l);for(let u=0;u<e.length;u++)l[u]=gt(e[u],u,r,a,o);return Object.hasOwn(e,"index")&&(l.index=e.index),Object.hasOwn(e,"input")&&(l.input=e.input),l}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let l=new RegExp(e.source,e.flags);return l.lastIndex=e.lastIndex,l}if(e instanceof Map){let l=new Map;a.set(e,l);for(let[u,s]of e)l.set(u,gt(s,u,r,a,o));return l}if(e instanceof Set){let l=new Set;a.set(e,l);for(let u of e)l.add(gt(u,void 0,r,a,o));return l}if(typeof Buffer<"u"&&Buffer.isBuffer(e))return e.subarray();if(Ta(e)){let l=new(Object.getPrototypeOf(e)).constructor(e.length);a.set(e,l);for(let u=0;u<e.length;u++)l[u]=gt(e[u],u,r,a,o);return l}if(e instanceof ArrayBuffer||typeof SharedArrayBuffer<"u"&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let l=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return a.set(e,l),ot(l,e,r,a,o),l}if(typeof File<"u"&&e instanceof File){let l=new File([e],e.name,{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Blob){let l=new Blob([e],{type:e.type});return a.set(e,l),ot(l,e,r,a,o),l}if(e instanceof Error){let l=new e.constructor;return a.set(e,l),l.message=e.message,l.name=e.name,l.stack=e.stack,l.cause=e.cause,ot(l,e,r,a,o),l}if(typeof e=="object"&&ru(e)){let l=Object.create(Object.getPrototypeOf(e));return a.set(e,l),ot(l,e,r,a,o),l}return e}function ot(e,t,r=e,a,o){let c=[...Object.keys(t),...Ia(t)];for(let l=0;l<c.length;l++){let u=c[l],s=Object.getOwnPropertyDescriptor(e,u);(s==null||s.writable)&&(e[u]=gt(t[u],u,r,a,o))}}function ru(e){switch(Fi(e)){case Ra:case $i:case Hi:case zi:case _a:case ji:case Zi:case Qi:case Yi:case Ji:case Xi:case Mi:case Ba:case Vi:case Li:case Ui:case Da:case Pi:case qi:case Gi:case Wi:case Ki:return!0;default:return!1}}var Gd=q(()=>{Ni(),qd(),eu(),zd(),Ri(),i(tu,"cloneDeepWith"),i(gt,"cloneDeepWithImpl"),i(ot,"copyProperties"),i(ru,"isCloneableObject")});function nu(e){return Number.isSafeInteger(e)&&e>=0}var Wd=q(()=>{i(nu,"isLength")});function nn(e){return e!=null&&typeof e!="function"&&nu(e.length)}var Na=q(()=>{Wd(),i(nn,"isArrayLike")});function au(e,t){return tu(e,(r,a,o,c)=>{let l=t?.(r,a,o,c);if(l!=null)return l;if(typeof e=="object")switch(Object.prototype.toString.call(e)){case Ba:case Da:case _a:{let u=new e.constructor(e?.valueOf());return ot(u,e),u}case Ra:{let u={};return ot(u,e),u.length=e.length,u[Symbol.iterator]=e[Symbol.iterator],u}default:return}})}var Kd=q(()=>{Gd(),eu(),i(au,"cloneDeepWith")});function ou(e){return au(e)}var Yd=q(()=>{Kd(),i(ou,"cloneDeep")});function lu(e,t,r=1){if(t==null&&(t=e,e=0),!Number.isInteger(r)||r===0)throw new Error("The step value must be a non-zero integer.");let a=Math.max(Math.ceil((t-e)/r),0),o=new Array(a);for(let c=0;c<a;c++)o[c]=e+c*r;return o}var Jd=q(()=>{i(lu,"range")});function iu(e){return nn(e)?Bi(Array.from(e)):[]}var Xd=q(()=>{Vd(),Na(),i(iu,"uniq")});function uu(e,t,{signal:r,edges:a}={}){let o,c=null,l=a!=null&&a.includes("leading"),u=a==null||a.includes("trailing"),s=i(()=>{c!==null&&(e.apply(o,c),o=void 0,c=null)},"invoke"),d=i(()=>{u&&s(),h()},"onTimerEnd"),m=null,f=i(()=>{m!=null&&clearTimeout(m),m=setTimeout(()=>{m=null,d()},t)},"schedule"),p=i(()=>{m!==null&&(clearTimeout(m),m=null)},"cancelTimer"),h=i(()=>{p(),o=void 0,c=null},"cancel"),g=i(()=>{p(),s()},"flush"),E=i(function(...y){if(r?.aborted)return;o=this,c=y;let v=m==null;f(),l&&v&&s()},"debounced");return E.schedule=f,E.cancel=h,E.flush=g,r?.addEventListener("abort",h,{once:!0}),E}var Zd=q(()=>{i(uu,"debounce")});function su(e,t=0,r={}){typeof r!="object"&&(r={});let{signal:a,leading:o=!1,trailing:c=!0,maxWait:l}=r,u=Array(2);o&&(u[0]="leading"),c&&(u[1]="trailing");let s,d=null,m=uu(function(...h){s=e.apply(this,h),d=null},t,{signal:a,edges:u}),f=i(function(...h){if(l!=null){if(d===null)d=Date.now();else if(Date.now()-d>=l)return s=e.apply(this,h),d=Date.now(),m.cancel(),m.schedule(),s}return m.apply(this,h),s},"debounced"),p=i(()=>(m.flush(),s),"flush");return f.cancel=m.cancel,f.flush=p,f}var Qd=q(()=>{Zd(),i(su,"debounce")});function cu(e){return typeof Buffer<"u"&&Buffer.isBuffer(e)}var ep=q(()=>{i(cu,"isBuffer")});function du(e){let t=e?.constructor,r=typeof t=="function"?t.prototype:Object.prototype;return e===r}var tp=q(()=>{i(du,"isPrototype")});function pu(e){return Ta(e)}var rp=q(()=>{Ri(),i(pu,"isTypedArray")});function mu(e,t){if(e=Di(e),e<1||!Number.isSafeInteger(e))return[];let r=new Array(e);for(let a=0;a<e;a++)r[a]=typeof t=="function"?t(a):a;return r}var np=q(()=>{Hd(),i(mu,"times")});function fu(e){if(e==null)return[];switch(typeof e){case"object":case"function":return nn(e)?gu(e):du(e)?hu(e):nr(e);default:return nr(Object(e))}}function nr(e){let t=[];for(let r in e)t.push(r);return t}function hu(e){return nr(e).filter(t=>t!=="constructor")}function gu(e){let t=mu(e.length,a=>`${a}`),r=new Set(t);return cu(e)&&(r.add("offset"),r.add("parent")),pu(e)&&(r.add("buffer"),r.add("byteLength"),r.add("byteOffset")),[...t,...nr(e).filter(a=>!r.has(a))]}var ap=q(()=>{ep(),tp(),Na(),rp(),np(),i(fu,"keysIn"),i(nr,"keysInImpl"),i(hu,"prototypeKeysIn"),i(gu,"arrayLikeKeysIn")});function bu(e){let t=[];for(;e;)t.push(...Ia(e)),e=Object.getPrototypeOf(e);return t}var op=q(()=>{Ni(),i(bu,"getSymbolsIn")});function yu(e,t){if(e==null)return{};let r={};if(t==null)return e;let a=nn(e)?lu(0,e.length):[...fu(e),...bu(e)];for(let o=0;o<a.length;o++){let c=Oa(a[o])?a[o]:a[o].toString(),l=e[c];t(l,c,e)&&(r[c]=l)}return r}var lp=q(()=>{ap(),Jd(),op(),Na(),Oi(),i(yu,"pickBy")}),an=q(()=>{Xd(),Qd(),Yd(),lp()}),Te,or,je=q(()=>{"use strict";Te=i(e=>`control-${e.replace(/\s+/g,"-")}`,"getControlId"),or=i(e=>`set-${e.replace(/\s+/g,"-")}`,"getControlSetterButtonId")}),ip=Y((e,t)=>{"use strict";t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}),Eu=Y((e,t)=>{var r=ip(),a={};for(let l of Object.keys(r))a[r[l]]=l;var o={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};t.exports=o;for(let l of Object.keys(o)){if(!("channels"in o[l]))throw new Error("missing channels property: "+l);if(!("labels"in o[l]))throw new Error("missing channel labels property: "+l);if(o[l].labels.length!==o[l].channels)throw new Error("channel and label counts mismatch: "+l);let{channels:u,labels:s}=o[l];delete o[l].channels,delete o[l].labels,Object.defineProperty(o[l],"channels",{value:u}),Object.defineProperty(o[l],"labels",{value:s})}o.rgb.hsl=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.min(u,s,d),f=Math.max(u,s,d),p=f-m,h,g;f===m?h=0:u===f?h=(s-d)/p:s===f?h=2+(d-u)/p:d===f&&(h=4+(u-s)/p),h=Math.min(h*60,360),h<0&&(h+=360);let E=(m+f)/2;return f===m?g=0:E<=.5?g=p/(f+m):g=p/(2-f-m),[h,g*100,E*100]},o.rgb.hsv=function(l){let u,s,d,m,f,p=l[0]/255,h=l[1]/255,g=l[2]/255,E=Math.max(p,h,g),y=E-Math.min(p,h,g),v=i(function(C){return(E-C)/6/y+1/2},"diffc");return y===0?(m=0,f=0):(f=y/E,u=v(p),s=v(h),d=v(g),p===E?m=d-s:h===E?m=1/3+u-d:g===E&&(m=2/3+s-u),m<0?m+=1:m>1&&(m-=1)),[m*360,f*100,E*100]},o.rgb.hwb=function(l){let u=l[0],s=l[1],d=l[2],m=o.rgb.hsl(l)[0],f=1/255*Math.min(u,Math.min(s,d));return d=1-1/255*Math.max(u,Math.max(s,d)),[m,f*100,d*100]},o.rgb.cmyk=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.min(1-u,1-s,1-d),f=(1-u-m)/(1-m)||0,p=(1-s-m)/(1-m)||0,h=(1-d-m)/(1-m)||0;return[f*100,p*100,h*100,m*100]};function c(l,u){return(l[0]-u[0])**2+(l[1]-u[1])**2+(l[2]-u[2])**2}i(c,"comparativeDistance"),o.rgb.keyword=function(l){let u=a[l];if(u)return u;let s=1/0,d;for(let m of Object.keys(r)){let f=r[m],p=c(l,f);p<s&&(s=p,d=m)}return d},o.keyword.rgb=function(l){return r[l]},o.rgb.xyz=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255;u=u>.04045?((u+.055)/1.055)**2.4:u/12.92,s=s>.04045?((s+.055)/1.055)**2.4:s/12.92,d=d>.04045?((d+.055)/1.055)**2.4:d/12.92;let m=u*.4124+s*.3576+d*.1805,f=u*.2126+s*.7152+d*.0722,p=u*.0193+s*.1192+d*.9505;return[m*100,f*100,p*100]},o.rgb.lab=function(l){let u=o.rgb.xyz(l),s=u[0],d=u[1],m=u[2];s/=95.047,d/=100,m/=108.883,s=s>.008856?s**(1/3):7.787*s+16/116,d=d>.008856?d**(1/3):7.787*d+16/116,m=m>.008856?m**(1/3):7.787*m+16/116;let f=116*d-16,p=500*(s-d),h=200*(d-m);return[f,p,h]},o.hsl.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100,m,f,p;if(s===0)return p=d*255,[p,p,p];d<.5?m=d*(1+s):m=d+s-d*s;let h=2*d-m,g=[0,0,0];for(let E=0;E<3;E++)f=u+1/3*-(E-1),f<0&&f++,f>1&&f--,6*f<1?p=h+(m-h)*6*f:2*f<1?p=m:3*f<2?p=h+(m-h)*(2/3-f)*6:p=h,g[E]=p*255;return g},o.hsl.hsv=function(l){let u=l[0],s=l[1]/100,d=l[2]/100,m=s,f=Math.max(d,.01);d*=2,s*=d<=1?d:2-d,m*=f<=1?f:2-f;let p=(d+s)/2,h=d===0?2*m/(f+m):2*s/(d+s);return[u,h*100,p*100]},o.hsv.rgb=function(l){let u=l[0]/60,s=l[1]/100,d=l[2]/100,m=Math.floor(u)%6,f=u-Math.floor(u),p=255*d*(1-s),h=255*d*(1-s*f),g=255*d*(1-s*(1-f));switch(d*=255,m){case 0:return[d,g,p];case 1:return[h,d,p];case 2:return[p,d,g];case 3:return[p,h,d];case 4:return[g,p,d];case 5:return[d,p,h]}},o.hsv.hsl=function(l){let u=l[0],s=l[1]/100,d=l[2]/100,m=Math.max(d,.01),f,p;p=(2-s)*d;let h=(2-s)*m;return f=s*m,f/=h<=1?h:2-h,f=f||0,p/=2,[u,f*100,p*100]},o.hwb.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100,m=s+d,f;m>1&&(s/=m,d/=m);let p=Math.floor(6*u),h=1-d;f=6*u-p,(p&1)!==0&&(f=1-f);let g=s+f*(h-s),E,y,v;switch(p){default:case 6:case 0:E=h,y=g,v=s;break;case 1:E=g,y=h,v=s;break;case 2:E=s,y=h,v=g;break;case 3:E=s,y=g,v=h;break;case 4:E=g,y=s,v=h;break;case 5:E=h,y=s,v=g;break}return[E*255,y*255,v*255]},o.cmyk.rgb=function(l){let u=l[0]/100,s=l[1]/100,d=l[2]/100,m=l[3]/100,f=1-Math.min(1,u*(1-m)+m),p=1-Math.min(1,s*(1-m)+m),h=1-Math.min(1,d*(1-m)+m);return[f*255,p*255,h*255]},o.xyz.rgb=function(l){let u=l[0]/100,s=l[1]/100,d=l[2]/100,m,f,p;return m=u*3.2406+s*-1.5372+d*-.4986,f=u*-.9689+s*1.8758+d*.0415,p=u*.0557+s*-.204+d*1.057,m=m>.0031308?1.055*m**(1/2.4)-.055:m*12.92,f=f>.0031308?1.055*f**(1/2.4)-.055:f*12.92,p=p>.0031308?1.055*p**(1/2.4)-.055:p*12.92,m=Math.min(Math.max(0,m),1),f=Math.min(Math.max(0,f),1),p=Math.min(Math.max(0,p),1),[m*255,f*255,p*255]},o.xyz.lab=function(l){let u=l[0],s=l[1],d=l[2];u/=95.047,s/=100,d/=108.883,u=u>.008856?u**(1/3):7.787*u+16/116,s=s>.008856?s**(1/3):7.787*s+16/116,d=d>.008856?d**(1/3):7.787*d+16/116;let m=116*s-16,f=500*(u-s),p=200*(s-d);return[m,f,p]},o.lab.xyz=function(l){let u=l[0],s=l[1],d=l[2],m,f,p;f=(u+16)/116,m=s/500+f,p=f-d/200;let h=f**3,g=m**3,E=p**3;return f=h>.008856?h:(f-16/116)/7.787,m=g>.008856?g:(m-16/116)/7.787,p=E>.008856?E:(p-16/116)/7.787,m*=95.047,f*=100,p*=108.883,[m,f,p]},o.lab.lch=function(l){let u=l[0],s=l[1],d=l[2],m;m=Math.atan2(d,s)*360/2/Math.PI,m<0&&(m+=360);let f=Math.sqrt(s*s+d*d);return[u,f,m]},o.lch.lab=function(l){let u=l[0],s=l[1],d=l[2]/360*2*Math.PI,m=s*Math.cos(d),f=s*Math.sin(d);return[u,m,f]},o.rgb.ansi16=function(l,u=null){let[s,d,m]=l,f=u===null?o.rgb.hsv(l)[2]:u;if(f=Math.round(f/50),f===0)return 30;let p=30+(Math.round(m/255)<<2|Math.round(d/255)<<1|Math.round(s/255));return f===2&&(p+=60),p},o.hsv.ansi16=function(l){return o.rgb.ansi16(o.hsv.rgb(l),l[2])},o.rgb.ansi256=function(l){let u=l[0],s=l[1],d=l[2];return u===s&&s===d?u<8?16:u>248?231:Math.round((u-8)/247*24)+232:16+36*Math.round(u/255*5)+6*Math.round(s/255*5)+Math.round(d/255*5)},o.ansi16.rgb=function(l){let u=l%10;if(u===0||u===7)return l>50&&(u+=3.5),u=u/10.5*255,[u,u,u];let s=(~~(l>50)+1)*.5,d=(u&1)*s*255,m=(u>>1&1)*s*255,f=(u>>2&1)*s*255;return[d,m,f]},o.ansi256.rgb=function(l){if(l>=232){let f=(l-232)*10+8;return[f,f,f]}l-=16;let u,s=Math.floor(l/36)/5*255,d=Math.floor((u=l%36)/6)/5*255,m=u%6/5*255;return[s,d,m]},o.rgb.hex=function(l){let u=(((Math.round(l[0])&255)<<16)+((Math.round(l[1])&255)<<8)+(Math.round(l[2])&255)).toString(16).toUpperCase();return"000000".substring(u.length)+u},o.hex.rgb=function(l){let u=l.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!u)return[0,0,0];let s=u[0];u[0].length===3&&(s=s.split("").map(h=>h+h).join(""));let d=parseInt(s,16),m=d>>16&255,f=d>>8&255,p=d&255;return[m,f,p]},o.rgb.hcg=function(l){let u=l[0]/255,s=l[1]/255,d=l[2]/255,m=Math.max(Math.max(u,s),d),f=Math.min(Math.min(u,s),d),p=m-f,h,g;return p<1?h=f/(1-p):h=0,p<=0?g=0:m===u?g=(s-d)/p%6:m===s?g=2+(d-u)/p:g=4+(u-s)/p,g/=6,g%=1,[g*360,p*100,h*100]},o.hsl.hcg=function(l){let u=l[1]/100,s=l[2]/100,d=s<.5?2*u*s:2*u*(1-s),m=0;return d<1&&(m=(s-.5*d)/(1-d)),[l[0],d*100,m*100]},o.hsv.hcg=function(l){let u=l[1]/100,s=l[2]/100,d=u*s,m=0;return d<1&&(m=(s-d)/(1-d)),[l[0],d*100,m*100]},o.hcg.rgb=function(l){let u=l[0]/360,s=l[1]/100,d=l[2]/100;if(s===0)return[d*255,d*255,d*255];let m=[0,0,0],f=u%1*6,p=f%1,h=1-p,g=0;switch(Math.floor(f)){case 0:m[0]=1,m[1]=p,m[2]=0;break;case 1:m[0]=h,m[1]=1,m[2]=0;break;case 2:m[0]=0,m[1]=1,m[2]=p;break;case 3:m[0]=0,m[1]=h,m[2]=1;break;case 4:m[0]=p,m[1]=0,m[2]=1;break;default:m[0]=1,m[1]=0,m[2]=h}return g=(1-s)*d,[(s*m[0]+g)*255,(s*m[1]+g)*255,(s*m[2]+g)*255]},o.hcg.hsv=function(l){let u=l[1]/100,s=l[2]/100,d=u+s*(1-u),m=0;return d>0&&(m=u/d),[l[0],m*100,d*100]},o.hcg.hsl=function(l){let u=l[1]/100,s=l[2]/100*(1-u)+.5*u,d=0;return s>0&&s<.5?d=u/(2*s):s>=.5&&s<1&&(d=u/(2*(1-s))),[l[0],d*100,s*100]},o.hcg.hwb=function(l){let u=l[1]/100,s=l[2]/100,d=u+s*(1-u);return[l[0],(d-u)*100,(1-d)*100]},o.hwb.hcg=function(l){let u=l[1]/100,s=1-l[2]/100,d=s-u,m=0;return d<1&&(m=(s-d)/(1-d)),[l[0],d*100,m*100]},o.apple.rgb=function(l){return[l[0]/65535*255,l[1]/65535*255,l[2]/65535*255]},o.rgb.apple=function(l){return[l[0]/255*65535,l[1]/255*65535,l[2]/255*65535]},o.gray.rgb=function(l){return[l[0]/100*255,l[0]/100*255,l[0]/100*255]},o.gray.hsl=function(l){return[0,0,l[0]]},o.gray.hsv=o.gray.hsl,o.gray.hwb=function(l){return[0,100,l[0]]},o.gray.cmyk=function(l){return[0,0,0,l[0]]},o.gray.lab=function(l){return[l[0],0,0]},o.gray.hex=function(l){let u=Math.round(l[0]/100*255)&255,s=((u<<16)+(u<<8)+u).toString(16).toUpperCase();return"000000".substring(s.length)+s},o.rgb.gray=function(l){return[(l[0]+l[1]+l[2])/3/255*100]}}),up=Y((e,t)=>{var r=Eu();function a(){let u={},s=Object.keys(r);for(let d=s.length,m=0;m<d;m++)u[s[m]]={distance:-1,parent:null};return u}i(a,"buildGraph");function o(u){let s=a(),d=[u];for(s[u].distance=0;d.length;){let m=d.pop(),f=Object.keys(r[m]);for(let p=f.length,h=0;h<p;h++){let g=f[h],E=s[g];E.distance===-1&&(E.distance=s[m].distance+1,E.parent=m,d.unshift(g))}}return s}i(o,"deriveBFS");function c(u,s){return function(d){return s(u(d))}}i(c,"link");function l(u,s){let d=[s[u].parent,u],m=r[s[u].parent][u],f=s[u].parent;for(;s[f].parent;)d.unshift(s[f].parent),m=c(r[s[f].parent][f],m),f=s[f].parent;return m.conversion=d,m}i(l,"wrapConversion"),t.exports=function(u){let s=o(u),d={},m=Object.keys(s);for(let f=m.length,p=0;p<f;p++){let h=m[p];s[h].parent!==null&&(d[h]=l(h,s))}return d}}),sp=Y((e,t)=>{var r=Eu(),a=up(),o={},c=Object.keys(r);function l(s){let d=i(function(...m){let f=m[0];return f==null?f:(f.length>1&&(m=f),s(m))},"wrappedFn");return"conversion"in s&&(d.conversion=s.conversion),d}i(l,"wrapRaw");function u(s){let d=i(function(...m){let f=m[0];if(f==null)return f;f.length>1&&(m=f);let p=s(m);if(typeof p=="object")for(let h=p.length,g=0;g<h;g++)p[g]=Math.round(p[g]);return p},"wrappedFn");return"conversion"in s&&(d.conversion=s.conversion),d}i(u,"wrapRounded"),c.forEach(s=>{o[s]={},Object.defineProperty(o[s],"channels",{value:r[s].channels}),Object.defineProperty(o[s],"labels",{value:r[s].labels});let d=a(s);Object.keys(d).forEach(m=>{let f=d[m];o[s][m]=u(f),o[s][m].raw=l(f)})}),t.exports=o});function rt(){return(rt=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function wr(e,t){if(e==null)return{};var r,a,o={},c=Object.keys(e);for(a=0;a<c.length;a++)t.indexOf(r=c[a])>=0||(o[r]=e[r]);return o}function Mr(e){var t=X(e),r=X(function(a){t.current&&t.current(a)});return t.current=e,r.current}function On(e,t,r){var a=Mr(r),o=R(function(){return e.toHsva(t)}),c=o[0],l=o[1],u=X({color:t,hsva:c});j(function(){if(!e.equal(t,u.current.color)){var d=e.toHsva(t);u.current={hsva:d,color:t},l(d)}},[t,e]),j(function(){var d;na(c,u.current.hsva)||e.equal(d=e.fromHsva(c),u.current.color)||(u.current={hsva:c,color:d},a(d))},[c,e,a]);var s=$(function(d){l(function(m){return Object.assign({},m,d)})},[]);return[c,s]}var pt,wt,kr,Tn,In,Or,kt,Tr,le,sl,cl,Ir,dl,pl,ml,fl,Dn,Dr,Gt,Bn,hl,Wt,gl,_n,Rn,Nn,na,Fn,bl,cp,yl,El,Ln,Pn,vl,xl,vu,Al,jn,Cl,xu,Sl,Au,dp=q(()=>{i(rt,"u"),i(wr,"c"),i(Mr,"i"),pt=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=1),e>r?r:e<t?t:e},"s"),wt=i(function(e){return"touches"in e},"f"),kr=i(function(e){return e&&e.ownerDocument.defaultView||self},"v"),Tn=i(function(e,t,r){var a=e.getBoundingClientRect(),o=wt(t)?function(c,l){for(var u=0;u<c.length;u++)if(c[u].identifier===l)return c[u];return c[0]}(t.touches,r):t;return{left:pt((o.pageX-(a.left+kr(e).pageXOffset))/a.width),top:pt((o.pageY-(a.top+kr(e).pageYOffset))/a.height)}},"d"),In=i(function(e){!wt(e)&&e.preventDefault()},"h"),Or=n.memo(function(e){var t=e.onMove,r=e.onKey,a=wr(e,["onMove","onKey"]),o=X(null),c=Mr(t),l=Mr(r),u=X(null),s=X(!1),d=ce(function(){var h=i(function(y){In(y),(wt(y)?y.touches.length>0:y.buttons>0)&&o.current?c(Tn(o.current,y,u.current)):E(!1)},"e"),g=i(function(){return E(!1)},"r");function E(y){var v=s.current,C=kr(o.current),w=y?C.addEventListener:C.removeEventListener;w(v?"touchmove":"mousemove",h),w(v?"touchend":"mouseup",g)}return i(E,"t"),[function(y){var v=y.nativeEvent,C=o.current;if(C&&(In(v),!function(k,I){return I&&!wt(k)}(v,s.current)&&C)){if(wt(v)){s.current=!0;var w=v.changedTouches||[];w.length&&(u.current=w[0].identifier)}C.focus(),c(Tn(C,v,u.current)),E(!0)}},function(y){var v=y.which||y.keyCode;v<37||v>40||(y.preventDefault(),l({left:v===39?.05:v===37?-.05:0,top:v===40?.05:v===38?-.05:0}))},E]},[l,c]),m=d[0],f=d[1],p=d[2];return j(function(){return p},[p]),n.createElement("div",rt({},a,{onTouchStart:m,onMouseDown:m,className:"react-colorful__interactive",ref:o,onKeyDown:f,tabIndex:0,role:"slider"}))}),kt=i(function(e){return e.filter(Boolean).join(" ")},"g"),Tr=i(function(e){var t=e.color,r=e.left,a=e.top,o=a===void 0?.5:a,c=kt(["react-colorful__pointer",e.className]);return n.createElement("div",{className:c,style:{top:100*o+"%",left:100*r+"%"}},n.createElement("div",{className:"react-colorful__pointer-fill",style:{backgroundColor:t}}))},"p"),le=i(function(e,t,r){return t===void 0&&(t=0),r===void 0&&(r=Math.pow(10,t)),Math.round(r*e)/r},"b"),sl={grad:.9,turn:360,rad:360/(2*Math.PI)},cl=i(function(e){return _n(Ir(e))},"x"),Ir=i(function(e){return e[0]==="#"&&(e=e.substring(1)),e.length<6?{r:parseInt(e[0]+e[0],16),g:parseInt(e[1]+e[1],16),b:parseInt(e[2]+e[2],16),a:e.length===4?le(parseInt(e[3]+e[3],16)/255,2):1}:{r:parseInt(e.substring(0,2),16),g:parseInt(e.substring(2,4),16),b:parseInt(e.substring(4,6),16),a:e.length===8?le(parseInt(e.substring(6,8),16)/255,2):1}},"C"),dl=i(function(e,t){return t===void 0&&(t="deg"),Number(e)*(sl[t]||1)},"E"),pl=i(function(e){var t=/hsla?\(?\s*(-?\d*\.?\d+)(deg|rad|grad|turn)?[,\s]+(-?\d*\.?\d+)%?[,\s]+(-?\d*\.?\d+)%?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?ml({h:dl(t[1],t[2]),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)}):{h:0,s:0,v:0,a:1}},"H"),ml=i(function(e){var t=e.s,r=e.l;return{h:e.h,s:(t*=(r<50?r:100-r)/100)>0?2*t/(r+t)*100:0,v:r+t,a:e.a}},"N"),fl=i(function(e){return gl(Bn(e))},"w"),Dn=i(function(e){var t=e.s,r=e.v,a=e.a,o=(200-t)*r/100;return{h:le(e.h),s:le(o>0&&o<200?t*r/100/(o<=100?o:200-o)*100:0),l:le(o/2),a:le(a,2)}},"y"),Dr=i(function(e){var t=Dn(e);return"hsl("+t.h+", "+t.s+"%, "+t.l+"%)"},"q"),Gt=i(function(e){var t=Dn(e);return"hsla("+t.h+", "+t.s+"%, "+t.l+"%, "+t.a+")"},"k"),Bn=i(function(e){var t=e.h,r=e.s,a=e.v,o=e.a;t=t/360*6,r/=100,a/=100;var c=Math.floor(t),l=a*(1-r),u=a*(1-(t-c)*r),s=a*(1-(1-t+c)*r),d=c%6;return{r:le(255*[a,u,l,l,s,a][d]),g:le(255*[s,a,a,u,l,l][d]),b:le(255*[l,l,s,a,a,u][d]),a:le(o,2)}},"I"),hl=i(function(e){var t=/rgba?\(?\s*(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?[,\s]+(-?\d*\.?\d+)(%)?,?\s*[/\s]*(-?\d*\.?\d+)?(%)?\s*\)?/i.exec(e);return t?_n({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):{h:0,s:0,v:0,a:1}},"z"),Wt=i(function(e){var t=e.toString(16);return t.length<2?"0"+t:t},"D"),gl=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=o<1?Wt(le(255*o)):"";return"#"+Wt(t)+Wt(r)+Wt(a)+c},"K"),_n=i(function(e){var t=e.r,r=e.g,a=e.b,o=e.a,c=Math.max(t,r,a),l=c-Math.min(t,r,a),u=l?c===t?(r-a)/l:c===r?2+(a-t)/l:4+(t-r)/l:0;return{h:le(60*(u<0?u+6:u)),s:le(c?l/c*100:0),v:le(c/255*100),a:o}},"L"),Rn=n.memo(function(e){var t=e.hue,r=e.onChange,a=kt(["react-colorful__hue",e.className]);return n.createElement("div",{className:a},n.createElement(Or,{onMove:i(function(o){r({h:360*o.left})},"onMove"),onKey:i(function(o){r({h:pt(t+360*o.left,0,360)})},"onKey"),"aria-label":"Hue","aria-valuenow":le(t),"aria-valuemax":"360","aria-valuemin":"0"},n.createElement(Tr,{className:"react-colorful__hue-pointer",left:t/360,color:Dr({h:t,s:100,v:100,a:1})})))}),Nn=n.memo(function(e){var t=e.hsva,r=e.onChange,a={backgroundColor:Dr({h:t.h,s:100,v:100,a:1})};return n.createElement("div",{className:"react-colorful__saturation",style:a},n.createElement(Or,{onMove:i(function(o){r({s:100*o.left,v:100-100*o.top})},"onMove"),onKey:i(function(o){r({s:pt(t.s+100*o.left,0,100),v:pt(t.v-100*o.top,0,100)})},"onKey"),"aria-label":"Color","aria-valuetext":"Saturation "+le(t.s)+"%, Brightness "+le(t.v)+"%"},n.createElement(Tr,{className:"react-colorful__saturation-pointer",top:1-t.v/100,left:t.s/100,color:Dr(t)})))}),na=i(function(e,t){if(e===t)return!0;for(var r in e)if(e[r]!==t[r])return!1;return!0},"F"),Fn=i(function(e,t){return e.replace(/\s/g,"")===t.replace(/\s/g,"")},"P"),bl=i(function(e,t){return e.toLowerCase()===t.toLowerCase()||na(Ir(e),Ir(t))},"X"),i(On,"Y"),yl=typeof window<"u"?pr:j,El=i(function(){return cp||(typeof __webpack_nonce__<"u"?__webpack_nonce__:void 0)},"$"),Ln=new Map,Pn=i(function(e){yl(function(){var t=e.current?e.current.ownerDocument:document;if(t!==void 0&&!Ln.has(t)){var r=t.createElement("style");r.innerHTML=`.react-colorful{position:relative;display:flex;flex-direction:column;width:200px;height:200px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.react-colorful__saturation{position:relative;flex-grow:1;border-color:transparent;border-bottom:12px solid #000;border-radius:8px 8px 0 0;background-image:linear-gradient(0deg,#000,transparent),linear-gradient(90deg,#fff,hsla(0,0%,100%,0))}.react-colorful__alpha-gradient,.react-colorful__pointer-fill{content:"";position:absolute;left:0;top:0;right:0;bottom:0;pointer-events:none;border-radius:inherit}.react-colorful__alpha-gradient,.react-colorful__saturation{box-shadow:inset 0 0 0 1px rgba(0,0,0,.05)}.react-colorful__alpha,.react-colorful__hue{position:relative;height:24px}.react-colorful__hue{background:linear-gradient(90deg,red 0,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,red)}.react-colorful__last-control{border-radius:0 0 8px 8px}.react-colorful__interactive{position:absolute;left:0;top:0;right:0;bottom:0;border-radius:inherit;outline:none;touch-action:none}.react-colorful__pointer{position:absolute;z-index:1;box-sizing:border-box;width:28px;height:28px;transform:translate(-50%,-50%);background-color:#fff;border:2px solid #fff;border-radius:50%;box-shadow:0 2px 4px rgba(0,0,0,.2)}.react-colorful__interactive:focus .react-colorful__pointer{transform:translate(-50%,-50%) scale(1.1)}.react-colorful__alpha,.react-colorful__alpha-pointer{background-color:#fff;background-image:url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')}.react-colorful__saturation-pointer{z-index:3}.react-colorful__hue-pointer{z-index:2}`,Ln.set(t,r);var a=El();a&&r.setAttribute("nonce",a),t.head.appendChild(r)}},[])},"Q"),vl=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),u=X(null);Pn(u);var s=On(r,o,c),d=s[0],m=s[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:u,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m,className:"react-colorful__last-control"}))},"U"),xl={defaultColor:"000",toHsva:cl,fromHsva:i(function(e){return fl({h:e.h,s:e.s,v:e.v,a:1})},"fromHsva"),equal:bl},vu=i(function(e){return n.createElement(vl,rt({},e,{colorModel:xl}))},"Z"),Al=i(function(e){var t=e.className,r=e.hsva,a=e.onChange,o={backgroundImage:"linear-gradient(90deg, "+Gt(Object.assign({},r,{a:0}))+", "+Gt(Object.assign({},r,{a:1}))+")"},c=kt(["react-colorful__alpha",t]),l=le(100*r.a);return n.createElement("div",{className:c},n.createElement("div",{className:"react-colorful__alpha-gradient",style:o}),n.createElement(Or,{onMove:i(function(u){a({a:u.left})},"onMove"),onKey:i(function(u){a({a:pt(r.a+u.left)})},"onKey"),"aria-label":"Alpha","aria-valuetext":l+"%","aria-valuenow":l,"aria-valuemin":"0","aria-valuemax":"100"},n.createElement(Tr,{className:"react-colorful__alpha-pointer",left:r.a,color:Gt(r)})))},"ee"),jn=i(function(e){var t=e.className,r=e.colorModel,a=e.color,o=a===void 0?r.defaultColor:a,c=e.onChange,l=wr(e,["className","colorModel","color","onChange"]),u=X(null);Pn(u);var s=On(r,o,c),d=s[0],m=s[1],f=kt(["react-colorful",t]);return n.createElement("div",rt({},l,{ref:u,className:f}),n.createElement(Nn,{hsva:d,onChange:m}),n.createElement(Rn,{hue:d.h,onChange:m}),n.createElement(Al,{hsva:d,onChange:m,className:"react-colorful__last-control"}))},"re"),Cl={defaultColor:"hsla(0, 0%, 0%, 1)",toHsva:pl,fromHsva:Gt,equal:Fn},xu=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Cl}))},"ue"),Sl={defaultColor:"rgba(0, 0, 0, 1)",toHsva:hl,fromHsva:i(function(e){var t=Bn(e);return"rgba("+t.r+", "+t.g+", "+t.b+", "+t.a+")"},"fromHsva"),equal:Fn},Au=i(function(e){return n.createElement(jn,rt({},e,{colorModel:Sl}))},"He")}),Cu={};jd(Cu,{ColorControl:()=>aa,default:()=>Su});var ke,wl,kl,Ol,Tl,Il,Dl,Bl,Mn,_l,Rl,Un,Br,Nl,Fl,Ll,_r,Pl,jl,Kt,$n,Ml,Ul,$l,mt,Hl,Vl,Yt,zl,aa,Su,pp=q(()=>{"use strict";ke=Se(sp()),an(),dp(),je(),wl=b.div({position:"relative",maxWidth:250,'&[aria-readonly="true"]':{opacity:.5}}),kl=b(oe)({position:"absolute",zIndex:1,top:4,left:4,"[aria-readonly=true] &":{cursor:"not-allowed"}}),Ol=b.div({width:200,margin:5,".react-colorful__saturation":{borderRadius:"4px 4px 0 0"},".react-colorful__hue":{boxShadow:"inset 0 0 0 1px rgb(0 0 0 / 5%)"},".react-colorful__last-control":{borderRadius:"0 0 4px 4px"}}),Tl=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),Il=b.div({display:"grid",gridTemplateColumns:"repeat(9, 16px)",gap:6,padding:3,marginTop:5,width:200}),Dl=b.div(({theme:e,active:t})=>({width:16,height:16,boxShadow:t?`${e.appBorderColor} 0 0 0 1px inset, ${e.textMutedColor}50 0 0 0 4px`:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:e.appBorderRadius})),Bl=`url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill-opacity=".05"><path d="M8 0h8v8H8zM0 8h8v8H0z"/></svg>')`,Mn=i(({value:e,style:t,...r})=>{let a=`linear-gradient(${e}, ${e}), ${Bl}, linear-gradient(#fff, #fff)`;return n.createElement(Dl,{...r,style:{...t,backgroundImage:a}})},"Swatch"),_l=b(de.Input)(({theme:e,readOnly:t})=>({width:"100%",paddingLeft:30,paddingRight:30,boxSizing:"border-box",fontFamily:e.typography.fonts.base})),Rl=b(ko)(({theme:e})=>({position:"absolute",zIndex:1,top:6,right:7,width:20,height:20,padding:4,boxSizing:"border-box",cursor:"pointer",color:e.input.color})),Un=(e=>(e.RGB="rgb",e.HSL="hsl",e.HEX="hex",e))(Un||{}),Br=Object.values(Un),Nl=/\(([0-9]+),\s*([0-9]+)%?,\s*([0-9]+)%?,?\s*([0-9.]+)?\)/,Fl=/^\s*rgba?\(([0-9]+),\s*([0-9]+),\s*([0-9]+),?\s*([0-9.]+)?\)\s*$/i,Ll=/^\s*hsla?\(([0-9]+),\s*([0-9]+)%,\s*([0-9]+)%,?\s*([0-9.]+)?\)\s*$/i,_r=/^\s*#?([0-9a-f]{3}|[0-9a-f]{6})\s*$/i,Pl=/^\s*#?([0-9a-f]{3})\s*$/i,jl={hex:vu,rgb:Au,hsl:xu},Kt={hex:"transparent",rgb:"rgba(0, 0, 0, 0)",hsl:"hsla(0, 0%, 0%, 0)"},$n=i(e=>{let t=e?.match(Nl);if(!t)return[0,0,0,1];let[,r,a,o,c=1]=t;return[r,a,o,c].map(Number)},"stringToArgs"),Ml=i(e=>{let[t,r,a,o]=$n(e),[c,l,u]=ke.default.rgb.hsl([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:ke.default.rgb.keyword([t,r,a]),colorSpace:"rgb",rgb:e,hsl:`hsla(${c}, ${l}%, ${u}%, ${o})`,hex:`#${ke.default.rgb.hex([t,r,a]).toLowerCase()}`}},"parseRgb"),Ul=i(e=>{let[t,r,a,o]=$n(e),[c,l,u]=ke.default.hsl.rgb([t,r,a])||[0,0,0];return{valid:!0,value:e,keyword:ke.default.hsl.keyword([t,r,a]),colorSpace:"hsl",rgb:`rgba(${c}, ${l}, ${u}, ${o})`,hsl:e,hex:`#${ke.default.hsl.hex([t,r,a]).toLowerCase()}`}},"parseHsl"),$l=i(e=>{let t=e.replace("#",""),r=ke.default.keyword.rgb(t)||ke.default.hex.rgb(t),a=ke.default.rgb.hsl(r),o=e;/[^#a-f0-9]/i.test(e)?o=t:_r.test(e)&&(o=`#${t}`);let c=!0;if(o.startsWith("#"))c=_r.test(o);else try{ke.default.keyword.hex(o)}catch{c=!1}return{valid:c,value:o,keyword:ke.default.rgb.keyword(r),colorSpace:"hex",rgb:`rgba(${r[0]}, ${r[1]}, ${r[2]}, 1)`,hsl:`hsla(${a[0]}, ${a[1]}%, ${a[2]}%, 1)`,hex:o}},"parseHexOrKeyword"),mt=i(e=>{if(e)return Fl.test(e)?Ml(e):Ll.test(e)?Ul(e):$l(e)},"parseValue"),Hl=i((e,t,r)=>{if(!e||!t?.valid)return Kt[r];if(r!=="hex")return t?.[r]||Kt[r];if(!t.hex.startsWith("#"))try{return`#${ke.default.keyword.hex(t.hex)}`}catch{return Kt.hex}let a=t.hex.match(Pl);if(!a)return _r.test(t.hex)?t.hex:Kt.hex;let[o,c,l]=a[1].split("");return`#${o}${o}${c}${c}${l}${l}`},"getRealValue"),Vl=i((e,t)=>{let[r,a]=R(e||""),[o,c]=R(()=>mt(r)),[l,u]=R(o?.colorSpace||"hex");j(()=>{let f=e||"",p=mt(f);a(f),c(p),u(p?.colorSpace||"hex")},[e]);let s=ce(()=>Hl(r,o,l).toLowerCase(),[r,o,l]),d=$(f=>{let p=mt(f),h=p?.value||f||"";a(h),h===""&&(c(void 0),t(void 0)),p&&(c(p),u(p.colorSpace),t(p.value))},[t]),m=$(()=>{let f=(Br.indexOf(l)+1)%Br.length,p=Br[f];u(p);let h=o?.[p]||"";a(h),t(h)},[o,l,t]);return{value:r,realValue:s,updateValue:d,color:o,colorSpace:l,cycleColorSpace:m}},"useColorInput"),Yt=i(e=>e.replace(/\s*/,"").toLowerCase(),"id"),zl=i((e,t,r)=>{let[a,o]=R(t?.valid?[t]:[]);j(()=>{t===void 0&&o([])},[t]);let c=ce(()=>(e||[]).map(u=>typeof u=="string"?mt(u):u.title?{...mt(u.color),keyword:u.title}:mt(u.color)).concat(a).filter(Boolean).slice(-27),[e,a]),l=$(u=>{u?.valid&&(c.some(s=>s&&s[r]&&Yt(s[r]||"")===Yt(u[r]||""))||o(s=>s.concat(u)))},[r,c]);return{presets:c,addPreset:l}},"usePresets"),aa=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,presetColors:c,startOpen:l=!1,argType:u})=>{let s=$(su(r,200),[r]),{value:d,realValue:m,updateValue:f,color:p,colorSpace:h,cycleColorSpace:g}=Vl(t,s),{presets:E,addPreset:y}=zl(c??[],p,h),v=jl[h],C=!!u?.table?.readonly;return n.createElement(wl,{"aria-readonly":C},n.createElement(kl,{startOpen:l,trigger:C?null:void 0,closeOnOutsideClick:!0,onVisibleChange:()=>p&&y(p),tooltip:n.createElement(Ol,null,n.createElement(v,{color:m==="transparent"?"#000000":m,onChange:f,onFocus:a,onBlur:o}),E.length>0&&n.createElement(Il,null,E.map((w,k)=>n.createElement(oe,{key:`${w?.value||k}-${k}`,hasChrome:!1,tooltip:n.createElement(Tl,{note:w?.keyword||w?.value||""})},n.createElement(Mn,{value:w?.[h]||"",active:!!(p&&w&&w[h]&&Yt(w[h]||"")===Yt(p[h])),onClick:()=>w&&f(w.value||"")})))))},n.createElement(Mn,{value:m,style:{margin:4}})),n.createElement(_l,{id:Te(e),value:d,onChange:w=>f(w.target.value),onFocus:w=>w.target.select(),readOnly:C,placeholder:"Choose color..."}),d?n.createElement(Rl,{onClick:g}):null)},"ColorControl"),Su=aa}),mp=Y((e,t)=>{(function(r){if(typeof e=="object"&&typeof t<"u")t.exports=r();else if(typeof define=="function"&&define.amd)define([],r);else{var a;typeof window<"u"||typeof window<"u"?a=window:typeof self<"u"?a=self:a=this,a.memoizerific=r()}})(function(){var r,a,o;return i(function c(l,u,s){function d(p,h){if(!u[p]){if(!l[p]){var g=typeof Sr=="function"&&Sr;if(!h&&g)return g(p,!0);if(m)return m(p,!0);var E=new Error("Cannot find module '"+p+"'");throw E.code="MODULE_NOT_FOUND",E}var y=u[p]={exports:{}};l[p][0].call(y.exports,function(v){var C=l[p][1][v];return d(C||v)},y,y.exports,c,l,u,s)}return u[p].exports}i(d,"s");for(var m=typeof Sr=="function"&&Sr,f=0;f<s.length;f++)d(s[f]);return d},"e")({1:[function(c,l,u){l.exports=function(s){if(typeof Map!="function"||s){var d=c("./similar");return new d}else return new Map}},{"./similar":2}],2:[function(c,l,u){function s(){return this.list=[],this.lastItem=void 0,this.size=0,this}i(s,"Similar"),s.prototype.get=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d))return this.lastItem.val;if(m=this.indexOf(d),m>=0)return this.lastItem=this.list[m],this.list[m].val},s.prototype.set=function(d,m){var f;return this.lastItem&&this.isEqual(this.lastItem.key,d)?(this.lastItem.val=m,this):(f=this.indexOf(d),f>=0?(this.lastItem=this.list[f],this.list[f].val=m,this):(this.lastItem={key:d,val:m},this.list.push(this.lastItem),this.size++,this))},s.prototype.delete=function(d){var m;if(this.lastItem&&this.isEqual(this.lastItem.key,d)&&(this.lastItem=void 0),m=this.indexOf(d),m>=0)return this.size--,this.list.splice(m,1)[0]},s.prototype.has=function(d){var m;return this.lastItem&&this.isEqual(this.lastItem.key,d)?!0:(m=this.indexOf(d),m>=0?(this.lastItem=this.list[m],!0):!1)},s.prototype.forEach=function(d,m){var f;for(f=0;f<this.size;f++)d.call(m||this,this.list[f].val,this.list[f].key,this)},s.prototype.indexOf=function(d){var m;for(m=0;m<this.size;m++)if(this.isEqual(this.list[m].key,d))return m;return-1},s.prototype.isEqual=function(d,m){return d===m||d!==d&&m!==m},l.exports=s},{}],3:[function(c,l,u){var s=c("map-or-similar");l.exports=function(p){var h=new s(!1),g=[];return function(E){var y=i(function(){var v=h,C,w,k=arguments.length-1,I=Array(k+1),T=!0,B;if((y.numArgs||y.numArgs===0)&&y.numArgs!==k+1)throw new Error("Memoizerific functions should always be called with the same number of arguments");for(B=0;B<k;B++){if(I[B]={cacheItem:v,arg:arguments[B]},v.has(arguments[B])){v=v.get(arguments[B]);continue}T=!1,C=new s(!1),v.set(arguments[B],C),v=C}return T&&(v.has(arguments[k])?w=v.get(arguments[k]):T=!1),T||(w=E.apply(null,arguments),v.set(arguments[k],w)),p>0&&(I[k]={cacheItem:v,arg:arguments[k]},T?d(g,I):g.push(I),g.length>p&&m(g.shift())),y.wasMemoized=T,y.numArgs=k+1,w},"memoizerific");return y.limit=p,y.wasMemoized=!1,y.cache=h,y.lru=g,y}};function d(p,h){var g=p.length,E=h.length,y,v,C;for(v=0;v<g;v++){for(y=!0,C=0;C<E;C++)if(!f(p[v][C].arg,h[C].arg)){y=!1;break}if(y)break}p.push(p.splice(v,1)[0])}i(d,"moveToMostRecentLru");function m(p){var h=p.length,g=p[h-1],E,y;for(g.cacheItem.delete(g.arg),y=h-2;y>=0&&(g=p[y],E=g.cacheItem.get(g.arg),!E||!E.size);y--)g.cacheItem.delete(g.arg)}i(m,"removeCachedResult");function f(p,h){return p===h||p!==p&&h!==h}i(f,"isEqual")},{"map-or-similar":1}]},{},[3])(3)})}),wu=Y((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223E",acd:"\u223F",acE:"\u223E\u0333",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",Acy:"\u0410",acy:"\u0430",AElig:"\xC6",aelig:"\xE6",af:"\u2061",Afr:"\u{1D504}",afr:"\u{1D51E}",Agrave:"\xC0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03B1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2A3F",amp:"&",AMP:"&",andand:"\u2A55",And:"\u2A53",and:"\u2227",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angmsd:"\u2221",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1D538}",aopf:"\u{1D552}",apacir:"\u2A6F",ap:"\u2248",apE:"\u2A70",ape:"\u224A",apid:"\u224B",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224A",Aring:"\xC5",aring:"\xE5",Ascr:"\u{1D49C}",ascr:"\u{1D4B6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224D",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",Backslash:"\u2216",Barv:"\u2AE7",barvee:"\u22BD",barwed:"\u2305",Barwed:"\u2306",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",Because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",Bernoullis:"\u212C",Beta:"\u0392",beta:"\u03B2",beth:"\u2136",between:"\u226C",Bfr:"\u{1D505}",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bNot:"\u2AED",bnot:"\u2310",Bopf:"\u{1D539}",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxbox:"\u29C9",boxdl:"\u2510",boxdL:"\u2555",boxDl:"\u2556",boxDL:"\u2557",boxdr:"\u250C",boxdR:"\u2552",boxDr:"\u2553",boxDR:"\u2554",boxh:"\u2500",boxH:"\u2550",boxhd:"\u252C",boxHd:"\u2564",boxhD:"\u2565",boxHD:"\u2566",boxhu:"\u2534",boxHu:"\u2567",boxhU:"\u2568",boxHU:"\u2569",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxul:"\u2518",boxuL:"\u255B",boxUl:"\u255C",boxUL:"\u255D",boxur:"\u2514",boxuR:"\u2558",boxUr:"\u2559",boxUR:"\u255A",boxv:"\u2502",boxV:"\u2551",boxvh:"\u253C",boxvH:"\u256A",boxVh:"\u256B",boxVH:"\u256C",boxvl:"\u2524",boxvL:"\u2561",boxVl:"\u2562",boxVL:"\u2563",boxvr:"\u251C",boxvR:"\u255E",boxVr:"\u255F",boxVR:"\u2560",bprime:"\u2035",breve:"\u02D8",Breve:"\u02D8",brvbar:"\xA6",bscr:"\u{1D4B7}",Bscr:"\u212C",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsolb:"\u29C5",bsol:"\\",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",Bumpeq:"\u224E",bumpeq:"\u224F",Cacute:"\u0106",cacute:"\u0107",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",cap:"\u2229",Cap:"\u22D2",capcup:"\u2A47",capdot:"\u2A40",CapitalDifferentialD:"\u2145",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",Cayleys:"\u212D",ccaps:"\u2A4D",Ccaron:"\u010C",ccaron:"\u010D",Ccedil:"\xC7",ccedil:"\xE7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2A4C",ccupssm:"\u2A50",Cdot:"\u010A",cdot:"\u010B",cedil:"\xB8",Cedilla:"\xB8",cemptyv:"\u29B2",cent:"\xA2",centerdot:"\xB7",CenterDot:"\xB7",cfr:"\u{1D520}",Cfr:"\u212D",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03A7",chi:"\u03C7",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",CircleDot:"\u2299",circledR:"\xAE",circledS:"\u24C8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cir:"\u25CB",cirE:"\u29C3",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",colon:":",Colon:"\u2237",Colone:"\u2A74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",Congruent:"\u2261",conint:"\u222E",Conint:"\u222F",ContourIntegral:"\u222E",copf:"\u{1D554}",Copf:"\u2102",coprod:"\u2210",Coproduct:"\u2210",copy:"\xA9",COPY:"\xA9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21B5",cross:"\u2717",Cross:"\u2A2F",Cscr:"\u{1D49E}",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cupbrcap:"\u2A48",cupcap:"\u2A46",CupCap:"\u224D",cup:"\u222A",Cup:"\u22D3",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dagger:"\u2020",Dagger:"\u2021",daleth:"\u2138",darr:"\u2193",Darr:"\u21A1",dArr:"\u21D3",dash:"\u2010",Dashv:"\u2AE4",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",Dcaron:"\u010E",dcaron:"\u010F",Dcy:"\u0414",dcy:"\u0434",ddagger:"\u2021",ddarr:"\u21CA",DD:"\u2145",dd:"\u2146",DDotrahd:"\u2911",ddotseq:"\u2A77",deg:"\xB0",Del:"\u2207",Delta:"\u0394",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",Dfr:"\u{1D507}",dfr:"\u{1D521}",dHar:"\u2965",dharl:"\u21C3",dharr:"\u21C2",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",diam:"\u22C4",diamond:"\u22C4",Diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",DifferentialD:"\u2146",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",Dopf:"\u{1D53B}",dopf:"\u{1D555}",Dot:"\xA8",dot:"\u02D9",DotDot:"\u20DC",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrowBar:"\u2913",downarrow:"\u2193",DownArrow:"\u2193",Downarrow:"\u21D3",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVectorBar:"\u2956",DownLeftVector:"\u21BD",DownRightTeeVector:"\u295F",DownRightVectorBar:"\u2957",DownRightVector:"\u21C1",DownTeeArrow:"\u21A7",DownTee:"\u22A4",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",Dscr:"\u{1D49F}",dscr:"\u{1D4B9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29F6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",DZcy:"\u040F",dzcy:"\u045F",dzigrarr:"\u27FF",Eacute:"\xC9",eacute:"\xE9",easter:"\u2A6E",Ecaron:"\u011A",ecaron:"\u011B",Ecirc:"\xCA",ecirc:"\xEA",ecir:"\u2256",ecolon:"\u2255",Ecy:"\u042D",ecy:"\u044D",eDDot:"\u2A77",Edot:"\u0116",edot:"\u0117",eDot:"\u2251",ee:"\u2147",efDot:"\u2252",Efr:"\u{1D508}",efr:"\u{1D522}",eg:"\u2A9A",Egrave:"\xC8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",Element:"\u2208",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25FB",emptyv:"\u2205",EmptyVerySmallSquare:"\u25AB",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",ENG:"\u014A",eng:"\u014B",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1D53C}",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",Epsilon:"\u0395",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",Equal:"\u2A75",equals:"=",EqualTilde:"\u2242",equest:"\u225F",Equilibrium:"\u21CC",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erarr:"\u2971",erDot:"\u2253",escr:"\u212F",Escr:"\u2130",esdot:"\u2250",Esim:"\u2A73",esim:"\u2242",Eta:"\u0397",eta:"\u03B7",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",exponentiale:"\u2147",ExponentialE:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",Ffr:"\u{1D509}",ffr:"\u{1D523}",filig:"\uFB01",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",Fopf:"\u{1D53D}",fopf:"\u{1D557}",forall:"\u2200",ForAll:"\u2200",fork:"\u22D4",forkv:"\u2AD9",Fouriertrf:"\u2131",fpartint:"\u2A0D",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",Fscr:"\u2131",gacute:"\u01F5",Gamma:"\u0393",gamma:"\u03B3",Gammad:"\u03DC",gammad:"\u03DD",gap:"\u2A86",Gbreve:"\u011E",gbreve:"\u011F",Gcedil:"\u0122",Gcirc:"\u011C",gcirc:"\u011D",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",ge:"\u2265",gE:"\u2267",gEl:"\u2A8C",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",gescc:"\u2AA9",ges:"\u2A7E",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",Gfr:"\u{1D50A}",gfr:"\u{1D524}",gg:"\u226B",Gg:"\u22D9",ggg:"\u22D9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gla:"\u2AA5",gl:"\u2277",glE:"\u2A92",glj:"\u2AA4",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gnE:"\u2269",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",Gopf:"\u{1D53E}",gopf:"\u{1D558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",gtcc:"\u2AA7",gtcir:"\u2A7A",gt:">",GT:">",Gt:"\u226B",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",Hacek:"\u02C7",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",HARDcy:"\u042A",hardcy:"\u044A",harrcir:"\u2948",harr:"\u2194",hArr:"\u21D4",harrw:"\u21AD",Hat:"^",hbar:"\u210F",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",Hfr:"\u210C",HilbertSpace:"\u210B",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",Hopf:"\u210D",horbar:"\u2015",HorizontalLine:"\u2500",hscr:"\u{1D4BD}",Hscr:"\u210B",hslash:"\u210F",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224E",HumpEqual:"\u224F",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xCD",iacute:"\xED",ic:"\u2063",Icirc:"\xCE",icirc:"\xEE",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",Ifr:"\u2111",Igrave:"\xCC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Imacr:"\u012A",imacr:"\u012B",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",Im:"\u2111",imof:"\u22B7",imped:"\u01B5",Implies:"\u21D2",incare:"\u2105",in:"\u2208",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",intcal:"\u22BA",int:"\u222B",Int:"\u222C",integers:"\u2124",Integral:"\u222B",intercal:"\u22BA",Intersection:"\u22C2",intlarhk:"\u2A17",intprod:"\u2A3C",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012E",iogon:"\u012F",Iopf:"\u{1D540}",iopf:"\u{1D55A}",Iota:"\u0399",iota:"\u03B9",iprod:"\u2A3C",iquest:"\xBF",iscr:"\u{1D4BE}",Iscr:"\u2110",isin:"\u2208",isindot:"\u22F5",isinE:"\u22F9",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xCF",iuml:"\xEF",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1D50D}",jfr:"\u{1D527}",jmath:"\u0237",Jopf:"\u{1D541}",jopf:"\u{1D55B}",Jscr:"\u{1D4A5}",jscr:"\u{1D4BF}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039A",kappa:"\u03BA",kappav:"\u03F0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041A",kcy:"\u043A",Kfr:"\u{1D50E}",kfr:"\u{1D528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040C",kjcy:"\u045C",Kopf:"\u{1D542}",kopf:"\u{1D55C}",Kscr:"\u{1D4A6}",kscr:"\u{1D4C0}",lAarr:"\u21DA",Lacute:"\u0139",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",Lambda:"\u039B",lambda:"\u03BB",lang:"\u27E8",Lang:"\u27EA",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",Laplacetrf:"\u2112",laquo:"\xAB",larrb:"\u21E4",larrbfs:"\u291F",larr:"\u2190",Larr:"\u219E",lArr:"\u21D0",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",latail:"\u2919",lAtail:"\u291B",lat:"\u2AAB",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lBarr:"\u290E",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",Lcaron:"\u013D",lcaron:"\u013E",Lcedil:"\u013B",lcedil:"\u013C",lceil:"\u2308",lcub:"{",Lcy:"\u041B",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",lE:"\u2266",LeftAngleBracket:"\u27E8",LeftArrowBar:"\u21E4",leftarrow:"\u2190",LeftArrow:"\u2190",Leftarrow:"\u21D0",LeftArrowRightArrow:"\u21C6",leftarrowtail:"\u21A2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVectorBar:"\u2959",LeftDownVector:"\u21C3",LeftFloor:"\u230A",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",LeftRightArrow:"\u2194",Leftrightarrow:"\u21D4",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",LeftRightVector:"\u294E",LeftTeeArrow:"\u21A4",LeftTee:"\u22A3",LeftTeeVector:"\u295A",leftthreetimes:"\u22CB",LeftTriangleBar:"\u29CF",LeftTriangle:"\u22B2",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVectorBar:"\u2958",LeftUpVector:"\u21BF",LeftVectorBar:"\u2952",LeftVector:"\u21BC",lEg:"\u2A8B",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",lescc:"\u2AA8",les:"\u2A7D",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2AA1",lesssim:"\u2272",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",lfisht:"\u297C",lfloor:"\u230A",Lfr:"\u{1D50F}",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lHar:"\u2962",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",llarr:"\u21C7",ll:"\u226A",Ll:"\u22D8",llcorner:"\u231E",Lleftarrow:"\u21DA",llhard:"\u296B",lltri:"\u25FA",Lmidot:"\u013F",lmidot:"\u0140",lmoustache:"\u23B0",lmoust:"\u23B0",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lnE:"\u2268",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",LongLeftArrow:"\u27F5",Longleftarrow:"\u27F8",longleftrightarrow:"\u27F7",LongLeftRightArrow:"\u27F7",Longleftrightarrow:"\u27FA",longmapsto:"\u27FC",longrightarrow:"\u27F6",LongRightArrow:"\u27F6",Longrightarrow:"\u27F9",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",Lopf:"\u{1D543}",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",Lscr:"\u2112",lsh:"\u21B0",Lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",Lstrok:"\u0141",lstrok:"\u0142",ltcc:"\u2AA6",ltcir:"\u2A79",lt:"<",LT:"<",Lt:"\u226A",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",ltrPar:"\u2996",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",Mcy:"\u041C",mcy:"\u043C",mdash:"\u2014",mDDot:"\u223A",measuredangle:"\u2221",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",mfr:"\u{1D52A}",mho:"\u2127",micro:"\xB5",midast:"*",midcir:"\u2AF0",mid:"\u2223",middot:"\xB7",minusb:"\u229F",minus:"\u2212",minusd:"\u2238",minusdu:"\u2A2A",MinusPlus:"\u2213",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",Mopf:"\u{1D544}",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",Mscr:"\u2133",mstpos:"\u223E",Mu:"\u039C",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natural:"\u266E",naturals:"\u2115",natur:"\u266E",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",Ncy:"\u041D",ncy:"\u043D",ndash:"\u2013",nearhk:"\u2924",nearr:"\u2197",neArr:"\u21D7",nearrow:"\u2197",ne:"\u2260",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1D511}",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",nGg:"\u22D9\u0338",ngsim:"\u2275",nGt:"\u226B\u20D2",ngt:"\u226F",ngtr:"\u226F",nGtv:"\u226B\u0338",nharr:"\u21AE",nhArr:"\u21CE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",NJcy:"\u040A",njcy:"\u045A",nlarr:"\u219A",nlArr:"\u21CD",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nleftarrow:"\u219A",nLeftarrow:"\u21CD",nleftrightarrow:"\u21AE",nLeftrightarrow:"\u21CE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nLl:"\u22D8\u0338",nlsim:"\u2274",nLt:"\u226A\u20D2",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nLtv:"\u226A\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xA0",nopf:"\u{1D55F}",Nopf:"\u2115",Not:"\u2AEC",not:"\xAC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",notin:"\u2209",notindot:"\u22F5\u0338",notinE:"\u22F9\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangle:"\u22EB",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",nparallel:"\u2226",npar:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",nprec:"\u2280",npreceq:"\u2AAF\u0338",npre:"\u2AAF\u0338",nrarrc:"\u2933\u0338",nrarr:"\u219B",nrArr:"\u21CF",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nRightarrow:"\u21CF",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",Nscr:"\u{1D4A9}",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",Ntilde:"\xD1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",Nu:"\u039D",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvDash:"\u22AD",nVdash:"\u22AE",nVDash:"\u22AF",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvHarr:"\u2904",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwarhk:"\u2923",nwarr:"\u2196",nwArr:"\u21D6",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xD3",oacute:"\xF3",oast:"\u229B",Ocirc:"\xD4",ocirc:"\xF4",ocir:"\u229A",Ocy:"\u041E",ocy:"\u043E",odash:"\u229D",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29BF",Ofr:"\u{1D512}",ofr:"\u{1D52C}",ogon:"\u02DB",Ograve:"\xD2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",Omacr:"\u014C",omacr:"\u014D",Omega:"\u03A9",omega:"\u03C9",Omicron:"\u039F",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",Oopf:"\u{1D546}",oopf:"\u{1D560}",opar:"\u29B7",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",operp:"\u29B9",oplus:"\u2295",orarr:"\u21BB",Or:"\u2A54",or:"\u2228",ord:"\u2A5D",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oS:"\u24C8",Oscr:"\u{1D4AA}",oscr:"\u2134",Oslash:"\xD8",oslash:"\xF8",osol:"\u2298",Otilde:"\xD5",otilde:"\xF5",otimesas:"\u2A36",Otimes:"\u2A37",otimes:"\u2297",Ouml:"\xD6",ouml:"\xF6",ovbar:"\u233D",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",para:"\xB6",parallel:"\u2225",par:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",PartialD:"\u2202",Pcy:"\u041F",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",Pfr:"\u{1D513}",pfr:"\u{1D52D}",Phi:"\u03A6",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",Pi:"\u03A0",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plus:"+",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",PlusMinus:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",Poincareplane:"\u210C",pointint:"\u2A15",popf:"\u{1D561}",Popf:"\u2119",pound:"\xA3",prap:"\u2AB7",Pr:"\u2ABB",pr:"\u227A",prcue:"\u227C",precapprox:"\u2AB7",prec:"\u227A",preccurlyeq:"\u227C",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",pre:"\u2AAF",prE:"\u2AB3",precsim:"\u227E",prime:"\u2032",Prime:"\u2033",primes:"\u2119",prnap:"\u2AB9",prnE:"\u2AB5",prnsim:"\u22E8",prod:"\u220F",Product:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",Proportional:"\u221D",Proportion:"\u2237",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",Pscr:"\u{1D4AB}",pscr:"\u{1D4C5}",Psi:"\u03A8",psi:"\u03C8",puncsp:"\u2008",Qfr:"\u{1D514}",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",Qopf:"\u211A",qprime:"\u2057",Qscr:"\u{1D4AC}",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quot:'"',QUOT:'"',rAarr:"\u21DB",race:"\u223D\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",Rang:"\u27EB",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raquo:"\xBB",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarr:"\u2192",Rarr:"\u21A0",rArr:"\u21D2",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",rAtail:"\u291C",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rBarr:"\u290F",RBarr:"\u2910",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",Re:"\u211C",rect:"\u25AD",reg:"\xAE",REG:"\xAE",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",Rfr:"\u211C",rHar:"\u2964",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",Rho:"\u03A1",rho:"\u03C1",rhov:"\u03F1",RightAngleBracket:"\u27E9",RightArrowBar:"\u21E5",rightarrow:"\u2192",RightArrow:"\u2192",Rightarrow:"\u21D2",RightArrowLeftArrow:"\u21C4",rightarrowtail:"\u21A3",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVectorBar:"\u2955",RightDownVector:"\u21C2",RightFloor:"\u230B",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",RightTeeArrow:"\u21A6",RightTee:"\u22A2",RightTeeVector:"\u295B",rightthreetimes:"\u22CC",RightTriangleBar:"\u29D0",RightTriangle:"\u22B3",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVectorBar:"\u2954",RightUpVector:"\u21BE",RightVectorBar:"\u2953",RightVector:"\u21C0",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoustache:"\u23B1",rmoust:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",Ropf:"\u211D",roplus:"\u2A2E",rotimes:"\u2A35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",Rrightarrow:"\u21DB",rsaquo:"\u203A",rscr:"\u{1D4C7}",Rscr:"\u211B",rsh:"\u21B1",Rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",RuleDelayed:"\u29F4",ruluhar:"\u2968",rx:"\u211E",Sacute:"\u015A",sacute:"\u015B",sbquo:"\u201A",scap:"\u2AB8",Scaron:"\u0160",scaron:"\u0161",Sc:"\u2ABC",sc:"\u227B",sccue:"\u227D",sce:"\u2AB0",scE:"\u2AB4",Scedil:"\u015E",scedil:"\u015F",Scirc:"\u015C",scirc:"\u015D",scnap:"\u2ABA",scnE:"\u2AB6",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",Scy:"\u0421",scy:"\u0441",sdotb:"\u22A1",sdot:"\u22C5",sdote:"\u2A66",searhk:"\u2925",searr:"\u2198",seArr:"\u21D8",searrow:"\u2198",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1D516}",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xAD",Sigma:"\u03A3",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",SOFTcy:"\u042C",softcy:"\u044C",solbar:"\u233F",solb:"\u29C4",sol:"/",Sopf:"\u{1D54A}",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",Sqrt:"\u221A",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",square:"\u25A1",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25AA",squ:"\u25A1",squf:"\u25AA",srarr:"\u2192",Sscr:"\u{1D4AE}",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",Star:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",Sub:"\u22D0",subdot:"\u2ABD",subE:"\u2AC5",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",Subset:"\u22D0",subseteq:"\u2286",subseteqq:"\u2AC5",SubsetEqual:"\u2286",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succapprox:"\u2AB8",succ:"\u227B",succcurlyeq:"\u227D",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",SuchThat:"\u220B",sum:"\u2211",Sum:"\u2211",sung:"\u266A",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",sup:"\u2283",Sup:"\u22D1",supdot:"\u2ABE",supdsub:"\u2AD8",supE:"\u2AC6",supe:"\u2287",supedot:"\u2AC4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",Supset:"\u22D1",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swarhk:"\u2926",swarr:"\u2199",swArr:"\u21D9",swarrow:"\u2199",swnwar:"\u292A",szlig:"\xDF",Tab:"	",target:"\u2316",Tau:"\u03A4",tau:"\u03C4",tbrk:"\u23B4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",Tfr:"\u{1D517}",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",Therefore:"\u2234",Theta:"\u0398",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",THORN:"\xDE",thorn:"\xFE",tilde:"\u02DC",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",timesbar:"\u2A31",timesb:"\u22A0",times:"\xD7",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",topbot:"\u2336",topcir:"\u2AF1",top:"\u22A4",Topf:"\u{1D54B}",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",TRADE:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",TripleDot:"\u20DB",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",Tscr:"\u{1D4AF}",tscr:"\u{1D4C9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040B",tshcy:"\u045B",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",Uacute:"\xDA",uacute:"\xFA",uarr:"\u2191",Uarr:"\u219F",uArr:"\u21D1",Uarrocir:"\u2949",Ubrcy:"\u040E",ubrcy:"\u045E",Ubreve:"\u016C",ubreve:"\u016D",Ucirc:"\xDB",ucirc:"\xFB",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21C5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",Ufr:"\u{1D518}",ufr:"\u{1D532}",Ugrave:"\xD9",ugrave:"\xF9",uHar:"\u2963",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",Umacr:"\u016A",umacr:"\u016B",uml:"\xA8",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1D54C}",uopf:"\u{1D566}",UpArrowBar:"\u2912",uparrow:"\u2191",UpArrow:"\u2191",Uparrow:"\u21D1",UpArrowDownArrow:"\u21C5",updownarrow:"\u2195",UpDownArrow:"\u2195",Updownarrow:"\u21D5",UpEquilibrium:"\u296E",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",upsi:"\u03C5",Upsi:"\u03D2",upsih:"\u03D2",Upsilon:"\u03A5",upsilon:"\u03C5",UpTeeArrow:"\u21A5",UpTee:"\u22A5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",Uring:"\u016E",uring:"\u016F",urtri:"\u25F9",Uscr:"\u{1D4B0}",uscr:"\u{1D4CA}",utdot:"\u22F0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",Uuml:"\xDC",uuml:"\xFC",uwangle:"\u29A7",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",vArr:"\u21D5",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vBar:"\u2AE8",Vbar:"\u2AEB",vBarv:"\u2AE9",Vcy:"\u0412",vcy:"\u0432",vdash:"\u22A2",vDash:"\u22A8",Vdash:"\u22A9",VDash:"\u22AB",Vdashl:"\u2AE6",veebar:"\u22BB",vee:"\u2228",Vee:"\u22C1",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",Verbar:"\u2016",vert:"|",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",Vopf:"\u{1D54D}",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",Vscr:"\u{1D4B1}",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",Vvdash:"\u22AA",vzigzag:"\u299A",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",Wedge:"\u22C0",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1D51A}",wfr:"\u{1D534}",Wopf:"\u{1D54E}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1D4B2}",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",Xfr:"\u{1D51B}",xfr:"\u{1D535}",xharr:"\u27F7",xhArr:"\u27FA",Xi:"\u039E",xi:"\u03BE",xlarr:"\u27F5",xlArr:"\u27F8",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",Xopf:"\u{1D54F}",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrarr:"\u27F6",xrArr:"\u27F9",Xscr:"\u{1D4B3}",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",Yacute:"\xDD",yacute:"\xFD",YAcy:"\u042F",yacy:"\u044F",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042B",ycy:"\u044B",yen:"\xA5",Yfr:"\u{1D51C}",yfr:"\u{1D536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1D550}",yopf:"\u{1D56A}",Yscr:"\u{1D4B4}",yscr:"\u{1D4CE}",YUcy:"\u042E",yucy:"\u044E",yuml:"\xFF",Yuml:"\u0178",Zacute:"\u0179",zacute:"\u017A",Zcaron:"\u017D",zcaron:"\u017E",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017B",zdot:"\u017C",zeetrf:"\u2128",ZeroWidthSpace:"\u200B",Zeta:"\u0396",zeta:"\u03B6",zfr:"\u{1D537}",Zfr:"\u2128",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",Zopf:"\u2124",Zscr:"\u{1D4B5}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}}),fp=Y((e,t)=>{t.exports={Aacute:"\xC1",aacute:"\xE1",Acirc:"\xC2",acirc:"\xE2",acute:"\xB4",AElig:"\xC6",aelig:"\xE6",Agrave:"\xC0",agrave:"\xE0",amp:"&",AMP:"&",Aring:"\xC5",aring:"\xE5",Atilde:"\xC3",atilde:"\xE3",Auml:"\xC4",auml:"\xE4",brvbar:"\xA6",Ccedil:"\xC7",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",COPY:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",Eacute:"\xC9",eacute:"\xE9",Ecirc:"\xCA",ecirc:"\xEA",Egrave:"\xC8",egrave:"\xE8",ETH:"\xD0",eth:"\xF0",Euml:"\xCB",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",GT:">",Iacute:"\xCD",iacute:"\xED",Icirc:"\xCE",icirc:"\xEE",iexcl:"\xA1",Igrave:"\xCC",igrave:"\xEC",iquest:"\xBF",Iuml:"\xCF",iuml:"\xEF",laquo:"\xAB",lt:"<",LT:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",Ntilde:"\xD1",ntilde:"\xF1",Oacute:"\xD3",oacute:"\xF3",Ocirc:"\xD4",ocirc:"\xF4",Ograve:"\xD2",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",Oslash:"\xD8",oslash:"\xF8",Otilde:"\xD5",otilde:"\xF5",Ouml:"\xD6",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',QUOT:'"',raquo:"\xBB",reg:"\xAE",REG:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",THORN:"\xDE",thorn:"\xFE",times:"\xD7",Uacute:"\xDA",uacute:"\xFA",Ucirc:"\xDB",ucirc:"\xFB",Ugrave:"\xD9",ugrave:"\xF9",uml:"\xA8",Uuml:"\xDC",uuml:"\xFC",Yacute:"\xDD",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"}}),ku=Y((e,t)=>{t.exports={amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}}),hp=Y((e,t)=>{t.exports={0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376}}),gp=Y(e=>{"use strict";var t=e&&e.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(e,"__esModule",{value:!0});var r=t(hp()),a=String.fromCodePoint||function(c){var l="";return c>65535&&(c-=65536,l+=String.fromCharCode(c>>>10&1023|55296),c=56320|c&1023),l+=String.fromCharCode(c),l};function o(c){return c>=55296&&c<=57343||c>1114111?"\uFFFD":(c in r.default&&(c=r.default[c]),a(c))}i(o,"decodeCodePoint"),e.default=o}),ql=Y(e=>{"use strict";var t=e&&e.__importDefault||function(m){return m&&m.__esModule?m:{default:m}};Object.defineProperty(e,"__esModule",{value:!0}),e.decodeHTML=e.decodeHTMLStrict=e.decodeXML=void 0;var r=t(wu()),a=t(fp()),o=t(ku()),c=t(gp()),l=/&(?:[a-zA-Z0-9]+|#[xX][\da-fA-F]+|#\d+);/g;e.decodeXML=u(o.default),e.decodeHTMLStrict=u(r.default);function u(m){var f=d(m);return function(p){return String(p).replace(l,f)}}i(u,"getStrictDecoder");var s=i(function(m,f){return m<f?1:-1},"sorter");e.decodeHTML=function(){for(var m=Object.keys(a.default).sort(s),f=Object.keys(r.default).sort(s),p=0,h=0;p<f.length;p++)m[h]===f[p]?(f[p]+=";?",h++):f[p]+=";";var g=new RegExp("&(?:"+f.join("|")+"|#[xX][\\da-fA-F]+;?|#\\d+;?)","g"),E=d(r.default);function y(v){return v.substr(-1)!==";"&&(v+=";"),E(v)}return i(y,"replacer"),function(v){return String(v).replace(g,y)}}();function d(m){return i(function(f){if(f.charAt(1)==="#"){var p=f.charAt(2);return p==="X"||p==="x"?c.default(parseInt(f.substr(3),16)):c.default(parseInt(f.substr(2),10))}return m[f.slice(1,-1)]||f},"replace")}i(d,"getReplacer")}),Gl=Y(e=>{"use strict";var t=e&&e.__importDefault||function(C){return C&&C.__esModule?C:{default:C}};Object.defineProperty(e,"__esModule",{value:!0}),e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=void 0;var r=t(ku()),a=s(r.default),o=d(a);e.encodeXML=v(a);var c=t(wu()),l=s(c.default),u=d(l);e.encodeHTML=h(l,u),e.encodeNonAsciiHTML=v(l);function s(C){return Object.keys(C).sort().reduce(function(w,k){return w[C[k]]="&"+k+";",w},{})}i(s,"getInverseObj");function d(C){for(var w=[],k=[],I=0,T=Object.keys(C);I<T.length;I++){var B=T[I];B.length===1?w.push("\\"+B):k.push(B)}w.sort();for(var F=0;F<w.length-1;F++){for(var M=F;M<w.length-1&&w[M].charCodeAt(1)+1===w[M+1].charCodeAt(1);)M+=1;var H=1+M-F;H<3||w.splice(F,H,w[F]+"-"+w[M])}return k.unshift("["+w.join("")+"]"),new RegExp(k.join("|"),"g")}i(d,"getInverseReplacer");var m=/(?:[\x80-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])/g,f=String.prototype.codePointAt!=null?function(C){return C.codePointAt(0)}:function(C){return(C.charCodeAt(0)-55296)*1024+C.charCodeAt(1)-56320+65536};function p(C){return"&#x"+(C.length>1?f(C):C.charCodeAt(0)).toString(16).toUpperCase()+";"}i(p,"singleCharReplacer");function h(C,w){return function(k){return k.replace(w,function(I){return C[I]}).replace(m,p)}}i(h,"getInverse");var g=new RegExp(o.source+"|"+m.source,"g");function E(C){return C.replace(g,p)}i(E,"escape"),e.escape=E;function y(C){return C.replace(o,p)}i(y,"escapeUTF8"),e.escapeUTF8=y;function v(C){return function(w){return w.replace(g,function(k){return C[k]||p(k)})}}i(v,"getASCIIEncoder")}),bp=Y(e=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.decodeXMLStrict=e.decodeHTML5Strict=e.decodeHTML4Strict=e.decodeHTML5=e.decodeHTML4=e.decodeHTMLStrict=e.decodeHTML=e.decodeXML=e.encodeHTML5=e.encodeHTML4=e.escapeUTF8=e.escape=e.encodeNonAsciiHTML=e.encodeHTML=e.encodeXML=e.encode=e.decodeStrict=e.decode=void 0;var t=ql(),r=Gl();function a(s,d){return(!d||d<=0?t.decodeXML:t.decodeHTML)(s)}i(a,"decode"),e.decode=a;function o(s,d){return(!d||d<=0?t.decodeXML:t.decodeHTMLStrict)(s)}i(o,"decodeStrict"),e.decodeStrict=o;function c(s,d){return(!d||d<=0?r.encodeXML:r.encodeHTML)(s)}i(c,"encode"),e.encode=c;var l=Gl();Object.defineProperty(e,"encodeXML",{enumerable:!0,get:i(function(){return l.encodeXML},"get")}),Object.defineProperty(e,"encodeHTML",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeNonAsciiHTML",{enumerable:!0,get:i(function(){return l.encodeNonAsciiHTML},"get")}),Object.defineProperty(e,"escape",{enumerable:!0,get:i(function(){return l.escape},"get")}),Object.defineProperty(e,"escapeUTF8",{enumerable:!0,get:i(function(){return l.escapeUTF8},"get")}),Object.defineProperty(e,"encodeHTML4",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")}),Object.defineProperty(e,"encodeHTML5",{enumerable:!0,get:i(function(){return l.encodeHTML},"get")});var u=ql();Object.defineProperty(e,"decodeXML",{enumerable:!0,get:i(function(){return u.decodeXML},"get")}),Object.defineProperty(e,"decodeHTML",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTMLStrict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML4",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML5",{enumerable:!0,get:i(function(){return u.decodeHTML},"get")}),Object.defineProperty(e,"decodeHTML4Strict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeHTML5Strict",{enumerable:!0,get:i(function(){return u.decodeHTMLStrict},"get")}),Object.defineProperty(e,"decodeXMLStrict",{enumerable:!0,get:i(function(){return u.decodeXML},"get")})}),yp=Y((e,t)=>{"use strict";function r(A,x){if(!(A instanceof x))throw new TypeError("Cannot call a class as a function")}i(r,"_classCallCheck");function a(A,x){for(var S=0;S<x.length;S++){var _=x[S];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(A,_.key,_)}}i(a,"_defineProperties");function o(A,x,S){return x&&a(A.prototype,x),S&&a(A,S),A}i(o,"_createClass");function c(A,x){var S=typeof Symbol<"u"&&A[Symbol.iterator]||A["@@iterator"];if(!S){if(Array.isArray(A)||(S=l(A))||x&&A&&typeof A.length=="number"){S&&(A=S);var _=0,O=i(function(){},"F");return{s:O,n:i(function(){return _>=A.length?{done:!0}:{done:!1,value:A[_++]}},"n"),e:i(function(J){throw J},"e"),f:O}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var D=!0,N=!1,V;return{s:i(function(){S=S.call(A)},"s"),n:i(function(){var J=S.next();return D=J.done,J},"n"),e:i(function(J){N=!0,V=J},"e"),f:i(function(){try{!D&&S.return!=null&&S.return()}finally{if(N)throw V}},"f")}}i(c,"_createForOfIteratorHelper");function l(A,x){if(A){if(typeof A=="string")return u(A,x);var S=Object.prototype.toString.call(A).slice(8,-1);if(S==="Object"&&A.constructor&&(S=A.constructor.name),S==="Map"||S==="Set")return Array.from(A);if(S==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(S))return u(A,x)}}i(l,"_unsupportedIterableToArray");function u(A,x){(x==null||x>A.length)&&(x=A.length);for(var S=0,_=new Array(x);S<x;S++)_[S]=A[S];return _}i(u,"_arrayLikeToArray");var s=bp(),d={fg:"#FFF",bg:"#000",newline:!1,escapeXML:!1,stream:!1,colors:m()};function m(){var A={0:"#000",1:"#A00",2:"#0A0",3:"#A50",4:"#00A",5:"#A0A",6:"#0AA",7:"#AAA",8:"#555",9:"#F55",10:"#5F5",11:"#FF5",12:"#55F",13:"#F5F",14:"#5FF",15:"#FFF"};return C(0,5).forEach(function(x){C(0,5).forEach(function(S){C(0,5).forEach(function(_){return f(x,S,_,A)})})}),C(0,23).forEach(function(x){var S=x+232,_=p(x*10+8);A[S]="#"+_+_+_}),A}i(m,"getDefaultColors");function f(A,x,S,_){var O=16+A*36+x*6+S,D=A>0?A*40+55:0,N=x>0?x*40+55:0,V=S>0?S*40+55:0;_[O]=h([D,N,V])}i(f,"setStyleColor");function p(A){for(var x=A.toString(16);x.length<2;)x="0"+x;return x}i(p,"toHexString");function h(A){var x=[],S=c(A),_;try{for(S.s();!(_=S.n()).done;){var O=_.value;x.push(p(O))}}catch(D){S.e(D)}finally{S.f()}return"#"+x.join("")}i(h,"toColorHexString");function g(A,x,S,_){var O;return x==="text"?O=I(S,_):x==="display"?O=y(A,S,_):x==="xterm256Foreground"?O=F(A,_.colors[S]):x==="xterm256Background"?O=M(A,_.colors[S]):x==="rgb"&&(O=E(A,S)),O}i(g,"generateOutput");function E(A,x){x=x.substring(2).slice(0,-1);var S=+x.substr(0,2),_=x.substring(5).split(";"),O=_.map(function(D){return("0"+Number(D).toString(16)).substr(-2)}).join("");return B(A,(S===38?"color:#":"background-color:#")+O)}i(E,"handleRgb");function y(A,x,S){x=parseInt(x,10);var _={"-1":i(function(){return"<br/>"},"_"),0:i(function(){return A.length&&v(A)},"_"),1:i(function(){return T(A,"b")},"_"),3:i(function(){return T(A,"i")},"_"),4:i(function(){return T(A,"u")},"_"),8:i(function(){return B(A,"display:none")},"_"),9:i(function(){return T(A,"strike")},"_"),22:i(function(){return B(A,"font-weight:normal;text-decoration:none;font-style:normal")},"_"),23:i(function(){return H(A,"i")},"_"),24:i(function(){return H(A,"u")},"_"),39:i(function(){return F(A,S.fg)},"_"),49:i(function(){return M(A,S.bg)},"_"),53:i(function(){return B(A,"text-decoration:overline")},"_")},O;return _[x]?O=_[x]():4<x&&x<7?O=T(A,"blink"):29<x&&x<38?O=F(A,S.colors[x-30]):39<x&&x<48?O=M(A,S.colors[x-40]):89<x&&x<98?O=F(A,S.colors[8+(x-90)]):99<x&&x<108&&(O=M(A,S.colors[8+(x-100)])),O}i(y,"handleDisplay");function v(A){var x=A.slice(0);return A.length=0,x.reverse().map(function(S){return"</"+S+">"}).join("")}i(v,"resetStyles");function C(A,x){for(var S=[],_=A;_<=x;_++)S.push(_);return S}i(C,"range");function w(A){return function(x){return(A===null||x.category!==A)&&A!=="all"}}i(w,"notCategory");function k(A){A=parseInt(A,10);var x=null;return A===0?x="all":A===1?x="bold":2<A&&A<5?x="underline":4<A&&A<7?x="blink":A===8?x="hide":A===9?x="strike":29<A&&A<38||A===39||89<A&&A<98?x="foreground-color":(39<A&&A<48||A===49||99<A&&A<108)&&(x="background-color"),x}i(k,"categoryForCode");function I(A,x){return x.escapeXML?s.encodeXML(A):A}i(I,"pushText");function T(A,x,S){return S||(S=""),A.push(x),"<".concat(x).concat(S?' style="'.concat(S,'"'):"",">")}i(T,"pushTag");function B(A,x){return T(A,"span",x)}i(B,"pushStyle");function F(A,x){return T(A,"span","color:"+x)}i(F,"pushForegroundColor");function M(A,x){return T(A,"span","background-color:"+x)}i(M,"pushBackgroundColor");function H(A,x){var S;if(A.slice(-1)[0]===x&&(S=A.pop()),S)return"</"+x+">"}i(H,"closeTag");function z(A,x,S){var _=!1,O=3;function D(){return""}i(D,"remove");function N(Be,_e){return S("xterm256Foreground",_e),""}i(N,"removeXterm256Foreground");function V(Be,_e){return S("xterm256Background",_e),""}i(V,"removeXterm256Background");function J(Be){return x.newline?S("display",-1):S("text",Be),""}i(J,"newline");function Lt(Be,_e){_=!0,_e.trim().length===0&&(_e="0"),_e=_e.trimRight(";").split(";");var ur=c(_e),so;try{for(ur.s();!(so=ur.n()).done;){var wc=so.value;S("display",wc)}}catch(kc){ur.e(kc)}finally{ur.f()}return""}i(Lt,"ansiMess");function At(Be){return S("text",Be),""}i(At,"realText");function Pt(Be){return S("rgb",Be),""}i(Pt,"rgb");var oo=[{pattern:/^\x08+/,sub:D},{pattern:/^\x1b\[[012]?K/,sub:D},{pattern:/^\x1b\[\(B/,sub:D},{pattern:/^\x1b\[[34]8;2;\d+;\d+;\d+m/,sub:Pt},{pattern:/^\x1b\[38;5;(\d+)m/,sub:N},{pattern:/^\x1b\[48;5;(\d+)m/,sub:V},{pattern:/^\n/,sub:J},{pattern:/^\r+\n/,sub:J},{pattern:/^\r/,sub:J},{pattern:/^\x1b\[((?:\d{1,3};?)+|)m/,sub:Lt},{pattern:/^\x1b\[\d?J/,sub:D},{pattern:/^\x1b\[\d{0,3};\d{0,3}f/,sub:D},{pattern:/^\x1b\[?[\d;]{0,3}/,sub:D},{pattern:/^(([^\x1b\x08\r\n])+)/,sub:At}];function lo(Be,_e){_e>O&&_||(_=!1,A=A.replace(Be.pattern,Be.sub))}i(lo,"process");var io=[],Ac=A,jt=Ac.length;e:for(;jt>0;){for(var pn=0,uo=0,Cc=oo.length;uo<Cc;pn=++uo){var Sc=oo[pn];if(lo(Sc,pn),A.length!==jt){jt=A.length;continue e}}if(A.length===jt)break;io.push(0),jt=A.length}return io}i(z,"tokenize");function Q(A,x,S){return x!=="text"&&(A=A.filter(w(k(S))),A.push({token:x,data:S,category:k(S)})),A}i(Q,"updateStickyStack");var ne=function(){function A(x){r(this,A),x=x||{},x.colors&&(x.colors=Object.assign({},d.colors,x.colors)),this.options=Object.assign({},d,x),this.stack=[],this.stickyStack=[]}return i(A,"Filter"),o(A,[{key:"toHtml",value:i(function(x){var S=this;x=typeof x=="string"?[x]:x;var _=this.stack,O=this.options,D=[];return this.stickyStack.forEach(function(N){var V=g(_,N.token,N.data,O);V&&D.push(V)}),z(x.join(""),O,function(N,V){var J=g(_,N,V,O);J&&D.push(J),O.stream&&(S.stickyStack=Q(S.stickyStack,N,V))}),_.length&&D.push(v(_)),D.join("")},"toHtml")}]),A}();t.exports=ne}),Fa=Y((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),Ep=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),La=Y((e,t)=>{var r=Ep();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),vp=Y((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),xp=Y((e,t)=>{var r=vp();function a(c,l){var u=Object.keys(c);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(c);l&&(s=s.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),u.push.apply(u,s)}return u}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var u=arguments[l]!=null?arguments[l]:{};l%2?a(u,!0).forEach(function(s){r(c,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):a(u).forEach(function(s){Object.defineProperty(c,s,Object.getOwnPropertyDescriptor(u,s))})}return c}i(o,"_objectSpread2"),t.exports=o}),Ap=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Cp=Y((e,t)=>{var r=Ap();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),Sp=Y((e,t)=>{function r(a,o,c){return o in a?Object.defineProperty(a,o,{value:c,enumerable:!0,configurable:!0,writable:!0}):a[o]=c,a}i(r,"_defineProperty"),t.exports=r}),wp=Y((e,t)=>{var r=Sp();function a(c,l){var u=Object.keys(c);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(c);l&&(s=s.filter(function(d){return Object.getOwnPropertyDescriptor(c,d).enumerable})),u.push.apply(u,s)}return u}i(a,"ownKeys");function o(c){for(var l=1;l<arguments.length;l++){var u=arguments[l]!=null?arguments[l]:{};l%2?a(u,!0).forEach(function(s){r(c,s,u[s])}):Object.getOwnPropertyDescriptors?Object.defineProperties(c,Object.getOwnPropertyDescriptors(u)):a(u).forEach(function(s){Object.defineProperty(c,s,Object.getOwnPropertyDescriptor(u,s))})}return c}i(o,"_objectSpread2"),t.exports=o}),kp=Y((e,t)=>{function r(){return t.exports=r=Object.assign||function(a){for(var o=1;o<arguments.length;o++){var c=arguments[o];for(var l in c)Object.prototype.hasOwnProperty.call(c,l)&&(a[l]=c[l])}return a},r.apply(this,arguments)}i(r,"_extends"),t.exports=r}),Op=Y((e,t)=>{function r(a,o){if(a==null)return{};var c={},l=Object.keys(a),u,s;for(s=0;s<l.length;s++)u=l[s],!(o.indexOf(u)>=0)&&(c[u]=a[u]);return c}i(r,"_objectWithoutPropertiesLoose"),t.exports=r}),Tp=Y((e,t)=>{var r=Op();function a(o,c){if(o==null)return{};var l=r(o,c),u,s;if(Object.getOwnPropertySymbols){var d=Object.getOwnPropertySymbols(o);for(s=0;s<d.length;s++)u=d[s],!(c.indexOf(u)>=0)&&Object.prototype.propertyIsEnumerable.call(o,u)&&(l[u]=o[u])}return l}i(a,"_objectWithoutProperties"),t.exports=a}),Wl=Object.prototype.hasOwnProperty;function oa(e,t,r){for(r of e.keys())if(ut(r,t))return r}i(oa,"find");function ut(e,t){var r,a,o;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((a=e.length)===t.length)for(;a--&&ut(e[a],t[a]););return a===-1}if(r===Set){if(e.size!==t.size)return!1;for(a of e)if(o=a,o&&typeof o=="object"&&(o=oa(t,o),!o)||!t.has(o))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(a of e)if(o=a[0],o&&typeof o=="object"&&(o=oa(t,o),!o)||!ut(a[1],t.get(o)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((a=e.byteLength)===t.byteLength)for(;a--&&e.getInt8(a)===t.getInt8(a););return a===-1}if(ArrayBuffer.isView(e)){if((a=e.byteLength)===t.byteLength)for(;a--&&e[a]===t[a];);return a===-1}if(!r||typeof e=="object"){a=0;for(r in e)if(Wl.call(e,r)&&++a&&!Wl.call(t,r)||!(r in t)||!ut(e[r],t[r]))return!1;return Object.keys(t).length===a}}return e!==e&&t!==t}i(ut,"dequal");an();function fe(){return fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},fe.apply(null,arguments)}i(fe,"_extends");function Ou(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}i(Ou,"_assertThisInitialized");function Rt(e,t){return Rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,a){return r.__proto__=a,r},Rt(e,t)}i(Rt,"_setPrototypeOf");function Tu(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Rt(e,t)}i(Tu,"_inheritsLoose");function Kr(e){return Kr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Kr(e)}i(Kr,"_getPrototypeOf");function Iu(e){try{return Function.toString.call(e).indexOf("[native code]")!==-1}catch{return typeof e=="function"}}i(Iu,"_isNativeFunction");function Pa(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch{}return(Pa=i(function(){return!!e},"_isNativeReflectConstruct"))()}i(Pa,"_isNativeReflectConstruct");function Du(e,t,r){if(Pa())return Reflect.construct.apply(null,arguments);var a=[null];a.push.apply(a,t);var o=new(e.bind.apply(e,a));return r&&Rt(o,r.prototype),o}i(Du,"_construct");function Yr(e){var t=typeof Map=="function"?new Map:void 0;return Yr=i(function(r){if(r===null||!Iu(r))return r;if(typeof r!="function")throw new TypeError("Super expression must either be null or a function");if(t!==void 0){if(t.has(r))return t.get(r);t.set(r,a)}function a(){return Du(r,arguments,Kr(this).constructor)}return i(a,"Wrapper"),a.prototype=Object.create(r.prototype,{constructor:{value:a,enumerable:!1,writable:!0,configurable:!0}}),Rt(a,r)},"_wrapNativeSuper"),Yr(e)}i(Yr,"_wrapNativeSuper");var Ip={1:`Passed invalid arguments to hsl, please pass multiple numbers e.g. hsl(360, 0.75, 0.4) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75 }).

`,2:`Passed invalid arguments to hsla, please pass multiple numbers e.g. hsla(360, 0.75, 0.4, 0.7) or an object e.g. rgb({ hue: 255, saturation: 0.4, lightness: 0.75, alpha: 0.7 }).

`,3:`Passed an incorrect argument to a color function, please pass a string representation of a color.

`,4:`Couldn't generate valid rgb string from %s, it returned %s.

`,5:`Couldn't parse the color string. Please provide the color as a string in hex, rgb, rgba, hsl or hsla notation.

`,6:`Passed invalid arguments to rgb, please pass multiple numbers e.g. rgb(255, 205, 100) or an object e.g. rgb({ red: 255, green: 205, blue: 100 }).

`,7:`Passed invalid arguments to rgba, please pass multiple numbers e.g. rgb(255, 205, 100, 0.75) or an object e.g. rgb({ red: 255, green: 205, blue: 100, alpha: 0.75 }).

`,8:`Passed invalid argument to toColorString, please pass a RgbColor, RgbaColor, HslColor or HslaColor object.

`,9:`Please provide a number of steps to the modularScale helper.

`,10:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,11:`Invalid value passed as base to modularScale, expected number or em string but got "%s"

`,12:`Expected a string ending in "px" or a number passed as the first argument to %s(), got "%s" instead.

`,13:`Expected a string ending in "px" or a number passed as the second argument to %s(), got "%s" instead.

`,14:`Passed invalid pixel value ("%s") to %s(), please pass a value like "12px" or 12.

`,15:`Passed invalid base value ("%s") to %s(), please pass a value like "12px" or 12.

`,16:`You must provide a template to this method.

`,17:`You passed an unsupported selector state to this method.

`,18:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,19:`fromSize and toSize must be provided as stringified numbers with the same units.

`,20:`expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,21:"expects the objects in the first argument array to have the properties `prop`, `fromSize`, and `toSize`.\n\n",22:"expects the first argument object to have the properties `prop`, `fromSize`, and `toSize`.\n\n",23:`fontFace expects a name of a font-family.

`,24:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,25:`fontFace expects localFonts to be an array.

`,26:`fontFace expects fileFormats to be an array.

`,27:`radialGradient requries at least 2 color-stops to properly render.

`,28:`Please supply a filename to retinaImage() as the first argument.

`,29:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,30:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",31:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation

`,32:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])
To pass a single animation please supply them in simple values, e.g. animation('rotate', '2s')

`,33:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation

`,34:`borderRadius expects a radius value as a string or number as the second argument.

`,35:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,36:`Property must be a string value.

`,37:`Syntax Error at %s.

`,38:`Formula contains a function that needs parentheses at %s.

`,39:`Formula is missing closing parenthesis at %s.

`,40:`Formula has too many closing parentheses at %s.

`,41:`All values in a formula must have the same unit or be unitless.

`,42:`Please provide a number of steps to the modularScale helper.

`,43:`Please pass a number or one of the predefined scales to the modularScale helper as the ratio.

`,44:`Invalid value passed as base to modularScale, expected number or em/rem string but got %s.

`,45:`Passed invalid argument to hslToColorString, please pass a HslColor or HslaColor object.

`,46:`Passed invalid argument to rgbToColorString, please pass a RgbColor or RgbaColor object.

`,47:`minScreen and maxScreen must be provided as stringified numbers with the same units.

`,48:`fromSize and toSize must be provided as stringified numbers with the same units.

`,49:`Expects either an array of objects or a single object with the properties prop, fromSize, and toSize.

`,50:`Expects the objects in the first argument array to have the properties prop, fromSize, and toSize.

`,51:`Expects the first argument object to have the properties prop, fromSize, and toSize.

`,52:`fontFace expects either the path to the font file(s) or a name of a local copy.

`,53:`fontFace expects localFonts to be an array.

`,54:`fontFace expects fileFormats to be an array.

`,55:`fontFace expects a name of a font-family.

`,56:`linearGradient requries at least 2 color-stops to properly render.

`,57:`radialGradient requries at least 2 color-stops to properly render.

`,58:`Please supply a filename to retinaImage() as the first argument.

`,59:`Passed invalid argument to triangle, please pass correct pointingDirection e.g. 'right'.

`,60:"Passed an invalid value to `height` or `width`. Please provide a pixel based unit.\n\n",61:`Property must be a string value.

`,62:`borderRadius expects a radius value as a string or number as the second argument.

`,63:`borderRadius expects one of "top", "bottom", "left" or "right" as the first argument.

`,64:`The animation shorthand only takes 8 arguments. See the specification for more information: http://mdn.io/animation.

`,65:`To pass multiple animations please supply them in arrays, e.g. animation(['rotate', '2s'], ['move', '1s'])\\nTo pass a single animation please supply them in simple values, e.g. animation('rotate', '2s').

`,66:`The animation shorthand arrays can only have 8 elements. See the specification for more information: http://mdn.io/animation.

`,67:`You must provide a template to this method.

`,68:`You passed an unsupported selector state to this method.

`,69:`Expected a string ending in "px" or a number passed as the first argument to %s(), got %s instead.

`,70:`Expected a string ending in "px" or a number passed as the second argument to %s(), got %s instead.

`,71:`Passed invalid pixel value %s to %s(), please pass a value like "12px" or 12.

`,72:`Passed invalid base value %s to %s(), please pass a value like "12px" or 12.

`,73:`Please provide a valid CSS variable.

`,74:`CSS variable not found and no default was provided.

`,75:`important requires a valid style object, got a %s instead.

`,76:`fromSize and toSize must be provided as stringified numbers with the same units as minScreen and maxScreen.

`,77:`remToPx expects a value in "rem" but you provided it in "%s".

`,78:`base must be set in "px" or "%" but you set it in "%s".
`};function Bu(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var a=t[0],o=[],c;for(c=1;c<t.length;c+=1)o.push(t[c]);return o.forEach(function(l){a=a.replace(/%[a-z]/,l)}),a}i(Bu,"format");var Ce=function(e){Tu(t,e);function t(r){for(var a,o=arguments.length,c=new Array(o>1?o-1:0),l=1;l<o;l++)c[l-1]=arguments[l];return a=e.call(this,Bu.apply(void 0,[Ip[r]].concat(c)))||this,Ou(a)}return i(t,"PolishedError"),t}(Yr(Error));function la(e,t){return e.substr(-t.length)===t}i(la,"endsWith");var Dp=/^([+-]?(?:\d+|\d*\.\d+))([a-z]*|%)$/;function ia(e){if(typeof e!="string")return e;var t=e.match(Dp);return t?parseFloat(e):e}i(ia,"stripUnit");var Bp=i(function(e){return function(t,r){r===void 0&&(r="16px");var a=t,o=r;if(typeof t=="string"){if(!la(t,"px"))throw new Ce(69,e,t);a=ia(t)}if(typeof r=="string"){if(!la(r,"px"))throw new Ce(70,e,r);o=ia(r)}if(typeof a=="string")throw new Ce(71,t,e);if(typeof o=="string")throw new Ce(72,r,e);return""+a/o+e}},"pxtoFactory"),_u=Bp,kC=_u("em"),OC=_u("rem");function Ur(e){return Math.round(e*255)}i(Ur,"colorToInt");function Ru(e,t,r){return Ur(e)+","+Ur(t)+","+Ur(r)}i(Ru,"convertToInt");function Nt(e,t,r,a){if(a===void 0&&(a=Ru),t===0)return a(r,r,r);var o=(e%360+360)%360/60,c=(1-Math.abs(2*r-1))*t,l=c*(1-Math.abs(o%2-1)),u=0,s=0,d=0;o>=0&&o<1?(u=c,s=l):o>=1&&o<2?(u=l,s=c):o>=2&&o<3?(s=c,d=l):o>=3&&o<4?(s=l,d=c):o>=4&&o<5?(u=l,d=c):o>=5&&o<6&&(u=c,d=l);var m=r-c/2,f=u+m,p=s+m,h=d+m;return a(f,p,h)}i(Nt,"hslToRgb");var Kl={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"639",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"};function Nu(e){if(typeof e!="string")return e;var t=e.toLowerCase();return Kl[t]?"#"+Kl[t]:e}i(Nu,"nameToHex");var _p=/^#[a-fA-F0-9]{6}$/,Rp=/^#[a-fA-F0-9]{8}$/,Np=/^#[a-fA-F0-9]{3}$/,Fp=/^#[a-fA-F0-9]{4}$/,Hn=/^rgb\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*\)$/i,Lp=/^rgb(?:a)?\(\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,)?\s*(\d{1,3})\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i,Pp=/^hsl\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*\)$/i,jp=/^hsl(?:a)?\(\s*(\d{0,3}[.]?[0-9]+(?:deg)?)\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,)?\s*(\d{1,3}[.]?[0-9]?)%\s*(?:,|\/)\s*([-+]?\d*[.]?\d+[%]?)\s*\)$/i;function bt(e){if(typeof e!="string")throw new Ce(3);var t=Nu(e);if(t.match(_p))return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16)};if(t.match(Rp)){var r=parseFloat((parseInt(""+t[7]+t[8],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[2],16),green:parseInt(""+t[3]+t[4],16),blue:parseInt(""+t[5]+t[6],16),alpha:r}}if(t.match(Np))return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16)};if(t.match(Fp)){var a=parseFloat((parseInt(""+t[4]+t[4],16)/255).toFixed(2));return{red:parseInt(""+t[1]+t[1],16),green:parseInt(""+t[2]+t[2],16),blue:parseInt(""+t[3]+t[3],16),alpha:a}}var o=Hn.exec(t);if(o)return{red:parseInt(""+o[1],10),green:parseInt(""+o[2],10),blue:parseInt(""+o[3],10)};var c=Lp.exec(t.substring(0,50));if(c)return{red:parseInt(""+c[1],10),green:parseInt(""+c[2],10),blue:parseInt(""+c[3],10),alpha:parseFloat(""+c[4])>1?parseFloat(""+c[4])/100:parseFloat(""+c[4])};var l=Pp.exec(t);if(l){var u=parseInt(""+l[1],10),s=parseInt(""+l[2],10)/100,d=parseInt(""+l[3],10)/100,m="rgb("+Nt(u,s,d)+")",f=Hn.exec(m);if(!f)throw new Ce(4,t,m);return{red:parseInt(""+f[1],10),green:parseInt(""+f[2],10),blue:parseInt(""+f[3],10)}}var p=jp.exec(t.substring(0,50));if(p){var h=parseInt(""+p[1],10),g=parseInt(""+p[2],10)/100,E=parseInt(""+p[3],10)/100,y="rgb("+Nt(h,g,E)+")",v=Hn.exec(y);if(!v)throw new Ce(4,t,y);return{red:parseInt(""+v[1],10),green:parseInt(""+v[2],10),blue:parseInt(""+v[3],10),alpha:parseFloat(""+p[4])>1?parseFloat(""+p[4])/100:parseFloat(""+p[4])}}throw new Ce(5)}i(bt,"parseToRgb");function Fu(e){var t=e.red/255,r=e.green/255,a=e.blue/255,o=Math.max(t,r,a),c=Math.min(t,r,a),l=(o+c)/2;if(o===c)return e.alpha!==void 0?{hue:0,saturation:0,lightness:l,alpha:e.alpha}:{hue:0,saturation:0,lightness:l};var u,s=o-c,d=l>.5?s/(2-o-c):s/(o+c);switch(o){case t:u=(r-a)/s+(r<a?6:0);break;case r:u=(a-t)/s+2;break;default:u=(t-r)/s+4;break}return u*=60,e.alpha!==void 0?{hue:u,saturation:d,lightness:l,alpha:e.alpha}:{hue:u,saturation:d,lightness:l}}i(Fu,"rgbToHsl");function Ye(e){return Fu(bt(e))}i(Ye,"parseToHsl");var Mp=i(function(e){return e.length===7&&e[1]===e[2]&&e[3]===e[4]&&e[5]===e[6]?"#"+e[1]+e[3]+e[5]:e},"reduceHexValue"),ua=Mp;function at(e){var t=e.toString(16);return t.length===1?"0"+t:t}i(at,"numberToHex");function $r(e){return at(Math.round(e*255))}i($r,"colorToHex");function Lu(e,t,r){return ua("#"+$r(e)+$r(t)+$r(r))}i(Lu,"convertToHex");function ar(e,t,r){return Nt(e,t,r,Lu)}i(ar,"hslToHex");function Pu(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return ar(e,t,r);if(typeof e=="object"&&t===void 0&&r===void 0)return ar(e.hue,e.saturation,e.lightness);throw new Ce(1)}i(Pu,"hsl");function ju(e,t,r,a){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?ar(e,t,r):"rgba("+Nt(e,t,r)+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?ar(e.hue,e.saturation,e.lightness):"rgba("+Nt(e.hue,e.saturation,e.lightness)+","+e.alpha+")";throw new Ce(2)}i(ju,"hsla");function Jr(e,t,r){if(typeof e=="number"&&typeof t=="number"&&typeof r=="number")return ua("#"+at(e)+at(t)+at(r));if(typeof e=="object"&&t===void 0&&r===void 0)return ua("#"+at(e.red)+at(e.green)+at(e.blue));throw new Ce(6)}i(Jr,"rgb");function Le(e,t,r,a){if(typeof e=="string"&&typeof t=="number"){var o=bt(e);return"rgba("+o.red+","+o.green+","+o.blue+","+t+")"}else{if(typeof e=="number"&&typeof t=="number"&&typeof r=="number"&&typeof a=="number")return a>=1?Jr(e,t,r):"rgba("+e+","+t+","+r+","+a+")";if(typeof e=="object"&&t===void 0&&r===void 0&&a===void 0)return e.alpha>=1?Jr(e.red,e.green,e.blue):"rgba("+e.red+","+e.green+","+e.blue+","+e.alpha+")"}throw new Ce(7)}i(Le,"rgba");var Up=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isRgb"),$p=i(function(e){return typeof e.red=="number"&&typeof e.green=="number"&&typeof e.blue=="number"&&typeof e.alpha=="number"},"isRgba"),Hp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&(typeof e.alpha!="number"||typeof e.alpha>"u")},"isHsl"),Vp=i(function(e){return typeof e.hue=="number"&&typeof e.saturation=="number"&&typeof e.lightness=="number"&&typeof e.alpha=="number"},"isHsla");function Je(e){if(typeof e!="object")throw new Ce(8);if($p(e))return Le(e);if(Up(e))return Jr(e);if(Vp(e))return ju(e);if(Hp(e))return Pu(e);throw new Ce(8)}i(Je,"toColorString");function ja(e,t,r){return i(function(){var a=r.concat(Array.prototype.slice.call(arguments));return a.length>=t?e.apply(this,a):ja(e,t,a)},"fn")}i(ja,"curried");function we(e){return ja(e,e.length,[])}i(we,"curry");function Mu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{hue:r.hue+parseFloat(e)}))}i(Mu,"adjustHue");var TC=we(Mu);function vt(e,t,r){return Math.max(e,Math.min(t,r))}i(vt,"guard");function Uu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness-parseFloat(e))}))}i(Uu,"darken");var zp=we(Uu),Ge=zp;function $u(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation-parseFloat(e))}))}i($u,"desaturate");var IC=we($u);function Hu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{lightness:vt(0,1,r.lightness+parseFloat(e))}))}i(Hu,"lighten");var qp=we(Hu),ht=qp;function Vu(e,t,r){if(t==="transparent")return r;if(r==="transparent")return t;if(e===0)return r;var a=bt(t),o=fe({},a,{alpha:typeof a.alpha=="number"?a.alpha:1}),c=bt(r),l=fe({},c,{alpha:typeof c.alpha=="number"?c.alpha:1}),u=o.alpha-l.alpha,s=parseFloat(e)*2-1,d=s*u===-1?s:s+u,m=1+s*u,f=(d/m+1)/2,p=1-f,h={red:Math.floor(o.red*f+l.red*p),green:Math.floor(o.green*f+l.green*p),blue:Math.floor(o.blue*f+l.blue*p),alpha:o.alpha*parseFloat(e)+l.alpha*(1-parseFloat(e))};return Le(h)}i(Vu,"mix");var Gp=we(Vu),zu=Gp;function qu(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,(a*100+parseFloat(e)*100)/100)});return Le(o)}i(qu,"opacify");var Wp=we(qu),Xt=Wp;function Gu(e,t){if(t==="transparent")return t;var r=Ye(t);return Je(fe({},r,{saturation:vt(0,1,r.saturation+parseFloat(e))}))}i(Gu,"saturate");var DC=we(Gu);function Wu(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{hue:parseFloat(e)}))}i(Wu,"setHue");var BC=we(Wu);function Ku(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{lightness:parseFloat(e)}))}i(Ku,"setLightness");var _C=we(Ku);function Yu(e,t){return t==="transparent"?t:Je(fe({},Ye(t),{saturation:parseFloat(e)}))}i(Yu,"setSaturation");var RC=we(Yu);function Ju(e,t){return t==="transparent"?t:zu(parseFloat(e),"rgb(0, 0, 0)",t)}i(Ju,"shade");var NC=we(Ju);function Xu(e,t){return t==="transparent"?t:zu(parseFloat(e),"rgb(255, 255, 255)",t)}i(Xu,"tint");var FC=we(Xu);function Zu(e,t){if(t==="transparent")return t;var r=bt(t),a=typeof r.alpha=="number"?r.alpha:1,o=fe({},r,{alpha:vt(0,1,+(a*100-parseFloat(e)*100).toFixed(2)/100)});return Le(o)}i(Zu,"transparentize");var Kp=we(Zu),W=Kp,Yp=b.div(Ze,({theme:e})=>({backgroundColor:e.base==="light"?"rgba(0,0,0,.01)":"rgba(255,255,255,.01)",borderRadius:e.appBorderRadius,border:`1px dashed ${e.appBorderColor}`,display:"flex",alignItems:"center",justifyContent:"center",padding:20,margin:"25px 0 40px",color:W(.3,e.color.defaultText),fontSize:e.typography.size.s2})),Qu=i(e=>n.createElement(Yp,{...e,className:"docblock-emptyblock sb-unstyled"}),"EmptyBlock"),Jp=b(Vt)(({theme:e})=>({fontSize:`${e.typography.size.s2-1}px`,lineHeight:"19px",margin:"25px 0 40px",borderRadius:e.appBorderRadius,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0","pre.prismjs":{padding:20,background:"inherit"}})),Xp=b.div(({theme:e})=>({background:e.background.content,borderRadius:e.appBorderRadius,border:`1px solid ${e.appBorderColor}`,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",margin:"25px 0 40px",padding:"20px 20px 20px 22px"})),Rr=b.div(({theme:e})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,height:17,marginTop:1,width:"60%",[`&:first-child${Go}`]:{margin:0}})),Zp=i(()=>n.createElement(Xp,null,n.createElement(Rr,null),n.createElement(Rr,{style:{width:"80%"}}),n.createElement(Rr,{style:{width:"30%"}}),n.createElement(Rr,{style:{width:"80%"}})),"SourceSkeleton"),Qp=i(({isLoading:e,error:t,language:r,code:a,dark:o,format:c=!0,...l})=>{let{typography:u}=ye();if(e)return n.createElement(Zp,null);if(t)return n.createElement(Qu,null,t);let s=n.createElement(Jp,{bordered:!0,copyable:!0,format:c,language:r??"jsx",className:"docblock-source sb-unstyled",...l},a);if(typeof o>"u")return s;let d=o?An.dark:An.light;return n.createElement(zo,{theme:qo({...d,fontCode:u.fonts.mono,fontBase:u.fonts.base})},s)},"Source"),ie=i(e=>`& :where(${e}:not(.sb-anchor, .sb-unstyled, .sb-unstyled ${e}))`,"toGlobalSelector"),Ma=600,WC=b.h1(Ze,({theme:e})=>({color:e.color.defaultText,fontSize:e.typography.size.m3,fontWeight:e.typography.weight.bold,lineHeight:"32px",[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.l1,lineHeight:"36px",marginBottom:"16px"}})),KC=b.h2(Ze,({theme:e})=>({fontWeight:e.typography.weight.regular,fontSize:e.typography.size.s3,lineHeight:"20px",borderBottom:"none",marginBottom:15,[`@media (min-width: ${Ma}px)`]:{fontSize:e.typography.size.m1,lineHeight:"28px",marginBottom:24},color:W(.25,e.color.defaultText)})),YC=b.div(({theme:e})=>{let t={fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s3,margin:0,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch"},r={margin:"20px 0 8px",padding:0,cursor:"text",position:"relative",color:e.color.defaultText,"&:first-of-type":{marginTop:0,paddingTop:0},"&:hover a.anchor":{textDecoration:"none"},"& code":{fontSize:"inherit"}},a={lineHeight:1,margin:"0 2px",padding:"3px 5px",whiteSpace:"nowrap",borderRadius:3,fontSize:e.typography.size.s2-1,border:e.base==="light"?`1px solid ${e.color.mediumlight}`:`1px solid ${e.color.darker}`,color:e.base==="light"?W(.1,e.color.defaultText):W(.3,e.color.defaultText),backgroundColor:e.base==="light"?e.color.lighter:e.color.border};return{maxWidth:1e3,width:"100%",minWidth:0,[ie("a")]:{...t,fontSize:"inherit",lineHeight:"24px",color:e.color.secondary,textDecoration:"none","&.absent":{color:"#cc0000"},"&.anchor":{display:"block",paddingLeft:30,marginLeft:-30,cursor:"pointer",position:"absolute",top:0,left:0,bottom:0}},[ie("blockquote")]:{...t,margin:"16px 0",borderLeft:`4px solid ${e.color.medium}`,padding:"0 15px",color:e.color.dark,"& > :first-of-type":{marginTop:0},"& > :last-child":{marginBottom:0}},[ie("div")]:t,[ie("dl")]:{...t,margin:"16px 0",padding:0,"& dt":{fontSize:"14px",fontWeight:"bold",fontStyle:"italic",padding:0,margin:"16px 0 4px"},"& dt:first-of-type":{padding:0},"& dt > :first-of-type":{marginTop:0},"& dt > :last-child":{marginBottom:0},"& dd":{margin:"0 0 16px",padding:"0 15px"},"& dd > :first-of-type":{marginTop:0},"& dd > :last-child":{marginBottom:0}},[ie("h1")]:{...t,...r,fontSize:`${e.typography.size.l1}px`,fontWeight:e.typography.weight.bold},[ie("h2")]:{...t,...r,fontSize:`${e.typography.size.m2}px`,paddingBottom:4,borderBottom:`1px solid ${e.appBorderColor}`},[ie("h3")]:{...t,...r,fontSize:`${e.typography.size.m1}px`,fontWeight:e.typography.weight.bold},[ie("h4")]:{...t,...r,fontSize:`${e.typography.size.s3}px`},[ie("h5")]:{...t,...r,fontSize:`${e.typography.size.s2}px`},[ie("h6")]:{...t,...r,fontSize:`${e.typography.size.s2}px`,color:e.color.dark},[ie("hr")]:{border:"0 none",borderTop:`1px solid ${e.appBorderColor}`,height:4,padding:0},[ie("img")]:{maxWidth:"100%"},[ie("li")]:{...t,fontSize:e.typography.size.s2,color:e.color.defaultText,lineHeight:"24px","& + li":{marginTop:".25em"},"& ul, & ol":{marginTop:".25em",marginBottom:0},"& code":a},[ie("ol")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0}},[ie("p")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",color:e.color.defaultText,"& code":a},[ie("pre")]:{...t,fontFamily:e.typography.fonts.mono,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",lineHeight:"18px",padding:"11px 1rem",whiteSpace:"pre-wrap",color:"inherit",borderRadius:3,margin:"1rem 0","&:not(.prismjs)":{background:"transparent",border:"none",borderRadius:0,padding:0,margin:0},"& pre, &.prismjs":{padding:15,margin:0,whiteSpace:"pre-wrap",color:"inherit",fontSize:"13px",lineHeight:"19px",code:{color:"inherit",fontSize:"inherit"}},"& code":{whiteSpace:"pre"},"& code, & tt":{border:"none"}},[ie("span")]:{...t,"&.frame":{display:"block",overflow:"hidden","& > span":{border:`1px solid ${e.color.medium}`,display:"block",float:"left",overflow:"hidden",margin:"13px 0 0",padding:7,width:"auto"},"& span img":{display:"block",float:"left"},"& span span":{clear:"both",color:e.color.darkest,display:"block",padding:"5px 0 0"}},"&.align-center":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"center"},"& span img":{margin:"0 auto",textAlign:"center"}},"&.align-right":{display:"block",overflow:"hidden",clear:"both","& > span":{display:"block",overflow:"hidden",margin:"13px 0 0",textAlign:"right"},"& span img":{margin:0,textAlign:"right"}},"&.float-left":{display:"block",marginRight:13,overflow:"hidden",float:"left","& span":{margin:"13px 0 0"}},"&.float-right":{display:"block",marginLeft:13,overflow:"hidden",float:"right","& > span":{display:"block",overflow:"hidden",margin:"13px auto 0",textAlign:"right"}}},[ie("table")]:{...t,margin:"16px 0",fontSize:e.typography.size.s2,lineHeight:"24px",padding:0,borderCollapse:"collapse","& tr":{borderTop:`1px solid ${e.appBorderColor}`,backgroundColor:e.appContentBg,margin:0,padding:0},"& tr:nth-of-type(2n)":{backgroundColor:e.base==="dark"?e.color.darker:e.color.lighter},"& tr th":{fontWeight:"bold",color:e.color.defaultText,border:`1px solid ${e.appBorderColor}`,margin:0,padding:"6px 13px"},"& tr td":{border:`1px solid ${e.appBorderColor}`,color:e.color.defaultText,margin:0,padding:"6px 13px"},"& tr th :first-of-type, & tr td :first-of-type":{marginTop:0},"& tr th :last-child, & tr td :last-child":{marginBottom:0}},[ie("ul")]:{...t,margin:"16px 0",paddingLeft:30,"& :first-of-type":{marginTop:0},"& :last-child":{marginBottom:0},listStyle:"disc"}}}),JC=b.div(({theme:e})=>({background:e.background.content,display:"flex",flexDirection:"row-reverse",justifyContent:"center",padding:"4rem 20px",minHeight:"100vh",boxSizing:"border-box",gap:"3rem",[`@media (min-width: ${Ma}px)`]:{}})),on=i(e=>({borderRadius:e.appBorderRadius,background:e.background.content,boxShadow:e.base==="light"?"rgba(0, 0, 0, 0.10) 0 1px 3px 0":"rgba(0, 0, 0, 0.20) 0 2px 5px 0",border:`1px solid ${e.appBorderColor}`}),"getBlockBackgroundStyle"),{window:m5}=globalThis,em=Ct({scale:1}),{PREVIEW_URL:h5}=globalThis,g5=b.strong(({theme:e})=>({color:e.color.orange})),tm=b(mn)({position:"absolute",left:0,right:0,top:0,transition:"transform .2s linear"}),rm=b.div({display:"flex",alignItems:"center",gap:4}),nm=b.div(({theme:e})=>({width:14,height:14,borderRadius:2,margin:"0 7px",backgroundColor:e.appBorderColor,animation:`${e.animation.glow} 1.5s ease-in-out infinite`})),am=i(({isLoading:e,storyId:t,baseUrl:r,zoom:a,resetZoom:o,...c})=>n.createElement(tm,{...c},n.createElement(rm,{key:"left"},e?[1,2,3].map(l=>n.createElement(nm,{key:l})):n.createElement(n.Fragment,null,n.createElement(G,{key:"zoomin",onClick:l=>{l.preventDefault(),a(.8)},title:"Zoom in"},n.createElement(Uo,null)),n.createElement(G,{key:"zoomout",onClick:l=>{l.preventDefault(),a(1.25)},title:"Zoom out"},n.createElement($o,null)),n.createElement(G,{key:"zoomreset",onClick:l=>{l.preventDefault(),o()},title:"Reset zoom"},n.createElement(Ho,null))))),"Toolbar"),om=b.div(({isColumn:e,columns:t,layout:r})=>({display:e||!t?"block":"flex",position:"relative",flexWrap:"wrap",overflow:"auto",flexDirection:e?"column":"row","& .innerZoomElementWrapper > *":e?{width:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"block"}:{maxWidth:r!=="fullscreen"?"calc(100% - 20px)":"100%",display:"inline-block"}}),({layout:e="padded",inline:t})=>e==="centered"||e==="padded"?{padding:t?"32px 22px":"0px","& .innerZoomElementWrapper > *":{width:"auto",border:"8px solid transparent!important"}}:{},({layout:e="padded",inline:t})=>e==="centered"&&t?{display:"flex",justifyContent:"center",justifyItems:"center",alignContent:"center",alignItems:"center"}:{},({columns:e})=>e&&e>1?{".innerZoomElementWrapper > *":{minWidth:`calc(100% / ${e} - 20px)`}}:{}),Yl=b(Qp)(({theme:e})=>({margin:0,borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:e.appBorderRadius,borderBottomRightRadius:e.appBorderRadius,border:"none",background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content),color:e.color.lightest,button:{background:e.base==="light"?"rgba(0, 0, 0, 0.85)":Ge(.05,e.background.content)}})),lm=b.div(({theme:e,withSource:t,isExpanded:r})=>({position:"relative",overflow:"hidden",margin:"25px 0 40px",...on(e),borderBottomLeftRadius:t&&r&&0,borderBottomRightRadius:t&&r&&0,borderBottomWidth:r&&0,"h3 + &":{marginTop:"16px"}}),({withToolbar:e})=>e&&{paddingTop:40}),im=i((e,t,r)=>{switch(!0){case!!(e&&e.error):return{source:null,actionItem:{title:"No code available",className:"docblock-code-toggle docblock-code-toggle--disabled",disabled:!0,onClick:i(()=>r(!1),"onClick")}};case t:return{source:n.createElement(Yl,{...e,dark:!0}),actionItem:{title:"Hide code",className:"docblock-code-toggle docblock-code-toggle--expanded",onClick:i(()=>r(!1),"onClick")}};default:return{source:n.createElement(Yl,{...e,dark:!0}),actionItem:{title:"Show code",className:"docblock-code-toggle",onClick:i(()=>r(!0),"onClick")}}}},"getSource");function es(e){if(cr.count(e)===1){let t=e;if(t.props)return t.props.id}return null}i(es,"getStoryId");var um=b(am)({position:"absolute",top:0,left:0,right:0,height:40}),sm=b.div({overflow:"hidden",position:"relative"}),cm=i(({isLoading:e,isColumn:t,columns:r,children:a,withSource:o,withToolbar:c=!1,isExpanded:l=!1,additionalActions:u,className:s,layout:d="padded",inline:m=!1,...f})=>{let[p,h]=R(l),{source:g,actionItem:E}=im(o,p,h),[y,v]=R(1),C=[s].concat(["sbdocs","sbdocs-preview","sb-unstyled"]),w=o?[E]:[],[k,I]=R(u?[...u]:[]),T=[...w,...k],{window:B}=globalThis,F=$(async H=>{let{createCopyToClipboardFunction:z}=await Promise.resolve().then(()=>(U(),fo));z()},[]),M=i(H=>{let z=B.getSelection();z&&z.type==="Range"||(H.preventDefault(),k.filter(Q=>Q.title==="Copied").length===0&&F(g?.props.code??"").then(()=>{I([...k,{title:"Copied",onClick:i(()=>{},"onClick")}]),B.setTimeout(()=>I(k.filter(Q=>Q.title!=="Copied")),1500)}))},"onCopyCapture");return n.createElement(lm,{withSource:o,withToolbar:c,...f,className:C.join(" ")},c&&n.createElement(um,{isLoading:e,border:!0,zoom:H=>v(y*H),resetZoom:()=>v(1),storyId:es(a),baseUrl:"./iframe.html"}),n.createElement(em.Provider,{value:{scale:y}},n.createElement(sm,{className:"docs-story",onCopyCapture:o&&M},n.createElement(om,{isColumn:t||!Array.isArray(a),columns:r,layout:d,inline:m},n.createElement(En.Element,{centered:d==="centered",scale:m?y:1},Array.isArray(a)?a.map((H,z)=>n.createElement("div",{key:z},H)):n.createElement("div",null,a))),n.createElement(Mt,{actionItems:T}))),o&&p&&g)},"Preview"),x5=b(cm)(()=>({".docs-story":{paddingTop:32,paddingBottom:40}})),D5=b.div(({theme:e})=>({marginRight:30,fontSize:`${e.typography.size.s1}px`,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText)})),B5=b.div({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),_5=b.div({display:"flex",flexDirection:"row",alignItems:"baseline","&:not(:last-child)":{marginBottom:"1rem"}}),R5=b.div(Ze,({theme:e})=>({...on(e),margin:"25px 0 40px",padding:"30px 20px"})),M5=b.div(({theme:e})=>({fontWeight:e.typography.weight.bold,color:e.color.defaultText})),U5=b.div(({theme:e})=>({color:e.base==="light"?W(.2,e.color.defaultText):W(.6,e.color.defaultText)})),$5=b.div({flex:"0 0 30%",lineHeight:"20px",marginTop:5}),H5=b.div(({theme:e})=>({flex:1,textAlign:"center",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,lineHeight:1,overflow:"hidden",color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText),"> div":{display:"inline-block",overflow:"hidden",maxWidth:"100%",textOverflow:"ellipsis"},span:{display:"block",marginTop:2}})),V5=b.div({display:"flex",flexDirection:"row"}),z5=b.div(({background:e})=>({position:"relative",flex:1,"&::before":{position:"absolute",top:0,left:0,width:"100%",height:"100%",background:e,content:'""'}})),q5=b.div(({theme:e})=>({...on(e),display:"flex",flexDirection:"row",height:50,marginBottom:5,overflow:"hidden",backgroundColor:"white",backgroundImage:"repeating-linear-gradient(-45deg, #ccc, #ccc 1px, #fff 1px, #fff 16px)",backgroundClip:"padding-box"})),G5=b.div({display:"flex",flexDirection:"column",flex:1,position:"relative",marginBottom:30}),W5=b.div({flex:1,display:"flex",flexDirection:"row"}),K5=b.div({display:"flex",alignItems:"flex-start"}),Y5=b.div({flex:"0 0 30%"}),J5=b.div({flex:1}),X5=b.div(({theme:e})=>({display:"flex",flexDirection:"row",alignItems:"center",paddingBottom:20,fontWeight:e.typography.weight.bold,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText)})),Z5=b.div(({theme:e})=>({fontSize:e.typography.size.s2,lineHeight:"20px",display:"flex",flexDirection:"column"})),aS=b.div(({theme:e})=>({fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s1,color:e.color.defaultText,marginLeft:10,lineHeight:1.2,display:"-webkit-box",overflow:"hidden",wordBreak:"break-word",textOverflow:"ellipsis",WebkitLineClamp:2,WebkitBoxOrient:"vertical"})),oS=b.div(({theme:e})=>({...on(e),overflow:"hidden",height:40,width:40,display:"flex",alignItems:"center",justifyContent:"center",flex:"none","> img, > svg":{width:20,height:20}})),lS=b.div({display:"inline-flex",flexDirection:"row",alignItems:"center",width:"100%"}),iS=b.div({display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(140px, 1fr))",gridGap:"8px 16px",gridAutoFlow:"row dense",gridAutoRows:50}),hS=b.aside(()=>({width:"10rem","@media (max-width: 768px)":{display:"none"}})),gS=b.nav(({theme:e})=>({position:"fixed",bottom:0,top:0,width:"10rem",paddingTop:"4rem",paddingBottom:"2rem",overflowY:"auto",fontFamily:e.typography.fonts.base,fontSize:e.typography.size.s2,WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",WebkitTapHighlightColor:"rgba(0, 0, 0, 0)",WebkitOverflowScrolling:"touch","& *":{boxSizing:"border-box"},"& > .toc-wrapper > .toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`,".toc-list":{paddingLeft:0,borderLeft:`solid 2px ${e.color.mediumlight}`}}},"& .toc-list-item":{position:"relative",listStyleType:"none",marginLeft:20,paddingTop:3,paddingBottom:3},"& .toc-list-item::before":{content:'""',position:"absolute",height:"100%",top:0,left:0,transform:"translateX(calc(-2px - 20px))",borderLeft:`solid 2px ${e.color.mediumdark}`,opacity:0,transition:"opacity 0.2s"},"& .toc-list-item.is-active-li::before":{opacity:1},"& .toc-list-item > a":{color:e.color.defaultText,textDecoration:"none"},"& .toc-list-item.is-active-li > a":{fontWeight:600,color:e.color.secondary,textDecoration:"none"}})),bS=b.p(({theme:e})=>({fontWeight:600,fontSize:"0.875em",color:e.textColor,textTransform:"uppercase",marginBottom:10}));function lt(){return lt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},lt.apply(this,arguments)}i(lt,"t");var dm=["children","options"],L={blockQuote:"0",breakLine:"1",breakThematic:"2",codeBlock:"3",codeFenced:"4",codeInline:"5",footnote:"6",footnoteReference:"7",gfmTask:"8",heading:"9",headingSetext:"10",htmlBlock:"11",htmlComment:"12",htmlSelfClosing:"13",image:"14",link:"15",linkAngleBraceStyleDetector:"16",linkBareUrlDetector:"17",linkMailtoDetector:"18",newlineCoalescer:"19",orderedList:"20",paragraph:"21",ref:"22",refImage:"23",refLink:"24",table:"25",tableSeparator:"26",text:"27",textBolded:"28",textEmphasized:"29",textEscaped:"30",textMarked:"31",textStrikethroughed:"32",unorderedList:"33"},Jl;(function(e){e[e.MAX=0]="MAX",e[e.HIGH=1]="HIGH",e[e.MED=2]="MED",e[e.LOW=3]="LOW",e[e.MIN=4]="MIN"})(Jl||(Jl={}));var Xl=["allowFullScreen","allowTransparency","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","charSet","classId","colSpan","contentEditable","contextMenu","crossOrigin","encType","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","hrefLang","inputMode","keyParams","keyType","marginHeight","marginWidth","maxLength","mediaGroup","minLength","noValidate","radioGroup","readOnly","rowSpan","spellCheck","srcDoc","srcLang","srcSet","tabIndex","useMap"].reduce((e,t)=>(e[t.toLowerCase()]=t,e),{class:"className",for:"htmlFor"}),Zl={amp:"&",apos:"'",gt:">",lt:"<",nbsp:"\xA0",quot:"\u201C"},pm=["style","script"],mm=["src","href","data","formAction","srcDoc","action"],fm=/([-A-Z0-9_:]+)(?:\s*=\s*(?:(?:"((?:\\.|[^"])*)")|(?:'((?:\\.|[^'])*)')|(?:\{((?:\\.|{[^}]*?}|[^}])*)\})))?/gi,hm=/mailto:/i,gm=/\n{2,}$/,ts=/^(\s*>[\s\S]*?)(?=\n\n|$)/,bm=/^ *> ?/gm,ym=/^(?:\[!([^\]]*)\]\n)?([\s\S]*)/,Em=/^ {2,}\n/,vm=/^(?:( *[-*_])){3,} *(?:\n *)+\n/,rs=/^(?: {1,3})?(`{3,}|~{3,}) *(\S+)? *([^\n]*?)?\n([\s\S]*?)(?:\1\n?|$)/,ns=/^(?: {4}[^\n]+\n*)+(?:\n *)+\n?/,xm=/^(`+)((?:\\`|(?!\1)`|[^`])+)\1/,Am=/^(?:\n *)*\n/,Cm=/\r\n?/g,Sm=/^\[\^([^\]]+)](:(.*)((\n+ {4,}.*)|(\n(?!\[\^).+))*)/,wm=/^\[\^([^\]]+)]/,km=/\f/g,Om=/^---[ \t]*\n(.|\n)*\n---[ \t]*\n/,Tm=/^\s*?\[(x|\s)\]/,as=/^ *(#{1,6}) *([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,os=/^ *(#{1,6}) +([^\n]+?)(?: +#*)?(?:\n *)*(?:\n|$)/,ls=/^([^\n]+)\n *(=|-){3,} *(?:\n *)+\n/,sa=/^ *(?!<[a-z][^ >/]* ?\/>)<([a-z][^ >/]*) ?((?:[^>]*[^/])?)>\n?(\s*(?:<\1[^>]*?>[\s\S]*?<\/\1>|(?!<\1\b)[\s\S])*?)<\/\1>(?!<\/\1>)\n*/i,Im=/&([a-z0-9]+|#[0-9]{1,6}|#x[0-9a-fA-F]{1,6});/gi,is=/^<!--[\s\S]*?(?:-->)/,Dm=/^(data|aria|x)-[a-z_][a-z\d_.-]*$/,ca=/^ *<([a-z][a-z0-9:]*)(?:\s+((?:<.*?>|[^>])*))?\/?>(?!<\/\1>)(\s*\n)?/i,Bm=/^\{.*\}$/,_m=/^(https?:\/\/[^\s<]+[^<.,:;"')\]\s])/,Rm=/^<([^ >]+@[^ >]+)>/,Nm=/^<([^ >]+:\/[^ >]+)>/,Fm=/-([a-z])?/gi,us=/^(\|.*)\n(?: *(\|? *[-:]+ *\|[-| :]*)\n((?:.*\|.*\n)*))?\n?/,Lm=/^\[([^\]]*)\]:\s+<?([^\s>]+)>?\s*("([^"]*)")?/,Pm=/^!\[([^\]]*)\] ?\[([^\]]*)\]/,jm=/^\[([^\]]*)\] ?\[([^\]]*)\]/,Mm=/(\n|^[-*]\s|^#|^ {2,}|^-{2,}|^>\s)/,Um=/\t/g,$m=/(^ *\||\| *$)/g,Hm=/^ *:-+: *$/,Vm=/^ *:-+ *$/,zm=/^ *-+: *$/,ln="((?:\\[.*?\\][([].*?[)\\]]|<.*?>(?:.*?<.*?>)?|`.*?`|\\\\\\1|[\\s\\S])+?)",qm=new RegExp(`^([*_])\\1${ln}\\1\\1(?!\\1)`),Gm=new RegExp(`^([*_])${ln}\\1(?!\\1)`),Wm=new RegExp(`^(==)${ln}\\1`),Km=new RegExp(`^(~~)${ln}\\1`),Ym=/^\\([^0-9A-Za-z\s])/,Ql=/\\([^0-9A-Za-z\s])/g,Jm=/^([\s\S](?:(?!  |[0-9]\.)[^=*_~\-\n<`\\\[!])*)/,Xm=/^\n+/,Zm=/^([ \t]*)/,Qm=/\\([^\\])/g,ef=/(?:^|\n)( *)$/,Ua="(?:\\d+\\.)",$a="(?:[*+-])";function Ha(e){return"( *)("+(e===1?Ua:$a)+") +"}i(Ha,"de");var ss=Ha(1),cs=Ha(2);function Va(e){return new RegExp("^"+(e===1?ss:cs))}i(Va,"fe");var tf=Va(1),rf=Va(2);function za(e){return new RegExp("^"+(e===1?ss:cs)+"[^\\n]*(?:\\n(?!\\1"+(e===1?Ua:$a)+" )[^\\n]*)*(\\n|$)","gm")}i(za,"ge");var nf=za(1),af=za(2);function qa(e){let t=e===1?Ua:$a;return new RegExp("^( *)("+t+") [\\s\\S]+?(?:\\n{2,}(?! )(?!\\1"+t+" (?!"+t+" ))\\n*|\\s*\\n*$)")}i(qa,"xe");var ds=qa(1),ps=qa(2);function da(e,t){let r=t===1,a=r?ds:ps,o=r?nf:af,c=r?tf:rf;return{match:yt(function(l,u){let s=ef.exec(u.prevCapture);return s&&(u.list||!u.inline&&!u.simple)?a.exec(l=s[1]+l):null}),order:1,parse(l,u,s){let d=r?+l[2]:void 0,m=l[0].replace(gm,`
`).match(o),f=!1;return{items:m.map(function(p,h){let g=c.exec(p)[0].length,E=new RegExp("^ {1,"+g+"}","gm"),y=p.replace(E,"").replace(c,""),v=h===m.length-1,C=y.indexOf(`

`)!==-1||v&&f;f=C;let w=s.inline,k=s.list,I;s.list=!0,C?(s.inline=!1,I=Ft(y)+`

`):(s.inline=!0,I=Ft(y));let T=u(I,s);return s.inline=w,s.list=k,T}),ordered:r,start:d}},render:i((l,u,s)=>e(l.ordered?"ol":"ul",{key:s.key,start:l.type===L.orderedList?l.start:void 0},l.items.map(function(d,m){return e("li",{key:m},u(d,s))})),"render")}}i(da,"Ce");var of=new RegExp(`^\\[((?:\\[[^\\]]*\\]|[^\\[\\]]|\\](?=[^\\[]*\\]))*)\\]\\(\\s*<?((?:\\([^)]*\\)|[^\\s\\\\]|\\\\.)*?)>?(?:\\s+['"]([\\s\\S]*?)['"])?\\s*\\)`),lf=/^!\[(.*?)\]\( *((?:\([^)]*\)|[^() ])*) *"?([^)"]*)?"?\)/,ms=[ts,rs,ns,as,ls,os,us,ds,ps],uf=[...ms,/^[^\n]+(?:  \n|\n{2,})/,sa,is,ca];function Ft(e){let t=e.length;for(;t>0&&e[t-1]<=" ";)t--;return e.slice(0,t)}i(Ft,"ze");function It(e){return e.replace(/[ÀÁÂÃÄÅàáâãäåæÆ]/g,"a").replace(/[çÇ]/g,"c").replace(/[ðÐ]/g,"d").replace(/[ÈÉÊËéèêë]/g,"e").replace(/[ÏïÎîÍíÌì]/g,"i").replace(/[Ññ]/g,"n").replace(/[øØœŒÕõÔôÓóÒò]/g,"o").replace(/[ÜüÛûÚúÙù]/g,"u").replace(/[ŸÿÝý]/g,"y").replace(/[^a-z0-9- ]/gi,"").replace(/ /gi,"-").toLowerCase()}i(It,"Le");function fs(e){return zm.test(e)?"right":Hm.test(e)?"center":Vm.test(e)?"left":null}i(fs,"Ae");function pa(e,t,r,a){let o=r.inTable;r.inTable=!0;let c=[[]],l="";function u(){if(!l)return;let s=c[c.length-1];s.push.apply(s,t(l,r)),l=""}return i(u,"a"),e.trim().split(/(`[^`]*`|\\\||\|)/).filter(Boolean).forEach((s,d,m)=>{s.trim()==="|"&&(u(),a)?d!==0&&d!==m.length-1&&c.push([]):l+=s}),u(),r.inTable=o,c}i(pa,"Oe");function hs(e,t,r){r.inline=!0;let a=e[2]?e[2].replace($m,"").split("|").map(fs):[],o=e[3]?function(l,u,s){return l.trim().split(`
`).map(function(d){return pa(d,u,s,!0)})}(e[3],t,r):[],c=pa(e[1],t,r,!!o.length);return r.inline=!1,o.length?{align:a,cells:o,header:c,type:L.table}:{children:c,type:L.paragraph}}i(hs,"Te");function ma(e,t){return e.align[t]==null?{}:{textAlign:e.align[t]}}i(ma,"Be");function yt(e){return e.inline=1,e}i(yt,"Me");function We(e){return yt(function(t,r){return r.inline?e.exec(t):null})}i(We,"Re");function Ke(e){return yt(function(t,r){return r.inline||r.simple?e.exec(t):null})}i(Ke,"Ie");function ze(e){return function(t,r){return r.inline||r.simple?null:e.exec(t)}}i(ze,"De");function Dt(e){return yt(function(t){return e.exec(t)})}i(Dt,"Ue");function gs(e,t){if(t.inline||t.simple)return null;let r="";e.split(`
`).every(o=>(o+=`
`,!ms.some(c=>c.test(o))&&(r+=o,!!o.trim())));let a=Ft(r);return a==""?null:[r,,a]}i(gs,"Ne");var sf=/(javascript|vbscript|data(?!:image)):/i;function bs(e){try{let t=decodeURIComponent(e).replace(/[^A-Za-z0-9/:]/g,"");if(sf.test(t))return null}catch{return null}return e}i(bs,"He");function fa(e){return e.replace(Qm,"$1")}i(fa,"Pe");function Zt(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!0,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(Zt,"_e");function ys(e,t,r){let a=r.inline||!1,o=r.simple||!1;r.inline=!1,r.simple=!0;let c=e(t,r);return r.inline=a,r.simple=o,c}i(ys,"Fe");function Es(e,t,r){let a=r.inline||!1;r.inline=!1;let o=e(t,r);return r.inline=a,o}i(Es,"We");var Vn=i((e,t,r)=>({children:Zt(t,e[2],r)}),"Ge");function Hr(){return{}}i(Hr,"Ze");function Vr(){return null}i(Vr,"qe");function vs(...e){return e.filter(Boolean).join(" ")}i(vs,"Qe");function zr(e,t,r){let a=e,o=t.split(".");for(;o.length&&(a=a[o[0]],a!==void 0);)o.shift();return a||r}i(zr,"Ve");function xs(e="",t={}){function r(p,h,...g){let E=zr(t.overrides,`${p}.props`,{});return t.createElement(function(y,v){let C=zr(v,y);return C?typeof C=="function"||typeof C=="object"&&"render"in C?C:zr(v,`${y}.component`,y):y}(p,t.overrides),lt({},h,E,{className:vs(h?.className,E.className)||void 0}),...g)}i(r,"u");function a(p){p=p.replace(Om,"");let h=!1;t.forceInline?h=!0:t.forceBlock||(h=Mm.test(p)===!1);let g=d(s(h?p:`${Ft(p).replace(Xm,"")}

`,{inline:h}));for(;typeof g[g.length-1]=="string"&&!g[g.length-1].trim();)g.pop();if(t.wrapper===null)return g;let E=t.wrapper||(h?"span":"div"),y;if(g.length>1||t.forceWrapper)y=g;else{if(g.length===1)return y=g[0],typeof y=="string"?r("span",{key:"outer"},y):y;y=null}return t.createElement(E,{key:"outer"},y)}i(a,"Z");function o(p,h){let g=h.match(fm);return g?g.reduce(function(E,y){let v=y.indexOf("=");if(v!==-1){let C=function(T){return T.indexOf("-")!==-1&&T.match(Dm)===null&&(T=T.replace(Fm,function(B,F){return F.toUpperCase()})),T}(y.slice(0,v)).trim(),w=function(T){let B=T[0];return(B==='"'||B==="'")&&T.length>=2&&T[T.length-1]===B?T.slice(1,-1):T}(y.slice(v+1).trim()),k=Xl[C]||C;if(k==="ref")return E;let I=E[k]=function(T,B,F,M){return B==="style"?function(H){let z=[],Q="",ne=!1,A=!1,x="";if(!H)return z;for(let _=0;_<H.length;_++){let O=H[_];if(O!=='"'&&O!=="'"||ne||(A?O===x&&(A=!1,x=""):(A=!0,x=O)),O==="("&&Q.endsWith("url")?ne=!0:O===")"&&ne&&(ne=!1),O!==";"||A||ne)Q+=O;else{let D=Q.trim();if(D){let N=D.indexOf(":");if(N>0){let V=D.slice(0,N).trim(),J=D.slice(N+1).trim();z.push([V,J])}}Q=""}}let S=Q.trim();if(S){let _=S.indexOf(":");if(_>0){let O=S.slice(0,_).trim(),D=S.slice(_+1).trim();z.push([O,D])}}return z}(F).reduce(function(H,[z,Q]){return H[z.replace(/(-[a-z])/g,ne=>ne[1].toUpperCase())]=M(Q,T,z),H},{}):mm.indexOf(B)!==-1?M(F,T,B):(F.match(Bm)&&(F=F.slice(1,F.length-1)),F==="true"||F!=="false"&&F)}(p,C,w,t.sanitizer);typeof I=="string"&&(sa.test(I)||ca.test(I))&&(E[k]=a(I.trim()))}else y!=="style"&&(E[Xl[y]||y]=!0);return E},{}):null}i(o,"q"),t.overrides=t.overrides||{},t.sanitizer=t.sanitizer||bs,t.slugify=t.slugify||It,t.namedCodesToUnicode=t.namedCodesToUnicode?lt({},Zl,t.namedCodesToUnicode):Zl,t.createElement=t.createElement||P;let c=[],l={},u={[L.blockQuote]:{match:ze(ts),order:1,parse(p,h,g){let[,E,y]=p[0].replace(bm,"").match(ym);return{alert:E,children:h(y,g)}},render(p,h,g){let E={key:g.key};return p.alert&&(E.className="markdown-alert-"+t.slugify(p.alert.toLowerCase(),It),p.children.unshift({attrs:{},children:[{type:L.text,text:p.alert}],noInnerParse:!0,type:L.htmlBlock,tag:"header"})),r("blockquote",E,h(p.children,g))}},[L.breakLine]:{match:Dt(Em),order:1,parse:Hr,render:i((p,h,g)=>r("br",{key:g.key}),"render")},[L.breakThematic]:{match:ze(vm),order:1,parse:Hr,render:i((p,h,g)=>r("hr",{key:g.key}),"render")},[L.codeBlock]:{match:ze(ns),order:0,parse:i(p=>({lang:void 0,text:Ft(p[0].replace(/^ {4}/gm,"")).replace(Ql,"$1")}),"parse"),render:i((p,h,g)=>r("pre",{key:g.key},r("code",lt({},p.attrs,{className:p.lang?`lang-${p.lang}`:""}),p.text)),"render")},[L.codeFenced]:{match:ze(rs),order:0,parse:i(p=>({attrs:o("code",p[3]||""),lang:p[2]||void 0,text:p[4],type:L.codeBlock}),"parse")},[L.codeInline]:{match:Ke(xm),order:3,parse:i(p=>({text:p[2].replace(Ql,"$1")}),"parse"),render:i((p,h,g)=>r("code",{key:g.key},p.text),"render")},[L.footnote]:{match:ze(Sm),order:0,parse:i(p=>(c.push({footnote:p[2],identifier:p[1]}),{}),"parse"),render:Vr},[L.footnoteReference]:{match:We(wm),order:1,parse:i(p=>({target:`#${t.slugify(p[1],It)}`,text:p[1]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href")},r("sup",{key:g.key},p.text)),"render")},[L.gfmTask]:{match:We(Tm),order:1,parse:i(p=>({completed:p[1].toLowerCase()==="x"}),"parse"),render:i((p,h,g)=>r("input",{checked:p.completed,key:g.key,readOnly:!0,type:"checkbox"}),"render")},[L.heading]:{match:ze(t.enforceAtxHeadings?os:as),order:1,parse:i((p,h,g)=>({children:Zt(h,p[2],g),id:t.slugify(p[2],It),level:p[1].length}),"parse"),render:i((p,h,g)=>r(`h${p.level}`,{id:p.id,key:g.key},h(p.children,g)),"render")},[L.headingSetext]:{match:ze(ls),order:0,parse:i((p,h,g)=>({children:Zt(h,p[1],g),level:p[2]==="="?1:2,type:L.heading}),"parse")},[L.htmlBlock]:{match:Dt(sa),order:1,parse(p,h,g){let[,E]=p[3].match(Zm),y=new RegExp(`^${E}`,"gm"),v=p[3].replace(y,""),C=(w=v,uf.some(F=>F.test(w))?Es:Zt);var w;let k=p[1].toLowerCase(),I=pm.indexOf(k)!==-1,T=(I?k:p[1]).trim(),B={attrs:o(T,p[2]),noInnerParse:I,tag:T};return g.inAnchor=g.inAnchor||k==="a",I?B.text=p[3]:B.children=C(h,v,g),g.inAnchor=!1,B},render:i((p,h,g)=>r(p.tag,lt({key:g.key},p.attrs),p.text||(p.children?h(p.children,g):"")),"render")},[L.htmlSelfClosing]:{match:Dt(ca),order:1,parse(p){let h=p[1].trim();return{attrs:o(h,p[2]||""),tag:h}},render:i((p,h,g)=>r(p.tag,lt({},p.attrs,{key:g.key})),"render")},[L.htmlComment]:{match:Dt(is),order:1,parse:i(()=>({}),"parse"),render:Vr},[L.image]:{match:Ke(lf),order:1,parse:i(p=>({alt:p[1],target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("img",{key:g.key,alt:p.alt||void 0,title:p.title||void 0,src:t.sanitizer(p.target,"img","src")}),"render")},[L.link]:{match:We(of),order:3,parse:i((p,h,g)=>({children:ys(h,p[1],g),target:fa(p[2]),title:p[3]}),"parse"),render:i((p,h,g)=>r("a",{key:g.key,href:t.sanitizer(p.target,"a","href"),title:p.title},h(p.children,g)),"render")},[L.linkAngleBraceStyleDetector]:{match:We(Nm),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],type:L.link}),"parse")},[L.linkBareUrlDetector]:{match:yt((p,h)=>h.inAnchor||t.disableAutoLink?null:We(_m)(p,h)),order:0,parse:i(p=>({children:[{text:p[1],type:L.text}],target:p[1],title:void 0,type:L.link}),"parse")},[L.linkMailtoDetector]:{match:We(Rm),order:0,parse(p){let h=p[1],g=p[1];return hm.test(g)||(g="mailto:"+g),{children:[{text:h.replace("mailto:",""),type:L.text}],target:g,type:L.link}}},[L.orderedList]:da(r,1),[L.unorderedList]:da(r,2),[L.newlineCoalescer]:{match:ze(Am),order:3,parse:Hr,render:i(()=>`
`,"render")},[L.paragraph]:{match:yt(gs),order:3,parse:Vn,render:i((p,h,g)=>r("p",{key:g.key},h(p.children,g)),"render")},[L.ref]:{match:We(Lm),order:0,parse:i(p=>(l[p[1]]={target:p[2],title:p[4]},{}),"parse"),render:Vr},[L.refImage]:{match:Ke(Pm),order:0,parse:i(p=>({alt:p[1]||void 0,ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("img",{key:g.key,alt:p.alt,src:t.sanitizer(l[p.ref].target,"img","src"),title:l[p.ref].title}):null,"render")},[L.refLink]:{match:We(jm),order:0,parse:i((p,h,g)=>({children:h(p[1],g),fallbackChildren:p[0],ref:p[2]}),"parse"),render:i((p,h,g)=>l[p.ref]?r("a",{key:g.key,href:t.sanitizer(l[p.ref].target,"a","href"),title:l[p.ref].title},h(p.children,g)):r("span",{key:g.key},p.fallbackChildren),"render")},[L.table]:{match:ze(us),order:1,parse:hs,render(p,h,g){let E=p;return r("table",{key:g.key},r("thead",null,r("tr",null,E.header.map(function(y,v){return r("th",{key:v,style:ma(E,v)},h(y,g))}))),r("tbody",null,E.cells.map(function(y,v){return r("tr",{key:v},y.map(function(C,w){return r("td",{key:w,style:ma(E,w)},h(C,g))}))})))}},[L.text]:{match:Dt(Jm),order:4,parse:i(p=>({text:p[0].replace(Im,(h,g)=>t.namedCodesToUnicode[g]?t.namedCodesToUnicode[g]:h)}),"parse"),render:i(p=>p.text,"render")},[L.textBolded]:{match:Ke(qm),order:2,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("strong",{key:g.key},h(p.children,g)),"render")},[L.textEmphasized]:{match:Ke(Gm),order:3,parse:i((p,h,g)=>({children:h(p[2],g)}),"parse"),render:i((p,h,g)=>r("em",{key:g.key},h(p.children,g)),"render")},[L.textEscaped]:{match:Ke(Ym),order:1,parse:i(p=>({text:p[1],type:L.text}),"parse")},[L.textMarked]:{match:Ke(Wm),order:3,parse:Vn,render:i((p,h,g)=>r("mark",{key:g.key},h(p.children,g)),"render")},[L.textStrikethroughed]:{match:Ke(Km),order:3,parse:Vn,render:i((p,h,g)=>r("del",{key:g.key},h(p.children,g)),"render")}};t.disableParsingRawHTML===!0&&(delete u[L.htmlBlock],delete u[L.htmlSelfClosing]);let s=function(p){let h=Object.keys(p);function g(E,y){let v,C,w=[],k="",I="";for(y.prevCapture=y.prevCapture||"";E;){let T=0;for(;T<h.length;){if(k=h[T],v=p[k],y.inline&&!v.match.inline){T++;continue}let B=v.match(E,y);if(B){I=B[0],y.prevCapture+=I,E=E.substring(I.length),C=v.parse(B,g,y),C.type==null&&(C.type=k),w.push(C);break}T++}}return y.prevCapture="",w}return i(g,"n"),h.sort(function(E,y){let v=p[E].order,C=p[y].order;return v!==C?v-C:E<y?-1:1}),function(E,y){return g(function(v){return v.replace(Cm,`
`).replace(km,"").replace(Um,"    ")}(E),y)}}(u),d=(m=function(p,h){return function(g,E,y){let v=p[g.type].render;return h?h(()=>v(g,E,y),g,E,y):v(g,E,y)}}(u,t.renderRule),i(function p(h,g={}){if(Array.isArray(h)){let E=g.key,y=[],v=!1;for(let C=0;C<h.length;C++){g.key=C;let w=p(h[C],g),k=typeof w=="string";k&&v?y[y.length-1]+=w:w!==null&&y.push(w),v=k}return g.key=E,y}return m(h,p,g)},"e"));var m;let f=a(e);return c.length?r("div",null,f,r("footer",{key:"footer"},c.map(function(p){return r("div",{id:t.slugify(p.identifier,It),key:p.identifier},p.identifier,d(s(p.footnote,{inline:!0})))}))):f}i(xs,"Xe");var cf=i(e=>{let{children:t="",options:r}=e,a=function(o,c){if(o==null)return{};var l,u,s={},d=Object.keys(o);for(u=0;u<d.length;u++)c.indexOf(l=d[u])>=0||(s[l]=o[l]);return s}(e,dm);return ae(xs(t,r),a)},"default");je();var df=b.label(({theme:e})=>({lineHeight:"18px",alignItems:"center",marginBottom:8,display:"inline-block",position:"relative",whiteSpace:"nowrap",background:e.boolean.background,borderRadius:"3em",padding:1,'&[aria-disabled="true"]':{opacity:.5,input:{cursor:"not-allowed"}},input:{appearance:"none",width:"100%",height:"100%",position:"absolute",left:0,top:0,margin:0,padding:0,border:"none",background:"transparent",cursor:"pointer",borderRadius:"3em","&:focus":{outline:"none",boxShadow:`${e.color.secondary} 0 0 0 1px inset !important`}},span:{textAlign:"center",fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:"1",cursor:"pointer",display:"inline-block",padding:"7px 15px",transition:"all 100ms ease-out",userSelect:"none",borderRadius:"3em",color:W(.5,e.color.defaultText),background:"transparent","&:hover":{boxShadow:`${Xt(.3,e.appBorderColor)} 0 0 0 1px inset`},"&:active":{boxShadow:`${Xt(.05,e.appBorderColor)} 0 0 0 2px inset`,color:Xt(1,e.appBorderColor)},"&:first-of-type":{paddingRight:8},"&:last-of-type":{paddingLeft:8}},"input:checked ~ span:last-of-type, input:not(:checked) ~ span:first-of-type":{background:e.boolean.selectedBackground,boxShadow:e.base==="light"?`${Xt(.1,e.appBorderColor)} 0 0 2px`:`${e.appBorderColor} 0 0 0 1px`,color:e.color.defaultText,padding:"7px 15px"}})),pf=i(e=>e==="true","parse"),mf=i(({name:e,value:t,onChange:r,onBlur:a,onFocus:o,argType:c})=>{let l=$(()=>r(!1),[r]),u=!!c?.table?.readonly;if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:l,disabled:u},"Set boolean");let s=Te(e),d=typeof t=="string"?pf(t):t;return n.createElement(df,{"aria-disabled":u,htmlFor:s,"aria-label":e},n.createElement("input",{id:s,type:"checkbox",onChange:m=>r(m.target.checked),checked:d,role:"switch",disabled:u,name:e,onBlur:a,onFocus:o}),n.createElement("span",{"aria-hidden":"true"},"False"),n.createElement("span",{"aria-hidden":"true"},"True"))},"BooleanControl");je();var ff=i(e=>{let[t,r,a]=e.split("-"),o=new Date;return o.setFullYear(parseInt(t,10),parseInt(r,10)-1,parseInt(a,10)),o},"parseDate"),hf=i(e=>{let[t,r]=e.split(":"),a=new Date;return a.setHours(parseInt(t,10)),a.setMinutes(parseInt(r,10)),a},"parseTime"),gf=i(e=>{let t=new Date(e),r=`000${t.getFullYear()}`.slice(-4),a=`0${t.getMonth()+1}`.slice(-2),o=`0${t.getDate()}`.slice(-2);return`${r}-${a}-${o}`},"formatDate"),bf=i(e=>{let t=new Date(e),r=`0${t.getHours()}`.slice(-2),a=`0${t.getMinutes()}`.slice(-2);return`${r}:${a}`},"formatTime"),ei=b(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),yf=b.div(({theme:e})=>({flex:1,display:"flex",input:{marginLeft:10,flex:1,height:32,"&::-webkit-calendar-picker-indicator":{opacity:.5,height:12,filter:e.base==="light"?void 0:"invert(1)"}},"input:first-of-type":{marginLeft:0,flexGrow:4},"input:last-of-type":{flexGrow:3}})),Ef=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,argType:c})=>{let[l,u]=R(!0),s=X(),d=X(),m=!!c?.table?.readonly;j(()=>{l!==!1&&(s&&s.current&&(s.current.value=t?gf(t):""),d&&d.current&&(d.current.value=t?bf(t):""))},[t]);let f=i(g=>{if(!g.target.value)return r();let E=ff(g.target.value),y=new Date(t??"");y.setFullYear(E.getFullYear(),E.getMonth(),E.getDate());let v=y.getTime();v&&r(v),u(!!v)},"onDateChange"),p=i(g=>{if(!g.target.value)return r();let E=hf(g.target.value),y=new Date(t??"");y.setHours(E.getHours()),y.setMinutes(E.getMinutes());let v=y.getTime();v&&r(v),u(!!v)},"onTimeChange"),h=Te(e);return n.createElement(yf,null,n.createElement(ei,{type:"date",max:"9999-12-31",ref:s,id:`${h}-date`,name:`${h}-date`,readOnly:m,onChange:f,onFocus:a,onBlur:o}),n.createElement(ei,{type:"time",id:`${h}-time`,name:`${h}-time`,ref:d,onChange:p,readOnly:m,onFocus:a,onBlur:o}),l?null:n.createElement("div",null,"invalid"))},"DateControl");je();var vf=b.label({display:"flex"}),xf=i(e=>{let t=parseFloat(e);return Number.isNaN(t)?void 0:t},"parse"),Af=b(de.Input)(({readOnly:e})=>({opacity:e?.5:1})),Cf=i(({name:e,value:t,onChange:r,min:a,max:o,step:c,onBlur:l,onFocus:u,argType:s})=>{let[d,m]=R(typeof t=="number"?t:""),[f,p]=R(!1),[h,g]=R(null),E=!!s?.table?.readonly,y=$(w=>{m(w.target.value);let k=parseFloat(w.target.value);Number.isNaN(k)?g(new Error(`'${w.target.value}' is not a number`)):(r(k),g(null))},[r,g]),v=$(()=>{m("0"),r(0),p(!0)},[p]),C=X(null);return j(()=>{f&&C.current&&C.current.select()},[f]),j(()=>{let w=typeof t=="number"?t:"";d!==w&&m(w)},[t]),t===void 0?n.createElement(ge,{variant:"outline",size:"medium",id:or(e),onClick:v,disabled:E},"Set number"):n.createElement(vf,null,n.createElement(Af,{ref:C,id:Te(e),type:"number",onChange:y,size:"flex",placeholder:"Edit number...",value:d,valid:h?"error":void 0,autoFocus:f,readOnly:E,name:e,min:a,max:o,step:c,onFocus:u,onBlur:l}))},"NumberControl");je();var As=i((e,t)=>{let r=t&&Object.entries(t).find(([a,o])=>o===e);return r?r[0]:void 0},"selectedKey"),ha=i((e,t)=>e&&t?Object.entries(t).filter(r=>e.includes(r[1])).map(r=>r[0]):[],"selectedKeys"),Cs=i((e,t)=>e&&t&&e.map(r=>t[r]),"selectedValues"),Sf=b.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),wf=b.span({"[aria-readonly=true] &":{opacity:.5}}),kf=b.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ti=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Checkbox with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=ha(r||[],t),[u,s]=R(l),d=!!c?.table?.readonly,m=i(p=>{let h=p.target.value,g=[...u];g.includes(h)?g.splice(g.indexOf(h),1):g.push(h),a(Cs(g,t)),s(g)},"handleChange");j(()=>{s(ha(r||[],t))},[r]);let f=Te(e);return n.createElement(Sf,{"aria-readonly":d,isInline:o},Object.keys(t).map((p,h)=>{let g=`${f}-${h}`;return n.createElement(kf,{key:g,htmlFor:g},n.createElement("input",{type:"checkbox",disabled:d,id:g,name:g,value:p,onChange:m,checked:u?.includes(p)}),n.createElement(wf,null,p))}))},"CheckboxControl");je();var Of=b.div(({isInline:e})=>e?{display:"flex",flexWrap:"wrap",alignItems:"flex-start",label:{display:"inline-flex",marginRight:15}}:{label:{display:"flex"}},e=>{if(e["aria-readonly"]==="true")return{input:{cursor:"not-allowed"}}}),Tf=b.span({"[aria-readonly=true] &":{opacity:.5}}),If=b.label({lineHeight:"20px",alignItems:"center",marginBottom:8,"&:last-child":{marginBottom:0},input:{margin:0,marginRight:6}}),ri=i(({name:e,options:t,value:r,onChange:a,isInline:o,argType:c})=>{if(!t)return tt.warn(`Radio with no options: ${e}`),n.createElement(n.Fragment,null,"-");let l=As(r,t),u=Te(e),s=!!c?.table?.readonly;return n.createElement(Of,{"aria-readonly":s,isInline:o},Object.keys(t).map((d,m)=>{let f=`${u}-${m}`;return n.createElement(If,{key:f,htmlFor:f},n.createElement("input",{type:"radio",id:f,name:u,disabled:s,value:d,onChange:p=>a(t[p.currentTarget.value]),checked:d===l}),n.createElement(Tf,null,d))}))},"RadioControl");je();var Df={appearance:"none",border:"0 none",boxSizing:"inherit",display:" block",margin:" 0",background:"transparent",padding:0,fontSize:"inherit",position:"relative"},Ss=b.select(Df,({theme:e})=>({boxSizing:"border-box",position:"relative",padding:"6px 10px",width:"100%",color:e.input.color||"inherit",background:e.input.background,borderRadius:e.input.borderRadius,boxShadow:`${e.input.border} 0 0 0 1px inset`,fontSize:e.typography.size.s2-1,lineHeight:"20px","&:focus":{boxShadow:`${e.color.secondary} 0 0 0 1px inset`,outline:"none"},"&[disabled]":{cursor:"not-allowed",opacity:.5},"::placeholder":{color:e.textMutedColor},"&[multiple]":{overflow:"auto",padding:0,option:{display:"block",padding:"6px 10px",marginLeft:1,marginRight:1}}})),ws=b.span(({theme:e})=>({display:"inline-block",lineHeight:"normal",overflow:"hidden",position:"relative",verticalAlign:"top",width:"100%",svg:{position:"absolute",zIndex:1,pointerEvents:"none",height:"12px",marginTop:"-6px",right:"12px",top:"50%",fill:e.textMutedColor,path:{fill:e.textMutedColor}}})),ni="Choose option...",Bf=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{a(r[d.currentTarget.value])},"handleChange"),l=As(t,r)||ni,u=Te(e),s=!!o?.table?.readonly;return n.createElement(ws,null,n.createElement(gr,null),n.createElement(Ss,{disabled:s,id:u,value:l,onChange:c},n.createElement("option",{key:"no-selection",disabled:!0},ni),Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"SingleSelect"),_f=i(({name:e,value:t,options:r,onChange:a,argType:o})=>{let c=i(d=>{let m=Array.from(d.currentTarget.options).filter(f=>f.selected).map(f=>f.value);a(Cs(m,r))},"handleChange"),l=ha(t,r),u=Te(e),s=!!o?.table?.readonly;return n.createElement(ws,null,n.createElement(Ss,{disabled:s,id:u,multiple:!0,value:l,onChange:c},Object.keys(r).map(d=>n.createElement("option",{key:d,value:d},d))))},"MultiSelect"),ai=i(e=>{let{name:t,options:r}=e;return r?e.isMulti?n.createElement(_f,{...e}):n.createElement(Bf,{...e}):(tt.warn(`Select with no options: ${t}`),n.createElement(n.Fragment,null,"-"))},"SelectControl"),Rf=i((e,t)=>Array.isArray(e)?e.reduce((r,a)=>(r[t?.[a]||String(a)]=a,r),{}):e,"normalizeOptions"),Nf={check:ti,"inline-check":ti,radio:ri,"inline-radio":ri,select:ai,"multi-select":ai},Ot=i(e=>{let{type:t="select",labels:r,argType:a}=e,o={...e,argType:a,options:a?Rf(a.options,r):{},isInline:t.includes("inline"),isMulti:t.includes("multi")},c=Nf[t];if(c)return n.createElement(c,{...o});throw new Error(`Unknown options type: ${t}`)},"OptionsControl");an();je();var Ff="Error",Lf="Object",Pf="Array",jf="String",Mf="Number",Uf="Boolean",$f="Date",Hf="Null",Vf="Undefined",zf="Function",qf="Symbol",ks="ADD_DELTA_TYPE",Os="REMOVE_DELTA_TYPE",Ts="UPDATE_DELTA_TYPE",Ga="value",Gf="key";function it(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)&&typeof e[Symbol.iterator]=="function"?"Iterable":Object.prototype.toString.call(e).slice(8,-1)}i(it,"getObjectType");function Wa(e,t){let r=it(e),a=it(t);return(r==="Function"||a==="Function")&&a!==r}i(Wa,"isComponentWillChange");var Is=class extends Re{constructor(t){super(t),this.state={inputRefKey:null,inputRefValue:null},this.refInputValue=this.refInputValue.bind(this),this.refInputKey=this.refInputKey.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onSubmit=this.onSubmit.bind(this)}componentDidMount(){let{inputRefKey:t,inputRefValue:r}=this.state,{onlyValue:a}=this.props;t&&typeof t.focus=="function"&&t.focus(),a&&r&&typeof r.focus=="function"&&r.focus(),document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){if(t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat)return;let{inputRefKey:r,inputRefValue:a}=this.state,{addButtonElement:o,handleCancel:c}=this.props;[r,a,o].some(l=>l===t.target)&&((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.onSubmit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),c()))}onSubmit(){let{handleAdd:t,onlyValue:r,onSubmitValueParser:a,keyPath:o,deep:c}=this.props,{inputRefKey:l,inputRefValue:u}=this.state,s={};if(!r){if(!l.value)return;s.key=l.value}s.newValue=a(!1,o,c,s.key,u.value),t(s)}refInputKey(t){this.state.inputRefKey=t}refInputValue(t){this.state.inputRefValue=t}render(){let{handleCancel:t,onlyValue:r,addButtonElement:a,cancelButtonElement:o,inputElementGenerator:c,keyPath:l,deep:u}=this.props,s=a&&ae(a,{onClick:this.onSubmit}),d=o&&ae(o,{onClick:t}),m=c(Ga,l,u),f=ae(m,{placeholder:"Value",ref:this.refInputValue}),p=null;if(!r){let h=c(Gf,l,u);p=ae(h,{placeholder:"Key",ref:this.refInputKey})}return n.createElement("span",{className:"rejt-add-value-node"},p,f,d,s)}};i(Is,"JsonAddValue");var Ka=Is;Ka.defaultProps={onlyValue:!1,addButtonElement:n.createElement("button",null,"+"),cancelButtonElement:n.createElement("button",null,"c")};var Ds=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={data:t.data,name:t.name,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveItem=this.handleRemoveItem.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleRemoveItem(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c,nextDeep:l}=this.state,u=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,u).then(()=>{let s={keyPath:c,deep:l,key:t,oldValue:u,type:Os};o.splice(t,1),this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(s)}).catch(a.error)}}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:u}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:s,onDeltaUpdate:d}=this.props;s(o[o.length-1],a),d({type:ks,keyPath:o,deep:c,key:t,newValue:r})}).catch(u.error)}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:u,nextDeep:s}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,u,s,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(u[u.length-1],l),f({type:Ts,keyPath:u,deep:s,key:t,newValue:r,oldValue:d}),a(void 0)}).catch(o)})}renderCollapsed(){let{name:t,data:r,keyPath:a,deep:o}=this.state,{handleRemove:c,readOnly:l,getStyle:u,dataType:s,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=u(t,r,a,o,s),p=l(t,r,a,o,s),h=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"[...] ",r.length," ",r.length===1?"item":"items"),!p&&h)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,addFormVisible:c,nextDeep:l}=this.state,{isCollapsed:u,handleRemove:s,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:I,beforeUpdateAction:T,logger:B,onSubmitValueParser:F}=this.props,{minus:M,plus:H,delimiter:z,ul:Q,addForm:ne}=f(t,r,a,o,p),A=m(t,r,a,o,p),x=w&&ae(w,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:H}),S=C&&ae(C,{onClick:s,className:"rejt-minus-menu",style:M});return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"["),!c&&x,n.createElement("ul",{className:"rejt-not-collapsed-list",style:Q},r.map((_,O)=>n.createElement(un,{key:O,name:O.toString(),data:_,keyPath:a,deep:l,isCollapsed:u,handleRemove:this.handleRemoveItem(O),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:I,beforeUpdateAction:T,logger:B,onSubmitValueParser:F}))),!A&&c&&n.createElement("div",{className:"rejt-add-form",style:ne},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,onlyValue:!0,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:y,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:z},"]"),!A&&S)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{dataType:l,getStyle:u}=this.props,s=r?this.renderCollapsed():this.renderNotCollapsed(),d=u(t,a,o,c,l);return n.createElement("div",{className:"rejt-array-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),s)}};i(Ds,"JsonArray");var Bs=Ds;Bs.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var _s=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:u,dataType:s}=this.props,d=u(a,o,c,l,s);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){let{inputRef:r}=this.state;t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||r!==t.target||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:u,deep:s}=this.state;if(!l)return;let d=o(!0,c,s,u,l.value),m={value:d,key:u};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:u,readOnly:s,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,textareaElementGenerator:h,minusMenuElement:g,keyPath:E}=this.props,y=m(t,u,o,c,d),v=null,C=null,w=s(t,u,o,c,d);if(a&&!w){let k=h(Ga,E,c,t,u,d),I=f&&ae(f,{onClick:this.handleEdit}),T=p&&ae(p,{onClick:this.handleCancelEdit}),B=ae(k,{ref:this.refInput,defaultValue:u});v=n.createElement("span",{className:"rejt-edit-form",style:y.editForm},B," ",T,I),C=null}else{v=n.createElement("span",{className:"rejt-value",style:y.value,onClick:w?void 0:this.handleEditMode},r);let k=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:y.minus});C=w?null:k}return n.createElement("li",{className:"rejt-function-value-node",style:y.li},n.createElement("span",{className:"rejt-name",style:y.name},t," :"," "),v,C)}};i(_s,"JsonFunctionValue");var Rs=_s;Rs.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>{},"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};var Ns=class extends Re{constructor(t){super(t),this.state={data:t.data,name:t.name,keyPath:t.keyPath,deep:t.deep}}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}render(){let{data:t,name:r,keyPath:a,deep:o}=this.state,{isCollapsed:c,handleRemove:l,handleUpdateValue:u,onUpdate:s,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:I,logger:T,onSubmitValueParser:B}=this.props,F=i(()=>!0,"readOnlyTrue"),M=it(t);switch(M){case Ff:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:F,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:I,logger:T,onSubmitValueParser:B});case Lf:return n.createElement(ga,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:m,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:I,logger:T,onSubmitValueParser:B});case Pf:return n.createElement(Bs,{data:t,name:r,isCollapsed:c,keyPath:a,deep:o,handleRemove:l,onUpdate:s,onDeltaUpdate:d,readOnly:m,dataType:M,getStyle:f,addButtonElement:p,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,textareaElementGenerator:y,minusMenuElement:v,plusMenuElement:C,beforeRemoveAction:w,beforeAddAction:k,beforeUpdateAction:I,logger:T,onSubmitValueParser:B});case jf:return n.createElement(nt,{name:r,value:`"${t}"`,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case Mf:return n.createElement(nt,{name:r,value:t,originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case Uf:return n.createElement(nt,{name:r,value:t?"true":"false",originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case $f:return n.createElement(nt,{name:r,value:t.toISOString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:F,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case Hf:return n.createElement(nt,{name:r,value:"null",originalValue:"null",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case Vf:return n.createElement(nt,{name:r,value:"undefined",originalValue:"undefined",keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});case zf:return n.createElement(Rs,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:m,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,textareaElementGenerator:y,minusMenuElement:v,logger:T,onSubmitValueParser:B});case qf:return n.createElement(nt,{name:r,value:t.toString(),originalValue:t,keyPath:a,deep:o,handleRemove:l,handleUpdateValue:u,readOnly:F,dataType:M,getStyle:f,cancelButtonElement:h,editButtonElement:g,inputElementGenerator:E,minusMenuElement:v,logger:T,onSubmitValueParser:B});default:return null}}};i(Ns,"JsonNode");var un=Ns;un.defaultProps={keyPath:[],deep:0};var Fs=class extends Re{constructor(t){super(t);let r=t.deep===-1?[]:[...t.keyPath||[],t.name];this.state={name:t.name,data:t.data,keyPath:r,deep:t.deep??0,nextDeep:(t.deep??0)+1,collapsed:t.isCollapsed(r,t.deep??0,t.data),addFormVisible:!1},this.handleCollapseMode=this.handleCollapseMode.bind(this),this.handleRemoveValue=this.handleRemoveValue.bind(this),this.handleAddMode=this.handleAddMode.bind(this),this.handleAddValueAdd=this.handleAddValueAdd.bind(this),this.handleAddValueCancel=this.handleAddValueCancel.bind(this),this.handleEditValue=this.handleEditValue.bind(this),this.onChildUpdate=this.onChildUpdate.bind(this),this.renderCollapsed=this.renderCollapsed.bind(this),this.renderNotCollapsed=this.renderNotCollapsed.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data?{data:t.data}:null}onChildUpdate(t,r){let{data:a,keyPath:o=[]}=this.state;a[t]=r,this.setState({data:a});let{onUpdate:c}=this.props,l=o.length;c(o[l-1],a)}handleAddMode(){this.setState({addFormVisible:!0})}handleAddValueCancel(){this.setState({addFormVisible:!1})}handleAddValueAdd({key:t,newValue:r}){let{data:a,keyPath:o=[],nextDeep:c}=this.state,{beforeAddAction:l,logger:u}=this.props;(l||Promise.resolve.bind(Promise))(t,o,c,r).then(()=>{a[t]=r,this.setState({data:a}),this.handleAddValueCancel();let{onUpdate:s,onDeltaUpdate:d}=this.props;s(o[o.length-1],a),d({type:ks,keyPath:o,deep:c,key:t,newValue:r})}).catch(u.error)}handleRemoveValue(t){return()=>{let{beforeRemoveAction:r,logger:a}=this.props,{data:o,keyPath:c=[],nextDeep:l}=this.state,u=o[t];(r||Promise.resolve.bind(Promise))(t,c,l,u).then(()=>{let s={keyPath:c,deep:l,key:t,oldValue:u,type:Os};delete o[t],this.setState({data:o});let{onUpdate:d,onDeltaUpdate:m}=this.props;d(c[c.length-1],o),m(s)}).catch(a.error)}}handleCollapseMode(){this.setState(t=>({collapsed:!t.collapsed}))}handleEditValue({key:t,value:r}){return new Promise((a,o)=>{let{beforeUpdateAction:c}=this.props,{data:l,keyPath:u=[],nextDeep:s}=this.state,d=l[t];(c||Promise.resolve.bind(Promise))(t,u,s,d,r).then(()=>{l[t]=r,this.setState({data:l});let{onUpdate:m,onDeltaUpdate:f}=this.props;m(u[u.length-1],l),f({type:Ts,keyPath:u,deep:s,key:t,newValue:r,oldValue:d}),a()}).catch(o)})}renderCollapsed(){let{name:t,keyPath:r,deep:a,data:o}=this.state,{handleRemove:c,readOnly:l,dataType:u,getStyle:s,minusMenuElement:d}=this.props,{minus:m,collapsed:f}=s(t,o,r,a,u),p=Object.getOwnPropertyNames(o),h=l(t,o,r,a,u),g=d&&ae(d,{onClick:c,className:"rejt-minus-menu",style:m});return n.createElement("span",{className:"rejt-collapsed"},n.createElement("span",{className:"rejt-collapsed-text",style:f,onClick:this.handleCollapseMode},"{...}"," ",p.length," ",p.length===1?"key":"keys"),!h&&g)}renderNotCollapsed(){let{name:t,data:r,keyPath:a,deep:o,nextDeep:c,addFormVisible:l}=this.state,{isCollapsed:u,handleRemove:s,onDeltaUpdate:d,readOnly:m,getStyle:f,dataType:p,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:I,beforeUpdateAction:T,logger:B,onSubmitValueParser:F}=this.props,{minus:M,plus:H,addForm:z,ul:Q,delimiter:ne}=f(t,r,a,o,p),A=Object.getOwnPropertyNames(r),x=m(t,r,a,o,p),S=w&&ae(w,{onClick:this.handleAddMode,className:"rejt-plus-menu",style:H}),_=C&&ae(C,{onClick:s,className:"rejt-minus-menu",style:M}),O=A.map(D=>n.createElement(un,{key:D,name:D,data:r[D],keyPath:a,deep:c,isCollapsed:u,handleRemove:this.handleRemoveValue(D),handleUpdateValue:this.handleEditValue,onUpdate:this.onChildUpdate,onDeltaUpdate:d,readOnly:m,getStyle:f,addButtonElement:h,cancelButtonElement:g,editButtonElement:E,inputElementGenerator:y,textareaElementGenerator:v,minusMenuElement:C,plusMenuElement:w,beforeRemoveAction:k,beforeAddAction:I,beforeUpdateAction:T,logger:B,onSubmitValueParser:F}));return n.createElement("span",{className:"rejt-not-collapsed"},n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:ne},"{"),!x&&S,n.createElement("ul",{className:"rejt-not-collapsed-list",style:Q},O),!x&&l&&n.createElement("div",{className:"rejt-add-form",style:z},n.createElement(Ka,{handleAdd:this.handleAddValueAdd,handleCancel:this.handleAddValueCancel,addButtonElement:h,cancelButtonElement:g,inputElementGenerator:y,keyPath:a,deep:o,onSubmitValueParser:F})),n.createElement("span",{className:"rejt-not-collapsed-delimiter",style:ne},"}"),!x&&_)}render(){let{name:t,collapsed:r,data:a,keyPath:o,deep:c}=this.state,{getStyle:l,dataType:u}=this.props,s=r?this.renderCollapsed():this.renderNotCollapsed(),d=l(t,a,o,c,u);return n.createElement("div",{className:"rejt-object-node"},n.createElement("span",{onClick:this.handleCollapseMode},n.createElement("span",{className:"rejt-name",style:d.name},t," :"," ")),s)}};i(Fs,"JsonObject");var ga=Fs;ga.defaultProps={keyPath:[],deep:0,minusMenuElement:n.createElement("span",null," - "),plusMenuElement:n.createElement("span",null," + ")};var Ls=class extends Re{constructor(t){super(t);let r=[...t.keyPath||[],t.name];this.state={value:t.value,name:t.name,keyPath:r,deep:t.deep,editEnabled:!1,inputRef:null},this.handleEditMode=this.handleEditMode.bind(this),this.refInput=this.refInput.bind(this),this.handleCancelEdit=this.handleCancelEdit.bind(this),this.handleEdit=this.handleEdit.bind(this),this.onKeydown=this.onKeydown.bind(this)}static getDerivedStateFromProps(t,r){return t.value!==r.value?{value:t.value}:null}componentDidUpdate(){let{editEnabled:t,inputRef:r,name:a,value:o,keyPath:c,deep:l}=this.state,{readOnly:u,dataType:s}=this.props,d=u(a,o,c,l,s);t&&!d&&typeof r.focus=="function"&&r.focus()}componentDidMount(){document.addEventListener("keydown",this.onKeydown)}componentWillUnmount(){document.removeEventListener("keydown",this.onKeydown)}onKeydown(t){let{inputRef:r}=this.state;t.altKey||t.ctrlKey||t.metaKey||t.shiftKey||t.repeat||r!==t.target||((t.code==="Enter"||t.key==="Enter")&&(t.preventDefault(),this.handleEdit()),(t.code==="Escape"||t.key==="Escape")&&(t.preventDefault(),this.handleCancelEdit()))}handleEdit(){let{handleUpdateValue:t,originalValue:r,logger:a,onSubmitValueParser:o,keyPath:c}=this.props,{inputRef:l,name:u,deep:s}=this.state;if(!l)return;let d=o(!0,c,s,u,l.value),m={value:d,key:u};(t||Promise.resolve.bind(Promise))(m).then(()=>{Wa(r,d)||this.handleCancelEdit()}).catch(a.error)}handleEditMode(){this.setState({editEnabled:!0})}refInput(t){this.state.inputRef=t}handleCancelEdit(){this.setState({editEnabled:!1})}render(){let{name:t,value:r,editEnabled:a,keyPath:o,deep:c}=this.state,{handleRemove:l,originalValue:u,readOnly:s,dataType:d,getStyle:m,editButtonElement:f,cancelButtonElement:p,inputElementGenerator:h,minusMenuElement:g,keyPath:E}=this.props,y=m(t,u,o,c,d),v=s(t,u,o,c,d),C=a&&!v,w=h(Ga,E,c,t,u,d),k=f&&ae(f,{onClick:this.handleEdit}),I=p&&ae(p,{onClick:this.handleCancelEdit}),T=ae(w,{ref:this.refInput,defaultValue:JSON.stringify(u)}),B=g&&ae(g,{onClick:l,className:"rejt-minus-menu",style:y.minus});return n.createElement("li",{className:"rejt-value-node",style:y.li},n.createElement("span",{className:"rejt-name",style:y.name},t," : "),C?n.createElement("span",{className:"rejt-edit-form",style:y.editForm},T," ",I,k):n.createElement("span",{className:"rejt-value",style:y.value,onClick:v?void 0:this.handleEditMode},String(r)),!v&&!C&&B)}};i(Ls,"JsonValue");var nt=Ls;nt.defaultProps={keyPath:[],deep:0,handleUpdateValue:i(()=>Promise.resolve(),"handleUpdateValue"),editButtonElement:n.createElement("button",null,"e"),cancelButtonElement:n.createElement("button",null,"c"),minusMenuElement:n.createElement("span",null," - ")};function Ps(e){let t=e;if(t.indexOf("function")===0)return(0,eval)(`(${t})`);try{t=JSON.parse(e)}catch{}return t}i(Ps,"parse");var Wf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Kf={minus:{color:"red"},plus:{color:"green"},collapsed:{color:"grey"},delimiter:{},ul:{padding:"0px",margin:"0 0 0 25px",listStyle:"none"},name:{color:"#2287CD"},addForm:{}},Yf={minus:{color:"red"},editForm:{},value:{color:"#7bba3d"},li:{minHeight:"22px",lineHeight:"22px",outline:"0px"},name:{color:"#2287CD"}},js=class extends Re{constructor(t){super(t),this.state={data:t.data,rootName:t.rootName},this.onUpdate=this.onUpdate.bind(this),this.removeRoot=this.removeRoot.bind(this)}static getDerivedStateFromProps(t,r){return t.data!==r.data||t.rootName!==r.rootName?{data:t.data,rootName:t.rootName}:null}onUpdate(t,r){this.setState({data:r}),this.props.onFullyUpdate?.(r)}removeRoot(){this.onUpdate(null,null)}render(){let{data:t,rootName:r}=this.state,{isCollapsed:a,onDeltaUpdate:o,readOnly:c,getStyle:l,addButtonElement:u,cancelButtonElement:s,editButtonElement:d,inputElement:m,textareaElement:f,minusMenuElement:p,plusMenuElement:h,beforeRemoveAction:g,beforeAddAction:E,beforeUpdateAction:y,logger:v,onSubmitValueParser:C,fallback:w=null}=this.props,k=it(t),I=c;it(c)==="Boolean"&&(I=i(()=>c,"readOnlyFunction"));let T=m;m&&it(m)!=="Function"&&(T=i(()=>m,"inputElementFunction"));let B=f;return f&&it(f)!=="Function"&&(B=i(()=>f,"textareaElementFunction")),k==="Object"||k==="Array"?n.createElement("div",{className:"rejt-tree"},n.createElement(un,{data:t,name:r||"root",deep:-1,isCollapsed:a??(()=>!1),onUpdate:this.onUpdate,onDeltaUpdate:o??(()=>{}),readOnly:I,getStyle:l??(()=>({})),addButtonElement:u,cancelButtonElement:s,editButtonElement:d,inputElementGenerator:T,textareaElementGenerator:B,minusMenuElement:p,plusMenuElement:h,handleRemove:this.removeRoot,beforeRemoveAction:g,beforeAddAction:E,beforeUpdateAction:y,logger:v??{},onSubmitValueParser:C??(F=>F)})):w}};i(js,"JsonTree");var Ms=js;Ms.defaultProps={rootName:"root",isCollapsed:i((e,t)=>t!==-1,"isCollapsed"),getStyle:i((e,t,r,a,o)=>{switch(o){case"Object":case"Error":return Wf;case"Array":return Kf;default:return Yf}},"getStyle"),readOnly:i(()=>!1,"readOnly"),onFullyUpdate:i(()=>{},"onFullyUpdate"),onDeltaUpdate:i(()=>{},"onDeltaUpdate"),beforeRemoveAction:i(()=>Promise.resolve(),"beforeRemoveAction"),beforeAddAction:i(()=>Promise.resolve(),"beforeAddAction"),beforeUpdateAction:i(()=>Promise.resolve(),"beforeUpdateAction"),logger:{error:i(()=>{},"error")},onSubmitValueParser:i((e,t,r,a,o)=>Ps(o),"onSubmitValueParser"),inputElement:i(()=>n.createElement("input",null),"inputElement"),textareaElement:i(()=>n.createElement("textarea",null),"textareaElement"),fallback:null};var{window:Jf}=globalThis,Xf=b.div(({theme:e})=>({position:"relative",display:"flex",'&[aria-readonly="true"]':{opacity:.5},".rejt-tree":{marginLeft:"1rem",fontSize:"13px"},".rejt-value-node, .rejt-object-node > .rejt-collapsed, .rejt-array-node > .rejt-collapsed, .rejt-object-node > .rejt-not-collapsed, .rejt-array-node > .rejt-not-collapsed":{"& > svg":{opacity:0,transition:"opacity 0.2s"}},".rejt-value-node:hover, .rejt-object-node:hover > .rejt-collapsed, .rejt-array-node:hover > .rejt-collapsed, .rejt-object-node:hover > .rejt-not-collapsed, .rejt-array-node:hover > .rejt-not-collapsed":{"& > svg":{opacity:1}},".rejt-edit-form button":{display:"none"},".rejt-add-form":{marginLeft:10},".rejt-add-value-node":{display:"inline-flex",alignItems:"center"},".rejt-name":{lineHeight:"22px"},".rejt-not-collapsed-delimiter":{lineHeight:"22px"},".rejt-plus-menu":{marginLeft:5},".rejt-object-node > span > *, .rejt-array-node > span > *":{position:"relative",zIndex:2},".rejt-object-node, .rejt-array-node":{position:"relative"},".rejt-object-node > span:first-of-type::after, .rejt-array-node > span:first-of-type::after, .rejt-collapsed::before, .rejt-not-collapsed::before":{content:'""',position:"absolute",top:0,display:"block",width:"100%",marginLeft:"-1rem",padding:"0 4px 0 1rem",height:22},".rejt-collapsed::before, .rejt-not-collapsed::before":{zIndex:1,background:"transparent",borderRadius:4,transition:"background 0.2s",pointerEvents:"none",opacity:.1},".rejt-object-node:hover, .rejt-array-node:hover":{"& > .rejt-collapsed::before, & > .rejt-not-collapsed::before":{background:e.color.secondary}},".rejt-collapsed::after, .rejt-not-collapsed::after":{content:'""',position:"absolute",display:"inline-block",pointerEvents:"none",width:0,height:0},".rejt-collapsed::after":{left:-8,top:8,borderTop:"3px solid transparent",borderBottom:"3px solid transparent",borderLeft:"3px solid rgba(153,153,153,0.6)"},".rejt-not-collapsed::after":{left:-10,top:10,borderTop:"3px solid rgba(153,153,153,0.6)",borderLeft:"3px solid transparent",borderRight:"3px solid transparent"},".rejt-value":{display:"inline-block",border:"1px solid transparent",borderRadius:4,margin:"1px 0",padding:"0 4px",cursor:"text",color:e.color.defaultText},".rejt-value-node:hover > .rejt-value":{background:e.color.lighter,borderColor:e.appBorderColor}})),zn=b.button(({theme:e,primary:t})=>({border:0,height:20,margin:1,borderRadius:4,background:t?e.color.secondary:"transparent",color:t?e.color.lightest:e.color.dark,fontWeight:t?"bold":"normal",cursor:"pointer",order:t?"initial":9})),Zf=b(fr)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.ancillary},"svg + &":{marginLeft:0}})),Qf=b(Lo)(({theme:e,disabled:t})=>({display:"inline-block",verticalAlign:"middle",width:15,height:15,padding:3,marginLeft:5,cursor:t?"not-allowed":"pointer",color:e.textMutedColor,"&:hover":t?{}:{color:e.color.negative},"svg + &":{marginLeft:0}})),oi=b.input(({theme:e,placeholder:t})=>({outline:0,margin:t?1:"1px 0",padding:"3px 4px",color:e.color.defaultText,background:e.background.app,border:`1px solid ${e.appBorderColor}`,borderRadius:4,lineHeight:"14px",width:t==="Key"?80:120,"&:focus":{border:`1px solid ${e.color.secondary}`}})),eh=b(G)(({theme:e})=>({position:"absolute",zIndex:2,top:2,right:2,height:21,padding:"0 3px",background:e.background.bar,border:`1px solid ${e.appBorderColor}`,borderRadius:3,color:e.textMutedColor,fontSize:"9px",fontWeight:"bold",textDecoration:"none",span:{marginLeft:3,marginTop:1}})),th=b(de.Textarea)(({theme:e})=>({flex:1,padding:"7px 6px",fontFamily:e.typography.fonts.mono,fontSize:"12px",lineHeight:"18px","&::placeholder":{fontFamily:e.typography.fonts.base,fontSize:"13px"},"&:placeholder-shown":{padding:"7px 10px"}})),rh={bubbles:!0,cancelable:!0,key:"Enter",code:"Enter",keyCode:13},nh=i(e=>{e.currentTarget.dispatchEvent(new Jf.KeyboardEvent("keydown",rh))},"dispatchEnterKey"),ah=i(e=>{e.currentTarget.select()},"selectValue"),oh=i(e=>()=>({name:{color:e.color.secondary},collapsed:{color:e.color.dark},ul:{listStyle:"none",margin:"0 0 0 1rem",padding:0},li:{outline:0}}),"getCustomStyleFunction"),li=i(({name:e,value:t,onChange:r,argType:a})=>{let o=ye(),c=ce(()=>t&&ou(t),[t]),l=c!=null,[u,s]=R(!l),[d,m]=R(null),f=!!a?.table?.readonly,p=$(w=>{try{w&&r(JSON.parse(w)),m(null)}catch(k){m(k)}},[r]),[h,g]=R(!1),E=$(()=>{r({}),g(!0)},[g]),y=X(null);if(j(()=>{h&&y.current&&y.current.select()},[h]),!l)return n.createElement(ge,{disabled:f,id:or(e),onClick:E},"Set object");let v=n.createElement(th,{ref:y,id:Te(e),name:e,defaultValue:t===null?"":JSON.stringify(t,null,2),onBlur:w=>p(w.target.value),placeholder:"Edit JSON string...",autoFocus:h,valid:d?"error":void 0,readOnly:f}),C=Array.isArray(t)||typeof t=="object"&&t?.constructor===Object;return n.createElement(Xf,{"aria-readonly":f},C&&n.createElement(eh,{onClick:w=>{w.preventDefault(),s(k=>!k)}},u?n.createElement(Eo,null):n.createElement(vo,null),n.createElement("span",null,"RAW")),u?v:n.createElement(Ms,{readOnly:f||!C,isCollapsed:C?void 0:()=>!0,data:c,rootName:e,onFullyUpdate:r,getStyle:oh(o),cancelButtonElement:n.createElement(zn,{type:"button"},"Cancel"),editButtonElement:n.createElement(zn,{type:"submit"},"Save"),addButtonElement:n.createElement(zn,{type:"submit",primary:!0},"Save"),plusMenuElement:n.createElement(Zf,null),minusMenuElement:n.createElement(Qf,null),inputElement:(w,k,I,T)=>T?n.createElement(oi,{onFocus:ah,onBlur:nh}):n.createElement(oi,null),fallback:v}))},"ObjectControl");je();var lh=b.input(({theme:e,min:t,max:r,value:a,disabled:o})=>({"&":{width:"100%",backgroundColor:"transparent",appearance:"none"},"&::-webkit-slider-runnable-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer"},"&::-webkit-slider-thumb":{marginTop:"-6px",width:16,height:16,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Le(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grab",appearance:"none",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:o?"not-allowed":"grab"}},"&:focus":{outline:"none","&::-webkit-slider-runnable-track":{borderColor:Le(e.color.secondary,.4)},"&::-webkit-slider-thumb":{borderColor:e.color.secondary,boxShadow:`0 0px 5px 0px ${e.color.secondary}`}},"&::-moz-range-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,borderRadius:6,width:"100%",height:6,cursor:o?"not-allowed":"pointer",outline:"none"},"&::-moz-range-thumb":{width:16,height:16,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:"50px",boxShadow:`0 1px 3px 0px ${Le(e.appBorderColor,.2)}`,cursor:o?"not-allowed":"grap",background:`${e.input.background}`,transition:"all 150ms ease-out","&:hover":{background:`${Ge(.05,e.input.background)}`,transform:"scale3d(1.1, 1.1, 1.1) translateY(-1px)",transition:"all 50ms ease-out"},"&:active":{background:`${e.input.background}`,transform:"scale3d(1, 1, 1) translateY(0px)",cursor:"grabbing"}},"&::-ms-track":{background:e.base==="light"?`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${Ge(.02,e.input.background)} 100%)`:`linear-gradient(to right, 
            ${e.color.green} 0%, ${e.color.green} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} ${(a-t)/(r-t)*100}%, 
            ${ht(.02,e.input.background)} 100%)`,boxShadow:`${e.appBorderColor} 0 0 0 1px inset`,color:"transparent",width:"100%",height:"6px",cursor:"pointer"},"&::-ms-fill-lower":{borderRadius:6},"&::-ms-fill-upper":{borderRadius:6},"&::-ms-thumb":{width:16,height:16,background:`${e.input.background}`,border:`1px solid ${Le(e.appBorderColor,.2)}`,borderRadius:50,cursor:"grab",marginTop:0},"@supports (-ms-ime-align:auto)":{"input[type=range]":{margin:"0"}}})),Us=b.span({paddingLeft:5,paddingRight:5,fontSize:12,whiteSpace:"nowrap",fontFeatureSettings:"tnum",fontVariantNumeric:"tabular-nums","[aria-readonly=true] &":{opacity:.5}}),ih=b(Us)(({numberOFDecimalsPlaces:e,max:t})=>({width:`${e+t.toString().length*2+3}ch`,textAlign:"right",flexShrink:0})),uh=b.div({display:"flex",alignItems:"center",width:"100%"});function $s(e){let t=e.toString().match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return t?Math.max(0,(t[1]?t[1].length:0)-(t[2]?+t[2]:0)):0}i($s,"getNumberOfDecimalPlaces");var sh=i(({name:e,value:t,onChange:r,min:a=0,max:o=100,step:c=1,onBlur:l,onFocus:u,argType:s})=>{let d=i(h=>{r(xf(h.target.value))},"handleChange"),m=t!==void 0,f=ce(()=>$s(c),[c]),p=!!s?.table?.readonly;return n.createElement(uh,{"aria-readonly":p},n.createElement(Us,null,a),n.createElement(lh,{id:Te(e),type:"range",disabled:p,onChange:d,name:e,min:a,max:o,step:c,onFocus:u,onBlur:l,value:t??a}),n.createElement(ih,{numberOFDecimalsPlaces:f,max:o},m?t.toFixed(f):"--"," / ",o))},"RangeControl");je();var ch=b.label({display:"flex"}),dh=b.div(({isMaxed:e})=>({marginLeft:"0.75rem",paddingTop:"0.35rem",color:e?"red":void 0})),ph=i(({name:e,value:t,onChange:r,onFocus:a,onBlur:o,maxLength:c,argType:l})=>{let u=i(h=>{r(h.target.value)},"handleChange"),s=!!l?.table?.readonly,[d,m]=R(!1),f=$(()=>{r(""),m(!0)},[m]);if(t===void 0)return n.createElement(ge,{variant:"outline",size:"medium",disabled:s,id:or(e),onClick:f},"Set string");let p=typeof t=="string";return n.createElement(ch,null,n.createElement(de.Textarea,{id:Te(e),maxLength:c,onChange:u,disabled:s,size:"flex",placeholder:"Edit string...",autoFocus:d,valid:p?void 0:"error",name:e,value:p?t:"",onFocus:a,onBlur:o}),c&&n.createElement(dh,{isMaxed:t?.length===c},t?.length??0," / ",c))},"TextControl");je();var mh=b(de.Input)({padding:10});function Hs(e){e.forEach(t=>{t.startsWith("blob:")&&URL.revokeObjectURL(t)})}i(Hs,"revokeOldUrls");var fh=i(({onChange:e,name:t,accept:r="image/*",value:a,argType:o})=>{let c=X(null),l=o?.control?.readOnly;function u(s){if(!s.target.files)return;let d=Array.from(s.target.files).map(m=>URL.createObjectURL(m));e(d),Hs(a||[])}return i(u,"handleFileChange"),j(()=>{a==null&&c.current&&(c.current.value="")},[a,t]),n.createElement(mh,{ref:c,id:Te(t),type:"file",name:t,multiple:!0,disabled:l,onChange:u,accept:r,size:"flex"})},"FilesControl"),hh=mo(()=>Promise.resolve().then(()=>(pp(),Cu))),gh=i(e=>n.createElement(co,{fallback:n.createElement("div",null)},n.createElement(hh,{...e})),"ColorControl"),bh={array:li,object:li,boolean:mf,color:gh,date:Ef,number:Cf,check:Ot,"inline-check":Ot,radio:Ot,"inline-radio":Ot,select:Ot,"multi-select":Ot,range:sh,text:ph,file:fh},ii=i(()=>n.createElement(n.Fragment,null,"-"),"NoControl"),yh=i(({row:e,arg:t,updateArgs:r,isHovered:a})=>{let{key:o,control:c}=e,[l,u]=R(!1),[s,d]=R({value:t});j(()=>{l||d({value:t})},[l,t]);let m=$(E=>(d({value:E}),r({[o]:E}),E),[r,o]),f=$(()=>u(!1),[]),p=$(()=>u(!0),[]);if(!c||c.disable){let E=c?.disable!==!0&&e?.type?.name!=="function";return a&&E?n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},"Setup controls"):n.createElement(ii,null)}let h={name:o,argType:e,value:s.value,onChange:m,onBlur:f,onFocus:p},g=bh[c.type]||ii;return n.createElement(g,{...h,...c,controlType:c.type})},"ArgControl"),Eh=b.table(({theme:e})=>({"&&":{borderCollapse:"collapse",borderSpacing:0,border:"none",tr:{border:"none !important",background:"none"},"td, th":{padding:0,border:"none",width:"auto!important"},marginTop:0,marginBottom:0,"th:first-of-type, td:first-of-type":{paddingLeft:0},"th:last-of-type, td:last-of-type":{paddingRight:0},td:{paddingTop:0,paddingBottom:4,"&:not(:first-of-type)":{paddingLeft:10,paddingRight:0}},tbody:{boxShadow:"none",border:"none"},code:Xe({theme:e}),div:{span:{fontWeight:"bold"}},"& code":{margin:0,display:"inline-block",fontSize:e.typography.size.s1}}})),vh=i(({tags:e})=>{let t=(e.params||[]).filter(c=>c.description),r=t.length!==0,a=e.deprecated!=null,o=e.returns!=null&&e.returns.description!=null;return!r&&!o&&!a?null:n.createElement(n.Fragment,null,n.createElement(Eh,null,n.createElement("tbody",null,a&&n.createElement("tr",{key:"deprecated"},n.createElement("td",{colSpan:2},n.createElement("strong",null,"Deprecated"),": ",e.deprecated?.toString())),r&&t.map(c=>n.createElement("tr",{key:c.name},n.createElement("td",null,n.createElement("code",null,c.name)),n.createElement("td",null,c.description))),o&&n.createElement("tr",{key:"returns"},n.createElement("td",null,n.createElement("code",null,"Returns")),n.createElement("td",null,e.returns?.description)))))},"ArgJsDoc");an();var xh=Se(mp()),ba=8,ui=b.div(({isExpanded:e})=>({display:"flex",flexDirection:e?"column":"row",flexWrap:"wrap",alignItems:"flex-start",marginBottom:"-4px",minWidth:100})),Ah=b.span(Xe,({theme:e,simple:t=!1})=>({flex:"0 0 auto",fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,wordBreak:"break-word",whiteSpace:"normal",maxWidth:"100%",margin:0,marginRight:"4px",marginBottom:"4px",paddingTop:"2px",paddingBottom:"2px",lineHeight:"13px",...t&&{background:"transparent",border:"0 none",paddingLeft:0}})),Ch=b.button(({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,marginBottom:"4px",background:"none",border:"none"})),Sh=b.div(Xe,({theme:e})=>({fontFamily:e.typography.fonts.mono,color:e.color.secondary,fontSize:e.typography.size.s1,margin:0,whiteSpace:"nowrap",display:"flex",alignItems:"center"})),wh=b.div(({theme:e,width:t})=>({width:t,minWidth:200,maxWidth:800,padding:15,fontFamily:e.typography.fonts.mono,fontSize:e.typography.size.s1,boxSizing:"content-box","& code":{padding:"0 !important"}})),kh=b(yo)({marginLeft:4}),Oh=b(gr)({marginLeft:4}),Th=i(()=>n.createElement("span",null,"-"),"EmptyArg"),Vs=i(({text:e,simple:t})=>n.createElement(Ah,{simple:t},e),"ArgText"),Ih=(0,xh.default)(1e3)(e=>{let t=e.split(/\r?\n/);return`${Math.max(...t.map(r=>r.length))}ch`}),Dh=i(e=>{if(!e)return[e];let t=e.split("|").map(r=>r.trim());return iu(t)},"getSummaryItems"),si=i((e,t=!0)=>{let r=e;return t||(r=e.slice(0,ba)),r.map(a=>n.createElement(Vs,{key:a,text:a===""?'""':a}))},"renderSummaryItems"),Bh=i(({value:e,initialExpandedArgs:t})=>{let{summary:r,detail:a}=e,[o,c]=R(!1),[l,u]=R(t||!1);if(r==null)return null;let s=typeof r.toString=="function"?r.toString():r;if(a==null){if(/[(){}[\]<>]/.test(s))return n.createElement(Vs,{text:s});let d=Dh(s),m=d.length;return m>ba?n.createElement(ui,{isExpanded:l},si(d,l),n.createElement(Ch,{onClick:()=>u(!l)},l?"Show less...":`Show ${m-ba} more...`)):n.createElement(ui,null,si(d))}return n.createElement(yn,{closeOnOutsideClick:!0,placement:"bottom",visible:o,onVisibleChange:d=>{c(d)},tooltip:n.createElement(wh,{width:Ih(a)},n.createElement(Vt,{language:"jsx",format:!1},a))},n.createElement(Sh,{className:"sbdocs-expandable"},n.createElement("span",null,s),o?n.createElement(kh,null):n.createElement(Oh,null)))},"ArgSummary"),qn=i(({value:e,initialExpandedArgs:t})=>e==null?n.createElement(Th,null):n.createElement(Bh,{value:e,initialExpandedArgs:t}),"ArgValue"),_h=b.span({fontWeight:"bold"}),Rh=b.span(({theme:e})=>({color:e.color.negative,fontFamily:e.typography.fonts.mono,cursor:"help"})),Nh=b.div(({theme:e})=>({"&&":{p:{margin:"0 0 10px 0"},a:{color:e.color.secondary}},code:{...Xe({theme:e}),fontSize:12,fontFamily:e.typography.fonts.mono},"& code":{margin:0,display:"inline-block"},"& pre > code":{whiteSpace:"pre-wrap"}})),Fh=b.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?W(.1,e.color.defaultText):W(.2,e.color.defaultText),marginTop:t?4:0})),Lh=b.div(({theme:e,hasDescription:t})=>({color:e.base==="light"?W(.1,e.color.defaultText):W(.2,e.color.defaultText),marginTop:t?12:0,marginBottom:12})),Ph=b.td(({expandable:e})=>({paddingLeft:e?"40px !important":"20px !important"})),jh=i(e=>e&&{summary:typeof e=="string"?e:e.name},"toSummary"),Nr=i(e=>{let[t,r]=R(!1),{row:a,updateArgs:o,compact:c,expandable:l,initialExpandedArgs:u}=e,{name:s,description:d}=a,m=a.table||{},f=m.type||jh(a.type),p=m.defaultValue||a.defaultValue,h=a.type?.required,g=d!=null&&d!=="";return n.createElement("tr",{onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},n.createElement(Ph,{expandable:l??!1},n.createElement(_h,null,s),h?n.createElement(Rh,{title:"Required"},"*"):null),c?null:n.createElement("td",null,g&&n.createElement(Nh,null,n.createElement(cf,null,d)),m.jsDocTags!=null?n.createElement(n.Fragment,null,n.createElement(Lh,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:u})),n.createElement(vh,{tags:m.jsDocTags})):n.createElement(Fh,{hasDescription:g},n.createElement(qn,{value:f,initialExpandedArgs:u}))),c?null:n.createElement("td",null,n.createElement(qn,{value:p,initialExpandedArgs:u})),o?n.createElement("td",null,n.createElement(yh,{...e,isHovered:t})):null)},"ArgRow"),Mh=b.div(({inAddonPanel:e,theme:t})=>({height:e?"100%":"auto",display:"flex",border:e?"none":`1px solid ${t.appBorderColor}`,borderRadius:e?0:t.appBorderRadius,padding:e?0:40,alignItems:"center",justifyContent:"center",flexDirection:"column",gap:15,background:t.background.content})),Uh=b.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),$h=i(({inAddonPanel:e})=>{let[t,r]=R(!0);return j(()=>{let a=setTimeout(()=>{r(!1)},100);return()=>clearTimeout(a)},[]),t?null:n.createElement(Mh,{inAddonPanel:e},n.createElement(Ht,{title:e?"Interactive story playground":"Args table with interactive controls couldn't be auto-generated",description:n.createElement(n.Fragment,null,"Controls give you an easy to use interface to test your components. Set your story args and you'll see controls appearing here automatically."),footer:n.createElement(Uh,null,e&&n.createElement(n.Fragment,null,n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs")),!e&&n.createElement(be,{href:"https://storybook.js.org/docs/essentials/controls",target:"_blank",withArrow:!0},n.createElement(dt,null)," Learn how to set that up"))}))},"Empty"),Hh=b(go)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?W(.25,e.color.defaultText):W(.3,e.color.defaultText),border:"none",display:"inline-block"})),Vh=b(bo)(({theme:e})=>({marginRight:8,marginLeft:-10,marginTop:-2,height:12,width:12,color:e.base==="light"?W(.25,e.color.defaultText):W(.3,e.color.defaultText),border:"none",display:"inline-block"})),zh=b.span(({theme:e})=>({display:"flex",lineHeight:"20px",alignItems:"center"})),qh=b.td(({theme:e})=>({position:"relative",letterSpacing:"0.35em",textTransform:"uppercase",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s1-1,color:e.base==="light"?W(.4,e.color.defaultText):W(.6,e.color.defaultText),background:`${e.background.app} !important`,"& ~ td":{background:`${e.background.app} !important`}})),Gh=b.td(({theme:e})=>({position:"relative",fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,background:e.background.app})),Wh=b.td({position:"relative"}),Kh=b.tr(({theme:e})=>({"&:hover > td":{backgroundColor:`${ht(.005,e.background.app)} !important`,boxShadow:`${e.color.mediumlight} 0 - 1px 0 0 inset`,cursor:"row-resize"}})),ci=b.button({background:"none",border:"none",padding:"0",font:"inherit",position:"absolute",top:0,bottom:0,left:0,right:0,height:"100%",width:"100%",color:"transparent",cursor:"row-resize !important"}),Gn=i(({level:e="section",label:t,children:r,initialExpanded:a=!0,colSpan:o=3})=>{let[c,l]=R(a),u=e==="subsection"?Gh:qh,s=r?.length||0,d=e==="subsection"?`${s} item${s!==1?"s":""}`:"",m=`${c?"Hide":"Show"} ${e==="subsection"?s:t} item${s!==1?"s":""}`;return n.createElement(n.Fragment,null,n.createElement(Kh,{title:m},n.createElement(u,{colSpan:1},n.createElement(ci,{onClick:f=>l(!c),tabIndex:0},m),n.createElement(zh,null,c?n.createElement(Hh,null):n.createElement(Vh,null),t)),n.createElement(Wh,{colSpan:o-1},n.createElement(ci,{onClick:f=>l(!c),tabIndex:-1,style:{outline:"none"}},m),c?null:d)),c?r:null)},"SectionRow"),Yh=b.div(({theme:e})=>({width:"100%",borderSpacing:0,color:e.color.defaultText})),Fr=b.div(({theme:e})=>({display:"flex",borderBottom:`1px solid ${e.appBorderColor}`,"&:last-child":{borderBottom:0}})),me=b.div(({position:e,theme:t})=>{let r={display:"flex",flexDirection:"column",gap:5,padding:"10px 15px",alignItems:"flex-start"};switch(e){case"first":return{...r,width:"25%",paddingLeft:20};case"second":return{...r,width:"35%"};case"third":return{...r,width:"15%"};case"last":return{...r,width:"25%",paddingRight:20}}}),ue=b.div(({theme:e,width:t,height:r})=>({animation:`${e.animation.glow} 1.5s ease-in-out infinite`,background:e.appBorderColor,width:t||"100%",height:r||16,borderRadius:3})),Jh=i(()=>n.createElement(Yh,null,n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"}))),n.createElement(Fr,null,n.createElement(me,{position:"first"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"second"},n.createElement(ue,{width:"80%"}),n.createElement(ue,{width:"30%"})),n.createElement(me,{position:"third"},n.createElement(ue,{width:"60%"})),n.createElement(me,{position:"last"},n.createElement(ue,{width:"60%"})))),"Skeleton"),Xh=b.table(({theme:e,compact:t,inAddonPanel:r})=>({"&&":{borderSpacing:0,color:e.color.defaultText,"td, th":{padding:0,border:"none",verticalAlign:"top",textOverflow:"ellipsis"},fontSize:e.typography.size.s2-1,lineHeight:"20px",textAlign:"left",width:"100%",marginTop:r?0:25,marginBottom:r?0:40,"thead th:first-of-type, td:first-of-type":{width:"25%"},"th:first-of-type, td:first-of-type":{paddingLeft:20},"th:nth-of-type(2), td:nth-of-type(2)":{...t?null:{width:"35%"}},"td:nth-of-type(3)":{...t?null:{width:"15%"}},"th:last-of-type, td:last-of-type":{paddingRight:20,...t?null:{width:"25%"}},th:{color:e.base==="light"?W(.25,e.color.defaultText):W(.45,e.color.defaultText),paddingTop:10,paddingBottom:10,paddingLeft:15,paddingRight:15},td:{paddingTop:"10px",paddingBottom:"10px","&:not(:first-of-type)":{paddingLeft:15,paddingRight:15},"&:last-of-type":{paddingRight:20}},marginLeft:r?0:1,marginRight:r?0:1,tbody:{...r?null:{filter:e.base==="light"?"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.10))":"drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.20))"},"> tr > *":{background:e.background.content,borderTop:`1px solid ${e.appBorderColor}`},...r?null:{"> tr:first-of-type > *":{borderBlockStart:`1px solid ${e.appBorderColor}`},"> tr:last-of-type > *":{borderBlockEnd:`1px solid ${e.appBorderColor}`},"> tr > *:first-of-type":{borderInlineStart:`1px solid ${e.appBorderColor}`},"> tr > *:last-of-type":{borderInlineEnd:`1px solid ${e.appBorderColor}`},"> tr:first-of-type > td:first-of-type":{borderTopLeftRadius:e.appBorderRadius},"> tr:first-of-type > td:last-of-type":{borderTopRightRadius:e.appBorderRadius},"> tr:last-of-type > td:first-of-type":{borderBottomLeftRadius:e.appBorderRadius},"> tr:last-of-type > td:last-of-type":{borderBottomRightRadius:e.appBorderRadius}}}}})),Zh=b(G)(({theme:e})=>({margin:"-4px -12px -4px 0"})),Qh=b.span({display:"flex",justifyContent:"space-between"}),e2={alpha:i((e,t)=>(e.name??"").localeCompare(t.name??""),"alpha"),requiredFirst:i((e,t)=>+!!t.type?.required-+!!e.type?.required||(e.name??"").localeCompare(t.name??""),"requiredFirst"),none:null},t2=i((e,t)=>{let r={ungrouped:[],ungroupedSubsections:{},sections:{}};if(!e)return r;Object.entries(e).forEach(([c,l])=>{let{category:u,subcategory:s}=l?.table||{};if(u){let d=r.sections[u]||{ungrouped:[],subsections:{}};if(!s)d.ungrouped.push({key:c,...l});else{let m=d.subsections[s]||[];m.push({key:c,...l}),d.subsections[s]=m}r.sections[u]=d}else if(s){let d=r.ungroupedSubsections[s]||[];d.push({key:c,...l}),r.ungroupedSubsections[s]=d}else r.ungrouped.push({key:c,...l})});let a=e2[t],o=i(c=>a?Object.keys(c).reduce((l,u)=>({...l,[u]:c[u].sort(a)}),{}):c,"sortSubsection");return{ungrouped:a?r.ungrouped.sort(a):r.ungrouped,ungroupedSubsections:o(r.ungroupedSubsections),sections:Object.keys(r.sections).reduce((c,l)=>({...c,[l]:{ungrouped:a?r.sections[l].ungrouped.sort(a):r.sections[l].ungrouped,subsections:o(r.sections[l].subsections)}}),{})}},"groupRows"),r2=i((e,t,r)=>{try{return ul(e,t,r)}catch(a){return nl.warn(a.message),!1}},"safeIncludeConditionalArg"),n2=i(e=>{let{updateArgs:t,resetArgs:r,compact:a,inAddonPanel:o,initialExpandedArgs:c,sort:l="none",isLoading:u}=e;if("error"in e){let{error:C}=e;return n.createElement(Qu,null,C,"\xA0",n.createElement(be,{href:"http://storybook.js.org/docs/",target:"_blank",withArrow:!0},n.createElement(dt,null)," Read the docs"))}if(u)return n.createElement(Jh,null);let{rows:s,args:d,globals:m}="rows"in e?e:{rows:void 0,args:void 0,globals:void 0},f=t2(yu(s||{},C=>!C?.table?.disable&&r2(C,d||{},m||{})),l),p=f.ungrouped.length===0,h=Object.entries(f.sections).length===0,g=Object.entries(f.ungroupedSubsections).length===0;if(p&&h&&g)return n.createElement($h,{inAddonPanel:o});let E=1;t&&(E+=1),a||(E+=2);let y=Object.keys(f.sections).length>0,v={updateArgs:t,compact:a,inAddonPanel:o,initialExpandedArgs:c};return n.createElement(hn,null,n.createElement(Xh,{compact:a,inAddonPanel:o,className:"docblock-argstable sb-unstyled"},n.createElement("thead",{className:"docblock-argstable-head"},n.createElement("tr",null,n.createElement("th",null,n.createElement("span",null,"Name")),a?null:n.createElement("th",null,n.createElement("span",null,"Description")),a?null:n.createElement("th",null,n.createElement("span",null,"Default")),t?n.createElement("th",null,n.createElement(Qh,null,"Control"," ",!u&&r&&n.createElement(Zh,{onClick:()=>r(),title:"Reset controls"},n.createElement(Er,{"aria-hidden":!0})))):null)),n.createElement("tbody",{className:"docblock-argstable-body"},f.ungrouped.map(C=>n.createElement(Nr,{key:C.key,row:C,arg:d&&d[C.key],...v})),Object.entries(f.ungroupedSubsections).map(([C,w])=>n.createElement(Gn,{key:C,label:C,level:"subsection",colSpan:E},w.map(k=>n.createElement(Nr,{key:k.key,row:k,arg:d&&d[k.key],expandable:y,...v})))),Object.entries(f.sections).map(([C,w])=>n.createElement(Gn,{key:C,label:C,level:"section",colSpan:E},w.ungrouped.map(k=>n.createElement(Nr,{key:k.key,row:k,arg:d&&d[k.key],...v})),Object.entries(w.subsections).map(([k,I])=>n.createElement(Gn,{key:k,label:k,level:"subsection",colSpan:E},I.map(T=>n.createElement(Nr,{key:T.key,row:T,arg:d&&d[T.key],expandable:y,...v})))))))))},"ArgsTable"),ya="addon-controls",zs="controls",a2=xn({from:{transform:"translateY(40px)"},to:{transform:"translateY(0)"}}),o2=xn({from:{background:"var(--highlight-bg-color)"},to:{}}),l2=b.div({containerType:"size",position:"sticky",bottom:0,height:39,overflow:"hidden",zIndex:1}),i2=b($t)(({theme:e})=>({"--highlight-bg-color":e.base==="dark"?"#153B5B":"#E0F0FF",display:"flex",flexDirection:"row-reverse",alignItems:"center",justifyContent:"space-between",flexWrap:"wrap",gap:6,padding:"6px 10px",animation:`${a2} 300ms, ${o2} 2s`,background:e.background.bar,borderTop:`1px solid ${e.appBorderColor}`,fontSize:e.typography.size.s2,"@container (max-width: 799px)":{flexDirection:"row",justifyContent:"flex-end"}})),u2=b.div({display:"flex",flex:"99 0 auto",alignItems:"center",marginLeft:10,gap:6}),s2=b.div(({theme:e})=>({display:"flex",flex:"1 0 0",alignItems:"center",gap:2,color:e.color.mediumdark,fontSize:e.typography.size.s2})),Wn=b.div({"@container (max-width: 799px)":{lineHeight:0,textIndent:"-9999px","&::after":{content:"attr(data-short-label)",display:"block",lineHeight:"initial",textIndent:"0"}}}),c2=b(de.Input)(({theme:e})=>({"::placeholder":{color:e.color.mediumdark},"&:invalid:not(:placeholder-shown)":{boxShadow:`${e.color.negative} 0 0 0 1px inset`}})),d2=i(({saveStory:e,createStory:t,resetArgs:r,portalSelector:a})=>{let o=n.useRef(null),[c,l]=n.useState(!1),[u,s]=n.useState(!1),[d,m]=n.useState(""),[f,p]=n.useState(null),h=i(async()=>{c||(l(!0),await e().catch(()=>{}),l(!1))},"onSaveStory"),g=i(()=>{s(!0),m(""),setTimeout(()=>o.current?.focus(),0)},"onShowForm"),E=i(y=>{let v=y.target.value.replace(/^[^a-z]/i,"").replace(/[^a-z0-9-_ ]/gi,"").replaceAll(/([-_ ]+[a-z0-9])/gi,C=>C.toUpperCase().replace(/[-_ ]/g,""));m(v.charAt(0).toUpperCase()+v.slice(1))},"onChange");return n.createElement(l2,{id:"save-from-controls"},n.createElement(i2,null,n.createElement(s2,null,n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Save changes to story"})},n.createElement(G,{"aria-label":"Save changes to story",disabled:c,onClick:h},n.createElement(hr,null),n.createElement(Wn,{"data-short-label":"Save"},"Update story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Create new story with these settings"})},n.createElement(G,{"aria-label":"Create new story with these settings",onClick:g},n.createElement(fr,null),n.createElement(Wn,{"data-short-label":"New"},"Create new story"))),n.createElement(oe,{as:"div",hasChrome:!1,trigger:"hover",tooltip:n.createElement(Ne,{note:"Reset changes"})},n.createElement(G,{"aria-label":"Reset changes",onClick:()=>r()},n.createElement(Er,null),n.createElement("span",null,"Reset")))),n.createElement(u2,null,n.createElement(Wn,{"data-short-label":"Unsaved changes"},"You modified this story. Do you want to save your changes?")),n.createElement(He,{width:350,open:u,onOpenChange:s,portalSelector:a},n.createElement(de,{onSubmit:i(async y=>{if(y.preventDefault(),!c)try{p(null),l(!0),await t(d.replace(/^[^a-z]/i,"").replaceAll(/[^a-z0-9]/gi,"")),s(!1),l(!1)}catch(v){p(v.message),l(!1)}},"onSubmitForm"),id:"create-new-story-form"},n.createElement(He.Content,null,n.createElement(He.Header,null,n.createElement(He.Title,null,"Create new story"),n.createElement(He.Description,null,"This will add a new story to your existing stories file.")),n.createElement(c2,{onChange:E,placeholder:"Story export name",readOnly:c,ref:o,value:d}),n.createElement(He.Actions,null,n.createElement(ge,{disabled:c||!d,size:"medium",type:"submit",variant:"solid"},"Create"),n.createElement(He.Dialog.Close,{asChild:!0},n.createElement(ge,{disabled:c,size:"medium",type:"reset"},"Cancel"))))),f&&n.createElement(He.Error,null,f))))},"SaveStory"),di=i(e=>Object.entries(e).reduce((t,[r,a])=>a!==void 0?Object.assign(t,{[r]:a}):t,{}),"clean"),p2=b.div({display:"grid",gridTemplateRows:"1fr 39px",height:"100%",maxHeight:"100vh",overflowY:"auto"}),m2=i(({saveStory:e,createStory:t})=>{let[r,a]=R(!0),[o,c,l,u]=Jo(),[s]=Ve(),d=xr(),{expanded:m,sort:f,presetColors:p,disableSaveFromUI:h=!1}=et(zs,{}),{path:g,previewInitialized:E}=Xo();j(()=>{E&&a(!1)},[E]);let y=Object.values(d).some(w=>w?.control),v=Object.entries(d).reduce((w,[k,I])=>{let T=I?.control;return typeof T!="object"||T?.type!=="color"||T?.presetColors?w[k]=I:w[k]={...I,control:{...T,presetColors:p}},w},{}),C=ce(()=>!!o&&!!u&&!ut(di(o),di(u)),[o,u]);return n.createElement(p2,null,n.createElement(n2,{key:g,compact:!m&&y,rows:v,args:o,globals:s,updateArgs:c,resetArgs:l,inAddonPanel:!0,sort:f,isLoading:r}),y&&C&&Qe.CONFIG_TYPE==="DEVELOPMENT"&&h!==!0&&n.createElement(d2,{resetArgs:l,saveStory:e,createStory:t}))},"ControlsPanel");function qs(){let e=ve().getSelectedPanel(),t=xr(),r=Object.values(t).filter(a=>a?.control&&!a?.table?.disable).length;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Controls"),r===0?null:n.createElement(ct,{compact:!0,status:e===ya?"active":"neutral"},r))}i(qs,"Title");var pi=i(e=>JSON.stringify(e,(t,r)=>typeof r=="function"?"__sb_empty_function_arg__":r),"stringifyArgs"),Nw=Z.register(ya,e=>{if(globalThis?.FEATURES?.controls){let t=Z.getChannel(),r=i(async()=>{let o=e.getCurrentStoryData();if(o.type!=="story")throw new Error("Not a story");try{let c=await Cn(t,Sn,Cr,{args:pi(Object.entries(o.args||{}).reduce((l,[u,s])=>(ut(s,o.initialArgs?.[u])||(l[u]=s),l),{})),csfId:o.id,importPath:o.importPath});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story saved",subHeadline:n.createElement(n.Fragment,null,"Updated story ",n.createElement("b",null,c.sourceStoryName),".")},duration:8e3})}catch(c){throw e.addNotification({id:"save-story-error",icon:n.createElement(xo,{color:vr.negative}),content:{headline:"Failed to save story",subHeadline:c?.message||"Check the Storybook process on the command line for more details."},duration:8e3}),c}},"saveStory"),a=i(async o=>{let c=e.getCurrentStoryData();if(c.type!=="story")throw new Error("Not a story");let l=await Cn(t,Sn,Cr,{args:c.args&&pi(c.args),csfId:c.id,importPath:c.importPath,name:o});e.addNotification({id:"save-story-success",icon:n.createElement(vn,{color:vr.positive}),content:{headline:"Story created",subHeadline:n.createElement(n.Fragment,null,"Added story ",n.createElement("b",null,l.newStoryName)," based on ",n.createElement("b",null,l.sourceStoryName),".")},duration:8e3,onClick:i(({onDismiss:u})=>{u(),e.selectStory(l.newStoryId)},"onClick")})},"createStory");Z.add(ya,{title:qs,type:Ee.PANEL,paramKey:zs,render:i(({active:o})=>!o||!e.getCurrentStoryData()?null:n.createElement(Ut,{active:o},n.createElement(m2,{saveStory:r,createStory:a})),"render")}),t.on(Cr,o=>{if(!o.success)return;let c=e.getCurrentStoryData();c.type==="story"&&(e.resetStoryArgs(c),o.payload.newStoryId&&e.selectStory(o.payload.newStoryId))})}}),f2="actions",lr="storybook/actions",Gs=`${lr}/panel`,Ea=`${lr}/action-event`,Ws=`${lr}/action-clear`;function Ks(){let e=ve().getSelectedPanel(),[{count:t},r]=St(lr,{count:0});return Ar({[Ea]:()=>{r(a=>({...a,count:a.count+1}))},[qt]:()=>{r(a=>({...a,count:0}))},[Ws]:()=>{r(a=>({...a,count:0}))}}),n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Actions"),t===0?null:n.createElement(ct,{compact:!0,status:e===Gs?"active":"neutral"},t))}i(Ks,"Title");var h2=Object.create,Ya=Object.defineProperty,g2=Object.getOwnPropertyDescriptor,Ys=Object.getOwnPropertyNames,b2=Object.getPrototypeOf,y2=Object.prototype.hasOwnProperty,Ja=i((e,t)=>i(function(){return t||(0,e[Ys(e)[0]])((t={exports:{}}).exports,t),t.exports},"__require"),"__commonJS"),E2=i((e,t)=>{for(var r in t)Ya(e,r,{get:t[r],enumerable:!0})},"__export"),v2=i((e,t,r,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Ys(t))!y2.call(e,o)&&o!==r&&Ya(e,o,{get:i(()=>t[o],"get"),enumerable:!(a=g2(t,o))||a.enumerable});return e},"__copyProps"),x2=i((e,t,r)=>(r=e!=null?h2(b2(e)):{},v2(t||!e||!e.__esModule?Ya(r,"default",{value:e,enumerable:!0}):r,e)),"__toESM"),A2=Ja({"node_modules/is-object/index.js"(e,t){"use strict";t.exports=i(function(r){return typeof r=="object"&&r!==null},"isObject")}}),C2=Ja({"node_modules/is-window/index.js"(e,t){"use strict";t.exports=function(r){if(r==null)return!1;var a=Object(r);return a===a.window}}}),S2=Ja({"node_modules/is-dom/index.js"(e,t){var r=A2(),a=C2();function o(c){return!r(c)||!a(window)||typeof window.Node!="function"?!1:typeof c.nodeType=="number"&&typeof c.nodeName=="string"}i(o,"isNode"),t.exports=o}}),Xr={};E2(Xr,{chromeDark:i(()=>w2,"chromeDark"),chromeLight:i(()=>k2,"chromeLight")});var w2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"rgb(36, 36, 36)",BASE_COLOR:"rgb(213, 213, 213)",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(227, 110, 236)",OBJECT_VALUE_NULL_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(127, 127, 127)",OBJECT_VALUE_REGEXP_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_STRING_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(233, 63, 59)",OBJECT_VALUE_NUMBER_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_BOOLEAN_COLOR:"hsl(252, 100%, 75%)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(85, 106, 242)",HTML_TAG_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_COLOR:"rgb(93, 176, 215)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(155, 187, 220)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(242, 151, 102)",HTML_COMMENT_COLOR:"rgb(137, 137, 137)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"rgb(145, 145, 145)",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"rgb(85, 85, 85)",TABLE_TH_BACKGROUND_COLOR:"rgb(44, 44, 44)",TABLE_TH_HOVER_COLOR:"rgb(48, 48, 48)",TABLE_SORT_ICON_COLOR:"black",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(rgba(255, 255, 255, 0), rgba(255, 255, 255, 0) 50%, rgba(51, 139, 255, 0.0980392) 50%, rgba(51, 139, 255, 0.0980392))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},k2={BASE_FONT_FAMILY:"Menlo, monospace",BASE_FONT_SIZE:"11px",BASE_LINE_HEIGHT:1.2,BASE_BACKGROUND_COLOR:"white",BASE_COLOR:"black",OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES:10,OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES:5,OBJECT_NAME_COLOR:"rgb(136, 19, 145)",OBJECT_VALUE_NULL_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_UNDEFINED_COLOR:"rgb(128, 128, 128)",OBJECT_VALUE_REGEXP_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_STRING_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_SYMBOL_COLOR:"rgb(196, 26, 22)",OBJECT_VALUE_NUMBER_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_BOOLEAN_COLOR:"rgb(28, 0, 207)",OBJECT_VALUE_FUNCTION_PREFIX_COLOR:"rgb(13, 34, 170)",HTML_TAG_COLOR:"rgb(168, 148, 166)",HTML_TAGNAME_COLOR:"rgb(136, 18, 128)",HTML_TAGNAME_TEXT_TRANSFORM:"lowercase",HTML_ATTRIBUTE_NAME_COLOR:"rgb(153, 69, 0)",HTML_ATTRIBUTE_VALUE_COLOR:"rgb(26, 26, 166)",HTML_COMMENT_COLOR:"rgb(35, 110, 37)",HTML_DOCTYPE_COLOR:"rgb(192, 192, 192)",ARROW_COLOR:"#6e6e6e",ARROW_MARGIN_RIGHT:3,ARROW_FONT_SIZE:12,ARROW_ANIMATION_DURATION:"0",TREENODE_FONT_FAMILY:"Menlo, monospace",TREENODE_FONT_SIZE:"11px",TREENODE_LINE_HEIGHT:1.2,TREENODE_PADDING_LEFT:12,TABLE_BORDER_COLOR:"#aaa",TABLE_TH_BACKGROUND_COLOR:"#eee",TABLE_TH_HOVER_COLOR:"hsla(0, 0%, 90%, 1)",TABLE_SORT_ICON_COLOR:"#6e6e6e",TABLE_DATA_BACKGROUND_IMAGE:"linear-gradient(to bottom, white, white 50%, rgb(234, 243, 255) 50%, rgb(234, 243, 255))",TABLE_DATA_BACKGROUND_SIZE:"128px 32px"},Js=Ct([{},()=>{}]),Kn={WebkitTouchCallout:"none",WebkitUserSelect:"none",KhtmlUserSelect:"none",MozUserSelect:"none",msUserSelect:"none",OUserSelect:"none",userSelect:"none"},qr=i(e=>({DOMNodePreview:{htmlOpenTag:{base:{color:e.HTML_TAG_COLOR},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM},htmlAttributeName:{color:e.HTML_ATTRIBUTE_NAME_COLOR},htmlAttributeValue:{color:e.HTML_ATTRIBUTE_VALUE_COLOR}},htmlCloseTag:{base:{color:e.HTML_TAG_COLOR},offsetLeft:{marginLeft:-e.TREENODE_PADDING_LEFT},tagName:{color:e.HTML_TAGNAME_COLOR,textTransform:e.HTML_TAGNAME_TEXT_TRANSFORM}},htmlComment:{color:e.HTML_COMMENT_COLOR},htmlDoctype:{color:e.HTML_DOCTYPE_COLOR}},ObjectPreview:{objectDescription:{fontStyle:"italic"},preview:{fontStyle:"italic"},arrayMaxProperties:e.OBJECT_PREVIEW_ARRAY_MAX_PROPERTIES,objectMaxProperties:e.OBJECT_PREVIEW_OBJECT_MAX_PROPERTIES},ObjectName:{base:{color:e.OBJECT_NAME_COLOR},dimmed:{opacity:.6}},ObjectValue:{objectValueNull:{color:e.OBJECT_VALUE_NULL_COLOR},objectValueUndefined:{color:e.OBJECT_VALUE_UNDEFINED_COLOR},objectValueRegExp:{color:e.OBJECT_VALUE_REGEXP_COLOR},objectValueString:{color:e.OBJECT_VALUE_STRING_COLOR},objectValueSymbol:{color:e.OBJECT_VALUE_SYMBOL_COLOR},objectValueNumber:{color:e.OBJECT_VALUE_NUMBER_COLOR},objectValueBoolean:{color:e.OBJECT_VALUE_BOOLEAN_COLOR},objectValueFunctionPrefix:{color:e.OBJECT_VALUE_FUNCTION_PREFIX_COLOR,fontStyle:"italic"},objectValueFunctionName:{fontStyle:"italic"}},TreeView:{treeViewOutline:{padding:0,margin:0,listStyleType:"none"}},TreeNode:{treeNodeBase:{color:e.BASE_COLOR,backgroundColor:e.BASE_BACKGROUND_COLOR,lineHeight:e.TREENODE_LINE_HEIGHT,cursor:"default",boxSizing:"border-box",listStyle:"none",fontFamily:e.TREENODE_FONT_FAMILY,fontSize:e.TREENODE_FONT_SIZE},treeNodePreviewContainer:{},treeNodePlaceholder:{whiteSpace:"pre",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...Kn},treeNodeArrow:{base:{color:e.ARROW_COLOR,display:"inline-block",fontSize:e.ARROW_FONT_SIZE,marginRight:e.ARROW_MARGIN_RIGHT,...parseFloat(e.ARROW_ANIMATION_DURATION)>0?{transition:`transform ${e.ARROW_ANIMATION_DURATION} ease 0s`}:{},...Kn},expanded:{WebkitTransform:"rotateZ(90deg)",MozTransform:"rotateZ(90deg)",transform:"rotateZ(90deg)"},collapsed:{WebkitTransform:"rotateZ(0deg)",MozTransform:"rotateZ(0deg)",transform:"rotateZ(0deg)"}},treeNodeChildNodesContainer:{margin:0,paddingLeft:e.TREENODE_PADDING_LEFT}},TableInspector:{base:{color:e.BASE_COLOR,position:"relative",border:`1px solid ${e.TABLE_BORDER_COLOR}`,fontFamily:e.BASE_FONT_FAMILY,fontSize:e.BASE_FONT_SIZE,lineHeight:"120%",boxSizing:"border-box",cursor:"default"}},TableInspectorHeaderContainer:{base:{top:0,height:"17px",left:0,right:0,overflowX:"hidden"},table:{tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",height:"100%",width:"100%",margin:0}},TableInspectorDataContainer:{tr:{display:"table-row"},td:{boxSizing:"border-box",border:"none",height:"16px",verticalAlign:"top",padding:"1px 4px",WebkitUserSelect:"text",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px"},div:{position:"static",top:"17px",bottom:0,overflowY:"overlay",transform:"translateZ(0)",left:0,right:0,overflowX:"hidden"},table:{positon:"static",left:0,top:0,right:0,bottom:0,borderTop:"0 none transparent",margin:0,backgroundImage:e.TABLE_DATA_BACKGROUND_IMAGE,backgroundSize:e.TABLE_DATA_BACKGROUND_SIZE,tableLayout:"fixed",borderSpacing:0,borderCollapse:"separate",width:"100%",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorTH:{base:{position:"relative",height:"auto",textAlign:"left",backgroundColor:e.TABLE_TH_BACKGROUND_COLOR,borderBottom:`1px solid ${e.TABLE_BORDER_COLOR}`,fontWeight:"normal",verticalAlign:"middle",padding:"0 4px",whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",lineHeight:"14px",":hover":{backgroundColor:e.TABLE_TH_HOVER_COLOR}},div:{whiteSpace:"nowrap",textOverflow:"ellipsis",overflow:"hidden",fontSize:e.BASE_FONT_SIZE,lineHeight:"120%"}},TableInspectorLeftBorder:{none:{borderLeft:"none"},solid:{borderLeft:`1px solid ${e.TABLE_BORDER_COLOR}`}},TableInspectorSortIcon:{display:"block",marginRight:3,width:8,height:7,marginTop:-7,color:e.TABLE_SORT_ICON_COLOR,fontSize:12,...Kn}}),"createTheme"),va="chromeLight",Xs=Ct(qr(Xr[va])),Oe=i(e=>dr(Xs)[e],"useStyles"),Xa=i(e=>i(({theme:t=va,...r})=>{let a=ce(()=>{switch(Object.prototype.toString.call(t)){case"[object String]":return qr(Xr[t]);case"[object Object]":return qr(t);default:return qr(Xr[va])}},[t]);return n.createElement(Xs.Provider,{value:a},n.createElement(e,{...r}))},"ThemeAcceptor"),"themeAcceptor"),O2=i(({expanded:e,styles:t})=>n.createElement("span",{style:{...t.base,...e?t.expanded:t.collapsed}},"\u25B6"),"Arrow"),T2=he(e=>{e={expanded:!0,nodeRenderer:i(({name:m})=>n.createElement("span",null,m),"nodeRenderer"),onClick:i(()=>{},"onClick"),shouldShowArrow:!1,shouldShowPlaceholder:!0,...e};let{expanded:t,onClick:r,children:a,nodeRenderer:o,title:c,shouldShowArrow:l,shouldShowPlaceholder:u}=e,s=Oe("TreeNode"),d=o;return n.createElement("li",{"aria-expanded":t,role:"treeitem",style:s.treeNodeBase,title:c},n.createElement("div",{style:s.treeNodePreviewContainer,onClick:r},l||cr.count(a)>0?n.createElement(O2,{expanded:t,styles:s.treeNodeArrow}):u&&n.createElement("span",{style:s.treeNodePlaceholder},"\xA0"),n.createElement(d,{...e})),n.createElement("ol",{role:"group",style:s.treeNodeChildNodesContainer},t?a:void 0))}),Zr="$",mi="*";function Qt(e,t){return!t(e).next().done}i(Qt,"hasChildNodes");var I2=i(e=>Array.from({length:e},(t,r)=>[Zr].concat(Array.from({length:r},()=>"*")).join(".")),"wildcardPathsFromLevel"),D2=i((e,t,r,a,o)=>{let c=[].concat(I2(a)).concat(r).filter(u=>typeof u=="string"),l=[];return c.forEach(u=>{let s=u.split("."),d=i((m,f,p)=>{if(p===s.length){l.push(f);return}let h=s[p];if(p===0)Qt(m,t)&&(h===Zr||h===mi)&&d(m,Zr,p+1);else if(h===mi)for(let{name:g,data:E}of t(m))Qt(E,t)&&d(E,`${f}.${g}`,p+1);else{let g=m[h];Qt(g,t)&&d(g,`${f}.${h}`,p+1)}},"populatePaths");d(e,"",0)}),l.reduce((u,s)=>(u[s]=!0,u),{...o})},"getExpandedPaths"),Zs=he(e=>{let{data:t,dataIterator:r,path:a,depth:o,nodeRenderer:c}=e,[l,u]=dr(Js),s=Qt(t,r),d=!!l[a],m=$(()=>s&&u(f=>({...f,[a]:!d})),[s,u,a,d]);return n.createElement(T2,{expanded:d,onClick:m,shouldShowArrow:s,shouldShowPlaceholder:o>0,nodeRenderer:c,...e},d?[...r(t)].map(({name:f,data:p,...h})=>n.createElement(Zs,{name:f,data:p,depth:o+1,path:`${a}.${f}`,key:f,dataIterator:r,nodeRenderer:c,...h})):null)}),Qs=he(({name:e,data:t,dataIterator:r,nodeRenderer:a,expandPaths:o,expandLevel:c})=>{let l=Oe("TreeView"),u=R({}),[,s]=u;return pr(()=>s(d=>D2(t,r,o,c,d)),[t,r,o,c]),n.createElement(Js.Provider,{value:u},n.createElement("ol",{role:"tree",style:l.treeViewOutline},n.createElement(Zs,{name:e,data:t,dataIterator:r,depth:0,path:Zr,nodeRenderer:a})))}),Za=i(({name:e,dimmed:t=!1,styles:r={}})=>{let a=Oe("ObjectName"),o={...a.base,...t?a.dimmed:{},...r};return n.createElement("span",{style:o},e)},"ObjectName"),er=i(({object:e,styles:t})=>{let r=Oe("ObjectValue"),a=i(o=>({...r[o],...t}),"mkStyle");switch(typeof e){case"bigint":return n.createElement("span",{style:a("objectValueNumber")},String(e),"n");case"number":return n.createElement("span",{style:a("objectValueNumber")},String(e));case"string":return n.createElement("span",{style:a("objectValueString")},'"',e,'"');case"boolean":return n.createElement("span",{style:a("objectValueBoolean")},String(e));case"undefined":return n.createElement("span",{style:a("objectValueUndefined")},"undefined");case"object":return e===null?n.createElement("span",{style:a("objectValueNull")},"null"):e instanceof Date?n.createElement("span",null,e.toString()):e instanceof RegExp?n.createElement("span",{style:a("objectValueRegExp")},e.toString()):Array.isArray(e)?n.createElement("span",null,`Array(${e.length})`):e.constructor?typeof e.constructor.isBuffer=="function"&&e.constructor.isBuffer(e)?n.createElement("span",null,`Buffer[${e.length}]`):n.createElement("span",null,e.constructor.name):n.createElement("span",null,"Object");case"function":return n.createElement("span",null,n.createElement("span",{style:a("objectValueFunctionPrefix")},"\u0192\xA0"),n.createElement("span",{style:a("objectValueFunctionName")},e.name,"()"));case"symbol":return n.createElement("span",{style:a("objectValueSymbol")},e.toString());default:return n.createElement("span",null)}},"ObjectValue"),ec=Object.prototype.hasOwnProperty,B2=Object.prototype.propertyIsEnumerable;function Qr(e,t){let r=Object.getOwnPropertyDescriptor(e,t);if(r.get)try{return r.get()}catch{return r.get}return e[t]}i(Qr,"getPropertyValue");function xa(e,t){return e.length===0?[]:e.slice(1).reduce((r,a)=>r.concat([t,a]),[e[0]])}i(xa,"intersperse");var Aa=i(({data:e})=>{let t=Oe("ObjectPreview"),r=e;if(typeof r!="object"||r===null||r instanceof Date||r instanceof RegExp)return n.createElement(er,{object:r});if(Array.isArray(r)){let a=t.arrayMaxProperties,o=r.slice(0,a).map((l,u)=>n.createElement(er,{key:u,object:l}));r.length>a&&o.push(n.createElement("span",{key:"ellipsis"},"\u2026"));let c=r.length;return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c===0?"":`(${c})\xA0`),n.createElement("span",{style:t.preview},"[",xa(o,", "),"]"))}else{let a=t.objectMaxProperties,o=[];for(let l in r)if(ec.call(r,l)){let u;o.length===a-1&&Object.keys(r).length>a&&(u=n.createElement("span",{key:"ellipsis"},"\u2026"));let s=Qr(r,l);if(o.push(n.createElement("span",{key:l},n.createElement(Za,{name:l||'""'}),":\xA0",n.createElement(er,{object:s}),u)),u)break}let c=r.constructor?r.constructor.name:"Object";return n.createElement(n.Fragment,null,n.createElement("span",{style:t.objectDescription},c==="Object"?"":`${c} `),n.createElement("span",{style:t.preview},"{",xa(o,", "),"}"))}},"ObjectPreview"),_2=i(({name:e,data:t})=>typeof e=="string"?n.createElement("span",null,n.createElement(Za,{name:e}),n.createElement("span",null,": "),n.createElement(Aa,{data:t})):n.createElement(Aa,{data:t}),"ObjectRootLabel"),R2=i(({name:e,data:t,isNonenumerable:r=!1})=>{let a=t;return n.createElement("span",null,typeof e=="string"?n.createElement(Za,{name:e,dimmed:r}):n.createElement(Aa,{data:e}),n.createElement("span",null,": "),n.createElement(er,{object:a}))},"ObjectLabel"),N2=i((e,t)=>i(function*(r){if(!(typeof r=="object"&&r!==null||typeof r=="function"))return;let a=Array.isArray(r);if(!a&&r[Symbol.iterator]){let o=0;for(let c of r){if(Array.isArray(c)&&c.length===2){let[l,u]=c;yield{name:l,data:u}}else yield{name:o.toString(),data:c};o++}}else{let o=Object.getOwnPropertyNames(r);t===!0&&!a?o.sort():typeof t=="function"&&o.sort(t);for(let c of o)if(B2.call(r,c)){let l=Qr(r,c);yield{name:c||'""',data:l}}else if(e){let l;try{l=Qr(r,c)}catch{}l!==void 0&&(yield{name:c,data:l,isNonenumerable:!0})}e&&r!==Object.prototype&&(yield{name:"__proto__",data:Object.getPrototypeOf(r),isNonenumerable:!0})}},"objectIterator"),"createIterator"),F2=i(({depth:e,name:t,data:r,isNonenumerable:a})=>e===0?n.createElement(_2,{name:t,data:r}):n.createElement(R2,{name:t,data:r,isNonenumerable:a}),"defaultNodeRenderer"),L2=i(({showNonenumerable:e=!1,sortObjectKeys:t,nodeRenderer:r,...a})=>{let o=N2(e,t),c=r||F2;return n.createElement(Qs,{nodeRenderer:c,dataIterator:o,...a})},"ObjectInspector"),P2=Xa(L2);function tc(e){if(typeof e=="object"){let t=[];if(Array.isArray(e)){let a=e.length;t=[...Array(a).keys()]}else e!==null&&(t=Object.keys(e));let r=t.reduce((a,o)=>{let c=e[o];return typeof c=="object"&&c!==null&&Object.keys(c).reduce((l,u)=>(l.includes(u)||l.push(u),l),a),a},[]);return{rowHeaders:t,colHeaders:r}}}i(tc,"getHeaders");var j2=i(({rows:e,columns:t,rowsData:r})=>{let a=Oe("TableInspectorDataContainer"),o=Oe("TableInspectorLeftBorder");return n.createElement("div",{style:a.div},n.createElement("table",{style:a.table},n.createElement("colgroup",null),n.createElement("tbody",null,e.map((c,l)=>n.createElement("tr",{key:c,style:a.tr},n.createElement("td",{style:{...a.td,...o.none}},c),t.map(u=>{let s=r[l];return typeof s=="object"&&s!==null&&ec.call(s,u)?n.createElement("td",{key:u,style:{...a.td,...o.solid}},n.createElement(er,{object:s[u]})):n.createElement("td",{key:u,style:{...a.td,...o.solid}})}))))))},"DataContainer"),M2=i(e=>n.createElement("div",{style:{position:"absolute",top:1,right:0,bottom:1,display:"flex",alignItems:"center"}},e.children),"SortIconContainer"),U2=i(({sortAscending:e})=>{let t=Oe("TableInspectorSortIcon"),r=e?"\u25B2":"\u25BC";return n.createElement("div",{style:t},r)},"SortIcon"),fi=i(({sortAscending:e=!1,sorted:t=!1,onClick:r=void 0,borderStyle:a={},children:o,...c})=>{let l=Oe("TableInspectorTH"),[u,s]=R(!1),d=$(()=>s(!0),[]),m=$(()=>s(!1),[]);return n.createElement("th",{...c,style:{...l.base,...a,...u?l.base[":hover"]:{}},onMouseEnter:d,onMouseLeave:m,onClick:r},n.createElement("div",{style:l.div},o),t&&n.createElement(M2,null,n.createElement(U2,{sortAscending:e})))},"TH"),$2=i(({indexColumnText:e="(index)",columns:t=[],sorted:r,sortIndexColumn:a,sortColumn:o,sortAscending:c,onTHClick:l,onIndexTHClick:u})=>{let s=Oe("TableInspectorHeaderContainer"),d=Oe("TableInspectorLeftBorder");return n.createElement("div",{style:s.base},n.createElement("table",{style:s.table},n.createElement("tbody",null,n.createElement("tr",null,n.createElement(fi,{borderStyle:d.none,sorted:r&&a,sortAscending:c,onClick:u},e),t.map(m=>n.createElement(fi,{borderStyle:d.solid,key:m,sorted:r&&o===m,sortAscending:c,onClick:l.bind(null,m)},m))))))},"HeaderContainer"),H2=i(({data:e,columns:t})=>{let r=Oe("TableInspector"),[{sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l},u]=R({sorted:!1,sortIndexColumn:!1,sortColumn:void 0,sortAscending:!1}),s=$(()=>{u(({sortIndexColumn:g,sortAscending:E})=>({sorted:!0,sortIndexColumn:!0,sortColumn:void 0,sortAscending:g?!E:!0}))},[]),d=$(g=>{u(({sortColumn:E,sortAscending:y})=>({sorted:!0,sortIndexColumn:!1,sortColumn:g,sortAscending:g===E?!y:!0}))},[]);if(typeof e!="object"||e===null)return n.createElement("div",null);let{rowHeaders:m,colHeaders:f}=tc(e);t!==void 0&&(f=t);let p=m.map(g=>e[g]),h;if(c!==void 0?h=p.map((g,E)=>typeof g=="object"&&g!==null?[g[c],E]:[void 0,E]):o&&(h=m.map((g,E)=>[m[E],E])),h!==void 0){let g=i((y,v)=>(C,w)=>{let k=y(C),I=y(w),T=typeof k,B=typeof I,F=i((H,z)=>H<z?-1:H>z?1:0,"lt"),M;if(T===B)M=F(k,I);else{let H={string:0,number:1,object:2,symbol:3,boolean:4,undefined:5,function:6};M=F(H[T],H[B])}return v||(M=-M),M},"comparator"),E=h.sort(g(y=>y[0],l)).map(y=>y[1]);m=E.map(y=>m[y]),p=E.map(y=>p[y])}return n.createElement("div",{style:r.base},n.createElement($2,{columns:f,sorted:a,sortIndexColumn:o,sortColumn:c,sortAscending:l,onTHClick:d,onIndexTHClick:s}),n.createElement(j2,{rows:m,columns:f,rowsData:p}))},"TableInspector"),V2=Xa(H2),z2=80,rc=i(e=>e.childNodes.length===0||e.childNodes.length===1&&e.childNodes[0].nodeType===Node.TEXT_NODE&&e.textContent.length<z2,"shouldInline"),q2=i(({tagName:e,attributes:t,styles:r})=>n.createElement("span",{style:r.base},"<",n.createElement("span",{style:r.tagName},e),(()=>{if(t){let a=[];for(let o=0;o<t.length;o++){let c=t[o];a.push(n.createElement("span",{key:o}," ",n.createElement("span",{style:r.htmlAttributeName},c.name),'="',n.createElement("span",{style:r.htmlAttributeValue},c.value),'"'))}return a}})(),">"),"OpenTag"),hi=i(({tagName:e,isChildNode:t=!1,styles:r})=>n.createElement("span",{style:Object.assign({},r.base,t&&r.offsetLeft)},"</",n.createElement("span",{style:r.tagName},e),">"),"CloseTag"),G2={1:"ELEMENT_NODE",3:"TEXT_NODE",7:"PROCESSING_INSTRUCTION_NODE",8:"COMMENT_NODE",9:"DOCUMENT_NODE",10:"DOCUMENT_TYPE_NODE",11:"DOCUMENT_FRAGMENT_NODE"},W2=i(({isCloseTag:e,data:t,expanded:r})=>{let a=Oe("DOMNodePreview");if(e)return n.createElement(hi,{styles:a.htmlCloseTag,isChildNode:!0,tagName:t.tagName});switch(t.nodeType){case Node.ELEMENT_NODE:return n.createElement("span",null,n.createElement(q2,{tagName:t.tagName,attributes:t.attributes,styles:a.htmlOpenTag}),rc(t)?t.textContent:!r&&"\u2026",!r&&n.createElement(hi,{tagName:t.tagName,styles:a.htmlCloseTag}));case Node.TEXT_NODE:return n.createElement("span",null,t.textContent);case Node.CDATA_SECTION_NODE:return n.createElement("span",null,"<![CDATA["+t.textContent+"]]>");case Node.COMMENT_NODE:return n.createElement("span",{style:a.htmlComment},"<!--",t.textContent,"-->");case Node.PROCESSING_INSTRUCTION_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_TYPE_NODE:return n.createElement("span",{style:a.htmlDoctype},"<!DOCTYPE ",t.name,t.publicId?` PUBLIC "${t.publicId}"`:"",!t.publicId&&t.systemId?" SYSTEM":"",t.systemId?` "${t.systemId}"`:"",">");case Node.DOCUMENT_NODE:return n.createElement("span",null,t.nodeName);case Node.DOCUMENT_FRAGMENT_NODE:return n.createElement("span",null,t.nodeName);default:return n.createElement("span",null,G2[t.nodeType])}},"DOMNodePreview"),K2=i(function*(e){if(e&&e.childNodes){if(rc(e))return;for(let t=0;t<e.childNodes.length;t++){let r=e.childNodes[t];r.nodeType===Node.TEXT_NODE&&r.textContent.trim().length===0||(yield{name:`${r.tagName}[${t}]`,data:r})}e.tagName&&(yield{name:"CLOSE_TAG",data:{tagName:e.tagName},isCloseTag:!0})}},"domIterator"),Y2=i(e=>n.createElement(Qs,{nodeRenderer:W2,dataIterator:K2,...e}),"DOMInspector"),J2=Xa(Y2),X2=x2(S2()),Z2=i(({table:e=!1,data:t,...r})=>e?n.createElement(V2,{data:t,...r}):(0,X2.default)(t)?n.createElement(J2,{data:t,...r}):n.createElement(P2,{data:t,...r}),"Inspector"),Q2=b.div({display:"flex",padding:0,borderLeft:"5px solid transparent",borderBottom:"1px solid transparent",transition:"all 0.1s",alignItems:"flex-start",whiteSpace:"pre"}),e0=b.div(({theme:e})=>({backgroundColor:Xt(.5,e.appBorderColor),color:e.color.inverseText,fontSize:e.typography.size.s1,fontWeight:e.typography.weight.bold,lineHeight:1,padding:"1px 5px",borderRadius:20,margin:"2px 0px"})),t0=b.div({flex:1,padding:"0 0 0 5px"}),nc=po(({children:e,className:t},r)=>n.createElement(gn,{ref:r,horizontal:!0,vertical:!0,className:t},e));nc.displayName="UnstyledWrapped";var r0=b(nc)({margin:0,padding:"10px 5px 20px"}),n0=Wo(({theme:e,...t})=>n.createElement(Z2,{theme:e.addonActionsTheme||"chromeLight",table:!1,...t})),a0=i(({actions:e,onClear:t})=>{let r=X(null),a=r.current,o=a&&a.scrollHeight-a.scrollTop===a.clientHeight;return j(()=>{o&&(r.current.scrollTop=r.current.scrollHeight)},[o,e.length]),n.createElement(Ie,null,n.createElement(r0,{ref:r},e.map(c=>n.createElement(Q2,{key:c.id},c.count>1&&n.createElement(e0,null,c.count),n.createElement(t0,null,n.createElement(n0,{sortObjectKeys:!0,showNonenumerable:!1,name:c.data.name,data:c.data.args??c.data}))))),n.createElement(Mt,{actionItems:[{title:"Clear",onClick:t}]}))},"ActionLogger"),o0=i((e,t)=>{try{return ut(e,t)}catch{return!1}},"safeDeepEqual"),ac=class extends Re{constructor(t){super(t),this.handleStoryChange=i(()=>{let{actions:r}=this.state;r.length>0&&r[0].options.clearOnStoryChange&&this.clearActions()},"handleStoryChange"),this.addAction=i(r=>{this.setState(a=>{let o=[...a.actions],c=o.length&&o[o.length-1];return c&&o0(c.data,r.data)?c.count++:(r.count=1,o.push(r)),{actions:o.slice(0,r.options.limit)}})},"addAction"),this.clearActions=i(()=>{let{api:r}=this.props;r.emit(Ws),this.setState({actions:[]})},"clearActions"),this.mounted=!1,this.state={actions:[]}}componentDidMount(){this.mounted=!0;let{api:t}=this.props;t.on(Ea,this.addAction),t.on(qt,this.handleStoryChange)}componentWillUnmount(){this.mounted=!1;let{api:t}=this.props;t.off(qt,this.handleStoryChange),t.off(Ea,this.addAction)}render(){let{actions:t=[]}=this.state,{active:r}=this.props,a={actions:t,onClear:this.clearActions};return r?n.createElement(a0,{...a}):null}};i(ac,"ActionLogger");var l0=ac,dk=Z.register(lr,e=>{globalThis?.FEATURES?.actions&&Z.add(Gs,{title:Ks,type:Ee.PANEL,render:i(({active:t})=>n.createElement(l0,{api:e,active:!!t}),"render"),paramKey:f2})}),sn="storybook/interactions",Qa=`${sn}/panel`,i0="writing-tests/integrations/vitest-addon",u0=`${i0}#what-happens-when-there-are-different-test-results-in-multiple-environments`,s0="writing-stories/play-function#writing-stories-with-the-play-function",De="internal_render_call",xt="storybook/a11y",Ek=`${xt}/panel`,vk=`${xt}/result`,xk=`${xt}/request`,Ak=`${xt}/running`,Ck=`${xt}/error`,Sk=`${xt}/manual`,wk=`${xt}/select`,c0="writing-tests/accessibility-testing",kk=`${c0}#why-are-my-tests-failing-in-different-environments`,oc="storybook/test",Ok=`${oc}/test-provider`,d0="STORYBOOK_ADDON_TEST_CHANNEL",p0="writing-tests/integrations/vitest-addon",Tk=`${p0}#what-happens-if-vitest-itself-has-an-error`,m0={id:oc,initialState:{config:{coverage:!1,a11y:!1},watching:!1,cancelling:!1,fatalError:void 0,indexUrl:void 0,previewAnnotations:[],currentRun:{triggeredBy:void 0,config:{coverage:!1,a11y:!1},componentTestCount:{success:0,error:0},a11yCount:{success:0,warning:0,error:0},storyIds:void 0,totalTestCount:void 0,startedAt:void 0,finishedAt:void 0,unhandledErrors:[],coverageSummary:void 0}}},Ik=`UNIVERSAL_STORE:${m0.id}`,f0="storybook/component-test",ft={CALL:"storybook/instrumenter/call",SYNC:"storybook/instrumenter/sync",START:"storybook/instrumenter/start",BACK:"storybook/instrumenter/back",GOTO:"storybook/instrumenter/goto",NEXT:"storybook/instrumenter/next",END:"storybook/instrumenter/end"},h0=Se(yp(),1);function lc({onlyFirst:e=!1}={}){let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}i(lc,"ansiRegex");var g0=lc();function ic(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(g0,"")}i(ic,"stripAnsi");function uc(e){return eo(e)||to(e)}i(uc,"isTestAssertionError");function eo(e){return e&&typeof e=="object"&&"name"in e&&typeof e.name=="string"&&e.name==="AssertionError"}i(eo,"isChaiError");function to(e){return e&&typeof e=="object"&&"message"in e&&typeof e.message=="string"&&ic(e.message).startsWith("expect(")}i(to,"isJestError");function sc(e){return new h0.default({escapeXML:!0,fg:e.color.defaultText,bg:e.background.content})}i(sc,"createAnsiToHtmlFilter");function cn(){let e=ye();return sc(e)}i(cn,"useAnsiToHtmlFilter");var b0=b.div(({theme:{color:e,typography:t,background:r}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2-1}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:r.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative"})),y0=i(({storyUrl:e})=>n.createElement(b0,null,"Debugger controls are not available on composed Storybooks."," ",n.createElement(be,{href:`${e}&addonPanel=${Qa}`,target:"_blank",rel:"noopener noreferrer",withArrow:!0},"Open in external Storybook")),"DetachedDebuggerMessage"),E0=b.div(({theme:e})=>({display:"flex",fontSize:e.typography.size.s2-1,gap:25})),v0=i(()=>{let[e,t]=R(!0),r=ve().getDocsUrl({subpath:s0,versioned:!0,renderer:!0});return j(()=>{let a=setTimeout(()=>{t(!1)},100);return()=>clearTimeout(a)},[]),e?null:n.createElement("div",null,n.createElement(Ht,{title:"Interactions",description:n.createElement(n.Fragment,null,"Interactions allow you to verify the functional aspects of UIs. Write a play function for your story and you'll see it run here."),footer:n.createElement(E0,null,n.createElement(be,{href:r,target:"_blank",withArrow:!0},n.createElement(dt,null)," Read docs"))}))},"Empty"),x0=Se(Fa()),A0=Se(La());function en(e){var t,r,a="";if(e)if(typeof e=="object")if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(r=en(e[t]))&&(a&&(a+=" "),a+=r);else for(t in e)e[t]&&(r=en(t))&&(a&&(a+=" "),a+=r);else typeof e!="boolean"&&!e.call&&(a&&(a+=" "),a+=e);return a}i(en,"toVal");function Pe(){for(var e=0,t,r="";e<arguments.length;)(t=en(arguments[e++]))&&(r&&(r+=" "),r+=t);return r}i(Pe,"default");var ro=i(e=>Array.isArray(e)||ArrayBuffer.isView(e)&&!(e instanceof DataView),"isArray"),cc=i(e=>e!==null&&typeof e=="object"&&!ro(e)&&!(e instanceof Date)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof WeakMap)&&!(e instanceof WeakSet),"isObject"),C0=i(e=>cc(e)||ro(e)||typeof e=="function"||e instanceof Promise,"isKnownObject"),dc=i(e=>{let t=/unique/;return Promise.race([e,t]).then(r=>r===t?["pending"]:["fulfilled",r],r=>["rejected",r])},"getPromiseState"),qe=i(async(e,t,r,a,o,c)=>{let l={key:e,depth:r,value:t,type:"value",parent:void 0};if(t&&C0(t)&&r<100){let u=[],s="object";if(ro(t)){for(let d=0;d<t.length;d++)u.push(async()=>{let m=await qe(d.toString(),t[d],r+1,a);return m.parent=l,m});s="array"}else{let d=Object.getOwnPropertyNames(t);a&&d.sort();for(let m=0;m<d.length;m++){let f;try{f=t[d[m]]}catch{}u.push(async()=>{let p=await qe(d[m],f,r+1,a);return p.parent=l,p})}if(typeof t=="function"&&(s="function"),t instanceof Promise){let[m,f]=await dc(t);u.push(async()=>{let p=await qe("<state>",m,r+1,a);return p.parent=l,p}),m!=="pending"&&u.push(async()=>{let p=await qe("<value>",f,r+1,a);return p.parent=l,p}),s="promise"}if(t instanceof Map){let m=Array.from(t.entries()).map(f=>{let[p,h]=f;return{"<key>":p,"<value>":h}});u.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),u.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),s="map"}if(t instanceof Set){let m=Array.from(t.entries()).map(f=>f[1]);u.push(async()=>{let f=await qe("<entries>",m,r+1,a);return f.parent=l,f}),u.push(async()=>{let f=await qe("size",t.size,r+1,a);return f.parent=l,f}),s="set"}}t!==Object.prototype&&c&&u.push(async()=>{let d=await qe("<prototype>",Object.getPrototypeOf(t),r+1,a,!0);return d.parent=l,d}),l.type=s,l.children=u,l.isPrototype=o}return l},"buildAST"),S0=i((e,t,r)=>qe("root",e,0,t===!1?t:!0,void 0,r===!1?r:!0),"parse"),gi=Se(xp()),w0=Se(Cp()),k0=["children"],Ca=n.createContext({theme:"chrome",colorScheme:"light"}),O0=i(e=>{let{children:t}=e,r=(0,w0.default)(e,k0),a=n.useContext(Ca);return n.createElement(Ca.Provider,{value:(0,gi.default)((0,gi.default)({},a),r)},t)},"ThemeProvider"),dn=i((e,t={})=>{let r=n.useContext(Ca),a=e.theme||r.theme||"chrome",o=e.colorScheme||r.colorScheme||"light",c=Pe(t[a],t[o]);return{currentColorScheme:o,currentTheme:a,themeClass:c}},"useTheme"),bi=Se(wp()),Yn=Se(kp()),T0=Se(Tp()),I0=n.createContext({isChild:!1,depth:0,hasHover:!0}),Jn=I0,xe={tree:"Tree-tree-fbbbe38",item:"Tree-item-353d6f3",group:"Tree-group-d3c3d8a",label:"Tree-label-d819155",focusWhite:"Tree-focusWhite-f1e00c2",arrow:"Tree-arrow-03ab2e7",hover:"Tree-hover-3cc4e5d",open:"Tree-open-3f1a336",dark:"Tree-dark-1b4aa00",chrome:"Tree-chrome-bcbcac6",light:"Tree-light-09174ee"},D0=["theme","hover","colorScheme","children","label","className","onUpdate","onSelect","open"],tn=i(e=>{let{theme:t,hover:r,colorScheme:a,children:o,label:c,className:l,onUpdate:u,onSelect:s,open:d}=e,m=(0,T0.default)(e,D0),{themeClass:f,currentTheme:p}=dn({theme:t,colorScheme:a},xe),[h,g]=R(d);j(()=>{g(d)},[d]);let E=i(O=>{g(O),u&&u(O)},"updateState"),y=n.Children.count(o)>0,v=i((O,D)=>{if(O.isSameNode(D||null))return;O.querySelector('[tabindex="-1"]')?.focus(),O.setAttribute("aria-selected","true"),D?.removeAttribute("aria-selected")},"updateFocus"),C=i((O,D)=>{let N=O;for(;N&&N.parentElement;){if(N.getAttribute("role")===D)return N;N=N.parentElement}return null},"getParent"),w=i(O=>{let D=C(O,"tree");return D?Array.from(D.querySelectorAll("li")):[]},"getListElements"),k=i(O=>{let D=C(O,"group"),N=D?.previousElementSibling;if(N&&N.getAttribute("tabindex")==="-1"){let V=N.parentElement,J=O.parentElement;v(V,J)}},"moveBack"),I=i((O,D)=>{let N=w(O);N.forEach(V=>{V.removeAttribute("aria-selected")}),D==="start"&&N[0]&&v(N[0]),D==="end"&&N[N.length-1]&&v(N[N.length-1])},"moveHome"),T=i((O,D)=>{let N=w(O)||[];for(let V=0;V<N.length;V++){let J=N[V];if(J.getAttribute("aria-selected")==="true"){D==="up"&&N[V-1]?v(N[V-1],J):D==="down"&&N[V+1]&&v(N[V+1],J);return}}v(N[0])},"moveFocusAdjacent"),B=i((O,D)=>{let N=O.target;(O.key==="Enter"||O.key===" ")&&E(!h),O.key==="ArrowRight"&&h&&!D?T(N,"down"):O.key==="ArrowRight"&&E(!0),O.key==="ArrowLeft"&&(!h||D)?k(N):O.key==="ArrowLeft"&&E(!1),O.key==="ArrowDown"&&T(N,"down"),O.key==="ArrowUp"&&T(N,"up"),O.key==="Home"&&I(N,"start"),O.key==="End"&&I(N,"end")},"handleKeypress"),F=i((O,D)=>{let N=O.target,V=C(N,"treeitem"),J=w(N)||[],Lt=!1;for(let At=0;At<J.length;At++){let Pt=J[At];if(Pt.getAttribute("aria-selected")==="true"){V&&(Lt=!0,v(V,Pt));break}}!Lt&&V&&v(V),D||E(!h)},"handleClick"),M=i(O=>{let D=O.currentTarget;!D.contains(document.activeElement)&&D.getAttribute("role")==="tree"&&D.setAttribute("tabindex","0")},"handleBlur"),H=i(O=>{let D=O.target;if(D.getAttribute("role")==="tree"){let N=D.querySelector('[aria-selected="true"]');N?v(N):T(D,"down"),D.setAttribute("tabindex","-1")}},"handleFocus"),z=i(()=>{s?.()},"handleButtonFocus"),Q=i(O=>{let D=O*.9+.3;return{paddingLeft:`${D}em`,width:`calc(100% - ${D}em)`}},"getPaddingStyles"),{isChild:ne,depth:A,hasHover:x}=n.useContext(Jn),S=x?r:!1;if(!ne)return n.createElement("ul",(0,Yn.default)({role:"tree",tabIndex:0,className:Pe(xe.tree,xe.group,f,l),onFocus:H,onBlur:M},m),n.createElement(Jn.Provider,{value:{isChild:!0,depth:0,hasHover:S}},n.createElement(tn,e)));if(!y)return n.createElement("li",(0,Yn.default)({role:"treeitem",className:xe.item},m),n.createElement("div",{role:"button",className:Pe(xe.label,{[xe.hover]:S,[xe.focusWhite]:p==="firefox"}),tabIndex:-1,style:Q(A),onKeyDown:i(O=>{B(O,ne)},"onKeyDown"),onClick:i(O=>F(O,!0),"onClick"),onFocus:z},n.createElement("span",null,c)));let _=Pe(xe.arrow,{[xe.open]:h});return n.createElement("li",{role:"treeitem","aria-expanded":h,className:xe.item},n.createElement("div",{role:"button",tabIndex:-1,className:Pe(xe.label,{[xe.hover]:S,[xe.focusWhite]:p==="firefox"}),style:Q(A),onClick:i(O=>F(O),"onClick"),onKeyDown:i(O=>B(O),"onKeyDown"),onFocus:z},n.createElement("span",null,n.createElement("span",{"aria-hidden":!0,className:_}),n.createElement("span",null,c))),n.createElement("ul",(0,Yn.default)({role:"group",className:Pe(l,xe.group)},m),h&&n.Children.map(o,O=>n.createElement(Jn.Provider,{value:{isChild:!0,depth:A+1,hasHover:S}},O))))},"Tree");tn.defaultProps={open:!1,hover:!0};var B0=Se(Fa()),_0=Se(La()),K={"object-inspector":"ObjectInspector-object-inspector-0c33e82",objectInspector:"ObjectInspector-object-inspector-0c33e82","object-label":"ObjectInspector-object-label-b81482b",objectLabel:"ObjectInspector-object-label-b81482b",text:"ObjectInspector-text-25f57f3",key:"ObjectInspector-key-4f712bb",value:"ObjectInspector-value-f7ec2e5",string:"ObjectInspector-string-c496000",regex:"ObjectInspector-regex-59d45a3",error:"ObjectInspector-error-b818698",boolean:"ObjectInspector-boolean-2dd1642",number:"ObjectInspector-number-a6daabb",undefined:"ObjectInspector-undefined-3a68263",null:"ObjectInspector-null-74acb50",function:"ObjectInspector-function-07bbdcd","function-decorator":"ObjectInspector-function-decorator-3d22c24",functionDecorator:"ObjectInspector-function-decorator-3d22c24",prototype:"ObjectInspector-prototype-f2449ee",dark:"ObjectInspector-dark-0c96c97",chrome:"ObjectInspector-chrome-2f3ca98",light:"ObjectInspector-light-78bef54"},R0=["ast","theme","showKey","colorScheme","className"],Ae=i((e,t,r,a,o)=>{let c=e.includes("-")?`"${e}"`:e,l=o<=0;return n.createElement("span",{className:K.text},!l&&a&&n.createElement(n.Fragment,null,n.createElement("span",{className:K.key},c),n.createElement("span",null,":\xA0")),n.createElement("span",{className:r},t))},"buildValue"),pc=i(e=>{let{ast:t,theme:r,showKey:a,colorScheme:o,className:c}=e,l=(0,_0.default)(e,R0),{themeClass:u}=dn({theme:r,colorScheme:o},K),[s,d]=R(n.createElement("span",null)),m=n.createElement("span",null);return j(()=>{t.value instanceof Promise&&i(async f=>{d(Ae(t.key,`Promise { "${await dc(f)}" }`,K.key,a,t.depth))},"waitForPromiseResult")(t.value)},[t,a]),typeof t.value=="number"||typeof t.value=="bigint"?m=Ae(t.key,String(t.value),K.number,a,t.depth):typeof t.value=="boolean"?m=Ae(t.key,String(t.value),K.boolean,a,t.depth):typeof t.value=="string"?m=Ae(t.key,`"${t.value}"`,K.string,a,t.depth):typeof t.value>"u"?m=Ae(t.key,"undefined",K.undefined,a,t.depth):typeof t.value=="symbol"?m=Ae(t.key,t.value.toString(),K.string,a,t.depth):typeof t.value=="function"?m=Ae(t.key,`${t.value.name}()`,K.key,a,t.depth):typeof t.value=="object"&&(t.value===null?m=Ae(t.key,"null",K.null,a,t.depth):Array.isArray(t.value)?m=Ae(t.key,`Array(${t.value.length})`,K.key,a,t.depth):t.value instanceof Date?m=Ae(t.key,`Date ${t.value.toString()}`,K.value,a,t.depth):t.value instanceof RegExp?m=Ae(t.key,t.value.toString(),K.regex,a,t.depth):t.value instanceof Error?m=Ae(t.key,t.value.toString(),K.error,a,t.depth):cc(t.value)?m=Ae(t.key,"{\u2026}",K.key,a,t.depth):m=Ae(t.key,t.value.constructor.name,K.key,a,t.depth)),n.createElement("span",(0,B0.default)({className:Pe(u,c)},l),s,m)},"ObjectValue");pc.defaultProps={showKey:!0};var mc=pc,Tt=Se(Fa()),N0=Se(La()),F0=["ast","theme","previewMax","open","colorScheme","className"],ir=i((e,t,r)=>{let a=[];for(let o=0;o<e.length;o++){let c=e[o];if(c.isPrototype||(a.push(n.createElement(mc,{key:c.key,ast:c,showKey:r})),o<e.length-1?a.push(", "):a.push(" ")),c.isPrototype&&o===e.length-1&&(a.pop(),a.push(" ")),o===t-1&&e.length>t){a.push("\u2026 ");break}}return a},"buildPreview"),L0=i((e,t,r,a)=>{let o=e.value.length;return t?n.createElement("span",null,"Array(",o,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Array":""}(${o}) [ `),ir(e.children,r,!1),n.createElement("span",null,"]"))},"getArrayLabel"),P0=i((e,t,r,a)=>e.isPrototype?n.createElement("span",null,`Object ${a==="firefox"?"{ \u2026 }":""}`):t?n.createElement("span",null,"{\u2026}"):n.createElement(n.Fragment,null,n.createElement("span",null,`${a==="firefox"?"Object ":""}{ `),ir(e.children,r,!0),n.createElement("span",null,"}")),"getObjectLabel"),j0=i((e,t,r)=>t?n.createElement("span",null,`Promise { "${String(e.children[0].value)}" }`):n.createElement(n.Fragment,null,n.createElement("span",null,"Promise { "),ir(e.children,r,!0),n.createElement("span",null,"}")),"getPromiseLabel"),M0=i((e,t,r,a)=>{let{size:o}=e.value;return t?n.createElement("span",null,`Map(${o})`):n.createElement(n.Fragment,null,n.createElement("span",null,`Map${a==="chrome"?`(${o})`:""} { `),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getMapLabel"),U0=i((e,t,r)=>{let{size:a}=e.value;return t?n.createElement("span",null,"Set(",a,")"):n.createElement(n.Fragment,null,n.createElement("span",null,`Set(${e.value.size}) {`),ir(e.children,r,!0),n.createElement("span",null,"}"))},"getSetLabel"),fc=i(e=>{let{ast:t,theme:r,previewMax:a,open:o,colorScheme:c,className:l}=e,u=(0,N0.default)(e,F0),{themeClass:s,currentTheme:d}=dn({theme:r,colorScheme:c},K),m=t.isPrototype||!1,f=Pe(K.objectLabel,s,l,{[K.prototype]:m}),p=t.depth<=0,h=i(()=>n.createElement("span",{className:m?K.prototype:K.key},p?"":`${t.key}: `),"Key");return t.type==="array"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),L0(t,o,a,d)):t.type==="function"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),d==="chrome"&&n.createElement("span",{className:K.functionDecorator},"\u0192 "),n.createElement("span",{className:Pe({[K.function]:!m})},`${t.value.name}()`)):t.type==="promise"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),j0(t,o,a)):t.type==="map"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),M0(t,o,a,d)):t.type==="set"?n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),U0(t,o,a)):n.createElement("span",(0,Tt.default)({className:f},u),n.createElement(h,null),P0(t,o,a,d))},"ObjectLabel");fc.defaultProps={previewMax:8,open:!1};var $0=fc,no=i(e=>{let{ast:t,expandLevel:r,depth:a}=e,[o,c]=R(),[l,u]=R(a<r);return j(()=>{i(async()=>{if(t.type!=="value"){let s=t.children.map(f=>f()),d=await Promise.all(s),m=(0,bi.default)((0,bi.default)({},t),{},{children:d});c(m)}},"resolve")()},[t]),o?n.createElement(tn,{hover:!1,open:l,label:n.createElement($0,{open:l,ast:o}),onSelect:i(()=>{var s;(s=e.onSelect)===null||s===void 0||s.call(e,t)},"onSelect"),onUpdate:i(s=>{u(s)},"onUpdate")},o.children.map(s=>n.createElement(no,{key:s.key,ast:s,depth:a+1,expandLevel:r,onSelect:e.onSelect}))):n.createElement(tn,{hover:!1,label:n.createElement(mc,{ast:t}),onSelect:i(()=>{var s;(s=e.onSelect)===null||s===void 0||s.call(e,t)},"onSelect")})},"ObjectInspectorItem");no.defaultProps={expandLevel:0,depth:0};var H0=no,V0=["data","expandLevel","sortKeys","includePrototypes","className","theme","colorScheme","onSelect"],hc=i(e=>{let{data:t,expandLevel:r,sortKeys:a,includePrototypes:o,className:c,theme:l,colorScheme:u,onSelect:s}=e,d=(0,A0.default)(e,V0),[m,f]=R(void 0),{themeClass:p,currentTheme:h,currentColorScheme:g}=dn({theme:l,colorScheme:u},K);return j(()=>{i(async()=>{f(await S0(t,a,o))},"runParser")()},[t,a,o]),n.createElement("div",(0,x0.default)({className:Pe(K.objectInspector,c,p)},d),m&&n.createElement(O0,{theme:h,colorScheme:g},n.createElement(H0,{ast:m,expandLevel:r,onSelect:s})))},"ObjectInspector");hc.defaultProps={expandLevel:0,sortKeys:!0,includePrototypes:!0};var z0={base:"#444",nullish:"#7D99AA",string:"#16B242",number:"#5D40D0",boolean:"#f41840",objectkey:"#698394",instance:"#A15C20",function:"#EA7509",muted:"#7D99AA",tag:{name:"#6F2CAC",suffix:"#1F99E5"},date:"#459D9C",error:{name:"#D43900",message:"#444"},regex:{source:"#A15C20",flags:"#EA7509"},meta:"#EA7509",method:"#0271B6"},q0={base:"#eee",nullish:"#aaa",string:"#5FE584",number:"#6ba5ff",boolean:"#ff4191",objectkey:"#accfe6",instance:"#E3B551",function:"#E3B551",muted:"#aaa",tag:{name:"#f57bff",suffix:"#8EB5FF"},date:"#70D4D3",error:{name:"#f40",message:"#eee"},regex:{source:"#FAD483",flags:"#E3B551"},meta:"#FAD483",method:"#5EC1FF"},se=i(()=>{let{base:e}=ye();return e==="dark"?q0:z0},"useThemeColors"),G0=/[^A-Z0-9]/i,yi=/[\s.,…]+$/gm,gc=i((e,t)=>{if(e.length<=t)return e;for(let r=t-1;r>=0;r-=1)if(G0.test(e[r])&&r>10)return`${e.slice(0,r).replace(yi,"")}\u2026`;return`${e.slice(0,t).replace(yi,"")}\u2026`},"ellipsize"),W0=i(e=>{try{return JSON.stringify(e,null,1)}catch{return String(e)}},"stringify"),bc=i((e,t)=>e.flatMap((r,a)=>a===e.length-1?[r]:[r,n.cloneElement(t,{key:`sep${a}`})]),"interleave"),Et=i(({value:e,nested:t,showObjectInspector:r,callsById:a,...o})=>{switch(!0){case e===null:return n.createElement(K0,{...o});case e===void 0:return n.createElement(Y0,{...o});case Array.isArray(e):return n.createElement(Q0,{...o,value:e,callsById:a});case typeof e=="string":return n.createElement(J0,{...o,value:e});case typeof e=="number":return n.createElement(X0,{...o,value:e});case typeof e=="boolean":return n.createElement(Z0,{...o,value:e});case Object.prototype.hasOwnProperty.call(e,"__date__"):return n.createElement(ag,{...o,...e.__date__});case Object.prototype.hasOwnProperty.call(e,"__error__"):return n.createElement(og,{...o,...e.__error__});case Object.prototype.hasOwnProperty.call(e,"__regexp__"):return n.createElement(lg,{...o,...e.__regexp__});case Object.prototype.hasOwnProperty.call(e,"__function__"):return n.createElement(rg,{...o,...e.__function__});case Object.prototype.hasOwnProperty.call(e,"__symbol__"):return n.createElement(ig,{...o,...e.__symbol__});case Object.prototype.hasOwnProperty.call(e,"__element__"):return n.createElement(ng,{...o,...e.__element__});case Object.prototype.hasOwnProperty.call(e,"__class__"):return n.createElement(tg,{...o,...e.__class__});case Object.prototype.hasOwnProperty.call(e,"__callId__"):return n.createElement(ao,{call:a?.get(e.__callId__),callsById:a});case Object.prototype.toString.call(e)==="[object Object]":return n.createElement(eg,{value:e,showInspector:r,callsById:a,...o});default:return n.createElement(ug,{value:e,...o})}},"Node"),K0=i(e=>{let t=se();return n.createElement("span",{style:{color:t.nullish},...e},"null")},"NullNode"),Y0=i(e=>{let t=se();return n.createElement("span",{style:{color:t.nullish},...e},"undefined")},"UndefinedNode"),J0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.string},...t},JSON.stringify(gc(e,50)))},"StringNode"),X0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.number},...t},e)},"NumberNode"),Z0=i(({value:e,...t})=>{let r=se();return n.createElement("span",{style:{color:r.boolean},...t},String(e))},"BooleanNode"),Q0=i(({value:e,nested:t=!1,callsById:r})=>{let a=se();if(t)return n.createElement("span",{style:{color:a.base}},"[\u2026]");let o=e.slice(0,3).map((l,u)=>n.createElement(Et,{key:`${u}--${JSON.stringify(l)}`,value:l,nested:!0,callsById:r})),c=bc(o,n.createElement("span",null,", "));return e.length<=3?n.createElement("span",{style:{color:a.base}},"[",c,"]"):n.createElement("span",{style:{color:a.base}},"(",e.length,") [",c,", \u2026]")},"ArrayNode"),eg=i(({showInspector:e,value:t,callsById:r,nested:a=!1})=>{let o=ye().base==="dark",c=se();if(e)return n.createElement(n.Fragment,null,n.createElement(hc,{id:"interactions-object-inspector",data:t,includePrototypes:!1,colorScheme:o?"dark":"light"}));if(a)return n.createElement("span",{style:{color:c.base}},"{\u2026}");let l=bc(Object.entries(t).slice(0,2).map(([u,s])=>n.createElement(Ie,{key:u},n.createElement("span",{style:{color:c.objectkey}},u,": "),n.createElement(Et,{value:s,callsById:r,nested:!0}))),n.createElement("span",null,", "));return Object.keys(t).length<=2?n.createElement("span",{style:{color:c.base}},"{ ",l," }"):n.createElement("span",{style:{color:c.base}},"(",Object.keys(t).length,") ","{ ",l,", \u2026 }")},"ObjectNode"),tg=i(({name:e})=>{let t=se();return n.createElement("span",{style:{color:t.instance}},e)},"ClassNode"),rg=i(({name:e})=>{let t=se();return e?n.createElement("span",{style:{color:t.function}},e):n.createElement("span",{style:{color:t.nullish,fontStyle:"italic"}},"anonymous")},"FunctionNode"),ng=i(({prefix:e,localName:t,id:r,classNames:a=[],innerText:o})=>{let c=e?`${e}:${t}`:t,l=se();return n.createElement("span",{style:{wordBreak:"keep-all"}},n.createElement("span",{key:`${c}_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_tag`,style:{color:l.tag.name}},c),n.createElement("span",{key:`${c}_suffix`,style:{color:l.tag.suffix}},r?`#${r}`:a.reduce((u,s)=>`${u}.${s}`,"")),n.createElement("span",{key:`${c}_gt`,style:{color:l.muted}},">"),!r&&a.length===0&&o&&n.createElement(n.Fragment,null,n.createElement("span",{key:`${c}_text`},o),n.createElement("span",{key:`${c}_close_lt`,style:{color:l.muted}},"<"),n.createElement("span",{key:`${c}_close_tag`,style:{color:l.tag.name}},"/",c),n.createElement("span",{key:`${c}_close_gt`,style:{color:l.muted}},">")))},"ElementNode"),ag=i(({value:e})=>{let t=new Date(e);isNaN(Number(t))&&(tt.warn("Invalid date value:",e),t=null);let r=se();if(!t)return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},"Invalid date");let[a,o,c]=t.toISOString().split(/[T.Z]/);return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.date}},a,n.createElement("span",{style:{opacity:.7}},"T"),o==="00:00:00"?n.createElement("span",{style:{opacity:.7}},o):o,c==="000"?n.createElement("span",{style:{opacity:.7}},".",c):`.${c}`,n.createElement("span",{style:{opacity:.7}},"Z"))},"DateNode"),og=i(({name:e,message:t})=>{let r=se();return n.createElement("span",{style:{color:r.error.name}},e,t&&": ",t&&n.createElement("span",{style:{color:r.error.message},title:t.length>50?t:""},gc(t,50)))},"ErrorNode"),lg=i(({flags:e,source:t})=>{let r=se();return n.createElement("span",{style:{whiteSpace:"nowrap",color:r.regex.flags}},"/",n.createElement("span",{style:{color:r.regex.source}},t),"/",e)},"RegExpNode"),ig=i(({description:e})=>{let t=se();return n.createElement("span",{style:{whiteSpace:"nowrap",color:t.instance}},"Symbol(",e&&n.createElement("span",{style:{color:t.meta}},'"',e,'"'),")")},"SymbolNode"),ug=i(({value:e})=>{let t=se();return n.createElement("span",{style:{color:t.meta}},W0(e))},"OtherNode"),sg=i(({label:e})=>{let t=se(),{typography:r}=ye();return n.createElement("span",{style:{color:t.base,fontFamily:r.fonts.base,fontSize:r.size.s2-1}},e)},"StepNode"),ao=i(({call:e,callsById:t})=>{if(!e)return null;if(e.method==="step"&&e.path?.length===0)return n.createElement(sg,{label:e.args[0]});let r=e.path?.flatMap((c,l)=>{let u=c.__callId__;return[u?n.createElement(ao,{key:`elem${l}`,call:t?.get(u),callsById:t}):n.createElement("span",{key:`elem${l}`},c),n.createElement("wbr",{key:`wbr${l}`}),n.createElement("span",{key:`dot${l}`},".")]}),a=e.args?.flatMap((c,l,u)=>{let s=n.createElement(Et,{key:`node${l}`,value:c,callsById:t});return l<u.length-1?[s,n.createElement("span",{key:`comma${l}`},",\xA0"),n.createElement("wbr",{key:`wbr${l}`})]:[s]}),o=se();return n.createElement(n.Fragment,null,n.createElement("span",{style:{color:o.base}},r),n.createElement("span",{style:{color:o.method}},e.method),n.createElement("span",{style:{color:o.base}},"(",n.createElement("wbr",null),a,n.createElement("wbr",null),")"))},"MethodCall"),Ei=i((e,t=0)=>{for(let r=t,a=1;r<e.length;r+=1)if(e[r]==="("?a+=1:e[r]===")"&&(a-=1),a===0)return e.slice(t,r);return""},"getParams"),Xn=i(e=>{try{return e==="undefined"?void 0:JSON.parse(e)}catch{return e}},"parseValue"),cg=b.span(({theme:e})=>({color:e.base==="light"?e.color.positiveText:e.color.positive})),dg=b.span(({theme:e})=>({color:e.base==="light"?e.color.negativeText:e.color.negative})),Zn=i(({value:e,parsed:t})=>t?n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#D43900"}}):n.createElement(dg,null,e),"Received"),Qn=i(({value:e,parsed:t})=>t?typeof e=="string"&&e.startsWith("called with")?n.createElement(n.Fragment,null,e):n.createElement(Et,{showObjectInspector:!0,value:e,style:{color:"#16B242"}}):n.createElement(cg,null,e),"Expected"),vi=i(({message:e,style:t={}})=>{let r=cn(),a=e.split(`
`);return n.createElement("pre",{style:{margin:0,padding:"8px 10px 8px 36px",fontSize:Fe.size.s1,...t}},a.flatMap((o,c)=>{if(o.startsWith("expect(")){let f=Ei(o,7),p=f?7+f.length:0,h=f&&o.slice(p).match(/\.(to|last|nth)[A-Z]\w+\(/);if(h){let g=p+(h.index??0)+h[0].length,E=Ei(o,g);if(E)return["expect(",n.createElement(Zn,{key:`received_${f}`,value:f}),o.slice(p,g),n.createElement(Qn,{key:`expected_${E}`,value:E}),o.slice(g+E.length),n.createElement("br",{key:`br${c}`})]}}if(o.match(/^\s*- /))return[n.createElement(Qn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];if(o.match(/^\s*\+ /)||o.match(/^Received: $/))return[n.createElement(Zn,{key:o+c,value:o}),n.createElement("br",{key:`br${c}`})];let[,l,u]=o.match(/^(Expected|Received): (.*)$/)||[];if(l&&u)return l==="Expected"?["Expected: ",n.createElement(Qn,{key:o+c,value:Xn(u),parsed:!0}),n.createElement("br",{key:`br${c}`})]:["Received: ",n.createElement(Zn,{key:o+c,value:Xn(u),parsed:!0}),n.createElement("br",{key:`br${c}`})];let[,s,d]=o.match(/(Expected number|Received number|Number) of calls: (\d+)$/i)||[];if(s&&d)return[`${s} of calls: `,n.createElement(Et,{key:o+c,value:Number(d)}),n.createElement("br",{key:`br${c}`})];let[,m]=o.match(/^Received has value: (.+)$/)||[];return m?["Received has value: ",n.createElement(Et,{key:o+c,value:Xn(m)}),n.createElement("br",{key:`br${c}`})]:[n.createElement("span",{key:o+c,dangerouslySetInnerHTML:{__html:r.toHtml(o)}}),n.createElement("br",{key:`br${c}`})]}))},"MatcherResult"),pg=b.div({width:14,height:14,display:"flex",alignItems:"center",justifyContent:"center"}),yc=i(({status:e})=>{let t=ye();switch(e){case"done":return n.createElement(hr,{color:t.color.positive,"data-testid":"icon-done"});case"error":return n.createElement(Fo,{color:t.color.negative,"data-testid":"icon-error"});case"active":return n.createElement(Bo,{color:t.color.secondary,"data-testid":"icon-active"});case"waiting":return n.createElement(pg,{"data-testid":"icon-waiting"},n.createElement(br,{color:W(.5,"#CCCCCC"),size:6}));default:return null}},"StatusIcon"),mg=b.div({fontFamily:Fe.fonts.mono,fontSize:Fe.size.s1,overflowWrap:"break-word",inlineSize:"calc( 100% - 40px )"}),fg=b("div",{shouldForwardProp:i(e=>!["call","pausedAt"].includes(e.toString()),"shouldForwardProp")})(({theme:e,call:t})=>({position:"relative",display:"flex",flexDirection:"column",borderBottom:`1px solid ${e.appBorderColor}`,fontFamily:Fe.fonts.base,fontSize:13,...t.status==="error"&&{backgroundColor:e.base==="dark"?W(.93,e.color.negative):e.background.warning},paddingLeft:(t.ancestors?.length??0)*20}),({theme:e,call:t,pausedAt:r})=>r===t.id&&{"&::before":{content:'""',position:"absolute",top:-5,zIndex:1,borderTop:"4.5px solid transparent",borderLeft:`7px solid ${e.color.warning}`,borderBottom:"4.5px solid transparent"},"&::after":{content:'""',position:"absolute",top:-1,zIndex:1,width:"100%",borderTop:`1.5px solid ${e.color.warning}`}}),hg=b.div(({theme:e,isInteractive:t})=>({display:"flex","&:hover":t?{}:{background:e.background.hoverable}})),gg=b("button",{shouldForwardProp:i(e=>!["call"].includes(e.toString()),"shouldForwardProp")})(({theme:e,disabled:t,call:r})=>({flex:1,display:"grid",background:"none",border:0,gridTemplateColumns:"15px 1fr",alignItems:"center",minHeight:40,margin:0,padding:"8px 15px",textAlign:"start",cursor:t||r.status==="error"?"default":"pointer","&:focus-visible":{outline:0,boxShadow:`inset 3px 0 0 0 ${r.status==="error"?e.color.warning:e.color.secondary}`,background:r.status==="error"?"transparent":e.background.hoverable},"& > div":{opacity:r.status==="waiting"?.5:1}})),bg=b.div({display:"flex",alignItems:"center",padding:6}),yg=b(G)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),Eg=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),ea=b("div")(({theme:e})=>({padding:"8px 10px 8px 36px",fontSize:Fe.size.s1,color:e.color.defaultText,pre:{margin:0,padding:0}})),vg=b.span(({theme:e})=>({color:e.base==="dark"?"#5EC1FF":"#0271B6"})),xg=b.span(({theme:e})=>({color:e.base==="dark"?"#eee":"#444"})),Ag=b.p(({theme:e})=>({color:e.base==="dark"?e.color.negative:e.color.negativeText,fontSize:e.typography.size.s2,maxWidth:500,textWrap:"balance"})),Cg=i(({exception:e})=>{let t=cn();if(!e)return null;if(e.callId===De)return P(ea,null,P("pre",null,P(vg,null,e.name,":")," ",P(xg,null,e.message)),P(Ag,null,"The component failed to render properly. Automated component tests will not run until this is resolved. Check the full error message in Storybook\u2019s canvas to debug."));if(to(e))return P(vi,{...e});if(eo(e))return P(ea,null,P(vi,{message:`${e.message}${e.diff?`

${e.diff}`:""}`,style:{padding:0}}),P("p",null,"See the full stack trace in the browser console."));let r=e.message.split(`

`),a=r.length>1;return P(ea,null,P("pre",{dangerouslySetInnerHTML:{__html:t.toHtml(r[0])}}),a&&P("p",null,"See the full stack trace in the browser console."))},"Exception"),Sg=i(({call:e,callsById:t,controls:r,controlStates:a,childCallIds:o,isHidden:c,isCollapsed:l,toggleCollapsed:u,pausedAt:s})=>{let[d,m]=R(!1),f=!a.goto||!e.interceptable||!!e.ancestors?.length;return c||e.id===De?null:P(fg,{call:e,pausedAt:s},P(hg,{isInteractive:f},P(gg,{"aria-label":"Interaction step",call:e,onClick:()=>r.goto(e.id),disabled:f,onMouseEnter:()=>a.goto&&m(!0),onMouseLeave:()=>a.goto&&m(!1)},P(yc,{status:d?"active":e.status}),P(mg,{style:{marginLeft:6,marginBottom:1}},P(ao,{call:e,callsById:t}))),P(bg,null,(o?.length??0)>0&&P(oe,{hasChrome:!1,tooltip:P(Eg,{note:`${l?"Show":"Hide"} interactions`})},P(yg,{onClick:u},P(wo,null))))),e.status==="error"&&e.exception?.callId===e.id&&P(Cg,{exception:e.exception}))},"Interaction"),wg={done:"positive",error:"negative",active:"warning",waiting:"warning"},kg=b.div(({theme:e,status:t})=>({padding:"4px 6px 4px 8px",borderRadius:"4px",backgroundColor:e.color[wg[t]],color:"white",fontFamily:Fe.fonts.base,textTransform:"uppercase",fontSize:Fe.size.s1,letterSpacing:3,fontWeight:Fe.weight.bold,width:65,textAlign:"center"})),Og={done:"Pass",error:"Fail",active:"Runs",waiting:"Runs"},Tg=i(({status:e})=>{let t=Og[e];return n.createElement(kg,{"aria-label":"Status of the test run",status:e},t)},"StatusBadge"),Ig=b.div(({theme:e})=>({boxShadow:`${e.appBorderColor} 0 -1px 0 0 inset`,background:e.background.app,position:"sticky",top:0,zIndex:1})),Dg=b.nav({height:40,display:"flex",alignItems:"center",justifyContent:"space-between",paddingLeft:15}),Bg=b(ge)(({theme:e})=>({borderRadius:4,padding:6,color:e.textMutedColor,"&:not(:disabled)":{"&:hover,&:focus-visible":{color:e.color.secondary}}})),Jt=b(Ne)(({theme:e})=>({fontFamily:e.typography.fonts.base})),tr=b(G)(({theme:e})=>({color:e.textMutedColor,margin:"0 3px"})),_g=b(bn)({marginTop:0}),Rg=b(fn)(({theme:e})=>({color:e.textMutedColor,justifyContent:"flex-end",textAlign:"right",whiteSpace:"nowrap",marginTop:"auto",marginBottom:1,paddingRight:15,fontSize:13})),xi=b.div({display:"flex",alignItems:"center"}),Ng=b(tr)({marginLeft:9}),Fg=b(Bg)({marginLeft:9,marginRight:9,marginBottom:1,lineHeight:"12px"}),Lg=b(tr)(({theme:e,animating:t,disabled:r})=>({opacity:r?.5:1,svg:{animation:t?`${e.animation.rotate360} 200ms ease-out`:void 0}})),Pg=i(({controls:e,controlStates:t,status:r,storyFileName:a,onScrollToEnd:o})=>{let c=r==="error"?"Scroll to error":"Scroll to end",l=ye();return n.createElement(Ig,null,n.createElement($t,{backgroundColor:l.background.app},n.createElement(Dg,{"aria-label":"Component tests toolbar"},n.createElement(xi,null,n.createElement(Tg,{status:r}),n.createElement(Fg,{onClick:o,disabled:!o},c),n.createElement(_g,null),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to start"})},n.createElement(Ng,{"aria-label":"Go to start",onClick:e.start,disabled:!t.start},n.createElement(Ro,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go back"})},n.createElement(tr,{"aria-label":"Go back",onClick:e.back,disabled:!t.back},n.createElement(Do,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go forward"})},n.createElement(tr,{"aria-label":"Go forward",onClick:e.next,disabled:!t.next},n.createElement(_o,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Go to end"})},n.createElement(tr,{"aria-label":"Go to end",onClick:e.end,disabled:!t.end},n.createElement(Ao,null))),n.createElement(oe,{trigger:"hover",hasChrome:!1,tooltip:n.createElement(Jt,{note:"Rerun"})},n.createElement(Lg,{"aria-label":"Rerun",onClick:e.rerun},n.createElement(Po,null)))),a&&n.createElement(xi,null,n.createElement(Rg,null,a)))))},"Subnav"),jg=b.div(({theme:{color:e,typography:t,background:r}})=>({textAlign:"start",padding:"11px 15px",fontSize:`${t.size.s2-1}px`,fontWeight:t.weight.regular,lineHeight:"1rem",background:r.app,borderBottom:`1px solid ${e.border}`,color:e.defaultText,backgroundClip:"padding-box",position:"relative",code:{fontSize:`${t.size.s1-1}px`,color:"inherit",margin:"0 0.2em",padding:"0 0.2em",background:"rgba(255, 255, 255, 0.8)",borderRadius:"2px",boxShadow:"0 0 0 1px rgba(0, 0, 0, 0.1)"}})),Mg=i(({browserTestStatus:e})=>{let t=ve().getDocsUrl({subpath:u0,versioned:!0,renderer:!0}),[r,a]=e==="error"?["the CLI","this browser"]:["this browser","the CLI"];return n.createElement(jg,null,"This interaction test passed in ",r,", but the tests failed in ",a,"."," ",n.createElement(be,{href:t,target:"_blank",withArrow:!0},"Learn what could cause this"))},"TestDiscrepancyMessage"),Ug=b.div(({theme:e})=>({height:"100%",background:e.background.content})),Ai=b.div(({theme:e})=>({borderBottom:`1px solid ${e.appBorderColor}`,backgroundColor:e.base==="dark"?W(.93,e.color.negative):e.background.warning,padding:15,fontSize:e.typography.size.s2-1,lineHeight:"19px"})),ta=b.code(({theme:e})=>({margin:"0 1px",padding:3,fontSize:e.typography.size.s1-1,lineHeight:1,verticalAlign:"top",background:"rgba(0, 0, 0, 0.05)",border:`1px solid ${e.appBorderColor}`,borderRadius:3})),Ci=b.div({paddingBottom:4,fontWeight:"bold"}),$g=b.p({margin:0,padding:"0 0 20px"}),Si=b.pre(({theme:e})=>({margin:0,padding:0,"&:not(:last-child)":{paddingBottom:16},fontSize:e.typography.size.s1-1})),Hg=he(i(function({storyUrl:e,calls:t,controls:r,controlStates:a,interactions:o,fileName:c,hasException:l,caughtException:u,unhandledErrors:s,isPlaying:d,pausedAt:m,onScrollToEnd:f,endRef:p,hasResultMismatch:h,browserTestStatus:g}){let E=cn(),y=o.some(v=>v.id!==De);return P(Ug,null,h&&P(Mg,{browserTestStatus:g}),a.detached&&(y||l)&&P(y0,{storyUrl:e}),(o.length>0||l)&&P(Pg,{controls:r,controlStates:a,status:g,storyFileName:c,onScrollToEnd:f}),P("div",{"aria-label":"Interactions list"},o.map(v=>P(Sg,{key:v.id,call:v,callsById:t,controls:r,controlStates:a,childCallIds:v.childCallIds,isHidden:v.isHidden,isCollapsed:v.isCollapsed,toggleCollapsed:v.toggleCollapsed,pausedAt:m}))),u&&!uc(u)&&P(Ai,null,P(Ci,null,"Caught exception in ",P(ta,null,"play")," function"),P(Si,{"data-chromatic":"ignore",dangerouslySetInnerHTML:{__html:E.toHtml(Sa(u))}})),s&&P(Ai,null,P(Ci,null,"Unhandled Errors"),P($g,null,"Found ",s.length," unhandled error",s.length>1?"s":""," ","while running the play function. This might cause false positive assertions. Resolve unhandled errors or ignore unhandled errors with setting the",P(ta,null,"test.dangerouslyIgnoreUnhandledErrors")," ","parameter to ",P(ta,null,"true"),"."),s.map((v,C)=>P(Si,{key:C,"data-chromatic":"ignore"},Sa(v)))),P("div",{ref:p}),!d&&!u&&!y&&P(v0,null))},"InteractionsPanel"));function Sa(e){return e.stack||`${e.name}: ${e.message}`}i(Sa,"printSerializedError");var Lr={detached:!1,start:!1,back:!1,goto:!1,next:!1,end:!1},Vg={done:"status-value:success",error:"status-value:error",active:"status-value:pending",waiting:"status-value:pending"},Pr=i(({log:e,calls:t,collapsed:r,setCollapsed:a})=>{let o=new Map,c=new Map;return e.map(({callId:l,ancestors:u,status:s})=>{let d=!1;return u.forEach(m=>{r.has(m)&&(d=!0),c.set(m,(c.get(m)||[]).concat(l))}),{...t.get(l),status:s,isHidden:d}}).map(l=>{let u=l.status==="error"&&l.ancestors&&o.get(l.ancestors.slice(-1)[0])?.status==="active"?"active":l.status;return o.set(l.id,{...l,status:u}),{...l,status:u,childCallIds:c.get(l.id),isCollapsed:r.has(l.id),toggleCollapsed:i(()=>a(s=>(s.has(l.id)?s.delete(l.id):s.add(l.id),new Set(s))),"toggleCollapsed")}})},"getInteractions"),ra=i((e,t)=>({id:De,method:"render",args:[],cursor:0,storyId:e,ancestors:[],path:[],interceptable:!0,retain:!1,exception:t}),"getInternalRenderCall"),jr=i(e=>({callId:De,status:e,ancestors:[]}),"getInternalRenderLogItem"),zg=he(i(function({refId:e,storyId:t,storyUrl:r}){let{statusValue:a,testRunId:o}=Yo(x=>{let S=e?void 0:x[t]?.[f0];return{statusValue:S?.value,testRunId:S?.data?.testRunId}}),[c,l]=St(sn,{controlStates:Lr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0}),[u,s]=R(void 0),[d,m]=R(new Set),[f,p]=R(!1),{controlStates:h=Lr,isErrored:g=!1,pausedAt:E=void 0,interactions:y=[],isPlaying:v=!1,caughtException:C=void 0,unhandledErrors:w=void 0}=c,k=X([jr("active")]),I=X(new Map([[De,ra(t)]])),T=i(({status:x,...S})=>I.current.set(S.id,S),"setCall"),B=X();j(()=>{let x;return Qe.IntersectionObserver&&(x=new Qe.IntersectionObserver(([S])=>s(S.isIntersecting?void 0:S.target),{root:Qe.document.querySelector("#panel-tab-content")}),B.current&&x.observe(B.current)),()=>x?.disconnect()},[]);let F=Ar({[ft.CALL]:T,[ft.SYNC]:x=>{k.current=[jr("done"),...x.logItems],l(S=>{let _=Pr({log:k.current,calls:I.current,collapsed:d,setCollapsed:m}),O=_.filter(({id:D,method:N})=>D!==De&&N!=="step").length;return{...S,controlStates:x.controlStates,pausedAt:x.pausedAt,interactions:_,interactionsCount:O}})},[el]:x=>{if(x.newPhase==="preparing")k.current=[jr("active")],I.current.set(De,ra(t)),l({controlStates:Lr,isErrored:!1,pausedAt:void 0,interactions:[],isPlaying:!1,hasException:!1,caughtException:void 0,interactionsCount:0,unhandledErrors:void 0});else{let S=Pr({log:k.current,calls:I.current,collapsed:d,setCollapsed:m}),_=S.filter(({id:O,method:D})=>O!==De&&D!=="step").length;l(O=>({...O,interactions:S,interactionsCount:_,isPlaying:x.newPhase==="playing",pausedAt:void 0}))}},[tl]:x=>{k.current=[jr("error")],I.current.set(De,ra(t,{...x,callId:De}));let S=Pr({log:k.current,calls:I.current,collapsed:d,setCollapsed:m});l(_=>({..._,isErrored:!0,hasException:!0,caughtException:void 0,controlStates:Lr,pausedAt:void 0,interactions:S,interactionsCount:0}))},[Qo]:x=>{l(S=>({...S,caughtException:x,hasException:!0}))},[rl]:x=>{l(S=>({...S,unhandledErrors:x,hasException:!0}))}},[d]);j(()=>{l(x=>{let S=Pr({log:k.current,calls:I.current,collapsed:d,setCollapsed:m}),_=S.filter(({id:O,method:D})=>O!==De&&D!=="step").length;return{...x,interactions:S,interactionsCount:_}})},[l,d]);let M=ce(()=>({start:i(()=>F(ft.START,{storyId:t}),"start"),back:i(()=>F(ft.BACK,{storyId:t}),"back"),goto:i(x=>F(ft.GOTO,{storyId:t,callId:x}),"goto"),next:i(()=>F(ft.NEXT,{storyId:t}),"next"),end:i(()=>F(ft.END,{storyId:t}),"end"),rerun:i(()=>{F(Zo,{storyId:t})},"rerun")}),[F,t]),H=et("fileName",""),[z]=H.toString().split("/").slice(-1),Q=i(()=>u?.scrollIntoView({behavior:"smooth",block:"end"}),"scrollToTarget"),ne=!!C||!!w||y.some(x=>x.status==="error"),A=ce(()=>!v&&(y.length>0||ne)?ne?"error":"done":v?"active":void 0,[v,y,ne]);return j(()=>{if(A&&a&&a!=="status-value:pending"&&a!==Vg[A]){let x=setTimeout(()=>p(S=>(S||F(d0,{type:"test-discrepancy",payload:{browserStatus:A==="done"?"PASS":"FAIL",cliStatus:A==="done"?"FAIL":"PASS",storyId:t,testRunId:o}}),!0)),2e3);return()=>clearTimeout(x)}else p(!1)},[F,A,a,t,o]),n.createElement(Ie,{key:"component-tests"},n.createElement(Hg,{storyUrl:r,hasResultMismatch:f,browserTestStatus:A,calls:I.current,controls:M,controlStates:{...h,detached:!!e||h.detached},interactions:y,fileName:z,hasException:ne,caughtException:C,unhandledErrors:w,isErrored:g,isPlaying:v,pausedAt:E,endRef:B,onScrollToEnd:u&&Q}))},"PanelMemoized"));function Ec(){let e=ve().getSelectedPanel(),[t={}]=St(sn),{isErrored:r,hasException:a,interactionsCount:o}=t;return n.createElement("div",{style:{display:"flex",alignItems:"center",gap:6}},n.createElement("span",null,"Interactions"),o&&!r&&!a?n.createElement(ct,{compact:!0,status:e===Qa?"active":"neutral"},o):null,r||a?n.createElement(yc,{status:"error"}):null)}i(Ec,"PanelTitle");var b3=Z.register(sn,()=>{if(globalThis?.FEATURES?.interactions){let e=i(({state:t})=>{let r=t.refId&&t.refs[t.refId]?.url||document.location.origin,{pathname:a,search:o=""}=t.location,c=a+(t.refId?o.replace(`/${t.refId}_`,"/"):o);return{refId:t.refId,storyId:t.storyId,storyUrl:r+c}},"filter");Z.add(Qa,{type:Ee.PANEL,title:i(()=>n.createElement(Ec,null),"title"),match:i(({viewMode:t})=>t==="story","match"),render:i(({active:t})=>n.createElement(Ut,{active:!!t},n.createElement(Ko,{filter:e},r=>n.createElement(zg,{...r}))),"render")})}}),wa="storybook/background",Gr="backgrounds",S3={UPDATE:`${wa}/update`},qg={light:{name:"light",value:"#F8F8F8"},dark:{name:"dark",value:"#333"}},Gg=he(i(function(){let e=et(Gr),[t,r,a]=Ve(),[o,c]=R(!1),{options:l=qg,disable:u=!0}=e||{};if(u)return null;let s=t[Gr]||{},d=s.value,m=s.grid||!1,f=l[d],p=!!a?.[Gr],h=Object.keys(l).length;return n.createElement(Wg,{length:h,backgroundMap:l,item:f,updateGlobals:r,backgroundName:d,setIsTooltipVisible:c,isLocked:p,isGridActive:m,isTooltipVisible:o})},"BackgroundSelector")),Wg=he(i(function(e){let{item:t,length:r,updateGlobals:a,setIsTooltipVisible:o,backgroundMap:c,backgroundName:l,isLocked:u,isGridActive:s,isTooltipVisible:d}=e,m=$(f=>{a({[Gr]:f})},[a]);return n.createElement(Ie,null,n.createElement(G,{key:"grid",active:s,disabled:u,title:"Apply a grid to the preview",onClick:()=>m({value:l,grid:!s})},n.createElement(Co,null)),r>0?n.createElement(oe,{key:"background",placement:"top",closeOnOutsideClick:!0,tooltip:({onHide:f})=>n.createElement(zt,{links:[...t?[{id:"reset",title:"Reset background",icon:n.createElement(yr,null),onClick:i(()=>{m(void 0),f()},"onClick")}]:[],...Object.entries(c).map(([p,h])=>({id:p,title:h.name,icon:n.createElement(br,{color:h?.value||"grey"}),active:p===l,onClick:i(()=>{m({value:p,grid:s}),f()},"onClick")}))].flat()}),onVisibleChange:o},n.createElement(G,{disabled:u,key:"background",title:"Change the background of the preview",active:!!t||d},n.createElement(Io,null))):null)},"PureTool")),w3=Z.register(wa,()=>{globalThis?.FEATURES?.backgrounds&&Z.add(wa,{title:"Backgrounds",type:Ee.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(Gg,null),"render")})}),Bt="storybook/measure-addon",vc=`${Bt}/tool`,_3={RESULT:`${Bt}/result`,REQUEST:`${Bt}/request`,CLEAR:`${Bt}/clear`},Kg=i(()=>{let[e,t]=Ve(),{measureEnabled:r}=e||{},a=ve(),o=$(()=>t({measureEnabled:!r}),[t,r]);return j(()=>{a.setAddonShortcut(Bt,{label:"Toggle Measure [M]",defaultShortcut:["M"],actionName:"measure",showInMenu:!1,action:o})},[o,a]),n.createElement(G,{key:vc,active:r,title:"Enable measure",onClick:o},n.createElement(No,null))},"Tool"),R3=Z.register(Bt,()=>{globalThis?.FEATURES?.measure&&Z.add(vc,{type:Ee.TOOL,title:"Measure",match:i(({viewMode:e,tabId:t})=>e==="story"&&!t,"match"),render:i(()=>n.createElement(Kg,null),"render")})}),ka="storybook/outline",wi="outline",Yg=he(i(function(){let[e,t]=Ve(),r=ve(),a=[!0,"true"].includes(e[wi]),o=$(()=>t({[wi]:!a}),[a]);return j(()=>{r.setAddonShortcut(ka,{label:"Toggle Outline",defaultShortcut:["alt","O"],actionName:"outline",showInMenu:!1,action:o})},[o,r]),n.createElement(G,{key:"outline",active:a,title:"Apply outlines to the preview",onClick:o},n.createElement(To,null))},"OutlineSelector")),U3=Z.register(ka,()=>{globalThis?.FEATURES?.outline&&Z.add(ka,{title:"Outline",type:Ee.TOOL,match:i(({viewMode:e,tabId:t})=>!!(e&&e.match(/^(story|docs)$/))&&!t,"match"),render:i(()=>n.createElement(Yg,null),"render")})}),_t="storybook/viewport",Wr="viewport",W3=`${_t}/panel`,Jg=`${_t}/tool`,Xg={mobile1:{name:"Small mobile",styles:{height:"568px",width:"320px"},type:"mobile"},mobile2:{name:"Large mobile",styles:{height:"896px",width:"414px"},type:"mobile"},tablet:{name:"Tablet",styles:{height:"1112px",width:"834px"},type:"tablet"},desktop:{name:"Desktop",styles:{height:"1024px",width:"1280px"},type:"desktop"}},rr={name:"Reset viewport",styles:{height:"100%",width:"100%"},type:"desktop"},xc=i((e,t)=>e.indexOf(t),"getCurrentViewportIndex"),Zg=i((e,t)=>{let r=xc(e,t);return r===e.length-1?e[0]:e[r+1]},"getNextViewport"),Qg=i((e,t)=>{let r=xc(e,t);return r<1?e[e.length-1]:e[r-1]},"getPreviousViewport"),eb=i(async(e,t,r,a)=>{await e.setAddonShortcut(_t,{label:"Previous viewport",defaultShortcut:["alt","shift","V"],actionName:"previous",action:i(()=>{r({viewport:Qg(a,t)})},"action")}),await e.setAddonShortcut(_t,{label:"Next viewport",defaultShortcut:["alt","V"],actionName:"next",action:i(()=>{r({viewport:Zg(a,t)})},"action")}),await e.setAddonShortcut(_t,{label:"Reset viewport",defaultShortcut:["alt","control","V"],actionName:"reset",action:i(()=>{r({viewport:{value:void 0,isRotated:!1}})},"action")})},"registerShortcuts"),tb=b.div({display:"inline-flex",alignItems:"center"}),ki=b.div(({theme:e})=>({display:"inline-block",textDecoration:"none",padding:10,fontWeight:e.typography.weight.bold,fontSize:e.typography.size.s2-1,lineHeight:"1",height:40,border:"none",borderTop:"3px solid transparent",borderBottom:"3px solid transparent",background:"transparent"})),rb=b(G)(()=>({display:"inline-flex",alignItems:"center"})),nb=b.div(({theme:e})=>({fontSize:e.typography.size.s2-1,marginLeft:10})),ab={desktop:n.createElement(ho,null),mobile:n.createElement(Oo,null),tablet:n.createElement(jo,null),other:n.createElement(Ie,null)},ob=i(({api:e})=>{let t=et(Wr),[r,a,o]=Ve(),[c,l]=R(!1),{options:u=Xg,disable:s}=t||{},d=r?.[Wr]||{},m=typeof d=="string"?d:d.value,f=typeof d=="string"?!1:d.isRotated,p=u[m]||rr,h=c||p!==rr,g=Wr in o,E=Object.keys(u).length;if(j(()=>{eb(e,m,a,Object.keys(u))},[u,m,a,e]),p.styles===null||!u||E<1)return null;if(typeof p.styles=="function")return console.warn("Addon Viewport no longer supports dynamic styles using a function, use css calc() instead"),null;let y=f?p.styles.height:p.styles.width,v=f?p.styles.width:p.styles.height;return s?null:n.createElement(lb,{item:p,updateGlobals:a,viewportMap:u,viewportName:m,isRotated:f,setIsTooltipVisible:l,isLocked:g,isActive:h,width:y,height:v})},"ViewportTool"),lb=n.memo(i(function(e){let{item:t,viewportMap:r,viewportName:a,isRotated:o,updateGlobals:c,setIsTooltipVisible:l,isLocked:u,isActive:s,width:d,height:m}=e,f=$(p=>c({[Wr]:p}),[c]);return n.createElement(Ie,null,n.createElement(oe,{placement:"bottom",tooltip:({onHide:p})=>n.createElement(zt,{links:[...length>0&&t!==rr?[{id:"reset",title:"Reset viewport",icon:n.createElement(yr,null),onClick:i(()=>{f(void 0),p()},"onClick")}]:[],...Object.entries(r).map(([h,g])=>({id:h,title:g.name,icon:ab[g.type],active:h===a,onClick:i(()=>{f({value:h,isRotated:!1}),p()},"onClick")}))].flat()}),closeOnOutsideClick:!0,onVisibleChange:l},n.createElement(rb,{disabled:u,key:"viewport",title:"Change the size of the preview",active:s,onDoubleClick:()=>{f({value:void 0,isRotated:!1})}},n.createElement(So,null),t!==rr?n.createElement(nb,null,t.name," ",o?"(L)":"(P)"):null)),n.createElement(Vo,{styles:{'iframe[data-is-storybook="true"]':{width:d,height:m}}}),t!==rr?n.createElement(tb,null,n.createElement(ki,{title:"Viewport width"},d.replace("px","")),u?"/":n.createElement(G,{key:"viewport-rotate",title:"Rotate viewport",onClick:()=>{f({value:a,isRotated:!o})}},n.createElement(Mo,null)),n.createElement(ki,{title:"Viewport height"},m.replace("px",""))):null)},"PureTool")),Z3=Z.register(_t,e=>{globalThis?.FEATURES?.viewport&&Z.add(Jg,{title:"viewport / media-queries",type:Ee.TOOL,match:i(({viewMode:t,tabId:r})=>t==="story"&&!r,"match"),render:i(()=>P(ob,{api:e}),"render")})}),ib="tag-filters",ub="static-filter",Q3=Z.register(ib,e=>{let t=Object.entries(Qe.TAGS_OPTIONS??{}).reduce((r,a)=>{let[o,c]=a;return c.excludeFromSidebar&&(r[o]=!0),r},{});e.experimental_setFilter(ub,r=>{let a=r.tags??[];return(a.includes("dev")||r.type==="docs")&&a.filter(o=>t[o]).length===0})});})();
}catch(e){ console.error("[Storybook] One of your manager-entries failed: " + import.meta.url, e); }
