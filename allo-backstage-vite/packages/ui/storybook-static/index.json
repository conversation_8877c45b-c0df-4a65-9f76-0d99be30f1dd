{"v": 5, "entries": {"configure-your-project--docs": {"id": "configure-your-project--docs", "title": "Configure your project", "name": "Docs", "importPath": "./src/Configure.mdx", "storiesImports": [], "type": "docs", "tags": ["dev", "test", "unattached-mdx"]}, "design-system-border-radius--docs": {"id": "design-system-border-radius--docs", "title": "Design System/Border Radius", "name": "Docs", "importPath": "./src/BorderRadius.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "design-system-border-radius--all-styles": {"type": "story", "id": "design-system-border-radius--all-styles", "name": "All Styles", "title": "Design System/Border Radius", "importPath": "./src/BorderRadius.stories.tsx", "componentPath": "./src/BorderRadiusComponent.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-border-radius--quick-reference": {"type": "story", "id": "design-system-border-radius--quick-reference", "name": "Quick Reference", "title": "Design System/Border Radius", "importPath": "./src/BorderRadius.stories.tsx", "componentPath": "./src/BorderRadiusComponent.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-border-radius-tokens--docs": {"id": "design-system-border-radius-tokens--docs", "title": "Design System/Border Radius To<PERSON>s", "name": "Docs", "importPath": "./src/BorderRadiusTokens.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "design-system-border-radius-tokens--complete": {"type": "story", "id": "design-system-border-radius-tokens--complete", "name": "Complete", "title": "Design System/Border Radius To<PERSON>s", "importPath": "./src/BorderRadiusTokens.stories.tsx", "componentPath": "./src/BorderRadiusTokens.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-border-radius-tokens--tokens-reference": {"type": "story", "id": "design-system-border-radius-tokens--tokens-reference", "name": "Tokens Reference", "title": "Design System/Border Radius To<PERSON>s", "importPath": "./src/BorderRadiusTokens.stories.tsx", "componentPath": "./src/BorderRadiusTokens.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-color-palette--docs": {"id": "design-system-color-palette--docs", "title": "Design System/Color Palette", "name": "Docs", "importPath": "./src/ColorPalette.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "design-system-color-palette--interactive": {"type": "story", "id": "design-system-color-palette--interactive", "name": "Interactive", "title": "Design System/Color Palette", "importPath": "./src/ColorPalette.stories.tsx", "componentPath": "./src/ColorPalette.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-color-palette--color-values-demo": {"type": "story", "id": "design-system-color-palette--color-values-demo", "name": "Color Values Demo", "title": "Design System/Color Palette", "importPath": "./src/ColorPalette.stories.tsx", "componentPath": "./src/ColorPalette.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-typography--docs": {"id": "design-system-typography--docs", "title": "Design System/Typography", "name": "Docs", "importPath": "./src/Typography.stories.tsx", "type": "docs", "tags": ["dev", "test", "autodocs"], "storiesImports": []}, "design-system-typography--all-styles": {"type": "story", "id": "design-system-typography--all-styles", "name": "All Styles", "title": "Design System/Typography", "importPath": "./src/Typography.stories.tsx", "componentPath": "./src/TypographyComponent.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-typography--with-numbers": {"type": "story", "id": "design-system-typography--with-numbers", "name": "With Numbers", "title": "Design System/Typography", "importPath": "./src/Typography.stories.tsx", "componentPath": "./src/TypographyComponent.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-typography--heading-sizes": {"type": "story", "id": "design-system-typography--heading-sizes", "name": "Heading Sizes", "title": "Design System/Typography", "importPath": "./src/Typography.stories.tsx", "componentPath": "./src/TypographyComponent.tsx", "tags": ["dev", "test", "autodocs"]}, "design-system-typography--body-text-sizes": {"type": "story", "id": "design-system-typography--body-text-sizes", "name": "Body Text Sizes", "title": "Design System/Typography", "importPath": "./src/Typography.stories.tsx", "componentPath": "./src/TypographyComponent.tsx", "tags": ["dev", "test", "autodocs"]}}}