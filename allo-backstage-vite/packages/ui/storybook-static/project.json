{"generatedAt": 1753292136746, "userSince": 1752848035951, "hasCustomBabel": false, "hasCustomWebpack": false, "hasStaticDirs": false, "hasStorybookEslint": true, "refCount": 0, "testPackages": {"@chromatic-com/storybook": "4.0.1", "@storybook/addon-vitest": "9.0.17", "vitest": "3.2.4"}, "hasRouterPackage": false, "monorepo": "Turborepo", "packageManager": {"type": "npm", "version": "10.8.2", "agent": "npm"}, "preview": {"usesGlobals": true}, "framework": {"name": "@storybook/react-vite", "options": {}}, "builder": "@storybook/builder-vite", "renderer": "@storybook/react", "portableStoriesFileCount": 0, "applicationFileCount": 0, "storybookVersion": "9.0.17", "language": "typescript", "storybookPackages": {"@chromatic-com/storybook": {"version": "4.0.1"}, "@storybook/addon-a11y": {"version": "9.0.17"}, "@storybook/addon-docs": {"version": "9.0.17"}, "@storybook/addon-onboarding": {"version": "9.0.17"}, "@storybook/addon-vitest": {"version": "9.0.17"}, "@storybook/react-vite": {"version": "9.0.17"}, "eslint-plugin-storybook": {"version": "9.0.17"}, "storybook": {"version": "9.0.17"}}, "addons": {"$SNIP/node_modules/@chromatic-com/storybook": {"version": null}, "$SNIP/node_modules/@storybook/addon-docs": {"version": null}, "$SNIP/node_modules/@storybook/addon-onboarding": {"version": null}, "$SNIP/node_modules/@storybook/addon-a11y": {"version": null}, "$SNIP/node_modules/@storybook/addon-vitest": {"version": null}}}