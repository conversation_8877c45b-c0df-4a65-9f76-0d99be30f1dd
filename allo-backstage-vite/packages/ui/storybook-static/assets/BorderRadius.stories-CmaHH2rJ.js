import{j as e}from"./iframe-CvZLrLnf.js";const r=[{name:"None",value:"var(--radius-none)",pixelValue:"0px",className:"rounded-none",description:"No border radius - sharp corners"},{name:"XS",value:"var(--radius-xs)",pixelValue:"2px",className:"rounded-xs",description:"Extra small radius for subtle rounding"},{name:"S",value:"var(--radius-s)",pixelValue:"4px",className:"rounded-s",description:"Small radius for buttons and small elements"},{name:"M",value:"var(--radius-m)",pixelValue:"6px",className:"rounded-m",description:"Medium radius for cards and containers"},{name:"L",value:"var(--radius-l)",pixelValue:"8px",className:"rounded-l",description:"Large radius for prominent elements"},{name:"XL",value:"var(--radius-xl)",pixelValue:"12px",className:"rounded-xl",description:"Extra large radius for special containers"},{name:"2XL",value:"var(--radius-2xl)",pixelValue:"16px",className:"rounded-2xl",description:"Very large radius for hero sections"},{name:"3XL",value:"var(--radius-3xl)",pixelValue:"24px",className:"rounded-3xl",description:"Extremely large radius for special cases"},{name:"4XL",value:"var(--radius-4xl)",pixelValue:"32px",className:"rounded-4xl",description:"Maximum radius for unique design elements"},{name:"Full",value:"var(--radius-full)",pixelValue:"100%",className:"rounded-full",description:"Fully rounded - creates circles and pills"}],d={minimal:r.slice(0,3),standard:r.slice(3,6),large:r.slice(6,9),special:[r[9]]},h=({style:s,size:a="medium"})=>{const n={small:"w-12 h-12",medium:"w-16 h-16",large:"w-20 h-20"},l={borderRadius:s.value};return e.jsxs("div",{className:"flex flex-col items-center space-y-3 p-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:`${n[a]} bg-branded-surface border-none`,style:l}),s.name==="None"&&e.jsx("div",{className:"absolute -top-1 -right-1 h-3 w-3 rounded-full border-none border-white bg-red-500"})]}),e.jsxs("div",{className:"space-y-1 text-center",children:[e.jsx("h4",{className:"text-primary text-sm font-semibold",children:s.name}),e.jsxs("div",{className:"text-secondary space-y-0.5 text-xs",children:[e.jsx("div",{children:s.value}),e.jsxs("div",{className:"text-disabled",children:["(",s.pixelValue,")"]})]}),e.jsx("code",{className:"bg-section text-primary block rounded px-2 py-1 text-xs",children:s.className})]})]})},i=({title:s,styles:a,description:n})=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-primary text-lg font-semibold",children:s}),n&&e.jsx("p",{className:"text-secondary mt-1 text-sm",children:n})]}),e.jsx("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-5",children:a.map(l=>e.jsx(h,{style:l},l.name))})]}),f=({styles:s})=>e.jsxs("div",{className:"space-y-4",children:[e.jsx("h3",{className:"text-primary text-lg font-semibold",children:"Size Comparison"}),e.jsx("div",{className:"bg-section rounded-lg p-6",children:e.jsx("div",{className:"flex flex-wrap items-end justify-center gap-6",children:s.map(a=>{const n=a.name==="Full"?"w-16 h-16":parseInt(a.pixelValue)>=16?"w-20 h-20":parseInt(a.pixelValue)>=8?"w-16 h-16":"w-12 h-12";return e.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[e.jsx("div",{className:`${n} bg-branded-surface border-card-outline border`,style:{borderRadius:a.value}}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-primary text-xs font-medium",children:a.name}),e.jsx("div",{className:"text-disabled text-xs",children:a.pixelValue})]})]},a.name)})})})]}),y=({showComparison:s=!0,showCategories:a=!0})=>e.jsxs("div",{className:"mx-auto max-w-7xl space-y-8 p-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-primary text-3xl font-bold",children:"Border Radius System"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("p",{className:"text-secondary",children:"Our border radius system provides consistent corner rounding across all interface elements. From sharp corners to fully rounded elements, these values ensure visual harmony and hierarchy."}),e.jsxs("div",{className:"text-disabled flex gap-4 text-sm",children:[e.jsxs("span",{children:["Total Styles: ",r.length]}),e.jsx("span",{children:"•"}),e.jsx("span",{children:"Range: 0px - 32px + Full"}),e.jsx("span",{children:"•"}),e.jsx("span",{children:"Categories: 4"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-primary text-xl font-semibold",children:"All Border Radius Styles"}),e.jsx("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-5 lg:grid-cols-10",children:r.map(n=>e.jsx(h,{style:n,size:"small"},n.name))})]}),s&&e.jsx(f,{styles:r}),a&&e.jsxs("div",{className:"space-y-8",children:[e.jsx("h2",{className:"text-primary text-xl font-semibold",children:"Border Radius Categories"}),e.jsx(i,{title:"Minimal Rounding",styles:d.minimal,description:"Subtle or no rounding for clean, sharp interfaces"}),e.jsx(i,{title:"Standard Rounding",styles:d.standard,description:"Most commonly used radius values for cards, buttons, and containers"}),e.jsx(i,{title:"Large Rounding",styles:d.large,description:"Prominent rounding for hero sections and special elements"}),e.jsx(i,{title:"Special Cases",styles:d.special,description:"Fully rounded elements for circles, pills, and avatars"})]})]});y.__docgenInfo={description:"",methods:[],displayName:"BorderRadius",props:{showComparison:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},showCategories:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}}}};const g={title:"Design System/Border Radius",component:y,parameters:{layout:"fullscreen",docs:{description:{component:`
Our border radius system provides a consistent set of corner rounding values that create visual hierarchy and maintain design consistency across all interface elements.

## Border Radius Scale

The system includes 10 distinct radius values:

### Minimal Rounding (0-4px)
- **None (0px)**
- **XS (2px)**
- **S (4px)**

### Standard Rounding (6-12px)
- **M (6px)**
- **L (8px)**
- **XL (12px)**

### Large Rounding (16-32px)
- **2XL (16px)**
- **3XL (24px)**
- **4XL (32px)**

### Special Cases
- **Full (100%)**: Fully rounded for circles, pills, and avatars

### CSS Implementation
Use the provided utility classes for consistent application:
\`\`\`css
.rounded-none   /* 0px */
.rounded-xs     /* 2px */
.rounded-s      /* 4px */
.rounded-m      /* 6px */
.rounded-l      /* 8px */
.rounded-xl     /* 12px */
.rounded-2xl    /* 16px */
.rounded-3xl    /* 24px */
.rounded-4xl    /* 32px */
.rounded-full   /* 100% */
\`\`\`
        `}}},tags:["autodocs"],argTypes:{showComparison:{control:"boolean",description:"Show size comparison section"},showCategories:{control:"boolean",description:"Show categorized border radius groups"}}},t={args:{showComparison:!0,showCategories:!0},parameters:{docs:{description:{story:"Complete overview of all border radius styles in the design system with visual examples and usage guidelines."}}}},o={render:()=>e.jsxs("div",{className:"space-y-6 p-6",children:[e.jsx("h1",{className:"mb-6 text-2xl font-bold",children:"Border Radius Quick Reference"}),e.jsxs("div",{className:"bg-section rounded-l p-6",children:[e.jsx("h3",{className:"text-primary mb-4 text-lg font-semibold",children:"Visual Reference"}),e.jsx("div",{className:"mb-6 grid grid-cols-5 gap-4 md:grid-cols-10",children:[{name:"None",value:"0",class:"rounded-none"},{name:"XS",value:"2px",class:"rounded-xs"},{name:"S",value:"4px",class:"rounded-s"},{name:"M",value:"6px",class:"rounded-m"},{name:"L",value:"8px",class:"rounded-l"},{name:"XL",value:"12px",class:"rounded-xl"},{name:"2XL",value:"16px",class:"rounded-2xl"},{name:"3XL",value:"24px",class:"rounded-3xl"},{name:"4XL",value:"32px",class:"rounded-4xl"},{name:"Full",value:"100%",class:"rounded-full"}].map(s=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`bg-branded-surface mx-auto mb-2 h-12 w-12 ${s.class}`}),e.jsx("div",{className:"text-primary text-xs font-medium",children:s.name}),e.jsx("div",{className:"text-secondary text-xs",children:s.value})]},s.name))}),e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-background-divider border-b",children:[e.jsx("th",{className:"text-primary py-2 text-left font-medium",children:"Name"}),e.jsx("th",{className:"text-primary py-2 text-left font-medium",children:"Value"}),e.jsx("th",{className:"text-primary py-2 text-left font-medium",children:"Pixels"}),e.jsx("th",{className:"text-primary py-2 text-left font-medium",children:"CSS Class"})]})}),e.jsx("tbody",{children:[{name:"None",value:"0",pixels:"0px",class:".rounded-none"},{name:"XS",value:"0.125rem",pixels:"2px",class:".rounded-xs"},{name:"S",value:"0.25rem",pixels:"4px",class:".rounded-s"},{name:"M",value:"0.375rem",pixels:"6px",class:".rounded-m"},{name:"L",value:"0.5rem",pixels:"8px",class:".rounded-l"},{name:"XL",value:"0.75rem",pixels:"12px",class:".rounded-xl"},{name:"2XL",value:"1rem",pixels:"16px",class:".rounded-2xl"},{name:"3XL",value:"1.5rem",pixels:"24px",class:".rounded-3xl"},{name:"4XL",value:"2rem",pixels:"32px",class:".rounded-4xl"},{name:"Full",value:"100%",pixels:"100%",class:".rounded-full"}].map((s,a)=>e.jsxs("tr",{className:a%2===0?"bg-card":"",children:[e.jsx("td",{className:"text-primary py-2 font-medium",children:s.name}),e.jsx("td",{className:"text-secondary py-2 font-mono text-xs",children:s.value}),e.jsx("td",{className:"text-secondary py-2",children:s.pixels}),e.jsx("td",{className:"text-primary bg-primary-surface rounded px-2 py-2 font-mono text-xs",children:s.class})]},s.name))})]})})]})]}),parameters:{docs:{description:{story:"Quick reference table showing all border radius values, their CSS classes, and recommended use cases."}}}};var c,m,x;t.parameters={...t.parameters,docs:{...(c=t.parameters)==null?void 0:c.docs,source:{originalSource:`{
  args: {
    showComparison: true,
    showCategories: true
  },
  parameters: {
    docs: {
      description: {
        story: "Complete overview of all border radius styles in the design system with visual examples and usage guidelines."
      }
    }
  }
}`,...(x=(m=t.parameters)==null?void 0:m.docs)==null?void 0:x.source}}};var u,p,v;o.parameters={...o.parameters,docs:{...(u=o.parameters)==null?void 0:u.docs,source:{originalSource:`{
  render: () => <div className="space-y-6 p-6">
      <h1 className="mb-6 text-2xl font-bold">Border Radius Quick Reference</h1>

      {/* Visual Reference Table */}
      <div className="bg-section rounded-l p-6">
        <h3 className="text-primary mb-4 text-lg font-semibold">
          Visual Reference
        </h3>
        <div className="mb-6 grid grid-cols-5 gap-4 md:grid-cols-10">
          {[{
          name: "None",
          value: "0",
          class: "rounded-none"
        }, {
          name: "XS",
          value: "2px",
          class: "rounded-xs"
        }, {
          name: "S",
          value: "4px",
          class: "rounded-s"
        }, {
          name: "M",
          value: "6px",
          class: "rounded-m"
        }, {
          name: "L",
          value: "8px",
          class: "rounded-l"
        }, {
          name: "XL",
          value: "12px",
          class: "rounded-xl"
        }, {
          name: "2XL",
          value: "16px",
          class: "rounded-2xl"
        }, {
          name: "3XL",
          value: "24px",
          class: "rounded-3xl"
        }, {
          name: "4XL",
          value: "32px",
          class: "rounded-4xl"
        }, {
          name: "Full",
          value: "100%",
          class: "rounded-full"
        }].map(item => <div key={item.name} className="text-center">
              <div className={\`bg-branded-surface mx-auto mb-2 h-12 w-12 \${item.class}\`}></div>
              <div className="text-primary text-xs font-medium">
                {item.name}
              </div>
              <div className="text-secondary text-xs">{item.value}</div>
            </div>)}
        </div>

        {/* CSS Classes Table */}
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-background-divider border-b">
                <th className="text-primary py-2 text-left font-medium">
                  Name
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  Value
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  Pixels
                </th>
                <th className="text-primary py-2 text-left font-medium">
                  CSS Class
                </th>
              </tr>
            </thead>
            <tbody>
              {[{
              name: "None",
              value: "0",
              pixels: "0px",
              class: ".rounded-none"
            }, {
              name: "XS",
              value: "0.125rem",
              pixels: "2px",
              class: ".rounded-xs"
            }, {
              name: "S",
              value: "0.25rem",
              pixels: "4px",
              class: ".rounded-s"
            }, {
              name: "M",
              value: "0.375rem",
              pixels: "6px",
              class: ".rounded-m"
            }, {
              name: "L",
              value: "0.5rem",
              pixels: "8px",
              class: ".rounded-l"
            }, {
              name: "XL",
              value: "0.75rem",
              pixels: "12px",
              class: ".rounded-xl"
            }, {
              name: "2XL",
              value: "1rem",
              pixels: "16px",
              class: ".rounded-2xl"
            }, {
              name: "3XL",
              value: "1.5rem",
              pixels: "24px",
              class: ".rounded-3xl"
            }, {
              name: "4XL",
              value: "2rem",
              pixels: "32px",
              class: ".rounded-4xl"
            }, {
              name: "Full",
              value: "100%",
              pixels: "100%",
              class: ".rounded-full"
            }].map((row, index) => <tr key={row.name} className={index % 2 === 0 ? "bg-card" : ""}>
                  <td className="text-primary py-2 font-medium">{row.name}</td>
                  <td className="text-secondary py-2 font-mono text-xs">
                    {row.value}
                  </td>
                  <td className="text-secondary py-2">{row.pixels}</td>
                  <td className="text-primary bg-primary-surface rounded px-2 py-2 font-mono text-xs">
                    {row.class}
                  </td>
                </tr>)}
            </tbody>
          </table>
        </div>
      </div>
    </div>,
  parameters: {
    docs: {
      description: {
        story: "Quick reference table showing all border radius values, their CSS classes, and recommended use cases."
      }
    }
  }
}`,...(v=(p=o.parameters)==null?void 0:p.docs)==null?void 0:v.source}}};const b=["AllStyles","QuickReference"];export{t as AllStyles,o as QuickReference,b as __namedExportsOrder,g as default};
