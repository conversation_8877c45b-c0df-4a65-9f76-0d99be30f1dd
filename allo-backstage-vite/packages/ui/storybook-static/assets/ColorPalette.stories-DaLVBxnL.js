import{j as t}from"./iframe-CvZLrLnf.js";const s={"primary-text":"#151413","secondary-text":"#66625E","disabled-text":"#B8B4B1","high-contrast-text":"#FFFFFF","alert-text":"#AA4D37","positive-text":"#1D6E53",background:"#F6F4F2",section:"#ECE9E7",card:"#F6F4F2",floating:"#1F1E1C","branded-surface":"#FF7452","branded-light-surface":"#FFC5B7","positive-surface":"#32BD8D","primary-surface":"#ECE9E7","secondary-surface":"#E1DFDC","tertiary-surface":"#D7D4D1","light-transparent-surface":"#FFFFFF","dark-transparent-surface":"#000000","background-divider":"#E1DFDC","focus-outline":"#B8B4B1","card-outline":"#E1DFDC","branded-outline":"#BF573E","branded-light-outline":"#FFA28C","positive-outline":"#258E6A"},p={"primary-text":"#F6F4F2","secondary-text":"#A39F9B","disabled-text":"#66625E","high-contrast-text":"#FFFFFF","alert-text":"#FF8060","positive-text":"#54C8A0",background:"#292826",section:"#33312F",card:"#484542",floating:"#1F1E1C","branded-surface":"#FF7452","branded-light-surface":"#FF977D","positive-surface":"#32BD8D","primary-surface":"#33312F","secondary-surface":"#3D3B39","tertiary-surface":"#484542","light-transparent-surface":"#FFFFFF","dark-transparent-surface":"#000000","background-divider":"#3D3B39","focus-outline":"#66625E","card-outline":"#5C5855","branded-outline":"#FF8B6F","branded-light-outline":"#FFB9A8","positive-outline":"#54C8A0"},x={"primary-text":"#151741","secondary-text":"#252858","disabled-text":"#6A6C9E","high-contrast-text":"#FFFFFF","alert-text":"#D14BC4","positive-text":"#2EB8B3",background:"#9C9EC4",section:"#888AB5",card:"#B0B2D3",floating:"#040529","branded-surface":"#D14BC4","branded-light-surface":"#FF977D","positive-surface":"#2EB8B3","primary-surface":"#A6A8CB","secondary-surface":"#9C9EC4","tertiary-surface":"#9294BC","light-transparent-surface":"#FFFFFF","dark-transparent-surface":"#000000","background-divider":"#7E80AD","focus-outline":"#6A6C9E","card-outline":"#7E80AD","branded-outline":"#852F7D","branded-light-outline":"#EC74E1","positive-outline":"#299E9A"},b={light:s,dark:p,custom:x},f=()=>Object.entries(s).map(([a,e])=>({key:a,value:e,name:a.split("-").map(r=>r.charAt(0).toUpperCase()+r.slice(1)).join(" ")})),v=()=>({text:Object.entries(s).filter(([e])=>e.includes("text")),background:Object.entries(s).filter(([e])=>e.includes("background")),surface:Object.entries(s).filter(([e])=>e.includes("surface")),outline:Object.entries(s).filter(([e])=>e.includes("outline")||e.includes("divider"))}),y=({name:a,value:e,className:r})=>{const o=()=>{navigator.clipboard.writeText(e)};return t.jsxs("div",{className:"group cursor-pointer rounded-lg border border-gray-200 p-3 transition-all hover:shadow-md",onClick:o,title:`Click to copy ${e}`,children:[t.jsx("div",{className:`mb-2 h-16 w-full ${r} rounded-md`,style:{backgroundColor:e}}),t.jsx("div",{className:"text-sm font-medium text-gray-900",children:a}),t.jsx("div",{className:"font-mono text-xs text-gray-500",children:e}),t.jsx("div",{className:"mt-1 font-mono text-xs text-blue-600",children:r})]})},F=({title:a,colors:e})=>t.jsxs("div",{className:"mb-8",children:[t.jsx("h3",{className:"mb-4 text-lg font-semibold text-gray-900",children:a}),t.jsx("div",{className:"grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6",children:e.map(r=>t.jsx(y,{name:r.name,value:r.value,className:r.className},r.name))})]}),i=({theme:a="light"})=>{const e=b[a],r=[{title:"Text",colors:[{name:"Primary Text",value:e["primary-text"],className:"text-primary"},{name:"Secondary Text",value:e["secondary-text"],className:"text-secondary"},{name:"Disabled Text",value:e["disabled-text"],className:"text-disabled"},{name:"High Contrast Text",value:e["high-contrast-text"],className:"text-high-contrast"},{name:"Alert Text",value:e["alert-text"],className:"text-alert"},{name:"Positive Text",value:e["positive-text"],className:"text-positive"}]},{title:"Backgrounds & Surfaces",colors:[{name:"Primary Background",value:e.background,className:"bg-background"},{name:"Section Background",value:e.section,className:"bg-section"},{name:"Card Background",value:e.card,className:"bg-card"},{name:"Floating Background",value:e.floating,className:"bg-floating"},{name:"Branded Surface",value:e["branded-surface"],className:"bg-branded-surface"},{name:"Branded Light Surface",value:e["branded-light-surface"],className:"bg-branded-light-surface"},{name:"Positive Surface",value:e["positive-surface"],className:"bg-positive-surface"},{name:"Primary Surface",value:e["primary-surface"],className:"bg-primary-surface"},{name:"Secondary Surface",value:e["secondary-surface"],className:"bg-secondary-surface"},{name:"Tertiary Surface",value:e["tertiary-surface"],className:"bg-tertiary-surface"},{name:"Light Transparent Surface",value:e["light-transparent-surface"],className:"bg-light-transparent-surface"},{name:"Dark Transparent Surface",value:e["dark-transparent-surface"],className:"bg-dark-transparent-surface"}]},{title:"Outlines & Dividers",colors:[{name:"Background Divider",value:e["background-divider"],className:"border-background-divider"},{name:"Focus Outline",value:e["focus-outline"],className:"border-focus-outline"},{name:"Card Outline",value:e["card-outline"],className:"border-card-outline"},{name:"Branded Outline",value:e["branded-outline"],className:"border-branded-outline"},{name:"Branded Light Outline",value:e["branded-light-outline"],className:"border-branded-light-outline"},{name:"Positive Outline",value:e["positive-outline"],className:"border-positive-outline"}]}];return t.jsxs("div",{className:"mx-auto max-w-7xl p-6",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"mb-2 text-3xl font-bold text-gray-900",children:"Color Palette"}),t.jsxs("p",{className:"text-gray-600",children:["Click on any color swatch to copy its hex value to clipboard. Current theme: ",t.jsx("span",{className:"font-semibold",children:a})]})]}),r.map(o=>t.jsx(F,{title:o.title,colors:o.colors},o.title))]})};i.__docgenInfo={description:"",methods:[],displayName:"ColorPalette",props:{theme:{required:!1,tsType:{name:"union",raw:'"light" | "dark" | "custom"',elements:[{name:"literal",value:'"light"'},{name:"literal",value:'"dark"'},{name:"literal",value:'"custom"'}]},description:"",defaultValue:{value:'"light"',computed:!1}}}};const N={title:"Design System/Color Palette",component:i,parameters:{layout:"fullscreen",docs:{description:{component:`
This component showcases the default Color Palette with interactive swatches. 
You can switch between different themes using the theme selector in the toolbar.

## Features

- **Interactive Color Swatches**: Click any color to copy its hex value to clipboard
- **Theme Support**: Switch between Light, Dark, and Custom themes
- **Responsive Grid**: Adapts to different screen sizes
- **Complete Palette**: Shows all major Tailwind color families

## Usage

The color palette is organized by color families (Gray, Blue, Green, Red, Yellow, Purple) 
with shades from 50 (lightest) to 900 (darkest).

## Themes

- **Light**: Default light theme with standard colors
- **Dark**: Dark theme with inverted background
- **Custom**: Purple-themed custom variant

Click on any color swatch to copy the hex value to your clipboard for use in your designs.
        `}}},tags:["autodocs"],argTypes:{theme:{control:"select",options:["light","dark","custom"],description:"Theme variant for the color palette"}}},l={render:(a,{globals:e})=>{const r=e.theme||a.theme||"light";return t.jsx(i,{...a,theme:r})},args:{theme:"light"},parameters:{docs:{description:{story:"This story automatically responds to the global theme selector in the toolbar. Change the theme using the paintbrush icon in the toolbar to see the palette adapt."}}}},c={render:()=>{const a=f(),e=v();return t.jsxs("div",{className:"space-y-8 p-6",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"mb-4 text-2xl font-bold",children:"Extracted Color Values Object"}),t.jsx("p",{className:"mb-4 text-gray-600",children:"All colors are now available as a typed object with slugified keys:"}),t.jsx("pre",{className:"overflow-x-auto rounded-lg bg-gray-100 p-4 text-sm",children:t.jsx("code",{children:JSON.stringify(s,null,2)})})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"mb-4 text-xl font-semibold",children:"Usage Examples"}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("h4",{className:"mb-2 font-medium",children:"Direct access:"}),t.jsx("pre",{className:"rounded bg-gray-100 p-3 text-sm",children:t.jsx("code",{children:`import { colorValues } from './colors';
const primaryText = colorValues['primary-text']; // "${s["primary-text"]}"`})})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"mb-2 font-medium",children:"All colors count:"}),t.jsxs("p",{className:"text-gray-700",children:["Total colors available: ",t.jsx("strong",{children:a.length})]})]}),t.jsxs("div",{children:[t.jsx("h4",{className:"mb-2 font-medium",children:"Colors by category:"}),t.jsxs("ul",{className:"space-y-1 text-gray-700",children:[t.jsxs("li",{children:["Text colors: ",t.jsx("strong",{children:e.text.length})]}),t.jsxs("li",{children:["Background colors:"," ",t.jsx("strong",{children:e.background.length})]}),t.jsxs("li",{children:["Surface colors:"," ",t.jsx("strong",{children:e.surface.length})]}),t.jsxs("li",{children:["Outline colors:"," ",t.jsx("strong",{children:e.outline.length})]})]})]})]})]})]})},parameters:{docs:{description:{story:"This story demonstrates the extracted color values object and helper functions. All colors are now available with slugified keys for easy programmatic access."}}}};var n,d,u;l.parameters={...l.parameters,docs:{...(n=l.parameters)==null?void 0:n.docs,source:{originalSource:`{
  render: (args, {
    globals
  }) => {
    const theme = globals.theme || args.theme || "light";
    return <ColorPalette {...args} theme={theme} />;
  },
  args: {
    theme: "light"
  },
  parameters: {
    docs: {
      description: {
        story: "This story automatically responds to the global theme selector in the toolbar. Change the theme using the paintbrush icon in the toolbar to see the palette adapt."
      }
    }
  }
}`,...(u=(d=l.parameters)==null?void 0:d.docs)==null?void 0:u.source}}};var m,h,g;c.parameters={...c.parameters,docs:{...(m=c.parameters)==null?void 0:m.docs,source:{originalSource:`{
  render: () => {
    const allColors = getAllColors();
    const categorizedColors = getColorsByCategory();
    return <div className="space-y-8 p-6">
        <div>
          <h2 className="mb-4 text-2xl font-bold">
            Extracted Color Values Object
          </h2>
          <p className="mb-4 text-gray-600">
            All colors are now available as a typed object with slugified keys:
          </p>
          <pre className="overflow-x-auto rounded-lg bg-gray-100 p-4 text-sm">
            <code>{JSON.stringify(colorValues, null, 2)}</code>
          </pre>
        </div>

        <div>
          <h3 className="mb-4 text-xl font-semibold">Usage Examples</h3>
          <div className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">Direct access:</h4>
              <pre className="rounded bg-gray-100 p-3 text-sm">
                <code>{\`import { colorValues } from './colors';
const primaryText = colorValues['primary-text']; // "\${colorValues["primary-text"]}"\`}</code>
              </pre>
            </div>

            <div>
              <h4 className="mb-2 font-medium">All colors count:</h4>
              <p className="text-gray-700">
                Total colors available: <strong>{allColors.length}</strong>
              </p>
            </div>

            <div>
              <h4 className="mb-2 font-medium">Colors by category:</h4>
              <ul className="space-y-1 text-gray-700">
                <li>
                  Text colors: <strong>{categorizedColors.text.length}</strong>
                </li>
                <li>
                  Background colors:{" "}
                  <strong>{categorizedColors.background.length}</strong>
                </li>
                <li>
                  Surface colors:{" "}
                  <strong>{categorizedColors.surface.length}</strong>
                </li>
                <li>
                  Outline colors:{" "}
                  <strong>{categorizedColors.outline.length}</strong>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>;
  },
  parameters: {
    docs: {
      description: {
        story: "This story demonstrates the extracted color values object and helper functions. All colors are now available with slugified keys for easy programmatic access."
      }
    }
  }
}`,...(g=(h=c.parameters)==null?void 0:h.docs)==null?void 0:g.source}}};const j=["Interactive","ColorValuesDemo"];export{c as ColorValuesDemo,l as Interactive,j as __namedExportsOrder,N as default};
