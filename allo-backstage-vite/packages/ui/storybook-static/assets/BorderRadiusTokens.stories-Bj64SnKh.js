import{j as e}from"./iframe-CvZLrLnf.js";const x=({showCSSVariables:a=!0,showUtilityClasses:d=!0})=>{const l=[{name:"None",variable:"--radius-none",value:"0",pixels:"0px",class:"rounded-none"},{name:"XS",variable:"--radius-xs",value:"0.125rem",pixels:"2px",class:"rounded-xs"},{name:"S",variable:"--radius-s",value:"0.25rem",pixels:"4px",class:"rounded-s"},{name:"M",variable:"--radius-m",value:"0.375rem",pixels:"6px",class:"rounded-m"},{name:"L",variable:"--radius-l",value:"0.5rem",pixels:"8px",class:"rounded-l"},{name:"XL",variable:"--radius-xl",value:"0.75rem",pixels:"12px",class:"rounded-xl"},{name:"2XL",variable:"--radius-2xl",value:"1rem",pixels:"16px",class:"rounded-2xl"},{name:"3XL",variable:"--radius-3xl",value:"1.5rem",pixels:"24px",class:"rounded-3xl"},{name:"4XL",variable:"--radius-4xl",value:"2rem",pixels:"32px",class:"rounded-4xl"},{name:"Full",variable:"--radius-full",value:"100%",pixels:"100%",class:"rounded-full"}];return e.jsxs("div",{className:"mx-auto max-w-7xl space-y-8 p-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-primary text-3xl font-bold",children:"Border Radius Design Tokens"}),e.jsx("p",{className:"text-secondary",children:"Border radius values are now available as CSS custom properties (design tokens) that can be used directly in your CSS or through utility classes. These tokens ensure consistency and make it easy to update values globally."})]}),a&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-primary text-xl font-semibold",children:"CSS Custom Properties"}),e.jsxs("div",{className:"bg-section rounded-l p-6",children:[e.jsx("p",{className:"text-secondary mb-4",children:"Use these CSS custom properties directly in your stylesheets:"}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-primary mb-3 font-medium",children:"Available Tokens"}),e.jsx("div",{className:"space-y-2 font-mono text-sm",children:l.map(s=>e.jsxs("div",{className:"flex items-center justify-between py-1",children:[e.jsxs("code",{className:"text-primary bg-card rounded px-2 py-1",children:["var(",s.variable,")"]}),e.jsx("span",{className:"text-secondary text-xs",children:s.pixels})]},s.name))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-primary mb-3 font-medium",children:"Usage Examples"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"bg-card border-card-outline rounded border p-4",children:[e.jsx("h5",{className:"text-primary mb-2 text-sm font-medium",children:"CSS"}),e.jsx("pre",{className:"text-secondary font-mono text-xs",children:`.my-component {
  border-radius: var(--radius-m);
}

.my-button {
  border-radius: var(--radius-s);
}

.my-avatar {
  border-radius: var(--radius-full);
}`})]}),e.jsxs("div",{className:"bg-card border-card-outline rounded border p-4",children:[e.jsx("h5",{className:"text-primary mb-2 text-sm font-medium",children:"Inline Styles"}),e.jsx("pre",{className:"text-secondary font-mono text-xs",children:`<div style={{
  borderRadius: 'var(--radius-l)'
}}>
  Content
</div>`})]})]})]})]})]})]}),d&&e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-primary text-xl font-semibold",children:"Utility Classes"}),e.jsxs("div",{className:"bg-section rounded-l p-6",children:[e.jsx("p",{className:"text-secondary mb-4",children:"Pre-built utility classes for quick application:"}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-primary mb-3 font-medium",children:"Available Classes"}),e.jsx("div",{className:"space-y-2",children:l.map(s=>e.jsxs("div",{className:"flex items-center justify-between py-1",children:[e.jsxs("code",{className:"text-primary bg-card rounded px-2 py-1 font-mono text-sm",children:[".",s.class]}),e.jsx("span",{className:"text-secondary text-xs",children:s.pixels})]},s.name))})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-primary mb-3 font-medium",children:"HTML Examples"}),e.jsx("div",{className:"space-y-3",children:e.jsx("div",{className:"bg-card border-card-outline rounded border p-4",children:e.jsx("pre",{className:"text-secondary font-mono text-xs",children:`<div class="rounded-m bg-card p-4">
  Standard card
</div>

<button class="rounded-s px-4 py-2">
  Button
</button>

<img class="rounded-full w-12 h-12">
  Avatar
</img>`})})})]})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("h2",{className:"text-primary text-xl font-semibold",children:"Visual Examples"}),e.jsx("div",{className:"bg-section rounded-l p-6",children:e.jsx("div",{className:"grid grid-cols-2 gap-4 md:grid-cols-5 lg:grid-cols-10",children:l.map(s=>e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"bg-branded-surface mx-auto mb-2 h-12 w-12",style:{borderRadius:`var(${s.variable})`}}),e.jsx("div",{className:"text-primary text-xs font-medium",children:s.name}),e.jsx("div",{className:"text-secondary text-xs",children:s.pixels}),e.jsx("code",{className:"text-disabled mt-1 block font-mono text-xs",children:s.variable})]},s.name))})})]})]})};x.__docgenInfo={description:"Component demonstrating CSS custom properties for border radius",methods:[],displayName:"BorderRadiusTokens",props:{showCSSVariables:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}},showUtilityClasses:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"true",computed:!1}}}};const v={title:"Design System/Border Radius Tokens",component:x,parameters:{layout:"fullscreen",docs:{description:{component:`
Border radius values are now implemented as CSS custom properties (design tokens) that provide a consistent, 
maintainable, and theme-aware approach to corner rounding throughout the application.

## What are Design Tokens?

Design tokens are named entities that store visual design attributes. They're the single source of truth for 
design decisions and help maintain consistency across products and platforms.

## CSS Custom Properties

All border radius values are available as CSS custom properties:

\`\`\`css
/* Available tokens */
var(--radius-none)   /* 0 */
var(--radius-xs)     /* 0.125rem / 2px */
var(--radius-s)      /* 0.25rem / 4px */
var(--radius-m)      /* 0.375rem / 6px */
var(--radius-l)      /* 0.5rem / 8px */
var(--radius-xl)     /* 0.75rem / 12px */
var(--radius-2xl)    /* 1rem / 16px */
var(--radius-3xl)    /* 1.5rem / 24px */
var(--radius-4xl)    /* 2rem / 32px */
var(--radius-full)   /* 100% */
\`\`\`

## Usage Examples

### In CSS
\`\`\`css
.my-component {
  border-radius: var(--radius-m);
}

.my-button {
  border-radius: var(--radius-s);
}
\`\`\`

### In React (inline styles)
\`\`\`jsx
<div style={{ borderRadius: 'var(--radius-l)' }}>
  Content
</div>
\`\`\`

### With Utility Classes
\`\`\`html
<div class="rounded-m">Standard card</div>
<button class="rounded-s">Button</button>
<img class="rounded-full" />
\`\`\`

## Benefits

1. **Consistency**: Single source of truth for all radius values
2. **Maintainability**: Update values globally from one location
3. **Theme Support**: Can be overridden per theme
4. **Performance**: CSS custom properties are efficient
5. **Developer Experience**: Clear, semantic naming

## Theme Integration

Border radius tokens are defined in the main theme and inherited by all theme variants:

\`\`\`css
@theme {
  --radius-none: 0;
  --radius-xs: 0.125rem;
  --radius-s: 0.25rem;
  /* ... */
}
\`\`\`

Different themes can override these values if needed for brand-specific requirements.
        `}}},tags:["autodocs"],argTypes:{showCSSVariables:{control:"boolean",description:"Show CSS custom properties documentation"},showUtilityClasses:{control:"boolean",description:"Show utility classes documentation"}}},r={args:{showCSSVariables:!0,showUtilityClasses:!0},parameters:{docs:{description:{story:"Complete documentation of border radius design tokens including CSS custom properties and utility classes."}}}},n={render:()=>e.jsxs("div",{className:"space-y-6 p-6",children:[e.jsx("h1",{className:"text-primary mb-6 text-2xl font-bold",children:"Border Radius Tokens Quick Reference"}),e.jsx("div",{className:"bg-section rounded-l p-6",children:e.jsxs("table",{className:"w-full text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-divider border-b",children:[e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"Token Name"}),e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"CSS Variable"}),e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"Value"}),e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"Pixels"}),e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"Utility Class"}),e.jsx("th",{className:"text-primary py-3 text-left font-medium",children:"Preview"})]})}),e.jsx("tbody",{children:[{name:"None",variable:"--radius-none",value:"0",pixels:"0px",class:".rounded-none"},{name:"XS",variable:"--radius-xs",value:"0.125rem",pixels:"2px",class:".rounded-xs"},{name:"S",variable:"--radius-s",value:"0.25rem",pixels:"4px",class:".rounded-s"},{name:"M",variable:"--radius-m",value:"0.375rem",pixels:"6px",class:".rounded-m"},{name:"L",variable:"--radius-l",value:"0.5rem",pixels:"8px",class:".rounded-l"},{name:"XL",variable:"--radius-xl",value:"0.75rem",pixels:"12px",class:".rounded-xl"},{name:"2XL",variable:"--radius-2xl",value:"1rem",pixels:"16px",class:".rounded-2xl"},{name:"3XL",variable:"--radius-3xl",value:"1.5rem",pixels:"24px",class:".rounded-3xl"},{name:"4XL",variable:"--radius-4xl",value:"2rem",pixels:"32px",class:".rounded-4xl"},{name:"Full",variable:"--radius-full",value:"100%",pixels:"100%",class:".rounded-full"}].map((a,d)=>e.jsxs("tr",{className:d%2===0?"bg-card":"",children:[e.jsx("td",{className:"text-primary py-3 font-medium",children:a.name}),e.jsx("td",{className:"py-3",children:e.jsx("code",{className:"text-primary rounded px-2 py-1 font-mono text-xs",children:a.variable})}),e.jsx("td",{className:"text-secondary py-3 font-mono text-xs",children:a.value}),e.jsx("td",{className:"text-secondary py-3",children:a.pixels}),e.jsx("td",{className:"py-3",children:e.jsx("code",{className:"text-primary rounded px-2 py-1 font-mono text-xs",children:a.class})}),e.jsx("td",{className:"py-3",children:e.jsx("div",{className:"bg-branded h-8 w-8",style:{borderRadius:`var(${a.variable})`}})})]},a.name))})]})}),e.jsxs("div",{className:"grid gap-6 md:grid-cols-2",children:[e.jsxs("div",{className:"bg-card border-card rounded-l border p-4",children:[e.jsx("h3",{className:"text-primary mb-3 font-semibold",children:"CSS Usage"}),e.jsx("pre",{className:"text-secondary bg-section rounded p-3 font-mono text-xs",children:`.component {
  border-radius: var(--radius-m);
}

.button {
  border-radius: var(--radius-s);
}

.avatar {
  border-radius: var(--radius-full);
}`})]}),e.jsxs("div",{className:"bg-card border-card rounded-l border p-4",children:[e.jsx("h3",{className:"text-primary mb-3 font-semibold",children:"HTML Usage"}),e.jsx("pre",{className:"text-secondary bg-section rounded p-3 font-mono text-xs",children:`<div class="rounded-m">
  Card content
</div>

<button class="rounded-s">
  Button
</button>

<img class="rounded-full" />`})]})]})]}),parameters:{docs:{description:{story:"Quick reference table showing all border radius tokens with their CSS variables, values, and usage examples."}}}};var t,i,o;r.parameters={...r.parameters,docs:{...(t=r.parameters)==null?void 0:t.docs,source:{originalSource:`{
  args: {
    showCSSVariables: true,
    showUtilityClasses: true
  },
  parameters: {
    docs: {
      description: {
        story: "Complete documentation of border radius design tokens including CSS custom properties and utility classes."
      }
    }
  }
}`,...(o=(i=r.parameters)==null?void 0:i.docs)==null?void 0:o.source}}};var m,c,u;n.parameters={...n.parameters,docs:{...(m=n.parameters)==null?void 0:m.docs,source:{originalSource:`{
  render: () => <div className="space-y-6 p-6">
      <h1 className="text-primary mb-6 text-2xl font-bold">
        Border Radius Tokens Quick Reference
      </h1>

      {/* Token Table */}
      <div className="bg-section rounded-l p-6">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-divider border-b">
              <th className="text-primary py-3 text-left font-medium">
                Token Name
              </th>
              <th className="text-primary py-3 text-left font-medium">
                CSS Variable
              </th>
              <th className="text-primary py-3 text-left font-medium">Value</th>
              <th className="text-primary py-3 text-left font-medium">
                Pixels
              </th>
              <th className="text-primary py-3 text-left font-medium">
                Utility Class
              </th>
              <th className="text-primary py-3 text-left font-medium">
                Preview
              </th>
            </tr>
          </thead>
          <tbody>
            {[{
            name: "None",
            variable: "--radius-none",
            value: "0",
            pixels: "0px",
            class: ".rounded-none"
          }, {
            name: "XS",
            variable: "--radius-xs",
            value: "0.125rem",
            pixels: "2px",
            class: ".rounded-xs"
          }, {
            name: "S",
            variable: "--radius-s",
            value: "0.25rem",
            pixels: "4px",
            class: ".rounded-s"
          }, {
            name: "M",
            variable: "--radius-m",
            value: "0.375rem",
            pixels: "6px",
            class: ".rounded-m"
          }, {
            name: "L",
            variable: "--radius-l",
            value: "0.5rem",
            pixels: "8px",
            class: ".rounded-l"
          }, {
            name: "XL",
            variable: "--radius-xl",
            value: "0.75rem",
            pixels: "12px",
            class: ".rounded-xl"
          }, {
            name: "2XL",
            variable: "--radius-2xl",
            value: "1rem",
            pixels: "16px",
            class: ".rounded-2xl"
          }, {
            name: "3XL",
            variable: "--radius-3xl",
            value: "1.5rem",
            pixels: "24px",
            class: ".rounded-3xl"
          }, {
            name: "4XL",
            variable: "--radius-4xl",
            value: "2rem",
            pixels: "32px",
            class: ".rounded-4xl"
          }, {
            name: "Full",
            variable: "--radius-full",
            value: "100%",
            pixels: "100%",
            class: ".rounded-full"
          }].map((token, index) => <tr key={token.name} className={index % 2 === 0 ? "bg-card" : ""}>
                <td className="text-primary py-3 font-medium">{token.name}</td>
                <td className="py-3">
                  <code className="text-primary rounded px-2 py-1 font-mono text-xs">
                    {token.variable}
                  </code>
                </td>
                <td className="text-secondary py-3 font-mono text-xs">
                  {token.value}
                </td>
                <td className="text-secondary py-3">{token.pixels}</td>
                <td className="py-3">
                  <code className="text-primary rounded px-2 py-1 font-mono text-xs">
                    {token.class}
                  </code>
                </td>
                <td className="py-3">
                  <div className="bg-branded h-8 w-8" style={{
                borderRadius: \`var(\${token.variable})\`
              }} />
                </td>
              </tr>)}
          </tbody>
        </table>
      </div>

      {/* Usage Examples */}
      <div className="grid gap-6 md:grid-cols-2">
        <div className="bg-card border-card rounded-l border p-4">
          <h3 className="text-primary mb-3 font-semibold">CSS Usage</h3>
          <pre className="text-secondary bg-section rounded p-3 font-mono text-xs">
            {\`.component {
  border-radius: var(--radius-m);
}

.button {
  border-radius: var(--radius-s);
}

.avatar {
  border-radius: var(--radius-full);
}\`}
          </pre>
        </div>

        <div className="bg-card border-card rounded-l border p-4">
          <h3 className="text-primary mb-3 font-semibold">HTML Usage</h3>
          <pre className="text-secondary bg-section rounded p-3 font-mono text-xs">
            {\`<div class="rounded-m">
  Card content
</div>

<button class="rounded-s">
  Button
</button>

<img class="rounded-full" />\`}
          </pre>
        </div>
      </div>
    </div>,
  parameters: {
    docs: {
      description: {
        story: "Quick reference table showing all border radius tokens with their CSS variables, values, and usage examples."
      }
    }
  }
}`,...(u=(c=n.parameters)==null?void 0:c.docs)==null?void 0:u.source}}};const b=["Complete","TokensReference"];export{r as Complete,n as TokensReference,b as __namedExportsOrder,v as default};
