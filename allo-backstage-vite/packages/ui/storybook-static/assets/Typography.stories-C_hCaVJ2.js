import{j as e}from"./iframe-CvZLrLnf.js";const S=[{name:"3XL",fontSize:"1.668rem",fontWeight:400,lineHeight:"2.25rem",letterSpacing:"-1%",className:"text-3xl",tailwindClasses:"font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)]"},{name:"3XL Uppercase",fontSize:"1.668rem",fontWeight:400,lineHeight:"2.25rem",letterSpacing:"-1%",uppercase:!0,className:"text-3xl",tailwindClasses:"font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)] uppercase"},{name:"3XL Tabular Numbers",fontSize:"1.668rem",fontWeight:400,lineHeight:"2.25rem",letterSpacing:"-1%",tabularNums:!0,className:"text-3xl",tailwindClasses:"font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)] tabular-nums"},{name:"2XL",fontSize:"1.5rem",fontWeight:400,lineHeight:"2rem",letterSpacing:"-1%",className:"text-2xl",tailwindClasses:"font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)]"},{name:"2XL Uppercase",fontSize:"1.5rem",fontWeight:400,lineHeight:"2rem",letterSpacing:"-1%",uppercase:!0,className:"text-2xl",tailwindClasses:"font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)] uppercase"},{name:"2XL Tabular Numbers",fontSize:"1.5rem",fontWeight:400,lineHeight:"2rem",letterSpacing:"-1%",tabularNums:!0,className:"text-2xl",tailwindClasses:"font-bricolage text-2xl font-normal leading-[var(--line-height-2xl-bri)] tracking-[var(--letter-spacing-tight)] tabular-nums"},{name:"XL",fontSize:"1.313rem",fontWeight:400,lineHeight:"1.75rem",letterSpacing:"-1%",className:"text-xl",tailwindClasses:"font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)]"},{name:"XL Uppercase",fontSize:"1.313rem",fontWeight:400,lineHeight:"1.75rem",letterSpacing:"-1%",uppercase:!0,className:"text-xl",tailwindClasses:"font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)] uppercase"},{name:"XL Tabular Numbers",fontSize:"1.313rem",fontWeight:400,lineHeight:"1.75rem",letterSpacing:"-1%",tabularNums:!0,className:"text-xl",tailwindClasses:"font-bricolage text-xl font-normal leading-[var(--line-height-xl-bri)] tracking-[var(--letter-spacing-tight)] tabular-nums"},{name:"L",fontSize:"1.125rem",fontWeight:500,lineHeight:"1.5rem",letterSpacing:"0%",className:"text-l",tailwindClasses:"font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)]"},{name:"L Uppercase",fontSize:"1.125rem",fontWeight:500,lineHeight:"1.5rem",letterSpacing:"0%",uppercase:!0,className:"text-l",tailwindClasses:"font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)] uppercase"},{name:"L Tabular Numbers",fontSize:"1.125rem",fontWeight:500,lineHeight:"1.5rem",letterSpacing:"0%",tabularNums:!0,className:"text-l",tailwindClasses:"font-bricolage text-l font-medium leading-[var(--line-height-l-bri)] tracking-[var(--letter-spacing-normal)] tabular-nums"},{name:"M",fontSize:"0.938rem",fontWeight:400,lineHeight:"1.25rem",letterSpacing:"0%",className:"text-m",tailwindClasses:"font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)]"},{name:"M Uppercase",fontSize:"0.938rem",fontWeight:400,lineHeight:"1.25rem",letterSpacing:"0%",uppercase:!0,className:"text-m",tailwindClasses:"font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)] uppercase"},{name:"M Tabular Numbers",fontSize:"0.938rem",fontWeight:400,lineHeight:"1.25rem",letterSpacing:"0%",tabularNums:!0,className:"text-m",tailwindClasses:"font-bricolage text-m font-normal tracking-[var(--letter-spacing-normal)] tabular-nums"},{name:"S",fontSize:"0.75rem",fontWeight:500,lineHeight:"1rem",letterSpacing:"0%",className:"text-s",tailwindClasses:"font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)]"},{name:"S Uppercase",fontSize:"0.75rem",fontWeight:500,lineHeight:"1rem",letterSpacing:"0%",uppercase:!0,className:"text-s",tailwindClasses:"font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)] uppercase"},{name:"S Tabular Numbers",fontSize:"0.75rem",fontWeight:500,lineHeight:"1rem",letterSpacing:"0%",tabularNums:!0,className:"text-s",tailwindClasses:"font-bricolage text-s font-medium tracking-[var(--letter-spacing-normal)] tabular-nums"},{name:"XS",fontSize:"0.563rem",fontWeight:400,lineHeight:"0.75rem",letterSpacing:"0%",className:"text-xs",tailwindClasses:"font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)]"},{name:"XS Uppercase",fontSize:"0.563rem",fontWeight:400,lineHeight:"0.75rem",letterSpacing:"0%",uppercase:!0,className:"text-xs",tailwindClasses:"font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)] uppercase"},{name:"XS Tabular Numbers",fontSize:"0.563rem",fontWeight:400,lineHeight:"0.75rem",letterSpacing:"0%",tabularNums:!0,className:"text-xs",tailwindClasses:"font-bricolage text-xs font-normal leading-[var(--line-height-xs-bri)] tracking-[var(--letter-spacing-normal)] tabular-nums"}],w=()=>["3XL","2XL","XL","L","M","S","XS"].map(a=>({size:a,styles:S.filter(s=>s.name.startsWith(a))})),z=({style:t,sampleText:a="The quick brown fox jumps over the lazy dog"})=>e.jsxs("div",{className:"space-y-4 rounded-lg border border-gray-200 p-6",children:[e.jsx("div",{className:`text-primary ${t.tailwindClasses}`,children:a}),e.jsxs("div",{className:"space-y-2 border-t border-gray-100 pt-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h4",{className:"text-sm font-semibold text-gray-900",children:t.name}),e.jsx("code",{className:"rounded bg-blue-50 px-2 py-1 text-xs text-blue-700",children:t.className})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-xs text-gray-600",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Size:"})," ",t.fontSize]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Weight:"})," ",t.fontWeight]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Line Height:"})," ",t.lineHeight]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium",children:"Letter Spacing:"})," ",t.letterSpacing]})]}),(t.uppercase||t.tabularNums)&&e.jsxs("div",{className:"flex gap-2 pt-2",children:[t.uppercase&&e.jsx("span",{className:"rounded bg-purple-50 px-2 py-1 text-xs text-purple-700",children:"UPPERCASE"}),t.tabularNums&&e.jsx("span",{className:"rounded bg-green-50 px-2 py-1 text-xs text-green-700",children:"TABULAR NUMS"})]})]})]}),H=({size:t,styles:a,sampleText:s})=>e.jsxs("div",{className:"space-y-4",children:[e.jsxs("h3",{className:"border-b border-gray-200 pb-2 text-xl font-semibold text-gray-900",children:[t," Styles"]}),e.jsx("div",{className:"grid gap-6",children:a.map(n=>e.jsx(z,{style:n,sampleText:s},n.name))})]}),j=({sampleText:t="The quick brown fox jumps over the lazy dog",showNumbers:a=!1})=>{const s=w(),n=a?"0123456789":t;return e.jsxs("div",{className:"mx-auto max-w-7xl space-y-8 p-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Typography System"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("p",{className:"text-gray-600",children:["Our typography system uses ",e.jsx("strong",{children:"Bricolage Grotesque"})," as the primary font family. All text styles are designed to provide consistent hierarchy and readability across the application."]}),e.jsxs("div",{className:"flex gap-4 text-sm text-gray-500",children:[e.jsx("span",{children:"Font Family: Bricolage Grotesque Variable"}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:["Total Styles: ",S.length]}),e.jsx("span",{children:"•"}),e.jsxs("span",{children:["Size Variants: ",s.length]})]})]})]}),e.jsxs("div",{className:"space-y-4 rounded-lg bg-gray-50 p-6",children:[e.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Font Information"}),e.jsx("div",{className:"grid gap-6 md:grid-cols-2",children:e.jsxs("div",{children:[e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Font Family"}),e.jsx("p",{className:"mb-4 text-sm text-gray-600",children:"Bricolage Grotesque Variable"}),e.jsx("h4",{className:"mb-2 font-medium text-gray-900",children:"Available Weights"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-600",children:[e.jsx("div",{children:"400 - Normal"}),e.jsx("div",{children:"500 - Medium"})]})]})})]}),e.jsx("div",{className:"space-y-12",children:s.map(({size:c,styles:T})=>e.jsx(H,{size:c,styles:T,sampleText:n},c))})]})};j.__docgenInfo={description:"",methods:[],displayName:"Typography",props:{sampleText:{required:!1,tsType:{name:"string"},description:"",defaultValue:{value:'"The quick brown fox jumps over the lazy dog"',computed:!1}},showNumbers:{required:!1,tsType:{name:"boolean"},description:"",defaultValue:{value:"false",computed:!1}}}};const X={title:"Design System/Typography",component:j,parameters:{layout:"fullscreen",docs:{description:{component:`
Our typography system is built around **Bricolage Grotesque**, a modern and versatile font family that provides excellent readability across all digital interfaces.

## Font Family

**Bricolage Grotesque Variable** is used as the primary font family throughout the application. This variable font provides:

- Consistent character spacing and proportions
- Excellent readability at all sizes
- Support for tabular numbers
- Optimized for digital interfaces

## Available Styles

### Size Hierarchy
- **3XL-2XL**: Large headings and hero text
- **XL-L**: Section headings and subheadings  
- **M**: Body text and paragraphs
- **S-XS**: Small text and captions

## Font Weights
- **400 (Normal)**: Default weight for most text
- **500 (Medium)**: For emphasis and headings
        `}}},tags:["autodocs"],argTypes:{sampleText:{control:"text",description:"Custom sample text to display in all typography styles"},showNumbers:{control:"boolean",description:"Show numbers instead of sample text to demonstrate tabular number styles"}}},r={args:{sampleText:"The quick brown fox jumps over the lazy dog",showNumbers:!1},parameters:{docs:{description:{story:"Complete overview of all typography styles in the design system with sample text."}}}},i={args:{sampleText:"0123456789",showNumbers:!0},parameters:{docs:{description:{story:"Typography styles displayed with numbers to showcase tabular number variants and numeric readability."}}}},l={render:()=>e.jsxs("div",{className:"space-y-8 p-6",children:[e.jsx("h1",{className:"mb-6 text-2xl font-bold",children:"Heading Sizes (3XL - XL)"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"3XL - Page Headlines"}),e.jsx("h1",{className:"font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)]",children:"Page Headline"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"2XL - Section Headlines"}),e.jsx("h2",{className:"font-bricolage text-2xl font-normal tracking-[var(--letter-spacing-tight)]",children:"Section Headline"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"XL - Subsection Headlines"}),e.jsx("h3",{className:"font-bricolage text-xl font-normal tracking-[var(--letter-spacing-tight)]",children:"Subsection Headline"})]})]})]}),parameters:{docs:{description:{story:"Demonstration of the largest typography sizes used for headlines and hero content."}}}},o={render:()=>e.jsxs("div",{className:"space-y-8 p-6",children:[e.jsx("h1",{className:"mb-6 text-2xl font-bold",children:"Body Text Sizes (L - XS)"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"L - Large Body Text"}),e.jsx("p",{className:"font-bricolage text-l font-medium",children:"This is large body text used for important paragraphs and introductory content."})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"M - Regular Body Text"}),e.jsx("p",{className:"font-bricolage text-m font-normal",children:"This is regular body text used for most paragraph content and general reading."})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"S - Small Body Text"}),e.jsx("p",{className:"font-bricolage text-s font-medium",children:"This is small body text used for secondary information and supporting content."})]}),e.jsxs("div",{children:[e.jsx("p",{className:"mb-2 text-sm text-gray-600",children:"XS - Caption Text"}),e.jsx("p",{className:"font-bricolage text-xs font-normal",children:"This is caption text used for image captions and supplementary information."})]})]})]}),parameters:{docs:{description:{story:"Demonstration of body text sizes used for content, captions, and fine print."}}}};var m,d,p;r.parameters={...r.parameters,docs:{...(m=r.parameters)==null?void 0:m.docs,source:{originalSource:`{
  args: {
    sampleText: "The quick brown fox jumps over the lazy dog",
    showNumbers: false
  },
  parameters: {
    docs: {
      description: {
        story: "Complete overview of all typography styles in the design system with sample text."
      }
    }
  }
}`,...(p=(d=r.parameters)==null?void 0:d.docs)==null?void 0:p.source}}};var g,x,h;i.parameters={...i.parameters,docs:{...(g=i.parameters)==null?void 0:g.docs,source:{originalSource:`{
  args: {
    sampleText: "0123456789",
    showNumbers: true
  },
  parameters: {
    docs: {
      description: {
        story: "Typography styles displayed with numbers to showcase tabular number variants and numeric readability."
      }
    }
  }
}`,...(h=(x=i.parameters)==null?void 0:x.docs)==null?void 0:h.source}}};var u,f,b;l.parameters={...l.parameters,docs:{...(u=l.parameters)==null?void 0:u.docs,source:{originalSource:`{
  render: () => <div className="space-y-8 p-6">
      <h1 className="mb-6 text-2xl font-bold">Heading Sizes (3XL - XL)</h1>

      <div className="space-y-6">
        <div>
          <p className="mb-2 text-sm text-gray-600">3XL - Page Headlines</p>
          <h1 className="font-bricolage text-3xl font-normal tracking-[var(--letter-spacing-tight)]">
            Page Headline
          </h1>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">2XL - Section Headlines</p>
          <h2 className="font-bricolage text-2xl font-normal tracking-[var(--letter-spacing-tight)]">
            Section Headline
          </h2>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">
            XL - Subsection Headlines
          </p>
          <h3 className="font-bricolage text-xl font-normal tracking-[var(--letter-spacing-tight)]">
            Subsection Headline
          </h3>
        </div>
      </div>
    </div>,
  parameters: {
    docs: {
      description: {
        story: "Demonstration of the largest typography sizes used for headlines and hero content."
      }
    }
  }
}`,...(b=(f=l.parameters)==null?void 0:f.docs)==null?void 0:b.source}}};var y,N,v;o.parameters={...o.parameters,docs:{...(y=o.parameters)==null?void 0:y.docs,source:{originalSource:`{
  render: () => <div className="space-y-8 p-6">
      <h1 className="mb-6 text-2xl font-bold">Body Text Sizes (L - XS)</h1>

      <div className="space-y-6">
        <div>
          <p className="mb-2 text-sm text-gray-600">L - Large Body Text</p>
          <p className="font-bricolage text-l font-medium">
            This is large body text used for important paragraphs and
            introductory content.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">M - Regular Body Text</p>
          <p className="font-bricolage text-m font-normal">
            This is regular body text used for most paragraph content and
            general reading.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">S - Small Body Text</p>
          <p className="font-bricolage text-s font-medium">
            This is small body text used for secondary information and
            supporting content.
          </p>
        </div>

        <div>
          <p className="mb-2 text-sm text-gray-600">XS - Caption Text</p>
          <p className="font-bricolage text-xs font-normal">
            This is caption text used for image captions and supplementary
            information.
          </p>
        </div>
      </div>
    </div>,
  parameters: {
    docs: {
      description: {
        story: "Demonstration of body text sizes used for content, captions, and fine print."
      }
    }
  }
}`,...(v=(N=o.parameters)==null?void 0:N.docs)==null?void 0:v.source}}};const k=["AllStyles","WithNumbers","HeadingSizes","BodyTextSizes"];export{r as AllStyles,o as BodyTextSizes,l as HeadingSizes,i as WithNumbers,k as __namedExportsOrder,X as default};
