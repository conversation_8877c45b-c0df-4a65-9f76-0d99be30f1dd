const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-B0c5zvoT.js","./iframe-CvZLrLnf.js","./iframe-D-J4J7uy.css"])))=>i.map(i=>d[i]);
import{_ as m,e as t,r as l}from"./iframe-CvZLrLnf.js";import{renderElement as p,unmountElement as u}from"./react-18-BdH11DE3.js";import{H as h,A as E,C as d,D as x}from"./blocks-DJ6pHcpl.js";import"./index-Dh4dca46.js";var D={code:d,a:E,...h},_=class extends l.Component{constructor(){super(...arguments),this.state={hasError:!1}}static getDerivedStateFromError(){return{hasError:!0}}componentDidCatch(r){let{showException:e}=this.props;e(r)}render(){let{hasError:r}=this.state,{children:e}=this.props;return r?null:t.createElement(t.Fragment,null,e)}},w=class{constructor(){this.render=async(r,e,n)=>{let s={...D,...e==null?void 0:e.components},a=x;return new Promise((c,i)=>{m(async()=>{const{MDXProvider:o}=await import("./index-B0c5zvoT.js");return{MDXProvider:o}},__vite__mapDeps([0,1,2]),import.meta.url).then(({MDXProvider:o})=>p(t.createElement(_,{showException:i,key:Math.random()},t.createElement(o,{components:s},t.createElement(a,{context:r,docsParameter:e}))),n)).then(()=>c())})},this.unmount=r=>{u(r)}}};export{w as DocsRenderer,D as defaultComponents};
