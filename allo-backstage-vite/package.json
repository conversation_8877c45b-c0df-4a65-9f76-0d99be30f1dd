{"name": "allo-backstage", "private": true, "type": "module", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "dev:federation": "turbo run dev:federation", "lint": "turbo run lint", "format": "prettier --write --ignore-unknown \"**/*.{json,js,ts,jsx,tsx,md}\"", "build:apps:pos": "turbo run build --filter=pos", "build:apps:operations-center": "turbo run build --filter=operations-center", "start:apps:pos": "turbo run start --filter=pos", "start:apps:operations-center": "turbo run start --filter=operations-center", "test": "turbo test", "test:watch": "turbo test:watch", "test:e2e": "turbo run test:e2e --only --env-mode=loose", "test:e2e:pos": "turbo run test:e2e --only --env-mode=loose --filter=pos", "test:e2e:operations-center": "turbo run test:e2e --only --env-mode=loose --filter=operations-center", "lint-staged": "lint-staged", "check-types": "turbo run check-types", "format:check": "prettier --check \"**/*.{json,js,ts,jsx,tsx,md}\"", "prepare": "husky install", "storybook": "turbo run storybook --filter=@monorepo/ui", "storybook:build": "turbo run storybook:build --filter=@monorepo/ui"}, "packageManager": "npm@10.8.2", "nodeLinker": "node-modules", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{json,js,ts,jsx,tsx}": ["prettier --write --ignore-unknown"]}, "devDependencies": {"@monorepo/eslint-prettier-config": "*", "corepack": "^0.32.0", "eslint": "^8.57.0", "husky": "^8.0.0", "lint-staged": "^15.5.0", "prettier": "^3.5.2", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "ts-jest": "^29.2.6", "turbo": "^2.5.0"}, "optionalDependencies": {"@rollup/rollup-android-arm-eabi": "4.39.0", "@rollup/rollup-android-arm64": "4.39.0", "@rollup/rollup-darwin-x64": "4.39.0", "@rollup/rollup-freebsd-arm64": "4.39.0", "@rollup/rollup-freebsd-x64": "4.39.0", "@rollup/rollup-linux-arm-gnueabihf": "4.39.0", "@rollup/rollup-linux-arm-musleabihf": "4.39.0", "@rollup/rollup-linux-arm64-gnu": "4.39.0", "@rollup/rollup-linux-arm64-musl": "4.39.0", "@rollup/rollup-linux-loongarch64-gnu": "4.39.0", "@rollup/rollup-linux-powerpc64le-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-gnu": "4.39.0", "@rollup/rollup-linux-riscv64-musl": "4.39.0", "@rollup/rollup-linux-s390x-gnu": "4.39.0", "@rollup/rollup-linux-x64-gnu": "^4.40.2", "@rollup/rollup-linux-x64-musl": "^4.40.2"}, "workspaces": ["apps/*", "packages/*"], "engines": {"node": ">=20", "npm": ">=10 <12", "yarn": "^4"}, "browserslist": ["defaults", "IE 11", "Edge >= 16", "Safari >= 12", "Firefox ESR"]}