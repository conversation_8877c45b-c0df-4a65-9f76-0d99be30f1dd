{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"]}, "lint": {}, "dev": {"cache": false, "persistent": true}, "dev:federation": {"cache": false, "persistent": true}, "storybook": {"cache": false, "persistent": false}, "storybook:build": {"cache": false}, "test": {"cache": false}, "test:watch": {"cache": false, "persistent": true}, "check-types": {"cache": false}}}