import React, { Fragment, useEffect, useMemo, useState } from "react";
import {useRouter} from "next/router";
import {useSelector} from "react-redux";
import axios from "axios";
import { ButtonBase } from "@material-ui/core";
import InputAdornment from "@material-ui/core/InputAdornment";
import {withTranslation} from "../../../i18n";
import Avatar from "@material-ui/core/Avatar";
import Typography from "@material-ui/core/Typography";
import { accountSelectors, applicationSelectors, restaurantSelectors } from "../../../redux/selectors";
import { ChevronUpDown, CloseIcon24, MagnifierIcon, PlusIconFilled20 } from "../../utils/icons";
import { defaultLogoUrl, noop } from "../../utils/const";
import isEmpty from "../../utils/isEmpty";
import {controlStringLength} from "../../utils/sliceString";
import { views } from "../../utils/administrationRoutes";
import typography from "../../../styles/typography";
import palette from "../../../styles/palette";
import { getMyRestaurants } from "../../api";
import Field from "../form/Field";
import useStyles from "./styles";
import debounce from 'lodash.debounce';

let request = null;

const OrganizationSwitcher = ({ t, setView }) => {
	const classes = useStyles();
	const router = useRouter();
	
	const [dropdown, setDropdown] = useState(false);
	
	const account = useSelector(accountSelectors.getAccountMemo);
	const { organizations = [], id, restaurants = [], total = 0, email } = (account || {});
	const isInternalUser = email && email.indexOf("@allo.restaurant") !== -1;
	
	const isEmbedded = useSelector(applicationSelectors.getIsEmbedded);
	
	const organization = isEmpty(organizations) ? {} : organizations[0];
	
	const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
	const { name, logoUrl, acronym, mode } = { ...organization, ...restaurant };
	const resolvedLogo = logoUrl || defaultLogoUrl
	
	const [myRestaurants, setMyRestaurants] = useState(restaurants)
	useEffect(() => {
		setMyRestaurants(restaurants)
	}, [restaurants]);
	
	const [query, setQuery] = useState("")
	const [limit, setLimit] = useState(20)
	
	const hasMoreRestaurantsToShow = total > 20
	const toggleDropdown = () => setDropdown(!dropdown);
	const closeDropdown = () => setDropdown(false);
	
	const fetchMyRestaurants = (value) => {
		// cancel previous ajax if exists
		if (request) {
			request.cancel();
		}
		
		request = axios.CancelToken.source();
		
		getMyRestaurants(value, 20, request.token)
			.then(({data})=>{
				const {data: innerData} = data;
				const { getMyRestaurants = {} } = innerData;
				const { items = [] } = (getMyRestaurants || {})
				setMyRestaurants(items || [])
			})
			.catch(()=>{})
	}
	
	const debouncedOnSearch = useMemo(
		() => debounce(fetchMyRestaurants, 500)
		, []);
	
	useEffect(() => {
		return () => {
			debouncedOnSearch.cancel();
		}
	}, [])
	
	const switchToOrganization = (id) => () => {
		closeDropdown();
		router.push(`/o/${id}`).then(noop).catch(noop)
	}
	
	const switchToRestaurant = (id) => () => {
		closeDropdown();
		router.push(`/r/${id}`, undefined, { shallow: true }).then(noop).catch(noop)
	}
	
	const navigateToRestaurantCreation = () => {
		setView(views.CREATE_RESTAURANT)
	}
	
	const onLoadMore = () => {
		const newLimit = limit + 10
		setLimit(newLimit)
		getMyRestaurants("", newLimit)
			.then(({data})=>{
				const {data: innerData} = data;
				const { getMyRestaurants = {} } = innerData;
				const { items = [] } = (getMyRestaurants || {})
				setMyRestaurants(items || [])
			})
			.catch(()=>{})
	}
	
	const renderEntity = (entity, onClick) => (
		<Fragment>
			<div className={classes.container} onClick={onClick} key={entity.id}>
				<div className={classes.content} style={{ overflow: "hidden", width: "100%" }}>
					<div className={classes.left} style={{ width: "100%" }}>
						<Avatar
							alt={entity.name}
							src={entity.logoUrl || defaultLogoUrl}
							classes={{ root: classes.avatar }}
							defaultValue="allO"
							style={{ width: "24px", height: "24px", borderRadius: 8 }}
						>
							{entity.acronym}
						</Avatar>
						<Typography className={classes.restaurantName} style={{ whiteSpace: "break-spaces" }}>
							{controlStringLength(entity.name, 35)}
						</Typography>
						{entity.mode === "EXPLORATION" && (
							<div className={classes.right}>
								<div style={{ right: 0 }}>
									<Typography style={{
										...typography.mini.medium,
										color: palette.grayscale["100"],
										marginLeft: 8,
										padding: "2px 6px",
										background: palette.primary["500"],
										borderRadius: 12
									}}>
										Test
									</Typography>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>
			<div style={{ marginLeft: 24, paddingLeft: 8, borderLeft: `1px dashed ${palette.grayscale["400"]}` }}>
				{!isEmpty(entity.branchRestaurants) && entity.branchRestaurants.map((restaurant) => renderEntity(restaurant, switchToRestaurant(restaurant.id)))}
			</div>
		</Fragment>
	)

	return (
		<div className={classes.wrapper}>
			<div className={classes.container} onClick={toggleDropdown}>
				<div className={classes.content}>
					<div className={classes.left}>
						{name && (
							<Avatar
								alt={name}
								src={resolvedLogo}
								classes={{ root: classes.avatar }}
								defaultValue="allO"
							>
								{acronym}
							</Avatar>
						)}
						<Typography className={classes.restaurantName} style={{ whiteSpace: "break-spaces" }}>
							{name ? controlStringLength(name, 30) : t("switch-restaurant")}
						</Typography>
						{mode === "EXPLORATION" && (
							<div className={classes.right}>
								<div style={{ right: 0 }}>
									<Typography style={{
										...typography.mini.medium,
										color: palette.grayscale["100"],
										marginLeft: 8,
										padding: "2px 6px",
										background: palette.primary["500"],
										borderRadius: 12
									}}>
										Test
									</Typography>
								</div>
							</div>
						)}
					</div>
					<div className={classes.right}>
						<ChevronUpDown />
					</div>
				</div>
			</div>
			{dropdown && (
				<div className={classes.dropdown}>
					{hasMoreRestaurantsToShow && (
						<>
							<div className={classes.searchBar}>
								<Field
									value={query}
									onChange={(e) => {
										const value = e.target.value;
										setQuery(value)
										debouncedOnSearch(value)
									}}
									variant="filled"
									id="restaurant-search-bar-input"
									autoComplete="off"
									style={{
										border: "none",
										backgroundColor: palette.grayscale["200"],
										height: 35
									}}
									startAdornment={
										<InputAdornment position="end">
											<MagnifierIcon/>
										</InputAdornment>
									}
									endAdornment={isEmpty(query) ? null :
										(
											<ButtonBase
												disableRipple
												disableTouchRipple
												style={{ padding: 6 }}
												aria-label="clear-query"
												onClick={() => {
													setQuery("")
													debouncedOnSearch("")
												}}
											>
												<CloseIcon24 />
											</ButtonBase>
										)
									}
								/>
							</div>
						</>
					)}
					{organizations.map((organization) => renderEntity(organization, switchToOrganization(organization.id)))}
					{myRestaurants.map((restaurant) => renderEntity(restaurant, switchToRestaurant(restaurant.id)))}
					{hasMoreRestaurantsToShow && !isEmpty(myRestaurants) && (!query) && (
						<div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", padding: 12 }}>
							<ButtonBase onClick={onLoadMore} style={{ display: "flex", alignItems: "center", justifyContent: "flex-end" }} disableRipple disableTouchRipple>
								<PlusIconFilled20 />
								<Typography style={{ ...typography.body.regular, marginLeft: 8 }}>{t("load-more")}</Typography>
							</ButtonBase>
							<Typography style={{ ...typography.small.medium, color: palette.grayscale["400"], textAlign: "right" }}>
								{t("organization-switcher-showing-number-of", { number: limit, total: total })}
							</Typography>
						</div>
					)}
					{hasMoreRestaurantsToShow && isEmpty(myRestaurants) && (
						<div style={{ margin: 0, display: "flex", justifyContent: "space-around", marginBottom: 6 }}>
							<Typography style={{ ...typography.body.regular, color: palette.grayscale["400"] }}>{t("no-match-found")}</Typography>
						</div>
					)}
				</div>
			)}
			{dropdown && isInternalUser && (
				<div className={classes.dropdown}>
					<div className={classes.container} onClick={navigateToRestaurantCreation}>
						<div className={classes.content}>
							<div className={classes.left}>
								<PlusIconFilled20 />
								<Typography className={classes.restaurantName}>
									{t('add-restaurant')}
								</Typography>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	)
}

export default withTranslation('common')(OrganizationSwitcher);
