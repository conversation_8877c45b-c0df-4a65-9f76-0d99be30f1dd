import React, { useState } from 'react';
import Button from '@material-ui/core/Button';
import clsx from 'clsx';
import {withTranslation} from '../../../i18n';
import Moment from 'react-moment';
import useStyles, { newStyles, useExpendableCardStyles, useMenuStyles } from "./styles";
import isEmpty from "../../utils/isEmpty";
import moment from "moment";
import {useSelector} from "react-redux";
import { accountSelectors, restaurantSelectors, terminalSelectors } from "../../../redux/selectors";
import { orderItemStatuses, permissionIdentifiers } from "../../utils/const";
import Typography from "@material-ui/core/Typography";
import { orderTypes } from "../../../redux/constants";
import MenuItem from "@material-ui/core/MenuItem";
import Menu from "@material-ui/core/Menu";
import MoreOptionsButton from "../_buttons/MoreOptionsButton";
import palette from "../../../styles/palette";
import typography from "../../../styles/typography";
import { FlameCircleIcon16 } from "../../utils/icons";
import UpdateOrderItemsTableModal from "../_popup/UpdateOrderItemsTableModal";

const ReceiptItem = ({
  t, orderType, orderTableId, orderStatus, item = {}, id, discountId, isDiscount, isReceipt, isPayment, options = [], extras = [], numeration, creationTime, modificationTime,
  currentAccountId, name, internalName, code, total, qtd, status, ongoing, customerId, accountId, removeItem,
  notes, predefinedNotes, guest, isPreview, isPickup, handleAdd, handleEdit, cancelItem, discountItem, promotionType,
  deleteItemDiscount, nested, nestedCount, addItemByCode, cancellationNotes, cancellationPredefinedNotes, isSearch, sourceTable, targetTable, createdByAccount,
  ongoingExtrasQuantityMax: additionsCrossCategoryMax, blockMovingItem = false
}) => {
  const classes = useStyles();
  const newClasses = newStyles();
  const menuClasses = useMenuStyles();
  const expendableCardClasses = useExpendableCardStyles();
  const [selectedMenuItem, setMenuItem] = useState(null);
  
  const restaurantId = useSelector(restaurantSelectors.getRestaurantId);
  
  const isExpress = orderType === orderTypes.EXPRESS.key;
  const isDineIn = orderType === orderTypes.DINE_IN.key;
  const orderIsStillOngoing = ["CLOSED", "CANCELLED"].indexOf(orderStatus) === -1;
  
  const [menuItemMenu, setMenuItemMenu] = useState(null);
  const openMenuItemMenu = (e) => setMenuItemMenu(e.currentTarget)
  const closeMenuItemMenu = () => setMenuItemMenu(null)
  
  const [selectedTableRotation, setTableRotation] = useState(null)
  const closeTableRotation = () => setTableRotation(null)
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const canCancelOrder = permissionIds.includes(permissionIdentifiers.CAN_CANCEL_ORDER.value)
  const canDiscountOrder = permissionIds.includes(permissionIdentifiers.CAN_DISCOUNT_ORDER.value)
  const canMoveOrderItem = permissionIds.includes(permissionIdentifiers.CAN_MOVE_ORDER_ITEM.value)
  
  const { groupBy } = useSelector(terminalSelectors.getGroupBy);

  const cancelled = status === 'CANCELLED' || status === 'REMOVED';
  const confirmed = status === 'CONFIRMED';
  const paid = status === 'PAID';
  total = ((total) || 0).toFixed(2);

  const optionItems = isEmpty(options) ? [] : options.reduce((acc, next) => {
    const { items } = next;
    if (next.qtd < 2) {
      items.map((i) => {
        delete i.qtd;
        return i;
      });
    }
    acc = acc.concat(items);
    return acc;
  }, []);

  const extraItems = isEmpty(extras) ? [] : extras.reduce((acc, next) => {
    const { items = [] } = next;

    items.forEach((i) => {
      if (i.max < 2) {
        delete i.qtd;
      }
      return i;
    });
    acc = acc.concat(items);
    return acc;
  }, []);
  
  if (!isEmpty(predefinedNotes)) {
    notes = predefinedNotes.map(i => i.name).concat(notes ? [notes] : []).join(', ')
  }
  
  if (!isEmpty(cancellationPredefinedNotes)) {
    cancellationNotes = cancellationPredefinedNotes.map(i => i.name).concat(cancellationNotes ? [cancellationNotes] : []).join(', ')
  }

  // const editItem = () => {
  //   getMenuItemByCode(code)
  //     .then(({ data }) => {
  //       setMenuItem(data)
  //     })
  //     .catch(() => {})
  // }

  const updateItem = (req) => {
    // if item already has id, send with id
    if (id) {
      req.id = id
    }
    handleAdd(req)
    setMenuItem(null)
  }
  
  const addItemQtd = () => {
    handleAdd({ ...item, qtd: qtd + 1, ignoreNull: true })
  }
  
  const removeItemQtd = () => {
    if (qtd >= 2) {
      handleAdd({ ...item, qtd: qtd - 1, ignoreNull: true })
    } else {
      removeItem()
    }
  }
  
  const hasAdditionsCrossCategoryMax = !!additionsCrossCategoryMax && (additionsCrossCategoryMax < 99)
  
  const orderItemReceiptStatus = orderItemStatuses[status]
  
  const haAdditions = !isEmpty(optionItems) || !isEmpty(extraItems)

  return (
    <React.Fragment>
      <div className={clsx(newClasses.container, { [newClasses.paid]: paid && !isReceipt, [newClasses.groupedContainerStyling]: !isPayment, [newClasses.nestedContainerStyling]: nested })} style={{ background: !isEmpty(selectedMenuItem) ? palette.grayscale["250"] : null }}>
        <div>
          <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'top', justifyContent: 'space-between', verticalAlign: 'middle' }}>
            <div style={{ display: 'flex', flex: 1 }}>
              {!nested && (
                <div className={clsx(newClasses.count, { [newClasses.strike]: !!cancelled })}>
                  {<Typography className={newClasses.countBadge}>{isDiscount ? "1" :qtd}</Typography>}
                </div>
              )}
              <div style={{ flex: 1 }}>
                <div className={clsx(newClasses.bodyGroup, {[newClasses.nestedBodyGroup]: !!nested})}>
                  <div className={newClasses.itemGroup}>
                    <div className={newClasses.itemTextGroup}>
                      {!nested && (
                        <div className={clsx(newClasses.name, { [newClasses.strike]: !!cancelled })}>
                          <Typography style={{ ...typography.body.regular }}>{`${numeration ? `${numeration} ` : ''}${internalName || name}`}
                            {isDiscount && !!promotionType && <span> {t(`promotion-type-${promotionType?.toLowerCase?.()}`)}</span>}
                            {isReceipt && isPreview && (
                              <span className={newClasses.creationTime}>
                                {moment(creationTime).format("h:mm a")}
                              </span>
                            )}
                          </Typography>
                        </div>
                      )}
                      {nested && (
                        <span className={clsx({ [newClasses.strike]: !!cancelled })}>{t('terminal-orders-reorder-ongoing-confirmed-item-round-label', { count: nestedCount })}{` `}</span>
                      )}
                      {nested && (
                        <span className={clsx({ [newClasses.strike]: !!cancelled })}>(<Moment format={"HH:mm"}>
                          {modificationTime}
                        </Moment>)</span>
                      )}
                      {notes && (
                        <div className={clsx(newClasses.notes, { [newClasses.strike]: !!cancelled })}>
                          <Typography style={{ ...typography.body.regular }}>{notes}</Typography>
                        </div>
                      )}
                      {cancellationNotes && (
                        <div className={clsx(newClasses.notes)}>
                          <Typography style={{ ...typography.body.regular }}>{cancellationNotes}</Typography>
                        </div>
                      )}
                    </div>
                    {!nested && (
                      <div className={clsx({ [newClasses.strike]: !!cancelled })}>
                        <Typography style={{ ...typography.body.regular }}>{`${total}€`}</Typography>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          {haAdditions && (
            <div style={{ display: 'flex', flexDirection: 'row', flexWrap: 'wrap', alignItems: 'center', alignContent: 'flex-start' }}>
              {!isSearch && !isEmpty(optionItems) && optionItems.map(({ id, qtd, unitPrice, name, internalName }) => (
                <div key={id} className={newClasses.additionTag} style={{ background: palette.primary["100"], borderRadius: 12, padding: '2px 8px 2px 4px', display: 'flex', alignItems: 'center', marginTop: 8 }}>
                  <div style={{ display: "flex", marginRight: 2 }}>
                    <FlameCircleIcon16 />
                  </div>
                  {qtd && (
                    <Typography style={{ ...typography.small.medium, color: palette.primary["500"], marginRight: 4 }}>{qtd}</Typography>
                  )}
                  <Typography style={{ ...typography.small.medium }}>
                    {internalName || name}
                  </Typography>
                </div>
              ))}
              {!isSearch && !isEmpty(extraItems) && extraItems.map(({ id, qtd, unitPrice, name, internalName }) => (
                <div key={id} className={newClasses.additionTag} style={{ background: palette.primary["100"], borderRadius: 12, padding: '2px 8px 2px 4px', display: 'flex', alignItems: 'center', marginTop: 6 }}>
                  <div style={{ display: "flex", marginRight: 2 }}>
                    <FlameCircleIcon16 />
                  </div>
                  {qtd && (
                    <Typography style={{ ...typography.small.medium, color: palette.primary["500"], marginRight: 4 }}>{qtd}</Typography>
                  )}
                  <Typography style={{ ...typography.small.medium }}>
                    {internalName || name}
                  </Typography>
                </div>
              ))}
            </div>
          )}
          {/*{status === orderItemStatus.UNCONFIRMED && ((customerId === currentAccountId) || (accountId === currentAccountId) || guest || isPickup) && !isPreview && !isDiscount && (*/}
          {/*  <div className={newClasses.actions}>*/}
          {/*    <div style={{ display: "flex", alignItems: "center" }}>*/}
          {/*      {nested && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "5px 11px" }} onClick={removeItem} className={newClasses.action}>*/}
          {/*          <Typography style={{ ...typography.body.medium, color: palette.negative["600"] }}>*/}
          {/*            {t('order-item-remove-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*      {!ongoing && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "5px 15px 5px 9px", display: "flex", alignItems: "center" }} onClick={handleEdit || editItem} className={newClasses.action}>*/}
          {/*          <div style={{ display: "flex", marginRight: 4 }}>*/}
          {/*            <EditItemIcon20 />*/}
          {/*          </div>*/}
          {/*          <Typography style={{ ...typography.body.medium }}>*/}
          {/*            {t('order-item-edit-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*      {!ongoing && !discountId && canDiscountOrder && isExpress && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "5px 11px" }} onClick={discountItem} className={newClasses.action}>*/}
          {/*          <Typography style={{ ...typography.body.medium }}>*/}
          {/*            {t('terminal-orders-add-discount-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*    </div>*/}
          {/*    {!nested && (!hasAdditionsCrossCategoryMax || ongoing) && (*/}
          {/*      <div className={classes.quantityBtn}>*/}
          {/*        <QuantityButton counter={qtd} classes={{ root: classes.counterBtn }} onMore={addItemQtd} onLess={removeItemQtd}/>*/}
          {/*      </div>*/}
          {/*    )}*/}
          {/*  </div>*/}
          {/*)}*/}
          {/*{status === orderItemStatus.CONFIRMED && !nested && !isPreview && !isDiscount && (*/}
          {/*  <div className={newClasses.actions}>*/}
          {/*    <div style={{ display: "flex", alignItems: "center" }}>*/}
          {/*      {canCancelOrder && cancelItem && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "5px 11px" }} disableRipple disableTouchRipple onClick={cancelItem} className={newClasses.action}>*/}
          {/*          <Typography style={{ ...typography.body.medium, color: palette.negative["600"] }}>*/}
          {/*            {t('terminal-orders-cancel-confirmed-item-action-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*      {!discountId && canDiscountOrder && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "5px 11px" }} disableRipple disableTouchRipple onClick={discountItem} className={newClasses.action}>*/}
          {/*          <Typography style={{ ...typography.body.medium }}>*/}
          {/*            {t('terminal-orders-add-discount-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*      {ongoing && (*/}
          {/*        <ButtonBase style={{ border: `1px solid ${palette.grayscale["350"]}`, borderRadius: 10, padding: "6px 11px" }} disableRipple disableTouchRipple onClick={editItem} className={newClasses.action}>*/}
          {/*          <Typography style={{ ...typography.body.medium }}>*/}
          {/*            {t('terminal-orders-reorder-ongoing-confirmed-item-action-btn')}*/}
          {/*          </Typography>*/}
          {/*        </ButtonBase>*/}
          {/*      )}*/}
          {/*    </div>*/}
          {/*    /!*{ongoing && (*!/*/}
          {/*    /!*  <TimerAction*!/*/}
          {/*    /!*    starting={lastOrderTime}*!/*/}
          {/*    /!*    limit={15}*!/*/}
          {/*    /!*  >*!/*/}
          {/*    /!*    {(label, disabled) => (*!/*/}
          {/*    /!*      <Button variant="outlined" size="small" disableElevation onClick={editItem} className={classes.itemActionBtn} disabled={disabled}>*!/*/}
          {/*    /!*        {disabled ? label : t('terminal-orders-reorder-ongoing-confirmed-item-action-btn')}*!/*/}
          {/*    /!*      </Button>*!/*/}
          {/*    /!*    )}*!/*/}
          {/*    /!*  </TimerAction>*!/*/}
          {/*    /!*)}*!/*/}
          {/*    {!ongoing && (*/}
          {/*      <CounterButton classes={{ root: classes.counterBtn }} onClick={() => addItemByCode(code)}/>*/}
          {/*    )}*/}
          {/*    /!*{!ongoing && (*!/*/}
          {/*    /!*  <Button variant="outlined" size="small" disableElevation onClick={printItem} className={classes.itemActionBtn}>*!/*/}
          {/*    /!*    {t('common-reprint')}*!/*/}
          {/*    /!*  </Button>*!/*/}
          {/*    /!*)}*!/*/}
          {/*  </div>*/}
          {/*)}*/}
          {isDiscount && !isPreview && (
            <div className={newClasses.actions}>
              <Button variant="outlined" size="small" disableElevation onClick={deleteItemDiscount} className={classes.itemActionBtn}>
                {t('common-remove')}
              </Button>
            </div>
          )}
          {isReceipt && !nested && (
            <div style={{ marginTop: 8, display: "flex", alignItems: "center", justifyContent: "space-between" }}>
              <div className={expendableCardClasses.tags}>
                <Typography className={expendableCardClasses.tag} style={orderItemReceiptStatus ? { background: orderItemReceiptStatus.background || null } : null}>
                  {orderItemReceiptStatus ? t(orderItemReceiptStatus.i18nKey) : ''}
                </Typography>
                {!isEmpty(createdByAccount) && createdByAccount?.firstName?.toLowerCase() !== 'system' && (
                  <Typography className={expendableCardClasses.tag}>
                    {t('ordered-by')}
                    {` `}
                    {`${createdByAccount.firstName ?? ""} ${createdByAccount.lastName ?? ""}`}
                  </Typography>
                )}
                {!isEmpty(sourceTable) && (
                  <Typography className={expendableCardClasses.tag}>
                    {t('moved-from-table')}
                    {` `}
                    {sourceTable.label || `#${sourceTable.code}`}
                  </Typography>
                )}
                {!isEmpty(targetTable) && (
                  <Typography className={expendableCardClasses.tag}>
                    {t('moved-to-table')}
                    {` `}
                    {targetTable.label || `#${targetTable.code}`}
                  </Typography>
                )}
              </div>
              {isDineIn && orderIsStillOngoing && confirmed && canMoveOrderItem && !blockMovingItem && (
                <MoreOptionsButton onClick={openMenuItemMenu} />
              )}
            </div>
          )}
        </div>
      </div>
      {/*{selectedMenuItem && (*/}
      {/*  <MenuItemDetails*/}
      {/*    open={selectedMenuItem}*/}
      {/*    onClose={() => setMenuItem(null)}*/}
      {/*    {...selectedMenuItem}*/}
      {/*    addOrderItem={updateItem}*/}
      {/*    orderItem={item}*/}
      {/*    ongoing={ongoing}*/}
      {/*  />*/}
      {/*)}*/}
      <Menu
        id="order-item-options--menu"
        anchorEl={menuItemMenu}
        keepMounted
        open={Boolean(menuItemMenu)}
        onClose={closeMenuItemMenu}
        classes={{
          paper: menuClasses.menu
        }}
      >
        <MenuItem onClick={() => {
          closeMenuItemMenu();
          setTableRotation(orderTableId)
        }} classes={{ root: menuClasses.menuItem }}>
          <div className={menuClasses.content}>
            <div className={menuClasses.left}>
              <Typography className={menuClasses.name}>
                {t('terminal-table-rotation-label')}
              </Typography>
            </div>
          </div>
        </MenuItem>
      </Menu>
      {selectedTableRotation && (
        <UpdateOrderItemsTableModal
          open={selectedTableRotation}
          tableId={selectedTableRotation}
          orderItemId={id}
          itemName={name}
          itemPrice={total}
          itemQtd={qtd}
          onClose={closeTableRotation}
          restaurantId={restaurantId}
          reload
        />
      )}
    </React.Fragment>
  )
};

export default withTranslation('common')(ReceiptItem)
