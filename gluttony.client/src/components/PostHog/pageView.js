import { useRouter } from "next/router";
import { useEffect } from "react";
import { usePostHog } from "posthog-js/react";
import { trackError } from "./errorTracker";

export default function PageView() {
  const router = useRouter();
  const pathname = router?.asPath;
  const query = router?.query;
  const posthog = usePostHog();

  useEffect(() => {
    if (!pathname || !posthog) return;

    try {
      if (typeof window !== "undefined") {
        const url = window.origin + pathname;
        posthog.capture("$pageview", {
          "$current_url": url,
          "$current_view": query?.v,
          "$current_subview": query?.sv,
          "$current_restaurantId": query?.id,
        });
      }
    } catch (error) {
      trackError(error, {
        component: "PostHogPageView",
        pathname,
        query,
      });
    }
  }, [pathname, posthog]);

  return null;
}