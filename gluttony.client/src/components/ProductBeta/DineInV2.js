import { memo, useRef } from "react";
import getConfig from "next/config";
import { useSelector } from "react-redux";
import { authSelectors } from "../../../redux/selectors";

const { publicRuntimeConfig } = getConfig();

const DineInIframe = memo((props) => {
  const iframeRef = useRef(null);
  const token = useSelector(authSelectors.getAuthToken)

  window.addEventListener("message", (event) => {
    if (event.data?.type === "request-token") {
      if (iframeRef.current) {
        iframeRef.current.contentWindow.postMessage({ token, restaurantId: props?.restaurantId }, "*");
      }
    }
  });

  return (
    <iframe 
      ref={iframeRef} 
      style={{ border: "none" }} 
      src={publicRuntimeConfig.BETA_DINE_IN_V2_URL} 
      width="100%" 
      height="100%" 
      {...props}
    />
  )
});

export default DineInIframe;
