import React, { useEffect, useState } from "react";
import { withTranslation } from "../../../../i18n";
import Typography from "@material-ui/core/Typography";
import DatePicker from "../../DatePicker";
import useStyles from "./styles";
import moment from "moment/moment";
import Table from "@material-ui/core/Table";
import TableContainer from "@material-ui/core/TableContainer";
import TableHead from "@material-ui/core/TableHead";
import TableRow from "@material-ui/core/TableRow";
import TableCell from "@material-ui/core/TableCell";
import TableBody from "@material-ui/core/TableBody";
import { ButtonBase, NativeSelect, TableFooter, useMediaQuery } from "@material-ui/core";
import { useDispatch, useSelector } from "react-redux";
import { restaurantSelectors, terminalsSelectors } from "../../../../redux/selectors";
import { getS700Charges } from "../../../api";
import palette from "../../../../styles/palette";
import typography from "../../../../styles/typography";
import CurrencyBadge from "../../_tags/CurrencyBadge";
import { formatNumber } from "../../../utils/formatNumber";
import { terminalsActions } from "../../../../redux/actions";
import FormControl from "@material-ui/core/FormControl";
import NativeSelectInput from "../../_input/NativeSelectInput";
import isEmpty from "../../../utils/isEmpty";
import { ChevronDown20new, NoItems80 } from "../../../utils/icons";
import Select from "@material-ui/core/Select";
import MenuItem from "@material-ui/core/MenuItem";
import { MenuProps } from "../../../utils/const";
import Pagination from "@material-ui/lab/Pagination";
import EmptyScreen from "../../_placeholder/EmptyScreen";

const ChargesS700 = ({ t }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const isMobile = useMediaQuery('(max-width:500px)');
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  
  const [date, setDate] = useState(moment().format('YYYY-MM-DD'))
  const [charges, setCharges] = useState({ total: 0, pages: 0, items: [], totalAmount: 0, tipAmount: 0,  amount: 0 })
  const { total, pages, items,  totalAmount, tipAmount, amount } = (charges || {})
  
  const pageSize = 10;
  const [pageOffset, setPageOffset] = useState(0);
  const loadedSize = (items || []).length
  
  const terminals = useSelector(terminalsSelectors.getTerminals);
  const s700Devices = terminals?.filter(s700 => s700.type === "STRIPE_S700" )
  const [selectedDevice, setSelectedDevice] = useState(!isEmpty(s700Devices) ? s700Devices[0].readerId : "")
  
  const fetchTerminals = () => {
    dispatch(terminalsActions.getTerminals(restaurantId, true))
  };
  
  useEffect(() => {
    fetchTerminals();
  }, []);
  
  useEffect(()=>{
    if(selectedDevice !== "" || isEmpty(s700Devices)) {
      return
    }
    setSelectedDevice(s700Devices[0].readerId)
  },[s700Devices])
  
  const fetchCharges = () => {
    getS700Charges(restaurantId, date, selectedDevice, pageOffset, pageSize)
     .then(({ data = [] }) => {
        const { total: fetchedTotal, items: fetchedItems, pages: fetchedPages, totalAmount: fetchedTotalAmount, tipAmount: fetchTipAmount, amount: fetchedAmount  } = data;
        // items should only be concatenated when the requested day is the same then the day of the creation of the charges, otherwise only fetched items should show
        let aggregatedItems = []
        if(!isMobile || isEmpty(items)){
          aggregatedItems = fetchedItems
        } else {
          const formattedCreationTime = moment(items[0].creationTime).format('YYYY-MM-DD')
          const isSameDay = moment(date).isSame(formattedCreationTime);
          aggregatedItems = isSameDay ? items.concat(fetchedItems) : fetchedItems
        }
       
        setCharges({ total: fetchedTotal, items: aggregatedItems, pages: fetchedPages, totalAmount: fetchedTotalAmount, tipAmount: fetchTipAmount, amount: fetchedAmount });
      })
      .catch(() => {});
  }
  
  useEffect(() => {
    fetchCharges();
  }, [pageOffset, date, selectedDevice]);
  
  const onSelectDevice = (e) => {
    setSelectedDevice(e.target.value)
  }
  
  const onLoadMore = () => {
    setPageOffset(loadedSize)
  }
  
  const getMobileList = () => {
    return (
      <div style={{ display: "flex", flexDirection: "column" }}>
        {charges && items.map(i => {
          const { amount, creationTime, tipAmount } = i;
          const totalPaidAmount = amount + tipAmount
          const formattedDate = moment(creationTime).format('DD.MM.YYYY')
        
          return (
            <ButtonBase>
              <div style={{ display: "flex", flexDirection: "column", width: "100%", textAlign: "left", borderBottom: `1px solid ${palette.grayscale.divider}`, padding: 12 }}>
                <div style={{ display: "flex", justifyContent: "space-between", alignItems: "center", width: "100%" }}>
                  <Typography style={{ ...typography.body.medium, width: "100%"}}>{formatNumber(amount)}€ {t("waiter-payment-user-details-paid-label")} </Typography>
                  <Typography style={{ ...typography.body.medium, width: "100%", textAlign: "right" }}>{formatNumber(totalPaidAmount)}€ {t("receipt-total-label")}</Typography>
                </div>
                <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{formatNumber(tipAmount)}€ {t("receipt-tip-label")}</Typography>
                <Typography style={{ ...typography.body.regular, marginTop: 2 }}>{formattedDate}</Typography>
              </div>
            </ButtonBase>
          )
        })}
  
        <Typography style={{ ...typography.body.regular, padding: 12, color: palette.grayscale["600"] }}>{`${loadedSize}${total > loadedSize ? `/${total}` : ''} ${t('charges')}`}</Typography>
  
        {(total > loadedSize)  && (
          <div style={{ display: "flex", justifyContent: "center", paddingTop: 12, paddingBottom: 12 }}>
            <ButtonBase onClick={onLoadMore} style={{ paddingTop: 5, paddingBottom: 5, paddingLeft: 12, paddingRight: 12, border: `1px solid ${palette.grayscale.border}`, borderRadius: 10 }}>
              <Typography style={{ ...typography.body.medium }}>
                {t('load-more')}
              </Typography>
            </ButtonBase>
          </div>
        )}
      </div>
    )
  }
  
  const getChargesOverview = () => {
    if (isMobile) {
      return getMobileList()
    }
    
    return (
      <div>
        <TableContainer>
          <Table stickyHeader aria-label="simple table">
            <TableHead>
              <TableRow>
                <TableCell>{t("reader-header")}</TableCell>
                <TableCell>{t("date")}</TableCell>
                <TableCell>{t('discount-editor-amount-field-label')}</TableCell>
                <TableCell>{t("change-calculator-tip-label")}</TableCell>
                <TableCell align="right">{t("receipt-total-label")}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {charges && items.map(i => {
                const { amount, creationTime, readerId, tipAmount, id } = i;
                const totalPaidAmount = amount + tipAmount
                const formattedDate = moment(creationTime).format('DD.MM.YYYY')
                
                return (
                    <TableRow key={id}>
                      <TableCell>{readerId}</TableCell>
                      <TableCell>{formattedDate}</TableCell>
                      <TableCell>{formatNumber(amount)}€</TableCell>
                      <TableCell>{formatNumber(tipAmount)}€</TableCell>
                      <TableCell align="right">
                        <CurrencyBadge amount={formatNumber(totalPaidAmount)}/>
                      </TableCell>
                    </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell>
                  {t('table-count', { count: total })}
                </TableCell>
                <TableCell></TableCell>
                <TableCell>
                  {t('table-total', { total: formatNumber(amount)})}
                </TableCell>
                <TableCell>
                  {t('table-total', { total: formatNumber(tipAmount)})}
                </TableCell>
                <TableCell align="right">
                  {t('table-total', { total: formatNumber(totalAmount)})}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
        {charges && pages > 1 && (
          <div className={classes.pagination}>
            <Pagination count={pages} variant="text" shape="rounded" onChange={(e, v) => setPageOffset((v - 1) * pageSize)}/>
          </div>
        )}
      </div>
    )
  }
  
  return (
    <div className={classes.content} data-testid="reporting-charges">
      <div className={classes.header} style={{ padding: isMobile ? "0px 12px" : null }}>
        <Typography className={classes.headerTitle}>{t('charges')}</Typography>
        <div className={classes.spacing} />
        <div className={classes.actions}>
          <DatePicker DatePicker value={date} setValue={(dt) => {
            const formattedDate = moment(dt).format('YYYY-MM-DD')
            setDate(formattedDate) }}
          />
          <FormControl style={{ minWidth: 150 }} className={classes.selectWrapper}>
            <NativeSelect
              value={isEmpty(selectedDevice) ? "" : selectedDevice }
              onChange={onSelectDevice}
              inputProps={{
                id: "device-selector",
              }}
              input={<NativeSelectInput />}
              disabled={isEmpty(s700Devices)}
            >
  
              <option value="">{t("select-reader")}</option>
              {s700Devices.map(s700 => (
                <option key={s700.id} value={s700.readerId}>{s700.label}</option>
              ))}
            </NativeSelect>
          </FormControl>
        </div>
      </div>
      {getChargesOverview()}
    </div>
  );
}

export default withTranslation('common')(ChargesS700);