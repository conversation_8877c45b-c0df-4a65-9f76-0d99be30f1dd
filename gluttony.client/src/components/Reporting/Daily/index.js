import React, {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";
import Typography from '@material-ui/core/Typography';
import {withTranslation} from '../../../../i18n';
import {
  closeReport,
  createReport, emailReport, getRestaurantCardsReport,
  getRestaurantDailyReport, getRestaurantDiscountsReport,
  printReport, printWaiterReport
} from "../../../api";
import { accountSelectors, restaurantSelectors } from "../../../../redux/selectors";
import {
  dateTimePickerRanges,
  dateTimePickerTypes,
  graphColorOptions,
  noop,
  permissionIdentifiers,
  reportingViews
} from "../../../utils/const";
import {formatNumber} from "../../../utils/formatNumber";
import useStyles from "./styles";
import clsx from "clsx";
import {
  ArrowLeftIcon,
  CashJournalIcon24,
  DiscountIcon24, GiftCardIcon,
  RevenueIcon24
} from "../../../utils/icons";
import ButtonBase from "@material-ui/core/ButtonBase";
import isEmpty from "../../../utils/isEmpty";
import {Avatar} from "@material-ui/core";
import {reportingActions} from "../../../../redux/actions";
import Button from "@material-ui/core/Button";
import Alert from "@material-ui/lab/Alert";
import Snackbar from "@material-ui/core/Snackbar";
import moment from "moment";
import CircularProgress from "@material-ui/core/CircularProgress";
import CustomDailyClosingModal from "../../_popup/CustomDailyClosingModal";
import { useRouter } from "next/router";
import ReportBlockingErrorModal from "../../_popup/ReportBlockingErrorModal";
import DailyReportModal from "../../_popup/DailyReportModal";

export const Daily = withTranslation('common')(({ t, params = {} }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  
  const router = useRouter();
  const { asPath } = router;
  const resolvedAsPath = asPath.replace(/#.*/, "").replace(/\?.*/, "")
  
  const { restaurant = {} } = useSelector(restaurantSelectors.getRestaurant);
  const { id: restaurantId } = restaurant;
  
  const [error, setError] = useState(false);
  const [dailyReport, setDailyReport] = useState({});
  const [marketingReport, setMarketingReport] = useState({});
  const { cashOutInRange } = marketingReport;
  const [discountsReport, setDiscountsReport] = useState({});
  const { count: discountsCount, amount: discountsTotal } = discountsReport;
  const [noReport, setNoReport] = useState(false);
  const [startingReport, setStartingReport] = useState(false);
  const { report = {}, cashJournal = {}, grossRevenue = {}, improvementReport = {}, cards = {} } = (dailyReport || {});
  const { transactions = {}, numberOfDeletedTotal, numberOfSoldTotal, numberOfStockTotal } = (cards || {});
  const { adjustmentTotal, cashInTotal, cashOutTotal, cashInByType = {} } = (transactions || {});
  const { cash = {}, externalVoucher = {}, other = {} } = (cashInByType || {})
  const giftCardBreakDown = [{ paymentChannel: "cash", totalValue: (cash?.totalValue || 0), percentage: (cash?.percentage || 0) },
    { paymentChannel: "other", totalValue: (other?.totalValue || 0), percentage: (other?.percentage || 0) },
    { paymentChannel: "externalVouchers", totalValue: (externalVoucher?.totalValue || 0), percentage: (externalVoucher?.percentage || 0) }]
  const { totalRevenue: cashJournalRevenue, items: cashJournalRevenueBreakdown = [] } = cashJournal;
  const { totalRevenue: totalGrossRevenue, items: totalGrossRevenueBreakdown = [] } = grossRevenue;
  const { itemsCancelled = 0, itemsRefunded = 0, itemsRemoved = 0, ordersCancelled = 0, ordersRefunded = 0, totalLoss = 0 } = improvementReport;
  const { id: reportId, identifier: reportIdentifier, creationTime, closeTime = new Date(), openedByAccount = {}, closed, closedByAccount = {} } = report;
  const minClosingTime = moment(creationTime).add(8, "h");
  
  
  const permissionIds = useSelector(accountSelectors.getPermissionIdentifiers);
  const hasPermission = permissionIds.some(permissionId => reportingViews.DAILY_REPORT.permissions.includes(permissionId))
  const hasCloseReportPermission = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_REPORTS.value, permissionIdentifiers.CAN_CLOSE_DAILY_REPORT.value].includes(permissionId))
  const hasFullReportingPermission = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_REPORTS.value].includes(permissionId))
  const canViewCashRegister = permissionIds.some(permissionId => [permissionIdentifiers.CAN_VIEW_CASH_REGISTER.value, permissionIdentifiers.CAN_UPDATE_CASH_REGISTER.value].includes(permissionId))
  
  const [customDateClosing, setCustomDateClosing] = useState(false);
  const [customDate, setCustomDate] = useState(null);
  const startCustomDateClosing = () => setCustomDateClosing(true);
  const stopCustomDateClosing = () => {
    setCustomDateClosing(false);
    setCustomDate(null);
  };
  const [closing, setClosing] = useState(false);
  const [printing, setPrinting] = useState(false);
  const [emailing, setEmailing] = useState(false);
  
  const [showingTimeBlockerError, setShowingTimeBlockerError] = useState(false)

  const accountEmail = useSelector(accountSelectors.getAccountEmail);
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);
  
  const getDailyReport = () => {
    const { id } = params;
    getRestaurantDailyReport(restaurantId, id)
      .then(({ data: dailyReportData }) => {
        setNoReport(false)
        setDailyReport(dailyReportData)
        setClosing(false)
      })
      .catch(({ response = {} }) => {
        const { status } = response;
        if (status === 404) {
          setNoReport(true)
        }
      })
  }

  useEffect(() => {
    getDailyReport();
  }, []);
  
  useEffect(() => {
    if (creationTime) {
      getRestaurantCardsReport(restaurantId, creationTime, closeTime, dateTimePickerTypes.BUSINESS_DAY.value)
        .then(({ data: marketingReportData }) => {
          setMarketingReport(marketingReportData)
        })
        .catch(noop)
      getRestaurantDiscountsReport(restaurantId, creationTime, closeTime, dateTimePickerTypes.BUSINESS_DAY.value)
        .then(({ data: discountsReportData }) => {
          setDiscountsReport(discountsReportData)
        })
        .catch(({ response = {} }) => {
          const { status } = (response || {});
          if (status === 422) {
            setShowingTimeBlockerError(true)
          }
        })
    }
    
  }, [creationTime])
  
  const updateReportingView = (value, params) => () => {
    dispatch(reportingActions.setView(value, params));
    if(value === "GIFT_CARDS") {
      dispatch(reportingActions.setGiftCardGroupBy("TRANSACTIONS"))
    }
    if (closed) {
      dispatch(reportingActions.setDateRange({ id: dateTimePickerRanges.CUSTOM.value, start: creationTime, end: closeTime }))
    }
  };
  
  if (!hasPermission) {
    return null;
  }
  
  const startDailyReport = () => {
    setStartingReport(true)
    createReport(restaurantId).then(() => {
      setStartingReport(false)
      getDailyReport()
    }).catch((e) => setError(e.response?.data?.title))
  };
  
  const [dailyReportModalIsOpen, setDailyReportModalIsOpen] = useState(false);
  
  const printDailyReport = (_reportId, targetAccount) => {
    setPrinting(true);
    if (!hasFullReportingPermission) {
      printWaiterReport(restaurantId, reportId).then(() => {}).catch((e) => setError(e.response?.data?.title))
        .finally(() => {
          setPrinting(false);
        })
    } else {
      printReport(restaurantId, reportId, targetAccount?.id ?? null).then(() => {}).catch((e) => setError(e.response?.data?.title))
        .finally(() => {
          setPrinting(false);
        })
    }
  };
  
  const [dailyReportEmail, setDailyReportEmail] = useState(accountEmail);
  const [editingDailyReportEmail, setEditingDailyReportEmail] = useState(false);
  
  const emailDailyReport = (email, account) => {
    setDailyReportEmail(email);
    setEmailing(true);
    emailReport(restaurantId, reportId, email, account?.id ?? null)
      .then(() => {})
      .catch((e) => setError(e.response?.data?.title))
      .finally(() => {
        setEmailing(false);
      })
  };
  
  const closeDailyReport = (dateTime) => {
    setClosing(true)
    if (!hasCloseReportPermission || closed) {
      return;
    }
    closeReport(restaurantId, dateTime).then(() => getDailyReport()).catch((e) => {
      setError(e.response?.data?.title)
      setClosing(false);
    })
  };
  
  if (noReport) {
    return (
      <div className={classes.emptyPlaceholder}>
        <Typography gutterBottom className={classes.placeholderText}>{t('report-history-no-reports')}</Typography>
        <Button disableElevation color={'secondary'} onClick={startDailyReport} variant="contained" disabled={startingReport || !hasFullReportingPermission}>
          {t('report-history-create-report-btn')}
        </Button>
        <Snackbar open={error} autoHideDuration={6000} onClose={() => setError(false)}>
          <Alert severity="error">
            {error || t('error-bar-message')}
          </Alert>
        </Snackbar>
      </div>
    )
  }
  
  const renderRevenueBarGraph = () => {
    if (isEmpty(totalGrossRevenueBreakdown)) {
      return null;
    }
    return [
      <div className={classes.verticalContent}>
        <div className={clsx(classes.row, classes.revenueGraph)}>
          {totalGrossRevenueBreakdown.map(({ revenue }, index) => {
            return (
              <Typography className={classes.revenueGraphBar} style={{flex: revenue, background: graphColorOptions[index].value }}/>
            )
          })}
        </div>
      </div>,
      <div className={classes.verticalContent}>
        <div className={classes.row}>
          {totalGrossRevenueBreakdown.map(({ revenue, taxType = "" }, index) => (
            <Typography className={classes.highlightText}>
              <span className={classes.highlightColor} style={{ background: graphColorOptions[index].value }}/>
              <span className={classes.highlightValue}>{formatNumber(revenue)}€</span> <span className={classes.highlightDescription}>{taxType === 'NO_TAX' ? t('tips') : t(`${taxType.toLowerCase()}-tax`)}</span>
            </Typography>
          ))}
        </div>
      </div>
    ]
  }
  
  const renderCashJournalGraph = () => {
    if (isEmpty(cashJournalRevenueBreakdown)) {
      return null;
    }
    return [
      <div className={classes.verticalContent}>
        <div className={clsx(classes.row, classes.revenueGraph)}>
          {cashJournalRevenueBreakdown.map(({ revenue, percentage }, index) => {
            return (
              <Typography className={classes.revenueGraphBar} style={{flex: percentage, background: graphColorOptions[index].value }}/>
            )
          })}
        </div>
      </div>,
      <div className={classes.verticalContent}>
        <div className={classes.row}>
          {cashJournalRevenueBreakdown.map(({ revenue, groupedPaymentChannel = "" }, index) => (
            <Typography className={classes.highlightText}>
              <span className={classes.highlightColor} style={{ background: graphColorOptions[index].value }}/>
              <span className={classes.highlightValue}>{formatNumber(revenue)}€</span> <span className={classes.highlightDescription}>{t(`payment-channels-${groupedPaymentChannel.toLowerCase()}`)}</span>
            </Typography>
          ))}
        </div>
      </div>
    ]
  }
  
  const renderGiftCardsGraph = () => {
    if (isEmpty(giftCardBreakDown)) {
      return null;
    }
    return [
      <div className={classes.verticalContent}>
        <div className={clsx(classes.row, classes.revenueGraph)}>
          {giftCardBreakDown.map(({ percentage, totalValue }, index) => {
            
            return (
              <Typography className={classes.revenueGraphBar} style={{flex: percentage, background: graphColorOptions[index].value }}/>
            )
          })}
        </div>
      </div>,
      <div className={classes.verticalContent}>
        <div className={classes.row}>
          {giftCardBreakDown.map(({ totalValue, paymentChannel  }, index) => (
            <Typography className={classes.highlightText}>
              <span className={classes.highlightColor} style={{ background: graphColorOptions[index].value }}/>
              <span className={classes.highlightValue}>{formatNumber(totalValue)}€</span> <span className={classes.highlightDescription}>{t(`payment-channels-${paymentChannel.toLowerCase()}`)}</span>
            </Typography>
          ))}
        </div>
      </div>
    ]
  }
  
  const renderImprovementsAreaGraph = () => {
    if (!totalLoss) {
      return null;
    }
    return [
      <div className={classes.verticalContent}>
        <div className={classes.row}>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{ordersCancelled}</span> {t('amount-orders-cancelled')}
          </Typography>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{ordersRefunded}</span> {t('amount-orders-refunded')}
          </Typography>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{itemsCancelled}</span> {t('amount-items-cancelled')}
          </Typography>
        </div>
      </div>
    ]
  }
  
  const renderGiftCardGraph = () => {
    if (isEmpty(transactions)) {
      return null;
    }
    return [
      <div className={classes.verticalContent}>
        <div className={classes.row}>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{numberOfSoldTotal}</span> {t('number-of-gift-cards-sold')}
          </Typography>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{numberOfStockTotal}</span> {t('number-of-gift-cards-in-stock')}
          </Typography>
          <Typography className={classes.highlightText}>
            <span className={classes.highlightValue}>{numberOfDeletedTotal}</span> {t('number-of-gift-cards-deleted')}
          </Typography>
        </div>
      </div>
    ]
  }

  const onPrint = () => {
    if (hasFullReportingPermission) {
      setDailyReportModalIsOpen(true)
    } else {
      printDailyReport();
    }
  }

  const onSendByEmail = () => setEditingDailyReportEmail(true)

  return (
    <div className={classes.wrapper} data-testid="reporting-daily-report" >
      <div className={classes.header}>
        <Typography className={classes.headerTitle}>
          {t('daily-report')}
          <span className={classes.reportIdentifier}>{reportIdentifier}</span>
        </Typography>
        <div className={classes.spacing} />
        <div className={classes.actions}>
          <div className={classes.loadingBtnWrapper}>
            <Button classes={{ root: classes.buttonRoot }} disabled={printing} onClick={onPrint} disableElevation>
              {t('report-history-print-report-btn')}
            </Button>
            {printing && <CircularProgress size={20} className={classes.loadingBtnProgress}/>}
          </div>
          <div className={classes.loadingBtnWrapper}>
            <Button classes={{ root: classes.buttonRoot }} disabled={emailing} onClick={onSendByEmail} disableElevation>
              {t('by-email')}
            </Button>
            {emailing && <CircularProgress size={20} className={classes.loadingBtnProgress}/>}
          </div>
          {!closed && hasCloseReportPermission && (
            <Button
              classes={{ root: classes.buttonRoot }}
              color="primary"
              onClick={startCustomDateClosing}
              className={classes.createButton}
              disabled={closing}
            >
              {t('report-history-close-report-btn')}
            </Button>
          )}
          {/*{!closed && hasCloseReportPermission && (*/}
          {/*  <Confirm*/}
          {/*    title={t('report-history-close-report-dialog-title')}*/}
          {/*    body={(*/}
          {/*      <Typography color="textSecondary" variant="body2">*/}
          {/*        {t('report-history-close-report-dialog-description-line-1')}*/}
          {/*        <br/>*/}
          {/*        <br/>*/}
          {/*        {t('report-history-close-report-dialog-description-line-2')}*/}
          {/*      </Typography>*/}
          {/*    )}*/}
          {/*  >*/}
          {/*    {confirm => (*/}
          {/*      <Button*/}
          {/*        classes={{ root: classes.buttonRoot }}*/}
          {/*        color="primary"*/}
          {/*        onClick={confirm(() => closeDailyReport())}*/}
          {/*        className={classes.createButton}*/}
          {/*        disabled={closing}*/}
          {/*      >*/}
          {/*        {t('report-history-close-report-btn')}*/}
          {/*      </Button>*/}
          {/*    )}*/}
          {/*  </Confirm>*/}
          {/*)}*/}
        </div>
      </div>
      <div className={classes.container}>
        <div className={classes.content}>
          <div className={classes.row}>
            <div className={clsx(classes.column, classes.revenueColumn, classes.revertColumn)}>
              <Typography className={clsx(classes.cardTitle, classes.cardTitleReverted)}>{creationTime ? moment(creationTime).format("DD.MM.YYYY HH:mm") : "--"}</Typography>
              <Typography className={clsx(classes.cardSubtitle, classes.cardSubtitleReverted)}>{t('start-time')}</Typography>
            </div>
            <div className={clsx(classes.column, classes.revenueColumn, classes.revertColumn)}>
              <Typography className={clsx(classes.cardTitle, classes.cardTitleReverted)}>
                {(closed && closeTime ? moment(closeTime).format("DD.MM.YYYY HH:mm") : "--")}
              </Typography>
              <Typography className={clsx(classes.cardSubtitle, classes.cardSubtitleReverted)}>{t('end-time')}</Typography>
            </div>
            <div className={clsx(classes.column, classes.revenueColumn, classes.revertColumn)}>
              <div className={clsx(classes.row, classes.user)}>
                <Avatar className={classes.userAvatarSmall} />
                <Typography className={classes.userFullName}>
                  {!isEmpty(openedByAccount) ? `${openedByAccount.firstName || ""} ${openedByAccount.lastName || ""}`: "--"}
                </Typography>
              </div>
              <Typography className={clsx(classes.cardSubtitle, classes.cardSubtitleReverted)}>{t('opened-by')}</Typography>
            </div>
            <div className={clsx(classes.column, classes.revenueColumn, classes.revertColumn)}>
              <div className={clsx(classes.row, classes.user)}>
                <Avatar className={classes.userAvatarSmall} />
                <Typography className={classes.userFullName}>
                  {!isEmpty(closedByAccount) ? `${closedByAccount.firstName || ""} ${closedByAccount.lastName || ""}`: "--"}
                </Typography>
              </div>
              <Typography className={clsx(classes.cardSubtitle, classes.cardSubtitleReverted)}>{t('closed-by')}</Typography>
            </div>
          </div>
        </div>
      </div>
      <div className={classes.container}>
        <div className={classes.topBar}>
          <div className={classes.containerTitle}>
            {t('revenue')}
          </div>
          <div>
            <ButtonBase disableRipple onClick={updateReportingView(reportingViews.REVENUE.value)}>
              <Typography className={classes.sourceBtnText}>
                {t('see-details')}
              </Typography>
              <div className={classes.sourceBtnIcon}>
                <ArrowLeftIcon />
              </div>
            </ButtonBase>
          </div>
        </div>
        <div className={classes.verticalContent}>
          <div className={classes.row}>
            <div className={clsx(classes.column, classes.summaryIcon)}>
              <RevenueIcon24 />
            </div>
            <div className={classes.column}>
              <Typography className={classes.cardTitle}>{formatNumber(totalGrossRevenue)}€</Typography>
              <Typography className={classes.cardSubtitle}>{t('total-revenue')}</Typography>
            </div>
          </div>
        </div>
        {renderRevenueBarGraph()}
      </div>
      <div className={classes.columns}>
        <div className={classes.left}>
          {canViewCashRegister && (
            <div className={classes.container}>
              <div className={classes.topBar}>
                <div className={classes.containerTitle}>
                  {t('administration-layout-cash-register-nav-label')}
                </div>
                <div>
                  <ButtonBase disableRipple onClick={updateReportingView(reportingViews.CASH_JOURNAL.value)}>
                    <Typography className={classes.sourceBtnText}>
                      {t('see-details')}
                    </Typography>
                    <div className={classes.sourceBtnIcon}>
                      <ArrowLeftIcon />
                    </div>
                  </ButtonBase>
                </div>
              </div>
              <div className={classes.verticalContent}>
                <div className={classes.row}>
                  <div className={clsx(classes.column, classes.summaryIcon)}>
                    <CashJournalIcon24 />
                  </div>
                  <div className={classes.column}>
                    <Typography className={classes.cardTitle}>{formatNumber(cashJournalRevenue)}€</Typography>
                    <Typography className={classes.cardSubtitle}>{t('total-revenue')}</Typography>
                  </div>
                </div>
              </div>
              {renderCashJournalGraph()}
            </div>
          )}
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('reporting-section-promotions-and-discounts')}
              </div>
              <div>
                <ButtonBase disableRipple onClick={updateReportingView(reportingViews.PROMOTION_AND_DISCOUNTS.value)}>
                  <Typography className={classes.sourceBtnText}>
                    {t('see-details')}
                  </Typography>
                  <div className={classes.sourceBtnIcon}>
                    <ArrowLeftIcon />
                  </div>
                </ButtonBase>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <DiscountIcon24 />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>{formatNumber(discountsTotal)}</Typography>
                  <Typography className={classes.cardSubtitle}>{t('discounted-amount')}</Typography>
                </div>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <Typography className={classes.highlightText}>
                  <span className={classes.highlightValue}>{discountsCount}</span> {t('amount-discounts-applied')}
                </Typography>
              </div>
            </div>
          </div>
        </div>
        <div className={classes.right}>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t('improvement-area')}
              </div>
              <div>
                <ButtonBase disableRipple onClick={updateReportingView('improvement-area-source', improvementReport)}>
                  <Typography className={classes.sourceBtnText}>
                    {t('see-details')}
                  </Typography>
                  <div className={classes.sourceBtnIcon}>
                    <ArrowLeftIcon />
                  </div>
                </ButtonBase>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <RevenueIcon24 />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>{formatNumber(totalLoss)}€</Typography>
                  <Typography className={classes.cardSubtitle}>{t('total-loss')}</Typography>
                </div>
              </div>
            </div>
            {renderImprovementsAreaGraph()}
          </div>
          <div className={classes.container}>
            <div className={classes.topBar}>
              <div className={classes.containerTitle}>
                {t("marketing-tabs-cards")}
              </div>
              <div>
                <ButtonBase disableRipple onClick={updateReportingView(reportingViews.GIFT_CARDS.value, "TRANSACTIONS")}>
                  <Typography className={classes.sourceBtnText}>
                    {t('see-details')}
                  </Typography>
                  <div className={classes.sourceBtnIcon}>
                    <ArrowLeftIcon />
                  </div>
                </ButtonBase>
              </div>
            </div>
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <GiftCardIcon />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>{formatNumber(cashInTotal)}€</Typography>
                  <Typography className={classes.cardSubtitle}>{t('cash-register-type-cash_in')}</Typography>
                </div>
              </div>
            </div>
            {renderGiftCardsGraph()}
            {/*{renderGiftCardGraph()}*/}
            <div className={classes.verticalContent}>
              <div className={classes.row}>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <GiftCardIcon />
                </div>
                <div className={classes.column} style={{ flex: 1 }}>
                  <Typography className={classes.cardTitle}>{formatNumber(cashOutTotal)}€</Typography>
                  <Typography className={classes.cardSubtitle}>{t('cash-register-type-cash_out')}</Typography>
                </div>
                <div className={clsx(classes.column, classes.summaryIcon)}>
                  <GiftCardIcon />
                </div>
                <div className={classes.column}>
                  <Typography className={classes.cardTitle}>{formatNumber(adjustmentTotal)}€</Typography>
                  <Typography className={classes.cardSubtitle}>{t('total-adjustments-gift-cards')}</Typography>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Snackbar open={error} autoHideDuration={6000} onClose={() => setError(false)}>
        <Alert severity="error">
          {error || t('error-bar-message')}
        </Alert>
      </Snackbar>
      {customDateClosing && hasCloseReportPermission && !closed && (
        <CustomDailyClosingModal open={customDateClosing} onClose={stopCustomDateClosing} value={customDate} minDatetime={minClosingTime}  submit={closeDailyReport} titleI18n={'close-report'}/>
      )}
      {editingDailyReportEmail && (
        <DailyReportModal
          titleI18n={"daily-report"}
          open={editingDailyReportEmail}
          onClose={() => setEditingDailyReportEmail(false)}
          dailyReportEmail={dailyReportEmail}
          restaurantId={restaurantId}
          hasFullReportingPermission={hasFullReportingPermission}
          setValue={emailDailyReport}
        />
      )}
      {dailyReportModalIsOpen && hasFullReportingPermission && (
        <DailyReportModal
          titleI18n={"daily-report"}
          open={dailyReportModalIsOpen}
          onClose={() => setDailyReportModalIsOpen(false)}
          restaurantId={restaurantId}
          hasFullReportingPermission={hasFullReportingPermission}
          setValue={printDailyReport}
          isPrinting
        />
      )}
      {showingTimeBlockerError && <ReportBlockingErrorModal open={showingTimeBlockerError} onClose={() => setShowingTimeBlockerError(false)} />}
    </div>
  );
});
