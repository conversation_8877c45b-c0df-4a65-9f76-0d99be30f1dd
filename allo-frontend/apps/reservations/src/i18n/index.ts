import { de, enUS, hi, vi, zhCN } from 'date-fns/locale';
import { Language } from '~/lib/api/types.ts';

export const defaultLocale = 'en';

export const locales = [defaultLocale, 'de', 'zh', 'hi', 'vi'];

export const languageMap: Record<(typeof locales)[number], string> = {
  en: 'English',
  de: 'Deutsch',
  zh: '中文',
  hi: 'हिंदी',
  vi: 'Tiếng Việt',
};

// Weird typescript stuff with just an object map here
export const getDateFnsLocale = (locale: Language) => {
  if (!locale) return enUS;
  if (locales.includes(locale)) {
    switch (locale) {
      case 'zh':
        return zhCN;
      case 'hi':
        return hi;
      case 'vi':
        return vi;
      case 'en':
        return enUS;
      case 'de':
        return de;
      default:
        return enUS;
    }
  }
  return enUS;
};
