/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
  '/v1/restaurants/{restaurantId}/configuration/windows/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns a reservation window
     * @description Returns a specific reservation window of a restaurant
     */
    get: operations['getReservationWindow'];
    /**
     * Overwrites a reservation window
     * @description Overwrites a specific reservation window of a restaurant
     */
    put: operations['updateReservationWindow'];
    post?: never;
    /**
     * Removes a reservation window
     * @description Removes a specific reservation window of a restaurant
     */
    delete: operations['deleteReservationWindow'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/table-groups/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns a table group
     * @description Returns a specific table group of a restaurant
     */
    get: operations['getReservationWindow_1'];
    /**
     * Overwrites a table group
     * @description Overwrites a specific table group of a restaurant
     */
    put: operations['updateTableGroup'];
    post?: never;
    /**
     * Removes a table group
     * @description Removes a specific table group of a restaurant
     */
    delete: operations['deleteTableGroup'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/special-days/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns a special day
     * @description Returns a specific special day of a restaurant
     */
    get: operations['getSpecialDay'];
    /**
     * Overwrites a special day
     * @description Overwrites a specific special day of a restaurant
     */
    put: operations['updateSpecialDay'];
    post?: never;
    /**
     * Removes a special day
     * @description Removes a specific special day of a restaurant
     */
    delete: operations['deleteSpecialDay'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/waiter/reservations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create a new reservation as a waiter
     * @description Allows reservations to be created even if customer reservations are disabled
     */
    post: operations['createWaiterReservation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/partner/time-suggestions/validate-batch': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Validate time suggestions for a partner in batch
     * @description Validate if a new reservation can be created by a partner at a time, day and people count, in batch
     */
    post: operations['getPartnerTimeSuggestionsValidationInBatch'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/customer/reservations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Create a new reservation as a customer
     * @description Allows reservations to be created if customer reservations are enabled
     */
    post: operations['createCustomerReservation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/windows': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns all reservation windows
     * @description Returns all reservation windows of a restaurant
     */
    get: operations['getReservationWindows'];
    put?: never;
    /**
     * Create a new reservation window
     * @description Allows reservation windows to be created
     */
    post: operations['createReservationWindow'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/table-groups': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns all table groups
     * @description Returns all table groups of a restaurant
     */
    get: operations['getTableGroups'];
    put?: never;
    /**
     * Create a new table group
     * @description Allows table groups to be created
     */
    post: operations['createTableGroup'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/special-days': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns all special days
     * @description Returns all special days of a restaurant
     */
    get: operations['getSpecialDays'];
    put?: never;
    /**
     * Create a new special day
     * @description Allows special days to be created
     */
    post: operations['createSpecialDay'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/cover/upload': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Uploads a new image for the cover
     * @description Uploads a new image for the customer UI cover of a restaurant
     */
    post: operations['uploadCover'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/partners/{partnerId}/reservations': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Search through all reservations as a partner
     * @description Allows reservations to be listed if partner reservations are enabled
     */
    get: operations['listPartnerReservations'];
    put?: never;
    /**
     * Create a new reservation as a partner
     * @description Allows reservations to be created if partner reservations are enabled
     */
    post: operations['createPartnerReservation'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/partners/{partnerId}/configuration/credentials': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    /**
     * Adds credential to partner configurations for reservation
     * @description Adds credential to partner configurations for reservation
     */
    post: operations['addPartnerCredential'];
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/waiter/reservations/{reservationId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Find a reservation as a waiter
     * @description Returns the details of a reservation for a waiter
     */
    get: operations['getWaiterReservation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update a reservation as a waiter
     * @description Allows reservations to be updated by waiters
     */
    patch: operations['updateWaiterReservation'];
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns the reservation configuration
     * @description Returns the reservation configuration of a restaurant
     */
    get: operations['getReservationConfiguration'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Updates the reservation configuration
     * @description Updates specific properties of the reservation configuration of a restaurant
     */
    patch: operations['patchReservationConfiguration'];
    trace?: never;
  };
  '/v1/partners/{partnerId}/reservations/{reservationId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Find a reservation as a partner
     * @description Returns the details of a reservation for a partner
     */
    get: operations['getPartnerReservation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update a reservation as a partner
     * @description Allows reservations to be updated if partner reservations are enabled
     */
    patch: operations['updatePartnerReservation'];
    trace?: never;
  };
  '/v1/customer/reservations/{reservationId}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Find a reservation as a customer
     * @description Returns the details of a reservation for a customer
     */
    get: operations['getCustomerReservation'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    /**
     * Update a reservation as a customer
     * @description Allows reservations to be updated if customer reservations are enabled
     */
    patch: operations['updatePartnerReservation_1'];
    trace?: never;
  };
  '/v1/waiter/restaurants': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * List all restaurants as a waiter
     * @description Returns the details of all restaurants with reservations enabled for a waiter
     */
    get: operations['getRestaurants'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/waiter/time-suggestions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Lists all customer time suggestions for a waiter
     * @description Lists all times of a day when a new reservation can be created by a customer
     */
    get: operations['getCustomerAvailability'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/waiter/table-suggestions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Lists all table suggestions for a waiter
     * @description Lists all tables available at a specific time of a day (or close enough) when a new reservation can be created by a waiter
     */
    get: operations['getTableSuggestions'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/waiter/reservation-times': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Lists all reservation times for a waiter
     * @description Lists all times of a day when a new reservation can be created by a waiter (considering opening hours and reservation windows)
     */
    get: operations['getWaiterReservationTimes'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/partner/time-suggestions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Lists all time suggestions for a partner
     * @description Lists all times of a day when a new reservation can be created by a partner
     */
    get: operations['getPartnerTimeSuggestions'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/customer/time-suggestions': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Lists all time suggestions for a customer
     * @description Lists all times of a day when a new reservation can be created by a customer
     */
    get: operations['getCustomerTimeSuggestions'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/{restaurantId}/configuration/enabled': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns if the new reservation system is enabled
     * @description Returns true or false depending if the new reservation system is enabled or not for a restaurant
     */
    get: operations['getReservationConfigurationEnabled'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/restaurants/slug/{slug}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Returns the restaurant
     * @description Returns the details of a restaurant that are needed by the reservation flow
     */
    get: operations['getRestaurantBySlug'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/partners/{partnerId}/restaurants': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Search through all the restaurants
     * @description Returns the details of all restaurants that have reservations enabled for a partner
     */
    get: operations['getRestaurantsByPartner'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/partners/{partnerId}/configuration': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /**
     * Get partner configurations for reservation
     * @description Returns the needed configurations for a partner to work properly
     */
    get: operations['findPartnerCredentials'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/actuator': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Actuator root web endpoint */
    get: operations['links'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/actuator/health': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    /** Actuator web endpoint 'health' */
    get: operations['health'];
    put?: never;
    post?: never;
    delete?: never;
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
  '/v1/partners/{partnerId}/configuration/credentials/{id}': {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    get?: never;
    put?: never;
    post?: never;
    /**
     * Removes credential to partner configurations for reservation
     * @description Removes credential to partner configurations for reservation
     */
    delete: operations['removePartnerCredential'];
    options?: never;
    head?: never;
    patch?: never;
    trace?: never;
  };
}
export type webhooks = Record<string, never>;
export interface components {
  schemas: {
    ReservationWindow: {
      id?: string;
      name?: string;
      /**
       * @description List of weekdays (0 to 6, where 0 = Sunday, 1 = Monday, etc.)
       * @example [
       *       0,
       *       1,
       *       2,
       *       3,
       *       4,
       *       5,
       *       6
       *     ]
       */
      weekDays?: number[];
      /** @example 14:30 */
      startTime: string;
      /** @example 14:30 */
      endTime: string;
      /** @enum {string} */
      endTimeLogic?: 'LATEST_STARTING_AT' | 'LATEST_ENDING_AT';
      hasLimits: boolean;
      /** Format: int32 */
      seatingCapacity: number;
      /** Format: int32 */
      startLimit: number;
      /** Format: int32 */
      minimumLeadTimeInMinutes?: number;
      slots?: components['schemas']['ReservationWindowSlot'][];
    };
    ReservationWindowSlot: {
      /** @example 14:30 */
      time: string;
      /** Format: int32 */
      seatingCapacity: number;
      /** Format: int32 */
      startLimit: number;
    };
    TableGroup: {
      id?: string;
      name?: string;
      floorId?: string;
      tableIds?: string[];
      /** Format: int32 */
      minimumCapacity?: number;
      /** Format: int32 */
      maximumCapacity: number;
      /** Format: int32 */
      currentNumberOfReservations?: number;
    };
    SpecialDay: {
      id?: string;
      name?: string;
      /** Format: date */
      startDate: string;
      /** Format: date */
      endDate: string;
      allowReservations: boolean;
      allowCustomerReservations: boolean;
      waiterReservationsDisabledMessage?: string;
      customerReservationsDisabledMessage?: string;
      reservationWindows?: components['schemas']['SpecialDayReservationWindow'][];
    };
    SpecialDayReservationWindow: {
      id?: string;
      name?: string;
      /** @example 14:30 */
      startTime: string;
      /** @example 14:30 */
      endTime: string;
      /** @enum {string} */
      endTimeLogic?: 'LATEST_STARTING_AT' | 'LATEST_ENDING_AT';
      hasLimits: boolean;
      /** Format: int32 */
      seatingCapacity: number;
      /** Format: int32 */
      startLimit: number;
      /** Format: int32 */
      minimumLeadTimeInMinutes?: number;
      slots?: components['schemas']['ReservationWindowSlot'][];
    };
    Address: {
      street?: string;
      number?: string;
      area?: string;
      city?: string;
      /** @enum {string} */
      country?:
        | 'DE'
        | 'BR'
        | 'GR'
        | 'AT'
        | 'DK'
        | 'FR'
        | 'IT'
        | 'ES'
        | 'PT'
        | 'NL'
        | 'CH'
        | 'FI'
        | 'PL'
        | 'CZ'
        | 'HU'
        | 'HR'
        | 'BG'
        | 'RO'
        | 'EE'
        | 'GB'
        | 'US'
        | 'CA';
      additionalInfo?: string;
      zipCode?: string;
      /** Format: double */
      lat?: number;
      /** Format: double */
      lng?: number;
    };
    CreateReservationRequestContract: {
      reservation: components['schemas']['Reservation'];
      customers: components['schemas']['Customer'][];
      partnerIdempotencyToken?: string;
    };
    Customer: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      title?: 'other' | 'mr' | 'ms';
      firstName?: string;
      lastName?: string;
      address?: components['schemas']['Address'];
      email?: string;
      phone?: string;
      userId?: string;
      billingId?: string;
      company?: string;
      avatar?: string;
      vatId?: string;
      externalCustomerId?: string;
      restaurantIds?: string[];
      tags?: string[];
    };
    Reservation: {
      id?: string;
      /** Format: date-time */
      createdAt?: string;
      /** Format: date-time */
      modifiedAt?: string;
      createdBy?: string;
      createdByWaiter?: boolean;
      modifiedBy?: string;
      restaurantId?: string;
      /** @enum {string} */
      status?:
        | 'UNCONFIRMED'
        | 'DELETED'
        | 'CONFIRMED'
        | 'CANCELLED'
        | 'IN_PROGRESS'
        | 'COMPLETED'
        | 'NO_SHOW';
      /** Format: int32 */
      people?: number;
      /** Format: date-time */
      startTime?: string;
      /** Format: date-time */
      endTime?: string;
      tableIds?: string[];
      tableGroupId?: string;
      note?: string;
      customerIds?: string[];
      /** Format: int32 */
      overlap?: number;
      partner?: components['schemas']['ReservationPartner'];
      orderId?: string;
      restaurantName?: string;
      restaurantSlug?: string;
      /** @example Europe/Paris */
      zoneId?: string;
      /** @enum {string} */
      language?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt';
      cancelledByCustomer?: boolean;
      cancelledByAccountId?: string;
      cancelledByAccount?: string;
      recovered?: boolean;
      recoveringFromReservationId?: string;
      replacedByReservationId?: string;
      updatedByGoogle?: boolean;
    };
    ReservationPartner: {
      /** @enum {string} */
      id?: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      userId?: string;
      merchantId?: string;
    };
    ReservationTimeSuggestionValidationContract: {
      /** Format: date-time */
      startTime: string;
      /** Format: int32 */
      people: number;
      available?: boolean;
    };
    Image: {
      src?: string;
      alt?: string;
    };
    PartnerCredential: {
      id?: string;
      username?: string;
      password?: string;
    };
    UpdateReservationRequestContract: {
      reservation: components['schemas']['Reservation'];
      customers?: components['schemas']['Customer'][];
    };
    BlockedDate: {
      /** Format: date */
      date?: string;
      isStaffBlocked?: boolean;
    };
    ReservationConfiguration: {
      id?: string;
      /** @deprecated */
      enabled?: boolean;
      /** @deprecated */
      specialDaysEnabled?: boolean;
      cover?: components['schemas']['Image'][];
      /** Format: int32 */
      maximumTimeOverEndInMinutes?: number;
      /** Format: int32 */
      minimumLeadTimeInMinutes?: number;
      allowSmsNotifications?: boolean;
      /** Format: int32 */
      maxOverlapMinutesForWaiters?: number;
      /** Format: int32 */
      maxGuestsPerCustomerReservation?: number;
      estimatedDiningMinutes?: {
        [key: string]: number;
      };
      allowCustomerReservations?: boolean;
      displaySlotExplainButton?: boolean;
      /** @deprecated */
      blockedDates?: components['schemas']['BlockedDate'][];
      replyTo?: string;
      reservationTerms?: components['schemas']['ReservationTerm'][];
    };
    ReservationTerm: {
      text?: string;
      textI18n?: {
        [key: string]: string;
      };
    };
    CategoryConsumption: {
      categoryId?: string;
      name?: string;
      /** @enum {string} */
      unit?: 'G' | 'KG' | 'L' | 'ML' | 'PC' | 'BT';
      amount?: number;
    };
    Course: {
      /** Format: int32 */
      number?: number;
      nameI18n?: {
        [key: string]: string;
      };
    };
    CourseConfig: {
      enabled?: boolean;
      courses?: components['schemas']['Course'][];
    };
    CrmSettings: {
      companyId?: string;
    };
    DailyReportConfig: {
      emails?: string[];
    };
    DatevConfiguration: {
      kasseKontonummer?: string;
      configurationRecords?: components['schemas']['Record'][];
    };
    DeliveryArea: {
      zipCode?: string;
      deliveryFee?: number;
      minOrderValue?: number;
    };
    DeliveryRadius: {
      /** Format: double */
      radiusInKm?: number;
      deliveryFee?: number;
      minOrderValue?: number;
    };
    DslCallSettings: {
      callMonitoringEnabled?: boolean;
    };
    Extra: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      total?: number;
      /** Format: int32 */
      max?: number;
      /** Format: int32 */
      rounds?: number;
      printerIds?: string[];
      items?: components['schemas']['ExtraItem'][];
      custom?: boolean;
    };
    ExtraItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      extraId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      thumbnailUrl?: string;
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      numeration?: string;
      unitPrice?: number;
      /** Format: int32 */
      qtd?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      taxCategory?: string;
      taxRate?: number;
      /** Format: int32 */
      max?: number;
      remarks?: components['schemas']['Remark'][];
      remarkAnnotations?: string[];
      total?: number;
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      metadata?: components['schemas']['ItemMetadata'];
      custom?: boolean;
    };
    Gallery: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      name?: string;
      restaurantId?: string;
      menuItemId?: string;
      items?: components['schemas']['GalleryItem'][];
    };
    GalleryItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      galleryId?: string;
      itemUrl?: string;
      thumbnailUrl?: string;
      /** @enum {string} */
      type?: 'IMAGE' | 'VIDEO';
    };
    GiftCardAmountSuggestion: {
      /** @enum {string} */
      type?: 'FIXED' | 'CUSTOM';
      value?: number;
    };
    GiftCardSettings: {
      suggestedAmounts?: components['schemas']['GiftCardAmountSuggestion'][];
    };
    InventoryConfig: {
      enableConsumption?: boolean;
      invoiceEmail?: string;
      /** @enum {string} */
      actionOnItemCancellation?: 'NEVER_RETURN' | 'ALWAYS_RETURN';
    };
    ItemMetadata: {
      consumptions?: components['schemas']['CategoryConsumption'][];
      /** @enum {string} */
      inventoryStatus?: 'OUT_OF_STOCK' | 'LOW_STOCK' | 'IN_STOCK';
    };
    KioskConfig: {
      languages?: (
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
      )[];
      paymentMethods?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      /** Format: url */
      heroImageUrl?: string;
      /** Format: url */
      topImageUrl?: string;
      allowPromotions?: boolean;
      allowGiftCards?: boolean;
      enableBypassSinglePaymentMethod?: boolean;
      tipEnabled?: boolean;
      diningOptions?: ('IN_HOUSE' | 'TO_GO')[];
    };
    Konto: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** Format: int32 */
      validity?: number;
      /** @enum {string} */
      standard?: 'SKR03' | 'SKR04';
      code?: string;
      label?: string;
      description?: string;
    };
    LieferandoConfig: {
      useV2Integration?: boolean;
    };
    LocalizedString: {
      en_value?: string;
      de_value?: string;
      zh_value?: string;
    };
    Menu: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      restricted?: boolean;
      disabled?: boolean;
      recommended?: boolean;
      hidden?: boolean;
      excluded?: boolean;
      deleted?: boolean;
      oneTimeMenu?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      imageUrl?: string;
      title?: string;
      titleI18n?: {
        [key: string]: string;
      };
      internalTitle?: string;
      internalTitleI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      /** @enum {string} */
      displayType?: 'ITEM' | 'CARD';
      /** Format: int32 */
      order?: number;
      items?: components['schemas']['MenuItem'][];
      sharedItemIds?: string[];
      schedule?: {
        [key: string]: components['schemas']['Period'][];
      };
      printerId?: string;
      printerIds?: string[];
      orderTypes?: ('PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS')[];
      /** @enum {string} */
      color?:
        | 'SALMON_500'
        | 'GREY_600'
        | 'GREY_800'
        | 'PURPLE_700'
        | 'YELLOW_700'
        | 'BLUE_700'
        | 'GREEN_600';
      partnerIds?: ('UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER')[];
      restaurantTags?: components['schemas']['RestaurantTag'][];
    };
    MenuItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      menuId?: string;
      sharedMenuId?: string;
      code?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      ongoing?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      unitPrice?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      volume?: string;
      thumbnailUrl?: string;
      gallery?: components['schemas']['Gallery'];
      numeration?: string;
      remarks?: components['schemas']['Remark'][];
      /** @enum {string} */
      printerCategory?: 'KIOSK' | 'MAIN' | 'KITCHEN' | 'BAR' | 'MONITOR' | 'NONE';
      printerIds?: string[];
      remarkAnnotations?: string[];
      /** Format: int32 */
      order?: number;
      options?: components['schemas']['Option'][];
      extras?: components['schemas']['Extra'][];
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      extraItems?: components['schemas']['ExtraItem'][];
      optionItems?: components['schemas']['OptionItem'][];
      /** @enum {string} */
      color?:
        | 'SALMON_500'
        | 'GREY_600'
        | 'GREY_800'
        | 'PURPLE_700'
        | 'YELLOW_700'
        | 'BLUE_700'
        | 'GREEN_600';
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      partnerIds?: ('UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER')[];
      /** Format: int32 */
      ongoingExtrasQuantityMax?: number;
      hasHighQuantitySelector?: boolean;
      restaurantTags?: components['schemas']['RestaurantTag'][];
      includedInPrice?: boolean;
      metadata?: components['schemas']['ItemMetadata'];
      /** Format: int32 */
      preparationTime?: number;
      templateSetMenuId?: string;
      /** Format: int32 */
      defaultCourseNumber?: number;
      /** Format: int32 */
      smartschankId?: number;
      /** @enum {string} */
      unitOfMeasure?: 'G' | 'KG' | 'L' | 'ML' | 'PC' | 'BT';
    };
    MunicipalRegistry: {
      im?: string;
      cnae?: string;
    };
    OpenInvoiceBankDetails: {
      enabled?: boolean;
      beneficiary?: string;
      iban?: string;
      bic?: string;
      bank?: string;
    };
    OpenInvoiceSettings: {
      /** Format: int32 */
      daysUntilDue?: number;
      additionalMemo?: string;
      paymentMethods?: ('CARD' | 'SEPA_DEBIT')[];
      bankDetails?: components['schemas']['OpenInvoiceBankDetails'];
    };
    Option: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      lieferando?: boolean;
      lieferandoId?: string;
      /** Format: int32 */
      order?: number;
      total?: number;
      /** Format: int32 */
      qtd?: number;
      /** Format: int32 */
      rounds?: number;
      items?: components['schemas']['OptionItem'][];
      printerIds?: string[];
      sourceId?: string;
      collapsed?: boolean;
    };
    OptionItem: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      optionId?: string;
      name?: string;
      nameI18n?: {
        [key: string]: string;
      };
      internalName?: string;
      internalNameI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      restricted?: boolean;
      disabled?: boolean;
      hidden?: boolean;
      deleted?: boolean;
      /** Format: int32 */
      order?: number;
      numeration?: string;
      thumbnailUrl?: string;
      unitPrice?: number;
      /** Format: int32 */
      qtd?: number;
      /** @enum {string} */
      category?: 'DISH' | 'BEVERAGE' | 'DISCOUNT' | 'FEE';
      /** @enum {string} */
      dineTaxCategory?: 'REDUCED' | 'NORMAL';
      /** @enum {string} */
      takeawayTaxCategory?: 'REDUCED' | 'NORMAL';
      taxCategory?: string;
      taxRate?: number;
      /** Format: int32 */
      min?: number;
      /** Format: int32 */
      max?: number;
      remarks?: components['schemas']['Remark'][];
      remarkAnnotations?: string[];
      total?: number;
      extraItems?: components['schemas']['ExtraItem'][];
      sourceId?: string;
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      partnerPrices?: {
        [key: string]: number;
      };
      alcoholPercentage?: number;
      metadata?: components['schemas']['ItemMetadata'];
    };
    OrderAction: {
      title?: string;
      content?: string;
      titleI18n?: {
        [key: string]: string;
      };
      contentI18n?: {
        [key: string]: string;
      };
    };
    PartnerConfig: {
      /** @enum {string} */
      partnerId?: 'UBER_EATS' | 'WOLT' | 'LIEFERANDO' | 'OTHER';
      offerCutlery?: boolean;
      offerVytalBowl?: boolean;
      skipKitchenPrintout?: boolean;
      unseenOrder?: components['schemas']['UnseenOrder'];
    };
    Period: {
      /** @example 14:30:00 */
      opens?: string;
      /** @example 14:30:00 */
      closes?: string;
    };
    Printer: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      sn?: string;
      key?: string;
      label?: string;
      ip?: string;
      osName?: string;
      /** @enum {string} */
      connectionType?: 'IP_ADDRESS' | 'USB_PORT' | 'HTTP_API';
      /** @enum {string} */
      printerCategory?: 'KIOSK' | 'MAIN' | 'KITCHEN' | 'BAR' | 'MONITOR' | 'NONE';
      /** @enum {string} */
      printerFormat?: 'PAPER' | 'LABEL';
      /** @enum {string} */
      brand?: 'FEIE' | 'EPSON' | 'STAR' | 'SUNMI';
      /** @enum {string} */
      model?:
        | 'FEIE_N20W'
        | 'FEIE_N21W'
        | 'FEIE_N80WC'
        | 'EPSON_TM_T20II'
        | 'EPSON_TM_M30'
        | 'STAR_MC_Print2'
        | 'STAR_MC_Print3'
        | 'SUNMI_Kiosk_K2';
      /** @enum {string} */
      paperSize?: 'LABEL_SMALL' | 'LABEL_BIG' | 'CUSTOM_50_30';
      /** @enum {string} */
      language?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt';
      hasCutter?: boolean;
      cutByCategory?: boolean;
      sortByCategory?: boolean;
      disabled?: boolean;
      multiplyAdditions?: boolean;
      printAccount?: boolean;
      orderTypes?: ('PICKUP' | 'DELIVERY' | 'DINE_IN' | 'EXPRESS')[];
      printLogo?: boolean;
      cashDrawer?: boolean;
      /** Format: int32 */
      topSpacingLines?: number;
      orderingDeviceId?: string;
    };
    ReceiptConfig: {
      hideCancelledOrdersWithoutPayments?: boolean;
      defaultOrderColumns?: (
        | 'IDENTIFIER'
        | 'ORDER_NUMBER'
        | 'ORDER_TYPE'
        | 'TABLE_NUMBER'
        | 'WAITER'
        | 'CLOSED_BY'
        | 'ORDER_STARTED'
        | 'ORDER_ENDED'
        | 'ORDER_TOTAL'
        | 'TABLE_TAX'
        | 'TIP'
        | 'TABLE_TOTAL'
        | 'PAYMENT_METHOD'
        | 'PAYMENT_STATUS'
      )[];
      defaultPaymentColumns?: (
        | 'IDENTIFIER'
        | 'ORDER_NUMBER'
        | 'ORDER_TYPE'
        | 'TABLE_NUMBER'
        | 'CLOSED_BY'
        | 'ORDER_STARTED'
        | 'ORDER_ENDED'
        | 'TIP'
        | 'PAYMENT_METHOD'
        | 'PAYMENT_STATUS'
        | 'PAYMENT'
        | 'PAYMENT_TOTAL'
      )[];
    };
    ReceiptOption: {
      /** @enum {string} */
      type?: 'NONE' | 'STANDARD' | 'BUSINESS' | 'PDF';
      email?: string;
      paymentId?: string;
    };
    Record: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      restaurantId?: string;
      cashRegisterId?: string;
      cashRegisterBalance?: number;
      /** @enum {string} */
      type?: 'CASH_IN' | 'CASH_OUT';
      /** @enum {string} */
      category?:
        | 'NORMAL'
        | 'TIP'
        | 'DAILY_REVENUE'
        | 'CASH_DEPOSIT'
        | 'BANK_DEPOSIT'
        | 'SALARY_PAYMENT'
        | 'MONEY_TRANSIT'
        | 'PRIVATE'
        | 'ADJUSTMENT'
        | 'GUTSCHEIN'
        | 'CANCELLATION'
        | 'MONEY_TRANSIT_LIEFERANDO'
        | 'MONEY_TRANSIT_STRIPE'
        | 'MONEY_TRANSIT_WOLT'
        | 'MONEY_TRANSIT_UBER_EATS'
        | 'MONEY_TRANSIT_ONLINE';
      /** @enum {string} */
      taxRate?: 'NONE' | 'SEVEN' | 'NINETEEN' | 'MIXED';
      amount?: number;
      kontoId?: string;
      konto?: components['schemas']['Konto'];
      gegenKontoId?: string;
      gegenKonto?: components['schemas']['Konto'];
      nestedRecordIds?: string[];
      nested?: boolean;
      supplierId?: string;
      supplier?: components['schemas']['Supplier'];
      documentId?: string;
      documentUrl?: string;
      name?: string;
      description?: string;
      note?: string;
      /** Format: date-time */
      date?: string;
      cardTransactionId?: string;
      cardCode?: string;
      cancelled?: boolean;
      /** Format: date-time */
      cancelledAt?: string;
      cancelledBy?: string;
      cancelledByRecordId?: string;
      createdByUserId?: string;
      createdByAccountId?: string;
    };
    RefundConfig: {
      allOPayAutoRefundEnabled?: boolean;
    };
    Remark: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      deleted?: boolean;
      annotation?: string;
      restaurantId?: string;
      shortText?: string;
      description?: string;
      global?: boolean;
      /** @enum {string} */
      type?: 'ADDITIVE' | 'ALLERGIES' | 'SPICE';
      /** @enum {string} */
      subType?:
        | 'CEREAL_UNSPECIFIED'
        | 'CRUSTACEANS'
        | 'EGGS'
        | 'FISH'
        | 'PEANUTS'
        | 'SOYBEANS'
        | 'MILK'
        | 'NUTS_UNSPECIFIED'
        | 'CELERY'
        | 'MUSTARD'
        | 'SESAME_SEEDS'
        | 'SULPHUR_DIOXIDE_SULPHITES'
        | 'LUPIN'
        | 'MOLLUSCS'
        | 'UNSPECIFIED'
        | 'COLORANT_UNSPECIFIED'
        | 'PRESERVATIVES_UNSPECIFIED'
        | 'ANTIOXIDANT'
        | 'FLAVOURE_ENHANCER'
        | 'SULFITES'
        | 'BLACKENED'
        | 'WAXED'
        | 'PHOSPHATE'
        | 'SWEETENER_UNSPECIFIED'
        | 'CAFFEINE_UNSPECIFIED'
        | 'QUININE'
        | 'GENETICALLY_MODIFIED'
        | 'ACIDIFIERS'
        | 'STABILISERS'
        | 'PROTEIN_UNSPECIFIED';
      /** @enum {string} */
      iconIdentifier?:
        | 'GLUTEN'
        | 'CRUSTACEANS'
        | 'EGGS'
        | 'FISH'
        | 'PEANUTS'
        | 'SOYBEANS'
        | 'MILK'
        | 'NUTS'
        | 'CELERY'
        | 'MUSTARD'
        | 'SESAME'
        | 'SULPHUR'
        | 'LUPINS'
        | 'MOLLUSKS';
      descriptionI18n?: {
        [key: string]: string;
      };
      shortTextI18n?: {
        [key: string]: string;
      };
      category?: string;
      /** Format: int32 */
      level?: number;
      icon?: string;
    };
    Restaurant: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      mode?: 'LIVE' | 'EXPLORATION';
      name?: string;
      slug?: string;
      intlName?: components['schemas']['LocalizedString'];
      description?: string;
      intlDescription?: components['schemas']['LocalizedString'];
      address?: components['schemas']['Address'];
      avatarId?: string;
      coverId?: string;
      code?: string;
      tables?: components['schemas']['Table'][];
      rating?: number;
      hidden?: boolean;
      taxId?: string;
      vatNumber?: string;
      phone?: string;
      email?: string;
      organizationId?: string;
      nameI18n?: {
        [key: string]: string;
      };
      descriptionI18n?: {
        [key: string]: string;
      };
      timezone?: string;
      logoUrl?: string;
      thumbnailUrl?: string;
      tags?: components['schemas']['Tag'][];
      tagIds?: string[];
      notificationMobile?: string;
      notificationEmail?: string;
      paymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      enabledPaymentChannels?: {
        [key: string]: (
          | 'APP'
          | 'CASH'
          | 'CARD'
          | 'LIEFERANDO'
          | 'LIEFERANDO_CASH'
          | 'WOLT'
          | 'FOODPANDA'
          | 'UBEREATS'
          | 'FOOD2GO'
          | 'MCHOICE'
          | 'LEBEN'
          | 'PAYPAL'
          | 'UEBERWEISUNG'
          | 'EC'
          | 'VISA'
          | 'MASTERCARD'
          | 'AMERICAN_EXPRESS'
          | 'JCB'
          | 'STRIPE'
          | 'GUTSCHEIN'
          | 'KAIYUAN'
          | 'SONSTIGE'
          | 'ORDER_SMART'
          | 'ALLO_PAY'
          | 'ALLO_PAY_LINK'
          | 'ALLO_PAY_ONLINE'
          | 'OFFENE_RECHNUNG'
          | 'KAUF_AUF_RECHNUNG'
          | 'ANZAHLUNG'
          | 'SODEXO'
          | 'BAYERNETS'
          | 'EDENRED'
        )[];
      };
      hasPickup?: boolean;
      /** Format: int32 */
      pickupRequests?: number;
      hasDelivery?: boolean;
      hasScan?: boolean;
      takeawayThreshold?: number;
      takeawayAreas?: string[];
      expressCheckout?: boolean;
      oneClickCheckout?: boolean;
      deliveryFee?: number;
      website?: string;
      openingHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      menus?: components['schemas']['Menu'][];
      galleries?: components['schemas']['Gallery'][];
      isOpen?: boolean;
      configuration?: components['schemas']['RestaurantConfiguration'];
      googlePlaceId?: string;
      googleReservationEnabled?: boolean;
      webshopUrl?: string;
      reservationUrl?: string;
      giftCardUrl?: string;
      isParent?: boolean;
      parentRestaurantId?: string;
      branchRestaurantIds?: string[];
      serializationType: string;
    };
    RestaurantAustria: {
      serializationType: 'RestaurantAustria';
    } & Omit<components['schemas']['Restaurant'], 'serializationType'>;
    RestaurantBrazil: {
      serializationType: 'RestaurantBrazil';
    } & (Omit<components['schemas']['Restaurant'], 'serializationType'> & {
      cnpj?: string;
      ie?: string;
      municipalRegistry?: components['schemas']['MunicipalRegistry'];
    });
    RestaurantConfiguration: {
      restaurantId?: string;
      printByMenu?: boolean;
      printByMenuItem?: boolean;
      printByAddition?: boolean;
      hasReservations?: boolean;
      hasDATEVExport?: boolean;
      hasTakeaway?: boolean;
      hasScanToOrder?: boolean;
      hasOngoingItems?: boolean;
      hasExpress?: boolean;
      partialCheckout?: boolean;
      newCheckout?: boolean;
      disableDailyReportPrintout?: boolean;
      disableDigitalReceiptPrintout?: boolean;
      showInventory?: boolean;
      views?: (
        | 'TERMINAL'
        | 'ACTIVITY'
        | 'PLANNER'
        | 'ORDERS'
        | 'MENU'
        | 'RECEIPTS'
        | 'CUSTOMERS'
        | 'PICKUP'
        | 'PAYMENTS_HISTORY'
        | 'REPORTING'
        | 'MY_REPORTING'
        | 'TAX_REPORTING'
        | 'MONTHLY_REPORTING'
        | 'CASH_REGISTER'
        | 'MENU_EDITOR'
        | 'RESERVATIONS'
        | 'PROCUREMENT'
        | 'ANALYTICS'
        | 'PAYMENTS'
        | 'MARKETING'
        | 'FEEDBACK'
        | 'SETTINGS'
      )[];
      disableCancelledItemPrintout?: boolean;
      pin?: string;
      discountPin?: string;
      /** @enum {string} */
      restaurantServiceMode?: 'TABLE' | 'EXPRESS' | 'HYBRID';
      onboardCompleted?: boolean;
      /** @enum {string} */
      defaultLanguage?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt';
      additionalLanguages?: (
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt'
      )[];
      templateConfig?: components['schemas']['TemplateConfig'];
      /** Format: int32 */
      estimatedDiningTime?: number;
      /** Format: int32 */
      estimatedPreparationTime?: number;
      checkoutAutoClose?: boolean;
      taxReportConfig?: components['schemas']['TaxReportConfig'];
      pollPrinterAccountIds?: string[];
      datevConfiguration?: components['schemas']['DatevConfiguration'];
      generateTipRecordInCashRegisterForDailyClosing?: boolean;
      createMenuSnapshot?: boolean;
      autoSyncWoltMenu?: boolean;
      partnerConfigs?: components['schemas']['PartnerConfig'][];
      dailyReportConfig?: components['schemas']['DailyReportConfig'];
      receiptOption?: components['schemas']['ReceiptOption'];
      useUpdatedMenuEditor?: boolean;
      useUpdatedTerminal?: boolean;
      useUpdatedMobileTerminal?: boolean;
      useUpdatedCheckout?: boolean;
      useUpdatedMobileCheckout?: boolean;
      takeawayConfig?: components['schemas']['TakeawayConfig'];
      webshopConfig?: components['schemas']['WebshopConfig'];
      terminalConfig?: components['schemas']['TerminalConfig'];
      preparationTimeInMinutes?: {
        [key: string]: number;
      };
      hasFlashTakeaway?: boolean;
      allowTableSelectionInExpress?: boolean;
      allowPagerInputInExpress?: boolean;
      createPayoutOnDailyClosing?: boolean;
      giftCardCheckoutEnabled?: boolean;
      giftCardSettings?: components['schemas']['GiftCardSettings'];
      courseConfig?: components['schemas']['CourseConfig'];
      scanToOrderConfig?: components['schemas']['ScanToOrderConfig'];
      tabletOrderingConfig?: components['schemas']['TabletOrderingConfig'];
      kioskConfig?: components['schemas']['KioskConfig'];
      disableCancellationReceipt?: boolean;
      enableTerminalPaymentReceipt?: boolean;
      hideItemsWithPriceZero?: boolean;
      hideMenuItemNotesFromCustomers?: boolean;
      orderActions?: components['schemas']['OrderAction'][];
      inventoryConfig?: components['schemas']['InventoryConfig'];
      enableTakeawayInDineIn?: boolean;
      smartschankConfig?: components['schemas']['SmartschankConfig'];
      fiskalyAutoReportingEnabled?: boolean;
      generatePassKitCard?: boolean;
      /** Format: int32 */
      minHoursReportCloseInterval?: number;
      /** Format: int32 */
      warningThresholdReportCloseInHours?: number;
      disableSendToKitchenConfirmation?: boolean;
      receiptConfig?: components['schemas']['ReceiptConfig'];
      enableKeyboardOrderingTerminal?: boolean;
      refundConfig?: components['schemas']['RefundConfig'];
      frontEndSettings?: {
        [key: string]: Record<string, never>;
      };
      displayOrderedItemsTotalsInMenu?: boolean;
      disableFinalReceiptForSplitPayments?: boolean;
      crmSettings?: components['schemas']['CrmSettings'];
      dslSettings?: components['schemas']['DslCallSettings'];
      enabledSingleKitchenPrintoutForTakeawayOrders?: boolean;
      targetKitchenPrinterId?: string;
      deepCopyReorderingEnabled?: boolean;
      openInvoiceSettings?: components['schemas']['OpenInvoiceSettings'];
      customExtrasEnabled?: boolean;
      useFirstOrderingInMonthlyReports?: boolean;
    };
    RestaurantTag: {
      id?: string;
      label?: string;
      /** Format: int32 */
      maxQtdPerPerson?: number;
      /** Format: int32 */
      maxQtdPerPersonPerOrdering?: number;
      /** Format: int32 */
      minQtdPerPersonPerOrderingThatTriggersBlocking?: number;
      /** Format: int32 */
      blockingTimeInMinIfMinQtdPerPersonPerOrderingReached?: number;
    };
    ResultsRestaurant: {
      /** Format: int64 */
      total?: number;
      /** Format: int32 */
      pages?: number;
      items?: (
        | components['schemas']['Restaurant']
        | components['schemas']['RestaurantAustria']
        | components['schemas']['RestaurantBrazil']
      )[];
    };
    ScanToOrderConfig: {
      guestPinEnabled?: boolean;
      guestApprovalPin?: string;
      /** Format: int32 */
      defaultPartySize?: number;
      /** Format: int32 */
      maxQtdPerPerson?: number;
      /** Format: int32 */
      maxQtdPerPersonPerOrdering?: number;
      /** Format: int32 */
      minQtdPerPersonPerOrderingThatTriggersBlocking?: number;
      /** Format: int32 */
      blockingTimeInMinIfMinQtdPerPersonPerOrderingReached?: number;
    };
    SmartschankConfig: {
      ip?: string;
      enabled?: boolean;
    };
    Supplier: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      name?: string;
      legalName?: string;
      address?: components['schemas']['Address'];
    };
    Table: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      deleted?: boolean;
      restaurantId?: string;
      floorId?: string;
      code?: string;
      label?: string;
      labelI18n?: {
        [key: string]: string;
      };
      description?: string;
      descriptionI18n?: {
        [key: string]: string;
      };
      /** @enum {string} */
      status?: 'FREE' | 'TAKEN' | 'PAYMENT_REQUESTED';
      layout?: components['schemas']['TableLayout'];
      restaurant?: components['schemas']['Restaurant'];
      customerId?: string;
      orderId?: string;
      orderingDeviceId?: string;
      orderingDeviceIds?: string[];
      /** @enum {string} */
      category?: 'INSIDE' | 'GARDEN' | 'TERRACE' | 'OUTSIDE';
      /** Format: int32 */
      minimumCapacity?: number;
      /** Format: int32 */
      capacity?: number;
      /** Format: int32 */
      order?: number;
      printers?: components['schemas']['Printer'][];
      printerIds?: string[];
      reservable?: boolean;
      /** @example Europe/Paris */
      zoneId?: string;
      /** @enum {string} */
      language?:
        | 'en'
        | 'de'
        | 'zh'
        | 'bg'
        | 'cs'
        | 'da'
        | 'el'
        | 'es'
        | 'et'
        | 'fi'
        | 'fr'
        | 'hr'
        | 'hu'
        | 'it'
        | 'ja'
        | 'ko'
        | 'nl'
        | 'pl'
        | 'ro'
        | 'ru'
        | 'sq'
        | 'th'
        | 'tr'
        | 'uk'
        | 'vi'
        | 'zh_CN'
        | 'zh_HK'
        | 'pt_BR'
        | 'pt_PT'
        | 'en_US'
        | 'en_GB'
        | 'de_DE'
        | 'de_AT'
        | 'de_CH'
        | 'de_LU'
        | 'el_GR'
        | 'ko_KR'
        | 'bg_BG'
        | 'cs_CZ'
        | 'da_DK'
        | 'es_ES'
        | 'et_EE'
        | 'fi_FI'
        | 'fr_FR'
        | 'hr_HR'
        | 'hu_HU'
        | 'it_IT'
        | 'ja_JP'
        | 'nl_NL'
        | 'pl_PL'
        | 'ro_RO'
        | 'ru_RU'
        | 'sq_AL'
        | 'th_TH'
        | 'tr_TR'
        | 'uk_UA'
        | 'vi_VN'
        | 'pt'
        | 'hi'
        | 'hi_IN'
        | 'zt';
    };
    TableLayout: {
      /** Format: int32 */
      x?: number;
      /** Format: int32 */
      y?: number;
      /** Format: int32 */
      w?: number;
      /** Format: int32 */
      h?: number;
      /** @enum {string} */
      shape?: 'SQUARE' | 'CIRCLE';
      /** Format: int32 */
      angle?: number;
    };
    TabletOrderingConfig: {
      waiterPinEnabled?: boolean;
      waiterPin?: string;
    };
    Tag: {
      id?: string;
      /** Format: date-time */
      creationTime?: string;
      /** Format: date-time */
      modificationTime?: string;
      /** @enum {string} */
      category?: 'TYPE' | 'CUISINE' | 'LIFESTYLE' | 'ALLERGIES' | 'INGREDIENT';
      identifier?: string;
      label?: string;
      labelI18n?: {
        [key: string]: string;
      };
    };
    TakeawayConfig: {
      /** Format: int32 */
      bufferTime?: number;
      wolt?: components['schemas']['WoltConfig'];
      lieferando?: components['schemas']['LieferandoConfig'];
      allowDriverAssignment?: boolean;
      printDriverOnReceipt?: boolean;
      orderReceiptPrinterId?: string;
      orderReceiptPrintStatus?: (
        | 'OPEN'
        | 'PAYMENT_REQUESTED'
        | 'PENDING'
        | 'PREPARING'
        | 'READY'
        | 'DELIVERING'
        | 'CLOSED'
        | 'CANCELLED'
      )[];
    };
    TaxReportConfig: {
      autoClosure?: boolean;
      /** @example 14:30:00 */
      closureTime?: string;
    };
    TemplateConfig: {
      capabilities?: ('DINE_IN' | 'TAKEAWAY' | 'RESERVATION' | 'EXPRESS')[];
    };
    TerminalConfig: {
      tipEnabled?: boolean;
    };
    UnseenOrder: {
      disabled?: boolean;
      /** @enum {string} */
      action?: 'ACCEPT' | 'REJECT';
      /** Format: int32 */
      waitingTimeInSeconds?: number;
    };
    WebshopConfig: {
      printWebshopUrlQRCodeOnFinalReceipt?: boolean;
      printAddressQrCodeForDriver?: boolean;
      deliveryAreas?: components['schemas']['DeliveryArea'][];
      deliveryRadius?: components['schemas']['DeliveryRadius'][];
      /** @enum {string} */
      deliveryFeeMode?: 'ZIP_CODES' | 'KM_RADIUS';
      /** Format: int32 */
      incomingOrderSound?: number;
      pollSoundNotificationAccountIds?: string[];
      unseenOrder?: components['schemas']['UnseenOrder'];
      pickupDiscountPercentage?: number;
      promotionText?: string;
      disableTakeawayEmailCopy?: boolean;
      disableNewOrderReceipt?: boolean;
      printUnpaidNewOrderReceipt?: boolean;
      pickupPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      deliveryPaymentChannels?: (
        | 'APP'
        | 'CASH'
        | 'CARD'
        | 'LIEFERANDO'
        | 'LIEFERANDO_CASH'
        | 'WOLT'
        | 'FOODPANDA'
        | 'UBEREATS'
        | 'FOOD2GO'
        | 'MCHOICE'
        | 'LEBEN'
        | 'PAYPAL'
        | 'UEBERWEISUNG'
        | 'EC'
        | 'VISA'
        | 'MASTERCARD'
        | 'AMERICAN_EXPRESS'
        | 'JCB'
        | 'STRIPE'
        | 'GUTSCHEIN'
        | 'KAIYUAN'
        | 'SONSTIGE'
        | 'ORDER_SMART'
        | 'ALLO_PAY'
        | 'ALLO_PAY_LINK'
        | 'ALLO_PAY_ONLINE'
        | 'OFFENE_RECHNUNG'
        | 'KAUF_AUF_RECHNUNG'
        | 'ANZAHLUNG'
        | 'SODEXO'
        | 'BAYERNETS'
        | 'EDENRED'
      )[];
      orderWithGoogleEnabled?: boolean;
      webshopHours?: {
        [key: string]: components['schemas']['Period'][];
      };
      hasDifferentWebshopHours?: boolean;
      allowGiftCards?: boolean;
      allowPromoCodes?: boolean;
      /** @enum {string} */
      status?: 'ONLINE' | 'OFFLINE';
      useNewWebshop?: boolean;
      covers?: components['schemas']['Image'][];
    };
    WoltConfig: {
      useV2Integration?: boolean;
    };
    ReservationTimeSuggestions: {
      slots?: components['schemas']['ReservationTimeSuggestionsSlot'][];
      explanation?: components['schemas']['UnavailabilityExplanation'];
    };
    ReservationTimeSuggestionsSlot: {
      /** Format: date-time */
      endTime?: string;
      /** @example 14:30:00 */
      time?: string;
      tableGroupId?: string;
      tableId?: string;
      /** Format: int32 */
      minimumCapacity?: number;
      /** @enum {string} */
      type?: 'AVAILABLE' | 'UNAVAILABLE';
      explanations?: components['schemas']['UnavailabilityExplanation'][];
      tableIds?: string[];
      /** Format: date-time */
      fullTime?: string;
      /** Format: int32 */
      tableCapacity?: number;
      /** Format: int32 */
      overlap?: number;
    };
    TableOverlap: {
      table?: components['schemas']['Table'];
      /** Format: int32 */
      overlapMinutes?: number;
      preSelected?: boolean;
    };
    UnavailabilityExplanation: {
      description?: string;
      conflictingReservations?: components['schemas']['Reservation'][];
      /** Format: date-time */
      periodStartTime?: string;
      /** Format: date-time */
      periodEndTime?: string;
      skippedOverlappingTables?: components['schemas']['TableOverlap'][];
    };
    ReservationTableSuggestionsResponseContract: {
      slots?: components['schemas']['ReservationTableSuggestionsResponseContractSlot'][];
      suggestionsPerSlot?: {
        [key: string]: components['schemas']['TableOverlap'][];
      };
      groupSuggestionsPerSlot?: {
        [key: string]: components['schemas']['TableGroupOverlap'][];
      };
    };
    ReservationTableSuggestionsResponseContractSlot: {
      time?: string;
      /** Format: int32 */
      overlap?: number;
      enabled?: boolean;
    };
    TableGroupOverlap: {
      tables?: components['schemas']['Table'][];
      /** Format: int32 */
      minimumCapacity?: number;
      /** Format: int32 */
      capacity?: number;
      tableGroupId?: string;
      /** Format: int32 */
      overlapMinutes?: number;
      preSelected?: boolean;
    };
    ReservationTimesResponseContract: {
      items?: string[];
    };
    ResultsServiceAvailability: {
      /** Format: int64 */
      total?: number;
      /** Format: int32 */
      pages?: number;
      items?: components['schemas']['ServiceAvailability'][];
    };
    ServiceAvailability: {
      /** Format: int32 */
      totalOfTables?: number;
      /** Format: int32 */
      freeTables?: number;
      /** Format: date-time */
      startTime?: string;
      /** Format: date-time */
      endTime?: string;
      /** Format: date-time */
      lastBookableTime?: string;
      /** Format: int32 */
      people?: number;
    };
    ResultsReservationWindow: {
      /** Format: int64 */
      total?: number;
      /** Format: int32 */
      pages?: number;
      items?: components['schemas']['ReservationWindow'][];
    };
    ResultsTableGroup: {
      /** Format: int64 */
      total?: number;
      /** Format: int32 */
      pages?: number;
      items?: components['schemas']['TableGroup'][];
    };
    ResultsSpecialDay: {
      /** Format: int64 */
      total?: number;
      /** Format: int32 */
      pages?: number;
      items?: components['schemas']['SpecialDay'][];
    };
    ReservationRestaurant: {
      id?: string;
      name?: string;
      logo?: string;
      description?: string;
      tags?: string[];
      services?: ('DINE_IN' | 'TAKEAWAY' | 'RESERVATION' | 'EXPRESS')[];
      phone?: string;
      email?: string;
      website?: string;
      address?: string;
      mapUrl?: string;
      cover?: components['schemas']['Image'][];
      menu?: components['schemas']['Image'][];
      allowCustomerReservations?: boolean;
      /** Format: int32 */
      maxGuestsPerReservation?: number;
      reservationTerms?: components['schemas']['ReservationTerm'][];
    };
    Link: {
      href?: string;
      templated?: boolean;
    };
  };
  responses: never;
  parameters: never;
  requestBodies: never;
  headers: never;
  pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
  getReservationWindow: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationWindow'];
        };
      };
    };
  };
  updateReservationWindow: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ReservationWindow'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationWindow'];
        };
      };
    };
  };
  deleteReservationWindow: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  getReservationWindow_1: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['TableGroup'];
        };
      };
    };
  };
  updateTableGroup: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['TableGroup'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['TableGroup'];
        };
      };
    };
  };
  deleteTableGroup: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  getSpecialDay: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['SpecialDay'];
        };
      };
    };
  };
  updateSpecialDay: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['SpecialDay'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['SpecialDay'];
        };
      };
    };
  };
  deleteSpecialDay: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  createWaiterReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateReservationRequestContract'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  getPartnerTimeSuggestionsValidationInBatch: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ReservationTimeSuggestionValidationContract'][];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationTimeSuggestionValidationContract'][];
        };
      };
    };
  };
  createCustomerReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateReservationRequestContract'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  getReservationWindows: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ResultsReservationWindow'];
        };
      };
    };
  };
  createReservationWindow: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ReservationWindow'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationWindow'];
        };
      };
    };
  };
  getTableGroups: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ResultsTableGroup'];
        };
      };
    };
  };
  createTableGroup: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['TableGroup'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['TableGroup'];
        };
      };
    };
  };
  getSpecialDays: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ResultsSpecialDay'];
        };
      };
    };
  };
  createSpecialDay: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['SpecialDay'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['SpecialDay'];
        };
      };
    };
  };
  uploadCover: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: {
      content: {
        'multipart/form-data': {
          /** Format: binary */
          file: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Image'];
        };
      };
    };
  };
  listPartnerReservations: {
    parameters: {
      query: {
        partnerUserId: string;
      };
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'][];
        };
      };
    };
  };
  createPartnerReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['CreateReservationRequestContract'];
      };
    };
    responses: {
      /** @description Created */
      201: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  addPartnerCredential: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['PartnerCredential'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
  getWaiterReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  updateWaiterReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateReservationRequestContract'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  getReservationConfiguration: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationConfiguration'];
        };
      };
    };
  };
  patchReservationConfiguration: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['ReservationConfiguration'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationConfiguration'];
        };
      };
    };
  };
  getPartnerReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  updatePartnerReservation: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateReservationRequestContract'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  getCustomerReservation: {
    parameters: {
      query?: {
        customerToken?: string;
      };
      header?: never;
      path: {
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  updatePartnerReservation_1: {
    parameters: {
      query?: {
        customerToken?: string;
      };
      header?: never;
      path: {
        reservationId: string;
      };
      cookie?: never;
    };
    requestBody: {
      content: {
        'application/json': components['schemas']['UpdateReservationRequestContract'];
      };
    };
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['Reservation'];
        };
      };
    };
  };
  getRestaurants: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ResultsRestaurant'];
        };
      };
    };
  };
  getCustomerAvailability: {
    parameters: {
      query: {
        day: string;
        people: number;
        reservationId?: string;
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationTimeSuggestions'];
        };
      };
    };
  };
  getTableSuggestions: {
    parameters: {
      query: {
        startTime: string;
        people: number;
        reservationId?: string;
        tableIds?: string[];
        limit?: number;
        offset?: number;
        allResults?: boolean;
        query?: string;
        floorId?: string;
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationTableSuggestionsResponseContract'];
        };
      };
    };
  };
  getWaiterReservationTimes: {
    parameters: {
      query: {
        date: string;
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationTimesResponseContract'];
        };
      };
    };
  };
  getPartnerTimeSuggestions: {
    parameters: {
      query: {
        startDate: string;
        endDate: string;
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ResultsServiceAvailability'];
        };
      };
    };
  };
  getCustomerTimeSuggestions: {
    parameters: {
      query: {
        day: string;
        people: number;
        reservationId?: string;
      };
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationTimeSuggestions'];
        };
      };
    };
  };
  getReservationConfigurationEnabled: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        restaurantId: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': boolean;
        };
      };
    };
  };
  getRestaurantBySlug: {
    parameters: {
      query?: never;
      header: {
        'content-language':
          | 'en'
          | 'de'
          | 'zh'
          | 'bg'
          | 'cs'
          | 'da'
          | 'el'
          | 'es'
          | 'et'
          | 'fi'
          | 'fr'
          | 'hr'
          | 'hu'
          | 'it'
          | 'ja'
          | 'ko'
          | 'nl'
          | 'pl'
          | 'ro'
          | 'ru'
          | 'sq'
          | 'th'
          | 'tr'
          | 'uk'
          | 'vi'
          | 'zh_CN'
          | 'zh_HK'
          | 'pt_BR'
          | 'pt_PT'
          | 'en_US'
          | 'en_GB'
          | 'de_DE'
          | 'de_AT'
          | 'de_CH'
          | 'de_LU'
          | 'el_GR'
          | 'ko_KR'
          | 'bg_BG'
          | 'cs_CZ'
          | 'da_DK'
          | 'es_ES'
          | 'et_EE'
          | 'fi_FI'
          | 'fr_FR'
          | 'hr_HR'
          | 'hu_HU'
          | 'it_IT'
          | 'ja_JP'
          | 'nl_NL'
          | 'pl_PL'
          | 'ro_RO'
          | 'ru_RU'
          | 'sq_AL'
          | 'th_TH'
          | 'tr_TR'
          | 'uk_UA'
          | 'vi_VN'
          | 'pt'
          | 'hi'
          | 'hi_IN'
          | 'zt';
      };
      path: {
        slug: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['ReservationRestaurant'];
        };
      };
    };
  };
  getRestaurantsByPartner: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': (
            | components['schemas']['Restaurant']
            | components['schemas']['RestaurantAustria']
            | components['schemas']['RestaurantBrazil']
          )[];
        };
      };
    };
  };
  findPartnerCredentials: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          '*/*': components['schemas']['PartnerConfig'];
        };
      };
    };
  };
  links: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.spring-boot.actuator.v3+json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
          'application/vnd.spring-boot.actuator.v2+json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
          'application/json': {
            [key: string]: {
              [key: string]: components['schemas']['Link'];
            };
          };
        };
      };
    };
  };
  health: {
    parameters: {
      query?: never;
      header?: never;
      path?: never;
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content: {
          'application/vnd.spring-boot.actuator.v3+json': Record<string, never>;
          'application/vnd.spring-boot.actuator.v2+json': Record<string, never>;
          'application/json': Record<string, never>;
        };
      };
    };
  };
  removePartnerCredential: {
    parameters: {
      query?: never;
      header?: never;
      path: {
        partnerId: 'OPEN_TABLE' | 'QUANDOO' | 'GOOGLE' | 'OTHER';
        id: string;
      };
      cookie?: never;
    };
    requestBody?: never;
    responses: {
      /** @description OK */
      200: {
        headers: {
          [name: string]: unknown;
        };
        content?: never;
      };
    };
  };
}
