import { isServer } from '@monorepo/utils';
import { env } from 'next-runtime-env';
import createClient from 'openapi-fetch';
import type { paths } from './api';

export const baseUrl = `${isServer() ? env('NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL') || env('NEXT_PUBLIC_BASE_URL') + '/reservation-service' || process.env.NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL || '' : env('NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL') || env('NEXT_PUBLIC_BASE_URL') + '/reservation-service' || process.env.NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL || ''}`;

export const client = createClient<paths>({
  baseUrl: baseUrl,
});
