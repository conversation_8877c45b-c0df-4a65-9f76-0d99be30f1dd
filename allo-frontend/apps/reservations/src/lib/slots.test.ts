import { findAdjacentSlots, findClosestSlot, getFullHours } from './slots';

const getDate = (hours: number, minutes: number) => {
  return new Date(
    `2028-01-01T${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`
  );
};

describe('getFullHours', () => {
  it('returns only full hours', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: false },
      { date: getDate(12, 30), available: true },
      { date: getDate(12, 45), available: true },
      { date: getDate(13, 0), available: false },
      { date: getDate(13, 15), available: true },
    ];

    expect(getFullHours(slots)).toStrictEqual([
      { date: getDate(12, 0), available: true },
      { date: getDate(13, 0), available: false },
    ]);
  });
});

describe('findClosestSlot', () => {
  it("returns the target if it's available", () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: false },
      { date: getDate(12, 30), available: true },
      { date: getDate(12, 45), available: true },
      { date: getDate(13, 0), available: false },
      { date: getDate(13, 15), available: true },
    ];

    expect(findClosestSlot(slots, getDate(12, 30))?.date).toStrictEqual(getDate(12, 30));
  });

  it("returns the slot regardless of whether it's available or not", () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: false },
      { date: getDate(12, 30), available: true },
      { date: getDate(12, 45), available: true },
      { date: getDate(13, 0), available: false },
      { date: getDate(13, 15), available: true },
    ];

    expect(findClosestSlot(slots, getDate(13, 0))?.date).toStrictEqual(getDate(13, 0));
  });

  it('returns the slot after the target if no option at the target', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(14, 0), available: false },
    ];

    expect(findClosestSlot(slots, getDate(13, 0))?.date).toStrictEqual(getDate(14, 0));
  });

  it('returns the last slot if no option after the target', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(13, 0), available: false },
    ];

    expect(findClosestSlot(slots, getDate(14, 0))?.date).toStrictEqual(getDate(13, 0));
  });

  it('returns null if no slots are available', () => {
    expect(findClosestSlot([], getDate(13, 0))).toBeNull();
  });
});

describe('findAdjacentSlots', () => {
  it('returns the adjacent slots', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: true },
      { date: getDate(12, 30), available: true },
      { date: getDate(12, 45), available: true },
      { date: getDate(13, 0), available: true },
      { date: getDate(13, 15), available: true },
    ];

    expect(
      findAdjacentSlots(slots, getDate(13, 0), {
        maxSlots: 3,
      })
    ).toStrictEqual([
      { date: getDate(12, 45), available: true },
      { date: getDate(13, 0), available: true },
      { date: getDate(13, 15), available: true },
    ]);
  });

  it("doesn't return past slots", () => {
    jest.useFakeTimers();
    jest.setSystemTime(getDate(12, 1));

    const slots = [
      { date: getDate(11, 0), available: true },
      { date: getDate(12, 0), available: true },
      { date: getDate(13, 0), available: true },
      { date: getDate(14, 0), available: true },
      { date: getDate(15, 0), available: true },
    ];

    expect(findAdjacentSlots(slots, getDate(14, 0))).toStrictEqual([
      { date: getDate(13, 0), available: true },
      { date: getDate(14, 0), available: true },
      { date: getDate(15, 0), available: true },
    ]);

    jest.useRealTimers();
  });

  it("doesn't return slots outside of the margin", () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 30), available: true },
      { date: getDate(13, 0), available: true },
      { date: getDate(18, 0), available: true },
      { date: getDate(18, 30), available: true },
      { date: getDate(19, 0), available: true },
      { date: getDate(19, 30), available: true },
      { date: getDate(20, 0), available: true },
    ];

    expect(
      findAdjacentSlots(slots, getDate(18, 0), { hourMargin: 1, maxSlots: 6 })
    ).toStrictEqual([
      { date: getDate(18, 0), available: true },
      { date: getDate(18, 30), available: true },
      { date: getDate(19, 0), available: true },
    ]);
  });

  it("doesn't return unavailable slots", () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 30), available: false },
    ];

    expect(findAdjacentSlots(slots, getDate(12, 0), { maxSlots: 3 })).toStrictEqual([
      { date: getDate(12, 0), available: true },
    ]);
  });

  it('fills up to max slots', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: true },
      { date: getDate(12, 45), available: true },
      { date: getDate(14, 0), available: true },
      { date: getDate(15, 0), available: true },
      { date: getDate(15, 30), available: true },
      { date: getDate(17, 0), available: true },
      { date: getDate(17, 15), available: true },
      { date: getDate(18, 30), available: true },
      { date: getDate(19, 0), available: true },
      { date: getDate(19, 30), available: true },
      { date: getDate(20, 0), available: true },
      { date: getDate(21, 15), available: true },
      { date: getDate(21, 45), available: true },
    ];

    expect(findAdjacentSlots(slots, getDate(17, 0))).toStrictEqual([
      { date: getDate(15, 0), available: true },
      { date: getDate(15, 30), available: true },
      { date: getDate(17, 0), available: true },
      { date: getDate(17, 15), available: true },
      { date: getDate(18, 30), available: true },
      { date: getDate(19, 0), available: true },
    ]);
  });

  it('handles real data correctly', () => {
    const slots = [
      { date: getDate(12, 0), available: true },
      { date: getDate(12, 15), available: false },
      { date: getDate(12, 30), available: true },
      { date: getDate(12, 45), available: false },
      { date: getDate(13, 0), available: false },
      { date: getDate(13, 15), available: true },
      { date: getDate(13, 30), available: false },
      { date: getDate(13, 45), available: true },
      { date: getDate(14, 0), available: false },
      { date: getDate(14, 15), available: false },
      { date: getDate(14, 30), available: true },
      { date: getDate(14, 45), available: false },
      { date: getDate(15, 0), available: true },
      { date: getDate(18, 0), available: false },
      { date: getDate(18, 15), available: true },
      { date: getDate(18, 30), available: false },
      { date: getDate(18, 45), available: false },
      { date: getDate(19, 0), available: true },
      { date: getDate(19, 15), available: true },
      { date: getDate(19, 30), available: false },
      { date: getDate(19, 45), available: true },
      { date: getDate(20, 0), available: false },
      { date: getDate(20, 15), available: true },
      { date: getDate(20, 30), available: true },
      { date: getDate(20, 45), available: true },
      { date: getDate(21, 0), available: false },
      { date: getDate(21, 15), available: false },
      { date: getDate(21, 30), available: false },
      { date: getDate(21, 45), available: true },
      { date: getDate(22, 0), available: false },
    ];

    const target = getDate(18, 0);

    expect(findAdjacentSlots(slots, target)).toStrictEqual([
      { date: getDate(18, 15), available: true },
      { date: getDate(19, 0), available: true },
      { date: getDate(19, 15), available: true },
      { date: getDate(19, 45), available: true },
    ]);
  });
});
