import {
  addHours,
  isAfter,
  isBefore,
  isSameMinute,
  isWithinInterval,
  subHours,
} from 'date-fns';

export type SlotData = {
  endTime: Date;
  fullTime: Date;
  tableCapacity: number;
  time: string;
  type: 'AVAILABLE' | 'UNAVAILABLE';
};

export type Slot = {
  date: Date;
  available: boolean;
  tableIds?: [];
  fullTime?: Date;
};

// UTILS

export const getFullHours = (slots: Slot[]) => {
  return slots.filter((slot) => slot.date.getMinutes() === 0);
};

export const findClosestSlot = (slots: Slot[], target: Date) => {
  // Find the first available slot at or after the target hour
  const targetSlot = slots.find((slot) => isSameMinute(slot.date, target));

  if (targetSlot) return targetSlot;

  // If no slots found after target, look for the closest after
  const closestAfter = slots.find((slot) => isAfter(slot.date, target));

  if (closestAfter) return closestAfter;

  // return the last one, or null
  return slots[slots.length - 1] || null;
};

export const findAdjacentSlots = (
  slots: Slot[],
  target: Date,
  config: {
    hourMargin?: number;
    maxSlots?: number;
  } = {}
) => {
  const { hourMargin = 2, maxSlots = 6 } = config;

  const validSlots = slots.filter((slot) => {
    const isInThePast = isBefore(slot.date, new Date());

    const isWithinMargin = isWithinInterval(slot.date, {
      start: subHours(target, hourMargin),
      end: addHours(target, hourMargin),
    });

    return !isInThePast && isWithinMargin && slot.available;
  });

  const targetSlot = findClosestSlot(validSlots, target);

  if (!targetSlot) return [];

  const targetIndex = validSlots.indexOf(targetSlot);

  const slotsEachSide = Math.floor((maxSlots - 1) / 2);
  const extraSlot = maxSlots % 2 === 0 ? 1 : 0;

  const startIndex = Math.max(0, targetIndex - slotsEachSide);
  const endIndex = Math.min(
    validSlots.length - 1,
    targetIndex + slotsEachSide + extraSlot
  );

  return validSlots.slice(startIndex, endIndex + 1);
};
