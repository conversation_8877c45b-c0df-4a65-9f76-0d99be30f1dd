import type { Metadata } from 'next';

import './globals.css';

import '@fontsource-variable/bricolage-grotesque';
import '@fontsource-variable/inter';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale } from 'next-intl/server';
import { PublicEnvScript } from 'next-runtime-env';
import { notFound } from 'next/navigation';
import { locales } from '~/i18n';

export const metadata: Metadata = {
  title: 'allO Reservations',
  description: 'Reservations',
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const locale = await getLocale();

  if (!locale) return notFound();
  if (!locales.includes(locale)) return notFound();

  return (
    <html lang={locale} key={locale}>
      <head>
        <PublicEnvScript />
        <link rel="icon" href="/apps/reservations/src/app/favicon.ico" sizes="any" />
      </head>
      <body>
        <NextIntlClientProvider>{children}</NextIntlClientProvider>
      </body>
    </html>
  );
}
