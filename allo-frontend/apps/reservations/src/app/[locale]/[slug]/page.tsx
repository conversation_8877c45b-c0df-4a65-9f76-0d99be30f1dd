import {
  Badge,
  cn,
  Gallery,
  GalleryControls,
  GalleryImage,
  GalleryProgress,
  Link,
} from '@allo/ui';
import { Copy } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import React from 'react';

import { CopyButton } from '~/components/copy-button';
import { Footer } from '~/components/footer';
import { BookForm } from '~/components/forms/book-form';
import { ServiceOptions } from '~/components/service-options/service-options';
import { Sidebar } from '~/components/sidebar';

import { Language } from '~/lib/api/types';
import { getRestaurant, getRestaurantMetadata } from '~/lib/data';

interface Props {
  params: Promise<{ slug: string; locale: Language }>;
}

export const generateMetadata = (props: Props) => getRestaurantMetadata(props);

export default async function Restaurant({ params }: Props) {
  const { slug, locale } = await params;

  const t = await getTranslations('home');

  const { data: restaurant } = await getRestaurant(slug, locale);
  if (!restaurant || !restaurant?.id) return notFound();

  const menuImages =
    restaurant.menu?.reduce<{ src: string; alt?: string }[]>((acc, image) => {
      if (image.src) acc.push({ src: image.src, alt: image.alt });
      return acc;
    }, []) ?? [];

  return (
    <>
      {restaurant.cover && restaurant.cover.length > 0 && (
        <Gallery data-testid="gallery" autoPlay className="h-[70vh] w-full">
          {restaurant?.cover?.map((image) => (
            <GalleryImage key={image.src} src={image.src} alt={image.alt} />
          ))}
          <GalleryProgress />
          <GalleryControls />
        </Gallery>
      )}
      <div className="relative min-h-screen md:flex">
        <div className="@container flex-1">
          <header className="border-border-soft border-b">
            <div className="flex items-center">
              <div className="border-border-soft flex aspect-square size-20 items-center justify-center border-r p-4 md:size-24 xl:size-28">
                {restaurant.logo && (
                  <Image
                    className="size-full object-cover"
                    src={restaurant.logo}
                    alt="" // decorative
                    width={72}
                    height={72}
                  />
                )}
              </div>
              <div className="w-full px-4 md:px-8">
                <h1
                  data-testid="restaurant-name"
                  className="font-display mx-auto line-clamp-2 max-w-6xl text-xl leading-tight md:text-2xl xl:text-3xl"
                >
                  {restaurant.name}
                </h1>
              </div>
            </div>
          </header>

          <main className="border-border-soft border-b">
            {((restaurant?.tags?.length || 0) > 0 || restaurant?.description) && (
              <Section data-testid="restaurant-tags">
                {restaurant.tags && restaurant.tags.length > 0 && (
                  <div className="mb-4 flex flex-wrap gap-3 md:mb-6">
                    {restaurant.tags.map((tag) => (
                      <Badge key={tag}>{tag}</Badge>
                    ))}
                  </div>
                )}
                {restaurant.description && (
                  <p
                    data-testid="restaurant-description"
                    style={{ textWrap: 'wrap' }}
                    className="font-display text-lg whitespace-pre"
                  >
                    {restaurant.description}
                  </p>
                )}
              </Section>
            )}

            {menuImages.length > 0 && (
              <Section>
                <Title>{t('menu-photo-gallery-title')}</Title>

                <div className="grid grid-cols-2 gap-4 @md:grid-cols-4">
                  {menuImages.map((image, i) => (
                    <div
                      key={image.src}
                      className="relative aspect-[3/4] overflow-hidden rounded-2xl"
                    >
                      <Image
                        src={image.src}
                        alt={image.alt || ''}
                        className="h-full w-full object-cover"
                        width={1920}
                        height={1080}
                      />
                      {i === menuImages.length - 1 && (
                        <button className="absolute inset-0 flex cursor-pointer items-center justify-center bg-black/50 text-white transition hover:bg-black/70">
                          {t('menu-photo-gallery-view-all')}
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </Section>
            )}

            {restaurant.services && (
              <Section>
                <Title>{t('service-options-title')}</Title>

                <ServiceOptions services={restaurant.services} />
              </Section>
            )}

            <Section>
              <Title>{t('contacts-title')}</Title>

              {/* TODO: add back when we have map coordinates */}
              {/* <div className="bg-background-highlight flex h-[280px] w-full items-center justify-center rounded-2xl">
                <span className="text-foreground-tertiary text-sm"></span>
              </div> */}

              <div className="mt-10 flex flex-wrap gap-10 text-sm">
                {restaurant.phone !== '' && restaurant.phone !== null && (
                  <div>
                    <h3>{t('contacts-phone')}</h3>
                    <Link href={`tel:${restaurant.phone}`}>{restaurant.phone}</Link>
                  </div>
                )}
                {restaurant.email !== '' && restaurant.email !== null && (
                  <div>
                    <h3>{t('contacts-email')}</h3>
                    <Link href={`mailto:${restaurant.email}`}>{restaurant.email}</Link>
                  </div>
                )}
                {restaurant.website !== '' && restaurant.website !== null && (
                  <div>
                    <h3>{t('contacts-website')}</h3>
                    <Link href={`${restaurant.website}`}>{restaurant.website}</Link>
                  </div>
                )}
                {restaurant.address && (
                  <div>
                    <h3>{t('contacts-address')}</h3>
                    <span className="inline-flex items-center gap-2">
                      <Link href={restaurant.mapUrl}>{restaurant.address}</Link>
                      <CopyButton content={restaurant.address}>
                        <Copy />
                      </CopyButton>
                    </span>
                  </div>
                )}
              </div>
            </Section>
          </main>

          <Footer />
        </div>

        <Sidebar>
          <BookForm
            restaurantId={restaurant.id}
            maxGuests={restaurant.maxGuestsPerReservation}
          />
        </Sidebar>
      </div>
    </>
  );
}

const Title = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <h2 className={cn('font-display mb-4 text-xl md:mb-6', className)}>{children}</h2>
  );
};

const Section = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn('not-last:border-b border-border-soft', className)}>
      <div className="mx-auto max-w-6xl p-4 md:p-6 xl:p-14 ">{children}</div>
    </div>
  );
};
