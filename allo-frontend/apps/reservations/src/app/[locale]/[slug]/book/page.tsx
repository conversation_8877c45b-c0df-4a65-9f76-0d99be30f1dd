import { z } from 'zod';

import { notFound } from 'next/navigation';
import { Footer } from '~/components/footer';
import { ReservationCard } from '~/components/reservation-card';

import { Button } from '@allo/ui';
import { getTranslations } from 'next-intl/server';
import { ReservationForm } from '~/components/forms/reservation-form';
import { Link } from '~/i18n/navigation';
import { Language } from '~/lib/api/types';
import { getRestaurant, getRestaurantMetadata } from '~/lib/data';

interface Props {
  params: Promise<{ slug: string; locale: Language }>;
  searchParams: Promise<{
    date: string;
    guests: string;
    slot: string;
    tableId?: string;
    timeSlot?: Date;
  }>;
}

export const generateMetadata = (props: Props) => getRestaurantMetadata(props);

const paramsSchema = z.object({
  guests: z.coerce.number().min(1).max(60),
  date: z.coerce.date(),
  slot: z.string().regex(/^([01]\d|2[0-3]):([0-5]\d)$/, 'Must be in HH:MM format'),
  tableId: z.string().optional(),
  timeSlot: z.optional(z.coerce.date()),
});

export default async function Book({ params, searchParams }: Props) {
  const t = await getTranslations('bookForm');

  const query = await searchParams;
  const parsed = paramsSchema.safeParse(query);
  const { locale, slug } = await params;

  if (!parsed.success) return notFound();

  const { data: restaurant } = await getRestaurant(slug, locale);

  if (!restaurant || !restaurant.name || !restaurant.id) return notFound();

  console.log(parsed.data.timeSlot);

  const isValid = true;
  // TODO: Need to test why this validation fails some times, it shouldn't
  // const isValid = await validateTimeSlot(restaurant.id, {
  //   date: parsed.data.date,
  //   slot: parsed.data.slot,
  //   guests: parsed.data.guests,
  // });

  const [hours, minutes] = parsed.data.slot.split(':');

  if (!hours || !minutes) throw new Error('Invalid slot');

  const slot = parsed.data.timeSlot;
  //   new Date(
  //   parsed.data.timeSlot ||
  //     new Date(parsed.data.date).setHours(Number(hours), Number(minutes))
  // );

  return (
    <div className="@container flex min-h-screen flex-col">
      <div className="flex-1 md:p-6">
        <ReservationCard
          restaurantName={restaurant.name}
          guests={parsed.data.guests}
          slot={slot}
          locale={locale}
        >
          {isValid ? (
            <ReservationForm
              restaurantId={restaurant.id}
              guests={parsed.data.guests}
              slot={slot}
              reservationTerms={restaurant.reservationTerms}
              tableId={parsed.data.tableId}
            />
          ) : (
            <>
              <div className="mx-auto max-w-sm text-center">
                <h2 className="mt-4 text-xl">{t('book-not-available-title')}</h2>

                <p className="text-foreground-secondary mt-2 text-sm">
                  {t('book-not-available-text')}
                </p>
              </div>

              <div className="mt-10">
                <Button asChild variant="accent" className="w-full">
                  <Link href={`/${slug}`}>{t('book-not-available-button-text')}</Link>
                </Button>
              </div>
            </>
          )}
        </ReservationCard>
      </div>

      <Footer />
    </div>
  );
}
