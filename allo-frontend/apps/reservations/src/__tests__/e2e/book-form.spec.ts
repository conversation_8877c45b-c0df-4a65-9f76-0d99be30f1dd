import { expect, test } from '@playwright/test';
import { sleep } from '~/__tests__/e2e/utils/utils.ts';

// test.beforeEach(async ({ page }) => {
//   await page.route('**/reservation-service/v1/restaurants/slug/*', (route) => {
//     route.fulfill({
//       status: 200,
//       contentType: 'application/json',
//       body: JSON.stringify(restaurantData),
//     });
//   });
// });

test.skip('Should properly display the booking form page', async ({ page }) => {
  await page.goto('/en/sushi-banana/book?date=2023-05-10&guests=1&slot=12:00');
  await sleep(page, 300);
  await expect(page.locator('span').filter({ hasText: 'SEEN Restaurant' })).toBeVisible();
  await sleep(page, 300);
  await expect(page.getByText('Book table')).toBeVisible();
  // await sleep(page, 300);
  // await expect(page.getByText('Book table')).toHaveAttribute('disabled');
});

test.skip('Should accept input but have wrong data in it', async ({ page }) => {
  await page.goto('/en/sushi-banana/book?date=2023-05-10&guests=1&slot=12:00');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Name' }).fill('John');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Email' }).fill('John');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Phone' }).fill('1234567890');
  // await sleep(page, 300);
  // await expect(page.getByText('Book table')).toBeDisabled();
});

test.skip('Should accept input and enable the book button', async ({ page }) => {
  await page.goto('/en/sushi-banana/book?date=2023-05-10&guests=1&slot=12:00');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Name' }).fill('John');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Email' }).fill('John');
  await sleep(page, 300);
  await page.getByRole('textbox', { name: 'Phone' }).fill('1234567890');
  await sleep(page, 300);
  await expect(page.getByText('Book table')).toBeVisible();
});
