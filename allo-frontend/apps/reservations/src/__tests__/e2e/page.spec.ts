import { expect, test } from '@playwright/test';

import { sleep } from '~/__tests__/e2e/utils/utils.ts';

// test.beforeEach(async ({ page }) => {
//   await page.route('**/reservation-service/v1/restaurants/slug/*', (route) => {
//     route.fulfill({
//       status: 200,
//       contentType: 'application/json',
//       body: JSON.stringify(restaurantData),
//     });
//   });
//   await page.route(
//     '**/reservation-service/v1/restaurants/62b1b639230d5d186d059699/customer/time-suggestions?*',
//     (route) => {
//       route.fulfill({
//         status: 200,
//         contentType: 'application/json',
//         body: JSON.stringify(timeSuggestions),
//       });
//     }
//   );
// });

test.skip('Should render the initial page of the app', async ({ page }) => {
  await page.goto('/en/sushi-banana');
  await expect(page.locator('h1')).toHaveText('SEEN Restaurant 1');

  // Service options
  await expect(page.getByRole('heading', { name: 'Service options' })).toBeVisible();
  await expect(page.getByText('Dine-in')).toBeVisible();
  await expect(page.getByText('Takeaway')).toBeVisible();
  // await expect(page.getByText('Reservation', { exact: true })).toBeVisible();

  //Contact Info
  await expect(page.getByRole('heading', { name: 'Contacts & Location' })).toBeVisible();
  await expect(
    page.getByRole('link', { name: 'Rosenheimer Strasse 143B, 803312 Berlin' })
  ).toBeVisible();
  await expect(page.getByRole('link', { name: '<EMAIL>' })).toBeVisible();
});

test.skip('Should fail to navigate to the landing page', async ({ page }) => {
  await page.goto('/randomLocale/sushi-banana');
  await expect(page.locator('h2')).toHaveText('This page could not be found.');
});

test.skip('Should properly display the booking form in landing page', async ({
  page,
}) => {
  await page.goto('/en/sushi-banana');
  await page.getByRole('button', { name: 'Date' }).click();
  await sleep(page, 300);
  await page.getByRole('button', { name: 'Tomorrow' }).click();
  await sleep(page, 300);
  await page.getByRole('button', { name: ':45' }).first().click();
  await sleep(page, 300);
  await expect(page.getByRole('button', { name: 'Book Now' })).toBeVisible();
});
