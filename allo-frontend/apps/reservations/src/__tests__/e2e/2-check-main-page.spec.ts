import { expect, test } from '@playwright/test';
import { sleep } from '~/__tests__/e2e/utils/utils.ts';
import { baseUrl } from '~/lib/api';
import { Restaurant } from '~/lib/api/types.ts';

// TODO: Will be passed from env
//
let LOCALE: string;
let TEST_SLUG: string;

let restaurantData: Restaurant = {};

test.beforeAll(async () => {
  LOCALE = process.env.TEST_LOCALE || 'de';
  TEST_SLUG = process.env.TEST_SLUG || 'random-slug';

  const restaurantResponse = await fetch(`${baseUrl}/v1/restaurants/slug/${TEST_SLUG}`, {
    headers: {
      accept: '*/*',
      'content-language': 'en',
      'content-type': 'application/json',
    },
    method: 'GET',
    mode: 'cors',
    credentials: 'omit',
  });
  restaurantData = await restaurantResponse.json();
});

test('Should render the main page with the correct title', async ({ page }) => {
  await page.goto(`/${LOCALE}/${TEST_SLUG}`);
  if (restaurantData.name)
    await expect(page.locator('h1')).toHaveText(restaurantData.name);
});

test('Should render a NOT FOUND page', async ({ page }) => {
  await page.goto('/en/random-slug');
  await expect(page.locator('h2')).toHaveText('This page could not be found.');
});

test('Should test the flow of the main page, including the correct data', async ({
  page,
}) => {
  const NUMBER_OF_GUESTS = 3;

  // ** MAIN PAGE **
  await page.goto(`/${LOCALE}/${TEST_SLUG}`);

  // ** Gallery Checks **
  if (restaurantData.cover && restaurantData.cover.length > 0) {
    // We should render the gallery
    await expect(page.getByTestId('gallery')).toBeVisible();
    // We should have the exact amount of img in the gallery
    const galleryImages = page.getByTestId('gallery').locator('img');
    await expect(galleryImages).toHaveCount(restaurantData.cover.length);
  }

  // ** Texts Checks **
  if (restaurantData.name)
    await expect(page.getByTestId('restaurant-name')).toHaveText(restaurantData.name);
  if (restaurantData.description) {
    await expect(page.getByTestId('restaurant-description')).toHaveText(
      restaurantData.description
    );
  }
  if (restaurantData.tags && restaurantData.tags.length > 0) {
    await expect(page.getByTestId('restaurant-tags')).toBeVisible();
  }

  await page.getByRole('combobox', { name: 'Party' }).click();
  await sleep(page, 300);
  await page.getByRole('option', { name: NUMBER_OF_GUESTS.toString() }).click();
  await sleep(page, 300);
  await page.getByRole('button', { name: 'Date' }).click();
  await sleep(page, 300);

  await page.getByRole('button', { name: 'Tomorrow' }).click();
  await sleep(page, 300);

  const value = await page.locator('input[name="date"]').inputValue();
  const timeSuggestionUrl =
    `${baseUrl}/v1/restaurants/${restaurantData.id}/customer/time-suggestions?` +
    new URLSearchParams({
      day: value,
      people: NUMBER_OF_GUESTS.toString(),
    }).toString();
  const response = await fetch(timeSuggestionUrl, {
    method: 'GET',
    headers: {
      accept: '*/*',
      'content-language': 'en',
      'content-type': 'application/json',
    },
    mode: 'cors',
    credentials: 'omit',
  });
  const data = await response.json();
  if (!data?.slots?.length) {
    console.log('No slots available', { data });
    return;
  }

  await page.getByRole('button', { name: ':' }).first().click();
  await sleep(page, 300);
  await expect(page.getByRole('button', { name: 'Book Now' })).toBeVisible();
  await sleep(page, 300);
  await page.getByRole('button', { name: 'Book Now' }).click();
  await sleep(page, 300);

  // ** BOOK FORM **
  await expect(page.getByTestId('reservation-form-title')).toBeVisible();
  if (restaurantData.name) {
    await expect(page.getByTestId('reservation-form-title')).toHaveText(
      restaurantData.name
    );
  }
  await expect(page.getByTestId('reservation-form-details-header')).toBeVisible();
  await expect(page.getByTestId('reservation-form-details-header')).toContainText(
    `${NUMBER_OF_GUESTS} Guests`
  );

  await page.getByTestId('reservation-form-name-field').fill('John');
  await sleep(page, 300);
  await page.getByTestId('reservation-form-email-field').fill('<EMAIL>');
  await sleep(page, 300);
  await page.getByTestId('reservation-form-phone-field').fill('123456789');
  await sleep(page, 300);
  // Terms here would be always there
  await page.getByTestId('reservation-form-extra-terms-field').click();
  await sleep(page, 300);
  // Extra terms that are configured from the POS
  const termsCheckboxes = page
    .getByTestId('reservation-form-terms-field')
    .getByRole('checkbox');
  if ((await termsCheckboxes.count()) > 0) {
    const count = await termsCheckboxes.count();
    for (let i = 0; i < count; i++) {
      await termsCheckboxes.nth(i).check();
      await sleep(page, 300);
    }
  } else {
    console.log('no extras');
  }
  await sleep(page, 300);
});
