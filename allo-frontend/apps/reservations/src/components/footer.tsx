import { cn, <PERSON> } from '@allo/ui';
import { ExternalLink } from 'lucide-react';
import { getTranslations } from 'next-intl/server';
import { LanguageSwitcher } from './language-switcher';

export const Footer = async ({ className }: { className?: string }) => {
  const t = await getTranslations('footer');

  return (
    <footer
      className={cn('mx-auto w-full max-w-6xl p-4 text-sm md:p-6 xl:px-14', className)}
    >
      <div className="flex flex-col-reverse justify-between gap-6 @2xl:flex-row @2xl:items-center">
        <Link
          href="https://allo.restaurant"
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center gap-2"
        >
          <ExternalLink className="shrink-0" />
          allO {new Date().getFullYear()} &copy; {t('all-rights-reserved')}
        </Link>
        <div className="flex flex-col gap-2 @md:flex-row @md:items-center @md:gap-4">
          <LanguageSwitcher />
          <Link href="https://allo.restaurant/privacy-policy" target="_blank">
            {t('privacy-policy')}
          </Link>
          <Link href="https://allo.restaurant/terms-guest" target="_blank">
            {t('terms-of-service')}
          </Link>
        </div>
      </div>
    </footer>
  );
};
