import { X } from 'lucide-react';

export const MobileHeader = ({
  children,
  onClose,
}: {
  children: React.ReactNode;
  onClose?: () => void;
}) => {
  return (
    <div className="border-border-soft flex h-14 items-center justify-between gap-2 border-b px-4 text-sm md:hidden">
      <p className="line-clamp-1 flex flex-1 items-center gap-2">{children}</p>
      {typeof onClose === 'function' && (
        <button
          className="flex size-6 translate-x-2 items-center justify-center"
          aria-label="Close"
          onClick={onClose}
        >
          <X />
        </button>
      )}
    </div>
  );
};
