'use client';

import { Dropdown, DropdownItem, DropdownItems, DropdownTrigger } from '@allo/ui';
import { Earth } from 'lucide-react';
import { useLocale } from 'next-intl';
import { useSearchParams } from 'next/navigation';
import { languageMap } from '~/i18n';
import { usePathname } from '~/i18n/navigation';

export const LanguageSwitcher = () => {
  const currentLocale = useLocale();

  const pathname = usePathname();
  const searchParams = useSearchParams();

  const fullPath = pathname + (searchParams.size ? `?${searchParams.toString()}` : '');

  return (
    <Dropdown>
      <DropdownTrigger
        className="inline-flex cursor-pointer items-center gap-2"
        type="button"
      >
        <Earth /> {languageMap[currentLocale]}
      </DropdownTrigger>
      <DropdownItems className="w-40 text-sm">
        {Object.entries(languageMap)
          .filter(([locale]) => locale !== currentLocale)
          .map(([locale, language]) => (
            <DropdownItem key={locale} asChild>
              <a href={`/${locale}${fullPath}`}>{language}</a>
            </DropdownItem>
          ))}
      </DropdownItems>
    </Dropdown>
  );
};
