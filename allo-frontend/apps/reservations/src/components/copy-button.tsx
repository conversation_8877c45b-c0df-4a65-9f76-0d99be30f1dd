'use client';

import { Tooltip } from '@allo/ui';
import { useEffect, useState } from 'react';

export const CopyButton = ({
  content,
  children,
}: {
  content: string;
  children: React.ReactNode;
}) => {
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (copied) {
      setTimeout(() => {
        setCopied(false);
      }, 1000);
    }
  }, [copied]);

  return (
    <Tooltip content={copied ? 'Copied' : 'Copy to clipboard'} persistOnClick>
      <button
        className="cursor-pointer"
        onClick={() => {
          navigator.clipboard.writeText(content);
          setCopied(true);
        }}
      >
        {children}
      </button>
    </Tooltip>
  );
};
