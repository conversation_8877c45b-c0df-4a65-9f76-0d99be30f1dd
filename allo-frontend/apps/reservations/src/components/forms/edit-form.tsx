'use client';

import { <PERSON><PERSON>, FieldError } from '@allo/ui';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { DateSelection } from '~/components/forms/date-selection';
import { GuestSelection } from '~/components/forms/guest-selection';
import { TimeSelection } from '~/components/forms/time-selection';
import { useRouter } from '~/i18n/navigation';
import { updateReservation } from '~/lib/data';

interface EditFormProps {
  initialData: {
    guests: number;
    slot: Date;
  };
  reservationId: string;
  restaurantId: string;
}

export const EditForm = ({ initialData, reservationId, restaurantId }: EditFormProps) => {
  const t = useTranslations('bookForm');
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(false);

  const [guests, setGuests] = useState(initialData.guests);
  const [date, setDate] = useState(initialData.slot);
  const [slot, setSlot] = useState<Date | null>(initialData.slot);

  const handleSubmit = async () => {
    if (!slot) return;

    setIsLoading(true);
    setError(false);

    const res = await updateReservation(reservationId, {
      guests,
      slot,
    });

    if (!res.data?.id) {
      setError(true);

      return setIsLoading(false);
    }

    router.push(`/reservations/${res.data.id}`);
  };

  return (
    <form inert={isLoading} className="flex flex-1 flex-col md:h-full">
      <h2 className="font-display mb-5 hidden text-base md:block">{t('edit-title')}</h2>

      <div className="flex flex-col gap-5">
        <div className="flex gap-2">
          <GuestSelection guests={guests} setGuests={setGuests} />
          <DateSelection date={date} setDate={setDate} />
        </div>
        <TimeSelection
          restaurantId={restaurantId}
          guests={guests}
          date={date}
          slot={slot}
          setSlot={setSlot}
          reservationId={reservationId}
        />
      </div>

      {error && <FieldError className="block mt-10">{t('edit-error')}</FieldError>}

      <Button
        disabled={!slot}
        isLoading={isLoading}
        className="mt-6"
        variant="accent"
        onClick={handleSubmit}
      >
        {t('edit-button-text')}
      </Button>
    </form>
  );
};
