'use client';

import {
  DatePicker,
  DatePickerPanel,
  DatePickerTrigger,
  DropdownDivider,
  DropdownItem,
  Label,
} from '@allo/ui';
import { format, isPast, isToday, isTomorrow, nextFriday } from 'date-fns';
import { useTranslations } from 'next-intl';

interface DateSelectionProps {
  date: Date;
  setDate: (date: Date) => void;
}

export const DateSelection = ({ date, setDate }: DateSelectionProps) => {
  const t = useTranslations('home');

  return (
    <div className="flex-1">
      <Label className="mb-2 inline-block" htmlFor="date-picker">
        {t('sidebar-date-title')}
      </Label>
      <DatePicker placement="bottom-start">
        <DatePickerTrigger
          className="whitespace-nowrap"
          placeholder="Select date"
          id="date-picker"
        >
          {isToday(date)
            ? t('sidebar-date-today')
            : isTomorrow(date)
              ? t('sidebar-date-tomorrow')
              : format(date, 'E dd MMM')}
        </DatePickerTrigger>
        <DatePickerPanel
          value={date}
          onDateChange={setDate}
          getIsDisabled={(date) => {
            return isPast(date) && !isToday(date);
          }}
          matchReferenceWidth={false}
        >
          <DropdownDivider />
          <DropdownItem
            onSelect={() => {
              setDate(new Date());
            }}
          >
            {t('sidebar-date-today')}
          </DropdownItem>
          <DropdownItem
            onSelect={() => {
              setDate(new Date(new Date().setDate(new Date().getDate() + 1)));
            }}
          >
            {t('sidebar-date-tomorrow')}
          </DropdownItem>
          <DropdownItem
            onSelect={() => {
              setDate(nextFriday(new Date()));
            }}
          >
            {t('sidebar-date-next-friday')}
          </DropdownItem>
        </DatePickerPanel>
      </DatePicker>
      <input type="hidden" name="date" value={format(date, 'yyyy-MM-dd')} />
    </div>
  );
};
