'use client';
import {
  Button,
  Dialog,
  DialogActions,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogTrigger,
  FieldError,
} from '@allo/ui';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useRouter } from '~/i18n/navigation';
import { cancelReservation } from '~/lib/data';

type CancelReservationProps = {
  reservationId: string;
};

export const CancelReservation = ({ reservationId }: CancelReservationProps) => {
  const t = useTranslations('bookForm');

  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(false);

  const handleCancelReservation = async () => {
    setIsLoading(true);
    setError(false);

    const res = await cancelReservation(reservationId);

    if (!res.response.ok) {
      setError(true);
      return setIsLoading(false);
    }

    router.push(`/reservations/${reservationId}/cancelled`);
  };

  return (
    <div
      inert={isLoading}
      className="border-border-soft bg-background-highlight mx-auto w-full border-y md:max-w-[500px] md:rounded-4xl md:border p-4 md:p-6"
    >
      <h2>{t('cancel-modal-button-text')}</h2>
      <p className="text-foreground-secondary text-sm mt-1">
        {t('cancel-button-description')}
      </p>

      <Dialog>
        <DialogTrigger asChild>
          <Button className="w-full mt-4">{t('cancel-button-title')}</Button>
        </DialogTrigger>
        <DialogContent className="w-112">
          <DialogTitle>{t('cancel-modal-title')}</DialogTitle>
          <DialogDescription>{t('cancel-modal-description')}</DialogDescription>

          {error && (
            <FieldError className="block mt-4">{t('cancel-modal-error')}</FieldError>
          )}
          <DialogActions>
            <Button
              variant="negative"
              isLoading={isLoading}
              onClick={handleCancelReservation}
            >
              {t('cancel-modal-cancel-button-text')}
            </Button>
            <DialogClose asChild>
              <Button>{t('cancel-modal-keep-button-text')}</Button>
            </DialogClose>
          </DialogActions>
        </DialogContent>
      </Dialog>
    </div>
  );
};
