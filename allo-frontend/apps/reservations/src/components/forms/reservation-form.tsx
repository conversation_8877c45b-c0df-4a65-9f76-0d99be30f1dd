'use client';

import {
  But<PERSON>,
  Checkbox,
  Field,
  FieldError,
  FieldIsolation,
  Input,
  Label,
  Link,
  Listbox,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  TextareaResize,
} from '@allo/ui';
import { zodResolver } from '@hookform/resolvers/zod';
import { COUNTRY_CODES } from '@monorepo/utils';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useRouter } from '~/i18n/navigation';
import { createReservation } from '~/lib/data';

type ReservationTerms = {
  text?: string;
  textI18n?: Record<string, string>;
};

interface ReservationFormProps {
  restaurantId: string;
  guests: number;
  slot: Date | undefined;
  reservationTerms?: ReservationTerms[];
  tableId?: string;
}

export const ReservationForm = ({
  restaurantId,
  guests,
  slot,
  reservationTerms,
  tableId,
}: ReservationFormProps) => {
  const t = useTranslations('bookForm');
  const router = useRouter();

  const [selectedCountry, setSelectedCountry] = useState(COUNTRY_CODES[0]);

  const schema = z.object({
    name: z.string().min(1, { message: t('error-name-required') }),
    email: z.string().email({ message: t('error-email-invalid') }),
    phone: z.string().min(1, { message: t('error-phone-required') }),
    specialRequests: z.string().optional(),
    terms: z.boolean().refine((val) => val, { message: t('error-terms-required') }),
    dynamicTerms: z.optional(
      z.array(z.boolean().refine((val) => val, { message: t('error-terms-required') }))
    ),
  });

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      specialRequests: '',
      terms: false,
      dynamicTerms: reservationTerms?.map(() => false),
    },
  });

  const [isLoading, setIsLoading] = useState(false);
  const onSubmit = handleSubmit(async ({ name, email, phone, specialRequests }) => {
    setIsLoading(true);
    const res = await createReservation(restaurantId, {
      name,
      email,
      phone: (selectedCountry?.code || '') + phone,
      guests,
      slot,
      specialRequests,
      tableId: tableId || '',
    });

    if (!res.data?.id) {
      setError('root', { message: t('error-reservation-failed') });

      return setIsLoading(false);
    }

    router.push(`/reservations/${res.data.id}`);
  });

  const termsError = errors.terms || errors.dynamicTerms?.find?.((error) => error);

  return (
    <>
      <h2 className="font-display mb-5 text-base">{t('title')}</h2>

      <form inert={isLoading} className="flex flex-1 flex-col gap-5" onSubmit={onSubmit}>
        <Field className="flex flex-col gap-2">
          <Label>{t('name')}</Label>
          <Input
            data-testid="reservation-form-name-field"
            type="text"
            placeholder={t('name-placeholder')}
            {...register('name')}
          />
          {errors.name && <FieldError>{errors.name.message}</FieldError>}
        </Field>

        <Field className="flex flex-col gap-2">
          <Label>{t('email')}</Label>
          <Input
            data-testid="reservation-form-email-field"
            type="email"
            placeholder={t('email-placeholder')}
            {...register('email')}
          />
          {errors.email && <FieldError>{errors.email.message}</FieldError>}
        </Field>

        <Field className="flex flex-col gap-2">
          <Label>{t('phone')}</Label>
          <div className="flex gap-2">
            <div className="w-[120px]">
              <FieldIsolation>
                <Listbox
                  value={selectedCountry}
                  onChange={setSelectedCountry}
                  matchReferenceWidth={false}
                  placement="bottom-start"
                >
                  <ListboxTrigger className="w-full" placeholder="+00">
                    {!!selectedCountry && (
                      <span className="flex items-center gap-2">
                        {selectedCountry.code}
                      </span>
                    )}
                  </ListboxTrigger>
                  <ListboxOptions className="max-h-[250px] w-80">
                    {COUNTRY_CODES.map((country) => (
                      <ListboxOption
                        key={country.code}
                        value={country}
                        className="flex-col items-start gap-0.5"
                      >
                        <span className="text-sm">{country.country}</span>
                        <span className="text-foreground-secondary text-xs">
                          {country.code}
                        </span>
                      </ListboxOption>
                    ))}
                  </ListboxOptions>
                </Listbox>
              </FieldIsolation>
            </div>
            <Input
              type="tel"
              data-testid="reservation-form-phone-field"
              placeholder={t('phone-placeholder')}
              {...register('phone')}
            />
          </div>
          {errors.phone && <FieldError>{errors.phone.message}</FieldError>}
        </Field>

        <Field className="flex flex-col gap-2">
          <Label>
            {t('special-requests')}{' '}
            <span className="text-foreground-tertiary">
              {t('special-requests-optional')}
            </span>
          </Label>
          <TextareaResize
            placeholder={t('special-requests-placeholder')}
            rows={1}
            {...register('specialRequests')}
          />
          {errors.specialRequests && (
            <FieldError>{errors.specialRequests.message}</FieldError>
          )}
        </Field>

        <div className="flex flex-col gap-2">
          <Field
            data-testid="reservation-form-extra-terms-field"
            className="flex items-start gap-2"
          >
            <Checkbox {...register('terms')} />
            <Label>
              {t.rich('terms', {
                privacy: (chunks) => (
                  <Link
                    target="_blank"
                    rel="noopener noreferrer"
                    href="https://allo.restaurant/privacy-policy"
                  >
                    {chunks}
                  </Link>
                ),
                terms: (chunks) => (
                  <Link
                    target="_blank"
                    rel="noopener noreferrer"
                    href="https://allo.restaurant/terms-guest"
                  >
                    {chunks}
                  </Link>
                ),
              })}
            </Label>
          </Field>

          {reservationTerms && (
            <>
              {reservationTerms.map((term, index) => (
                <Field
                  data-testid="reservation-form-terms-field"
                  key={`dynamic_term_${index}`}
                  className="flex items-start gap-2"
                >
                  <Checkbox {...register(`dynamicTerms.${index}`)} />
                  <Label>{term.text}</Label>
                </Field>
              ))}
            </>
          )}

          {termsError && <FieldError>{termsError.message}</FieldError>}
        </div>

        {errors.root && (
          <FieldError className="block mt-10">{errors.root.message}</FieldError>
        )}

        <Button
          data-testid="reservation-form-book-button"
          type="submit"
          variant="accent"
          className="w-full"
          isLoading={isLoading}
        >
          {t('book-table-button-text')}
        </Button>
      </form>
    </>
  );
};
