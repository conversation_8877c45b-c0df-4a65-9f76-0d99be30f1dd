import { addHours, format } from 'date-fns';

import { Calendar, Clock, MapPin, Users } from 'lucide-react';

import { useTranslations } from 'next-intl';
import { getDateFnsLocale } from '~/i18n';
import { Language } from '~/lib/api/types.ts';
import { MobileHeader } from './mobile-header';

interface ReservationCardProps {
  guests: number;
  slot: Date | undefined;
  restaurantName: string;
  locale: Language;
  children: React.ReactNode;
}

export const ReservationCard = ({
  guests,
  slot,
  restaurantName,
  locale,
  children,
}: ReservationCardProps) => {
  const t = useTranslations('home');
  const localeFns = getDateFnsLocale(locale);
  const conpensatedHoursForServer = new Date(addHours(slot, 2));
  return (
    <div className="border-border-soft bg-background-highlight mx-auto w-full border-b md:max-w-[500px] md:rounded-4xl md:border">
      <header>
        <MobileHeader>{restaurantName}</MobileHeader>

        <div
          data-testid="reservation-form-title"
          className="border-border-soft hidden h-14 items-center justify-center gap-2 border-b text-sm md:flex"
        >
          <MapPin />
          <span>{restaurantName}</span>
        </div>

        <div
          data-testid="reservation-form-details-header"
          className="grid grid-cols-3 text-sm"
        >
          {[
            {
              icon: Users,
              label: `${guests} ${t('sidebar-guests-label')}`,
            },
            {
              icon: Calendar,
              label: format(slot, 'EEE. dd/MM', {
                locale: localeFns,
              }),
            },
            {
              icon: Clock,
              label: format(conpensatedHoursForServer, 'HH:mm'),
            },
          ].map(({ icon: Icon, label }) => (
            <div
              key={label}
              className="border-border-soft flex h-12 items-center justify-center gap-2 border-b not-last:border-r md:h-14"
            >
              <Icon />
              <span>{label}</span>
            </div>
          ))}
        </div>
      </header>

      <main className="p-4 md:p-6">{children}</main>
    </div>
  );
};
