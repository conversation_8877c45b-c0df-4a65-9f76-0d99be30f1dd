'use client';

import { Spinner } from '@allo/ui';
import { useEffect } from 'react';
import { useRouter } from '~/i18n/navigation';

export const ClientRedirect = ({
  target,
  delay = 5000,
  className,
}: {
  target: string;
  delay?: number;
  className?: string;
}) => {
  const router = useRouter();

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      router.replace(target);
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [router, target, delay]);

  return <Spinner className={className} />;
};
