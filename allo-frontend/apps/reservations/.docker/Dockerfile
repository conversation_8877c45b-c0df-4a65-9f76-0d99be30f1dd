ARG NODEJS_VERSION=20.19.3
ARG ALPINE_VERSION=3.22
ARG RUNTIME_SECRETS_INIT_IMAGE=europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/secrets-init:0.5.3-alpine
ARG RUNTIME_BASE_IMAGE=node:${NODEJS_VERSION}-alpine${ALPINE_VERSION}



#
# ---- Secrets Init -  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
# hadolint ignore=DL3006
FROM ${RUNTIME_SECRETS_INIT_IMAGE} AS secrets-init



#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
# hadolint ignore=DL3006
FROM ${RUNTIME_BASE_IMAGE} AS base

RUN      apk update \
      && apk upgrade --no-cache \
      && apk add --no-cache \
         bash \
         tini \
         libstdc++ dumb-init \
      && rm -rf /tmp/* \
      && rm -rvf /var/cache/* \
      # no need in --no-cache option above as we need to reuse APKINDEX and we delete afterwards with rm:
      && rm -fv /var/cache/apk/*



#
# ---- Runner -- ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
# hadolint ignore=DL3006
FROM base AS runner


# Don't run production as root
RUN   addgroup --system --gid 1001 nodejs
RUN   adduser --system --uid 1001 nextjs

WORKDIR /app

COPY  --from=secrets-init --chown=nextjs:nodejs --chmod=0755  /secrets-init ./secrets-init

ARG BUILD_APP=kiosk
ENV BUILD_APP=$BUILD_APP
ENV NEXT_TELEMETRY_DISABLED=1

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY  --link --chown=nextjs:nodejs apps/$BUILD_APP/.next/standalone ./next/standalone
COPY  --link --chown=nextjs:nodejs apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP/.next/static ./next/standalone/apps/$BUILD_APP/.next/static
COPY  --link --chown=nextjs:nodejs apps/$BUILD_APP/.next/standalone/apps/$BUILD_APP/public ./next/standalone/apps/$BUILD_APP/public


USER nextjs

EXPOSE 3000
ENV PORT=3000

ENV HOSTNAME="0.0.0.0"

# wrapper can be any installed tool like:
# /sbin/tini
# /usr/bin/dumb-init
# /app/secrets-init --provider google --exit-early
ARG WRAPPER="/app/secrets-init --provider google --exit-early"
ENV WRAPPER=$WRAPPER

ENTRYPOINT [ "/bin/bash", "-c", "${WRAPPER[@]} \"${@}\"", "--"]
CMD printenv | sort && echo ==== && node next/standalone/apps/$BUILD_APP/server.js