import { Badge, cn } from '@allo/ui';
import { Check, X } from 'lucide-react';
import { useMemo } from 'react';
import { ProductConfigurationOptionItem } from '~/components/product-configuration/product-configuration-option-item.tsx';
import { WebShopMenuItemOption, WebShopMenuItemOptionItem } from '~/lib/api/types.ts';
import { getCartItemGroupError } from '~/lib/order/cart/validation';
import { useProductConfigurationContext } from './product-configuration-context';

interface ProductConfigurationGroupProps {
  group: WebShopMenuItemOption;
  shouldValidate: boolean;
  className?: string;
}

export const ProductConfigurationGroupOption = ({
  group,
  shouldValidate,
  className,
}: ProductConfigurationGroupProps) => {
  const { cartItem } = useProductConfigurationContext();

  const isRadioGroup = group.qtd === 1;

  const isRequired = (group.qtd || 0) > 0;
  const isInCart = cartItem.options.some((o) => o.groupId === group.id);

  const error = useMemo(
    () =>
      shouldValidate ? getCartItemGroupError(cartItem, group, 'options') : undefined,
    [cartItem, group, shouldValidate]
  );

  return (
    <div id={group.id} className={cn('border-border-soft not-last:border-b', className)}>
      <div className={cn(shouldValidate && error && 'animate-shake')}>
        <div className="flex items-center gap-2">
          <h2 className="inline">{group.name}</h2>
          {shouldValidate && error ? (
            <div className="flex gap-2">
              <Badge size="sm" variant="negative">
                <X /> {error.message}
              </Badge>
            </div>
          ) : (
            <>
              {isRequired && (
                <Badge size="sm" variant={isInCart && !error ? 'positive' : 'default'}>
                  {isInCart && !error && <Check />} Required
                </Badge>
              )}
            </>
          )}
        </div>
      </div>
      <div className="mt-4 grid auto-rows-fr grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-3">
        {group?.items &&
          group?.items.map((option: WebShopMenuItemOptionItem, index: number) => (
            <ProductConfigurationOptionItem
              preselected={index === 0}
              key={option.id}
              type={isRadioGroup ? 'radio' : 'quantity'}
              groupId={group.id || ''}
              option={option}
            />
          ))}
      </div>
    </div>
  );
};
