'use client';

import {
  <PERSON><PERSON>,
  Price,
  ProductCard,
  ProductCardContent,
  ProductCardDescription,
  ProductCardFooter,
  ProductCardTitle,
  QuantitySelector,
  QuantitySelectorDecrease,
  QuantitySelectorDelete,
  QuantitySelectorIncrease,
  QuantitySelectorQuantity,
  Radio,
} from '@allo/ui';
import { Plus } from 'lucide-react';
import { useEffect } from 'react';
import { WebShopMenuItemOptionItem } from '~/lib/api/types.ts';
import {
  calculateCartItemOptionTotalPrice,
  webShopMenuItemOptionItemToCartItemOption,
} from '~/lib/order/cart/utils';
import { useProductConfigurationContext } from './product-configuration-context';

interface ProductConfigurationOptionProps {
  type: 'radio' | 'quantity';
  groupId: string;
  option: WebShopMenuItemOptionItem;
  preselected: boolean;
}

export const ProductConfigurationOptionItem = ({
  type,
  groupId,
  option,
  preselected = false,
}: ProductConfigurationOptionProps) => {
  const { cartItem, dispatch } = useProductConfigurationContext();
  const optionInCart = cartItem.options.find((o) => option.id === o.id);

  const Wrapper = type === 'radio' ? 'label' : 'div';

  useEffect(() => {
    if (preselected && !optionInCart && type === 'radio') {
      dispatch({
        type: 'set-in-group',
        payload: webShopMenuItemOptionItemToCartItemOption(groupId, option, 1),
      });
    }
  }, [preselected, type]);

  return (
    <ProductCard key={option.id} depth={type !== 'quantity'} asChild>
      <Wrapper>
        <ProductCardContent>
          <ProductCardTitle>
            <span>{option.name}</span>
          </ProductCardTitle>
          {option.description && (
            <ProductCardDescription>{option.description}</ProductCardDescription>
          )}
          <ProductCardFooter>
            <Price
              prefix={
                optionInCart &&
                optionInCart.extraUnitPrice &&
                optionInCart.quantity > optionInCart.initialQuantity
                  ? '+'
                  : undefined
              }
              amount={
                optionInCart
                  ? calculateCartItemOptionTotalPrice(optionInCart) ||
                    option.unitPrice ||
                    0
                  : option.unitPrice || 0
              }
            />

            {type === 'radio' ? (
              <Radio
                name={groupId}
                value={option.id}
                checked={!!(optionInCart && optionInCart.quantity === 1)}
                onChange={() => {
                  dispatch({
                    type: 'set-in-group',
                    payload: webShopMenuItemOptionItemToCartItemOption(
                      groupId,
                      option,
                      1
                    ),
                  });
                }}
              />
            ) : (
              <>
                {optionInCart ? (
                  <QuantitySelector
                    size="sm"
                    quantity={optionInCart.quantity}
                    min={0}
                    max={option.max || undefined}
                    onChange={(qty) => {
                      dispatch({
                        type: 'set',
                        payload: webShopMenuItemOptionItemToCartItemOption(
                          groupId,
                          option,
                          qty
                        ),
                      });
                    }}
                  >
                    {optionInCart.quantity === 1 ? (
                      <QuantitySelectorDelete
                        onClick={() => {
                          return optionInCart.initialQuantity
                            ? dispatch({
                                type: 'set',
                                payload: webShopMenuItemOptionItemToCartItemOption(
                                  groupId,
                                  option,
                                  0
                                ),
                              })
                            : dispatch({
                                type: 'remove',
                                payload: option.id || '',
                              });
                        }}
                      />
                    ) : (
                      <QuantitySelectorDecrease />
                    )}
                    <QuantitySelectorQuantity />
                    <QuantitySelectorIncrease />
                  </QuantitySelector>
                ) : (
                  <Button
                    size="sm"
                    aria-label="Add"
                    square
                    onClick={() => {
                      dispatch({
                        type: 'set',
                        payload: webShopMenuItemOptionItemToCartItemOption(
                          groupId,
                          option,
                          1
                        ),
                      });
                    }}
                  >
                    <Plus />
                  </Button>
                )}
              </>
            )}
          </ProductCardFooter>
        </ProductCardContent>
      </Wrapper>
    </ProductCard>
  );
};
