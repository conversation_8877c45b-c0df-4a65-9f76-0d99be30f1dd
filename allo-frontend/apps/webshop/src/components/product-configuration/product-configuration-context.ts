import { createContext, use } from 'react';
import { CartItem, CartItemOption } from '~/lib/order/types';

type CartItemSet = {
  type: 'set';
  payload: CartItemOption;
};

type CartItemSetExtra = {
  type: 'set-extra';
  payload: CartItemOption;
};

type CartItemSetInGroup = {
  type: 'set-in-group';
  payload: CartItemOption;
};

type CartItemSetInGroupExtra = {
  type: 'set-in-group-extra';
  payload: CartItemOption;
};

type CartItemRemove = {
  type: 'remove';
  payload: string;
};

type CartItemRemoveExtra = {
  type: 'remove-extra';
  payload: string;
};

type CartItemQuantity = {
  type: 'quantity';
  payload: number;
};

type CartItemAction =
  | CartItemSet
  | CartItemSetInGroup
  | CartItemRemove
  | CartItemQuantity
  | CartItemSetInGroupExtra
  | CartItemSetExtra
  | CartItemRemoveExtra;

export const cartItemReducer = (state: CartItem, action: CartItemAction) => {
  switch (action.type) {
    case 'set':
      return {
        ...state,
        options: [
          ...state.options.filter((o) => o.id !== action.payload.id),
          action.payload,
        ],
      };
    case 'set-extra':
      return {
        ...state,
        extras: [
          ...state.extras.filter((o) => o.id !== action.payload.id),
          action.payload,
        ],
      };
    case 'set-in-group':
      return {
        ...state,
        options: [
          ...state.options.filter((o) => o.groupId !== action.payload.groupId),
          action.payload,
        ],
      };
    case 'set-in-group-extra':
      return {
        ...state,
        extras: [
          ...state.extras.filter((o) => o.groupId !== action.payload.groupId),
          action.payload,
        ],
      };
    case 'remove':
      return {
        ...state,
        options: state.options.filter((o) => o.id !== action.payload),
      };
    case 'remove-extra':
      return {
        ...state,
        extras: state.extras.filter((o) => o.id !== action.payload),
      };
    case 'quantity':
      return {
        ...state,
        quantity: action.payload,
      };
    default:
      return state;
  }
};

export const ProductConfigurationContext = createContext<null | {
  cartItem: CartItem;
  dispatch: React.Dispatch<CartItemAction>;
}>(null);

export const useProductConfigurationContext = () => {
  const ctx = use(ProductConfigurationContext);

  if (!ctx) throw new Error('ProductConfigurationContext not found');

  return ctx;
};
