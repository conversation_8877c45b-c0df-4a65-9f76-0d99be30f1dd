import Image from 'next/image';
import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { cdnUrl } from '~/lib/utils/cdn';

/**
 * A wrapper around `next/image` that will display a fallback image if the original
 * image fails to load. If the original image fails to load, it will be replaced
 * by the fallback image.
 */
const ImageWithFallback = (props) => {
  const { src, fallbackSrc, ...rest } = props;
  const [imgSrc, setImgSrc] = useState(src || fallbackSrc);
  const [error, setError] = useState(false);

  useEffect(() => {
    // Reset error state and image source when src prop changes
    setImgSrc(src || fallbackSrc);
    setError(false);
  }, [src, fallbackSrc]);

  const handleError = () => {
    if (!error && fallbackSrc) {
      console.log('Image failed to load, using fallback:', fallbackSrc);
      setImgSrc(fallbackSrc);
      setError(true);
    }
  };

  // If we're already showing the fallback image, use it directly
  if (error || !src) {
    return (
      <div className="isolation">
        <div className="cls-optimization">
          <Image className="media" {...rest} src={fallbackSrc} onError={handleError} />
        </div>
      </div>
    );
  }

  // Process the image URL for CDN if not using fallback
  const shortImgSrc = imgSrc
    ? imgSrc.replace('https://storage.googleapis.com/leviee_public/', '')
    : null;

  const dataTwicSrc = shortImgSrc ? 'image:' + shortImgSrc : '';
  const shortImgSrcDevCdnPreview = shortImgSrc
    ? cdnUrl + '/c/p/twicpics/' + shortImgSrc + '?twic=v1/output=preview'
    : fallbackSrc;

  return (
    <div className="isolation h-full">
      <div
        className="cls-optimization h-full"
        style={rest?.height && rest?.width ? { paddingTop: rest?.height } : null}
      >
        <Image
          className="media"
          {...rest}
          src={shortImgSrcDevCdnPreview}
          data-twic-src={dataTwicSrc}
          onError={handleError}
        />
      </div>
    </div>
  );
};

ImageWithFallback.propTypes = {
  src: PropTypes.string,
  fallbackSrc: PropTypes.string.isRequired,
  style: PropTypes.object,
  width: PropTypes.number,
  height: PropTypes.number,
  sizes: PropTypes.string,
  alt: PropTypes.string,
  className: PropTypes.string,
};

export default ImageWithFallback;
