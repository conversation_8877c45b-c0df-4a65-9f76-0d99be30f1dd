import {
  Card,
  Label,
  Listbox,
  ListboxEmpty,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  Radio,
  RadioGroup,
  Skeleton,
  Spinner,
} from '@allo/ui';
import { TZDate } from '@date-fns/tz';
import { format, isToday, isTomorrow, set } from 'date-fns';
import { toZonedTime } from 'date-fns-tz';
import { CalendarIcon, ClockIcon, ZapIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useState } from 'react';
import useSWR from 'swr';
import { getScheduleTimes } from '~/lib/api';
import { useOrderConfiguration } from '~/lib/order/store';

import { OrderTime, OrderTimeType } from '~/lib/order/types';
import { useRestaurant } from '~/lib/restaurant';
import { useRestaurantConfig } from '~/lib/store/restaurantConfig';
import { isRestaurantOpenByOpeningHours } from '~/lib/utils/dates';

interface TimeSelectionProps {
  value: OrderTime | null;
  onChange: (value: OrderTime | null) => void;
}

export const TimeSelection = ({ value, onChange }: TimeSelectionProps) => {
  // use a new state here instead of directly using the value
  // because we need to reset the time from time to time (e.g.: when changing date or hour)
  // and we don't want to trigger a `type` change

  const { openingHours } = useRestaurant();
  const t = useTranslations();
  const { isOpen: isRestaurantOpenConfig } = useRestaurantConfig();

  const isOpen = isRestaurantOpenByOpeningHours(openingHours) || isRestaurantOpenConfig;
  const [type, setType] = useState<OrderTime['type']>(
    isOpen ? (value?.type ?? 'standard') : 'schedule'
  );

  return (
    <div className="space-y-4">
      <RadioGroup className="flex gap-2" defaultValue={isOpen ? 'standard' : 'schedule'}>
        {(['standard', 'schedule'] as OrderTimeType[]).map((option) => (
          <Card
            key={option}
            depth
            className="text-sm px-3 py-2 flex flex-1 rounded-xl items-center justify-between gap-2"
            asChild
          >
            <label>
              {option === 'standard' && (
                <ZapIcon className="text-foreground-secondary size-3.5" />
              )}
              {option === 'schedule' && (
                <CalendarIcon className="text-foreground-secondary size-3.5" />
              )}
              <span className="capitalize mr-auto">{t(option)}</span>
              <Radio
                id={option}
                name="order-time"
                checked={type === option}
                disabled={option === 'standard' && !isOpen}
                onChange={() => {
                  setType(option);
                  onChange(option === 'standard' ? { type: 'standard' } : null);
                }}
              />
            </label>
          </Card>
        ))}
      </RadioGroup>
      {type === 'standard' && <StandardTimeWarning />}
      {type === 'schedule' && <ScheduleTimeSelection value={value} onChange={onChange} />}
    </div>
  );
};

export const StandardTimeWarning = () => {
  const t = useTranslations();
  const restaurant = useRestaurant();
  const { type } = useOrderConfiguration();

  const preparationTime =
    type === 'DELIVERY'
      ? restaurant.deliveryPreparationTimeInMinutes
      : restaurant.pickupPreparationTimeInMinutes;

  return (
    <p>
      {preparationTime && (
        <span>
          {t('earliest-possible-arrival-time', {
            start: preparationTime.min,
            end: preparationTime.max,
          })}
        </span>
      )}
      {!preparationTime && (
        <span className="text-foreground-secondary">
          {t('unable-to-load-delivery-window')}
        </span>
      )}
    </p>
  );
};

const TIMEZONE = 'Europe/Berlin'; // TODO: timezone from restaurant

type ScheduleTimeSelectionProps = Pick<TimeSelectionProps, 'value' | 'onChange'>;

export const ScheduleTimeSelection = ({
  value,
  onChange,
}: ScheduleTimeSelectionProps) => {
  const restaurant = useRestaurant();
  const t = useTranslations();

  const [day, setDay] = useState<string | null>(
    value?.type === 'schedule' ? format(value.date, 'yyyy-MM-dd HH:mm') : null
  );
  const [hour, setHour] = useState<string | null>(
    value?.type === 'schedule' ? format(value.date, 'HH:mm') : null
  );

  const { data: slots = [], isLoading } = useSWR(
    ['getCurrentDeliveryWindow', restaurant.id],
    async () => {
      const slots = await getScheduleTimes(restaurant.id || '');

      return slots.data?.items?.map?.((slot) => ({
        day: new TZDate(slot.day as string, TIMEZONE),
        times: slot.times,
      }));
    }
  );

  const fullHours = useMemo(() => {
    const slotFound = slots?.find?.(
      (slot) => format(slot.day, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
    );

    return slotFound?.times;
  }, [slots, day]);

  const formatWeekday = (day: Date) => {
    if (isToday(day)) return t('today');

    if (isTomorrow(day)) return t('tomorrow');

    return format(day, 'cccc');
  };

  useEffect(() => {
    if (!day || !hour) return;

    const [hours, minutes] = hour?.split(':') || ['00', '00'];
    const _fullDate = day
      ? set(day, { hours: Number(hours), minutes: Number(minutes) })
      : null;

    if (_fullDate && !isNaN(_fullDate.getTime())) {
      const zoned = toZonedTime(_fullDate, TIMEZONE);

      onChange({ type: 'schedule', date: new Date(zoned) });
    }
  }, [day, hour, onChange]);

  return (
    <div className="space-y-3">
      <Label>{t('select-day-and-time')}</Label>
      <div className="grid grid-cols-2 gap-2">
        <Listbox
          value={day}
          onChange={(day) => {
            setDay(day as string);
            setHour(null);
            onChange(null);
          }}
        >
          <ListboxTrigger>
            <CalendarIcon className="shrink-0 text-foreground-secondary" />
            <span className="leading-none capitalize truncate">{formatWeekday(day)}</span>
          </ListboxTrigger>
          <ListboxOptions>
            {slots.map((slot) => (
              <ListboxOption key={slot.day} value={slot.day}>
                <div className="text-sm leading-tight">
                  <p>{formatWeekday(slot.day)}</p>
                  <p className="text-foreground-tertiary">{format(slot.day, 'dd MMM')}</p>
                </div>
              </ListboxOption>
            ))}
          </ListboxOptions>
        </Listbox>
        <Listbox
          value={hour}
          onChange={(hour) => {
            setHour(hour);
            onChange(null);
          }}
        >
          <ListboxTrigger id="time-listbox">
            <span className="text-foreground-secondary inline-flex size-4 items-center justify-center">
              {isLoading ? <Spinner size="sm" /> : <ClockIcon />}
            </span>
            <span>{hour || t('select')}</span>
          </ListboxTrigger>
          <ListboxOptions>
            {fullHours?.map((hour) => (
              <ListboxOption key={hour} value={hour} className="capitalize">
                {hour}
              </ListboxOption>
            ))}
            {!fullHours?.length && (
              <ListboxEmpty className="text-sm">{t('no-options')}</ListboxEmpty>
            )}
          </ListboxOptions>
        </Listbox>
      </div>
      {isLoading ? (
        <div className="grid grid-cols-3 gap-2">
          {new Array(6).fill(null).map((_, i) => (
            <Skeleton key={i} />
          ))}
        </div>
      ) : (
        <>
          {/* {availableSlots.length > 0 ? (
            <div className="grid grid-cols-3 gap-2">
              {availableSlots.slice(0, 6).map((slot, index) => (
                <Button
                  key={index}
                  onClick={() => onChange({ type: 'schedule', date: slot })}
                  variant={
                    value?.type === 'schedule' && isSameMinute(value.date, slot)
                      ? 'accent'
                      : 'primary'
                  }
                >
                  {format(slot, 'HH:mm')}
                </Button>
              ))}
            </div>
          ) : (
            <p>
              <ClockAlertIcon className="size-4 inline-block mr-1.5 text-foreground-secondary mb-0.5" />
              <span className="text-foreground-secondary">{t('no-slots-available')}</span>
            </p>
          )} */}
        </>
      )}
    </div>
  );
};
