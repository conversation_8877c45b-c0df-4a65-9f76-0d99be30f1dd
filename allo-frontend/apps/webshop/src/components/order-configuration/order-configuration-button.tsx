'use client';

import { But<PERSON> } from '@allo/ui';
import { ChevronDownIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useEffect } from 'react';
import { useOrderConfiguration } from '~/lib/order/store';
import { useRestaurant } from '~/lib/restaurant';
import { formatScheduledTime } from '~/lib/utils/dates';
import { useOrderConfigurationDialog } from './order-configuration-dialog';

export const OrderConfigurationButton = () => {
  const t = useTranslations();
  const { type, address, time, setType } = useOrderConfiguration();
  const { open } = useOrderConfigurationDialog();
  const restaurant = useRestaurant();

  const isDelivery = type === 'DELIVERY';
  const hasOptionsToOrder = restaurant.hasDelivery || restaurant.hasPickup;

  useEffect(() => {
    if (!hasOptionsToOrder) {
      setType(null);
    }
  }, [hasOptionsToOrder, setType]);

  return (
    <Button onClick={open}>
      <div className="flex items-center gap-1 max-md:hidden">
        {!hasOptionsToOrder ? (
          <span className="text-foreground-secondary">
            {t('no-order-options-available')}
          </span>
        ) : (
          <>
            <span>{isDelivery ? t('delivery') : t('pick-up')}</span>
            <span>·</span>
            <span>
              {time.type === 'standard' ? t('now') : formatScheduledTime(time.date)}
            </span>
            {isDelivery && (
              <>
                <span>·</span>
                {address ? (
                  <span
                    className="max-w-[20ch] truncate"
                    title={address?.formattedAddress}
                  >
                    {address?.formattedAddress}
                  </span>
                ) : (
                  <span className="text-foreground-secondary">
                    {t('no-address-added-yet')}
                  </span>
                )}
              </>
            )}
          </>
        )}
      </div>
      <div className="md:hidden">{t('modify-order')}</div>
      <ChevronDownIcon className="text-foreground-secondary" />
    </Button>
  );
};
