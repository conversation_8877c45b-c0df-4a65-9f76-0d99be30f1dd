import posthog from 'posthog-js';
import { useCallback } from 'react';
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { WebShopOrderCart } from '~/lib/api/types.ts';
import { getCaptureId } from '../utils/posthog';
import { mergeCartItems, productsToCartItems } from './cart/utils';
import {
  Cart,
  CartItem,
  Discount,
  OrderAddress,
  OrderBilling,
  OrderConfiguration,
  OrderTime,
  OrderType,
} from './types';

export type OrderStore = OrderConfiguration & Cart & { billing: null | OrderBilling };

export type OrderStoreActions = {
  setType: (type: OrderType | null) => void;
  setTime: (time: OrderTime) => void;
  setAddress: (address: OrderAddress) => void;
  setDiscount: (discount: Discount | null) => void;
  addItem: (item: CartItem) => void;
  setItems: (items: CartItem[]) => void;
  removeItem: (id: string, amount: number) => void;
  setBillingDetails: (billing: OrderBilling | null) => void;
  setCartData: (cart: WebShopOrderCart, withResetStore: boolean) => void;
  setCartNotes: (notes: string) => void;
};

export const useOrderStore = create<OrderStore & OrderStoreActions>()(
  devtools(
    persist(
      (set) => ({
        cartId: '',
        items: [],
        discount: null,
        type: 'DELIVERY',
        address: null,
        time: { type: 'standard' },
        billing: null,
        totalPrice: 0,
        priceWithoutTaxes: 0,
        taxesTotal: 0,
        priceDetails: [],
        notes: '',
        // order methods
        setCartNotes: (notes: string) => {
          set((state) => ({ ...state, notes }));
        },
        setCartData: (cartData: WebShopOrderCart) => {
          set((state) => {
            const items = productsToCartItems(cartData.items || []);
            return {
              ...state,
              ...cartData,
              items: items || [],
              totalPrice: cartData.total || 0,
              taxesTotal: cartData.taxesTotal || 0,
              priceWithoutTaxes: cartData.totalWithoutTaxes || 0,
              cartId: cartData.id,
            };
          });
        },
        setType: (type) =>
          set((state) => {
            if (type === 'PICKUP') {
              return { ...state, type };
            }

            return { ...state, type };
          }),
        setTime: (time) => set((state) => ({ ...state, time })),
        setAddress: (address) =>
          set((state) => {
            if (state.type === 'DELIVERY') {
              return { ...state, address };
            }

            console.warn('[order-store]: Address can only be set for delivery orders');

            return state;
          }),

        // cart methods
        setDiscount: (discount) => set((state) => ({ ...state, discount })),
        addItem: (item) => {
          return set((state) => {
            const updatedItems = mergeCartItems([...state.items, item]);
            const updatedItem = updatedItems.find((i) => i.id === item.id)!;

            posthog.capture(getCaptureId('cart', 'add_item', 'click'), {
              item: updatedItem,
              added_quantity: item.quantity,
              previous_total_quantity: updatedItem.quantity - item.quantity,
              total_quantity: updatedItem.quantity,
            });

            return { ...state, items: updatedItems };
          });
        },
        setItems: (items: CartItem[]) => {
          return set((state) => {
            const updatedItems = items.length > 0 ? mergeCartItems(items) : [];
            return { ...state, items: updatedItems };
          });
        },

        removeItem: (id, amount = 1) => {
          return set((state) => {
            const updatedItem = state.items.find((i) => i.id === id);
            if (!updatedItem) return state;

            updatedItem.quantity = Math.max(updatedItem.quantity - amount, 0);

            posthog.capture(getCaptureId('cart', 'remove_item', 'click'), {
              item: updatedItem,
              removed_quantity: amount,
              previous_total_quantity: updatedItem.quantity + amount,
              total_quantity: updatedItem.quantity,
            });

            const updatedItems = state.items
              .map((item) => (item.id === id ? updatedItem : item))
              .filter((item) => item.quantity > 0);

            return { ...state, items: updatedItems };
          });
        },
        // billing methods
        setBillingDetails: (billing) => set((state) => ({ ...state, billing })),
      }),
      { name: 'order-storage' }
    )
  )
);

export const useOrderCart = () => {
  const items = useOrderStore((state) => state.items);
  const cartId = useOrderStore((state) => state.cartId);
  const totalPrice = useOrderStore((state) => state.totalPrice);
  const priceWithoutTaxes = useOrderStore((state) => state.priceWithoutTaxes);
  const taxesTotal = useOrderStore((state) => state.taxesTotal);
  const priceDetails = useOrderStore((state) => state.priceDetails);
  const addItem = useOrderStore((state) => state.addItem);
  const removeItem = useOrderStore((state) => state.removeItem);
  const setItems = useOrderStore((state) => state.setItems);
  const setCartData = useOrderStore((state) => state.setCartData);
  const hasCartId = !!cartId;
  const cartNotes = useOrderStore((state) => state.notes);
  const setCartNotes = useOrderStore((state) => state.setCartNotes);

  return {
    items,
    cartId,
    addItem,
    removeItem,
    setItems,
    setCartData,
    totalPrice,
    priceWithoutTaxes,
    taxesTotal,
    hasCartId,
    priceDetails,
    cartNotes,
    setCartNotes,
  };
};

export const useOrderDiscount = () => {
  const discount = useOrderStore((state) => state.discount);
  const setDiscount = useOrderStore((state) => state.setDiscount);

  return { discount, setDiscount };
};

export const useOrderConfiguration = () => {
  const type = useOrderStore((state) => state.type);
  const time = useOrderStore((state) => state.time);
  const address = useOrderStore(
    (state) => (state.type === 'DELIVERY' && state.address) || undefined
  );
  const billing = useOrderStore((state) => state.billing);

  const setType = useOrderStore((state) => state.setType);
  const setTime = useOrderStore((state) => state.setTime);
  const setAddress = useOrderStore((state) => state.setAddress);
  const setBillingDetails = useOrderStore((state) => state.setBillingDetails);

  const set = useCallback(
    (configuration: OrderConfiguration) => {
      if (configuration.type === 'DELIVERY' && configuration.address) {
        setAddress(configuration.address);
      }

      setType(configuration.type);
      setTime(configuration.time);
    },
    [setType, setTime, setAddress]
  );

  return {
    type,
    address,
    time,
    billing,
    set,
    setType,
    setTime,
    setAddress,
    setBillingDetails,
  };
};

export const useIsValidOrderConfiguration = () => {
  const { type, address } = useOrderConfiguration();

  return type === 'DELIVERY' ? !!address : true;
};
