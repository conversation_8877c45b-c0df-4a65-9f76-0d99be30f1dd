import { components } from './api';

export type WebshopRestaurant = components['schemas']['WebshopRestaurant'];

export type WebshopMenuItem = components['schemas']['MenuItem'];
export type WebshopBranchRestaurant = components['schemas']['WebshopBranchRestaurant'];
export type WebshopMenu = components['schemas']['Menu'];
export type WebShopMenuItemExtra = components['schemas']['Extra'];
export type WebShopMenuItemOption = components['schemas']['Option'];
export type WebShopMenuItemExtraItem = components['schemas']['ExtraItem'];
export type WebShopMenuItemOptionItem = components['schemas']['OptionItem'];
export type WebShopCartNewOrderCart = components['schemas']['NewOrderCartRequest'];
export type WebShopOrderCart = components['schemas']['WebshopOrderCart'];
export type WebShopOrderCartItem = components['schemas']['OrderItem'];
export type WebShopOrderCartAddItemRequest = components['schemas']['AddItemRequest'];
export type WebShopOrder = components['schemas']['WebshopOrder'];
export type OptionDTO = components['schemas']['OptionDTO'];
export type ExtraDTO = components['schemas']['ExtraDTO'];
export type WebshopOpeningHourByWeekDay =
  components['schemas']['WebshopOpeningHourByWeekDay'];
export type WebShopError = {
  title: string;
  status: number;
  timestamp: string;
};
