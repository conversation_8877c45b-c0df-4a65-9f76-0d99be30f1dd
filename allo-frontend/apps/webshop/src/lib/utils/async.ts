export const debounce = <T extends (...args: Parameters<T>) => void>(
  func: T,
  intervalMs = 64
) => {
  let id: ReturnType<typeof setTimeout> | null;

  const debouncedFn = (...args: Parameters<T>) => {
    if (id) {
      clearTimeout(id);
    }

    id = setTimeout(() => {
      func(...args);
    }, intervalMs);
  };

  debouncedFn.cancel = () => {
    if (id) {
      clearTimeout(id);
      id = null;
    }
  };

  return debouncedFn;
};

export const sleep = (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};
