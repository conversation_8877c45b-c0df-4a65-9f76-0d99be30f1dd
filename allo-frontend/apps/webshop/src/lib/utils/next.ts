const BREAKPOINTS = {
  xs: '480',
  sm: '640',
  md: '768',
  lg: '1024',
  xl: '1280',
};

type Breakpoint = keyof typeof BREAKPOINTS;
type Sizes = { [key in Breakpoint | 'default']?: `${number}vw` };

/**
 * Get the next/image sizes based on (tailwind) breakpoints
 */
export const getImageSizes = (sizes: Sizes) => {
  return Object.entries(sizes)
    .sort(([keyA], [keyB]) => {
      if (keyA === 'default') return 1;
      if (keyB === 'default') return -1;

      const breakpointA = parseInt(BREAKPOINTS[keyA as Breakpoint]);
      const breakpointB = parseInt(BREAKPOINTS[keyB as Breakpoint]);

      return breakpointB - breakpointA;
    })
    .map(([key, value]) => {
      if (key === 'default') {
        return value;
      }
      const breakpointValue = BREAKPOINTS[key as Breakpoint];
      return `(min-width: ${breakpointValue}) ${value}`;
    })
    .join(', ');
};
