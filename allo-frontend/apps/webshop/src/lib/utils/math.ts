import { GeoCoordinates } from '~/lib/types';

// convert degrees to radians
export const toRadians = (num: number) => {
  return (num * Math.PI) / 180;
};

// calculate the distance (in kms) between two points on earth using the haversine formula
export const haversine = (pointA?: GeoCoordinates, pointB?: GeoCoordinates) => {
  if (!pointA?.lat || !pointA.lng || !pointB?.lat || !pointB?.lng) return 0;

  const EARTH_RADIUS_KM = 6371;

  const diffLat = toRadians(pointB.lat - pointA.lat);
  const diffLon = toRadians(pointB.lng - pointA.lng);
  const latA = toRadians(pointA.lat);
  const latB = toRadians(pointB.lat);

  const a =
    Math.sin(diffLat / 2) * Math.sin(diffLat / 2) +
    Math.sin(diffLon / 2) * Math.sin(diffLon / 2) * Math.cos(latA) * Math.cos(latB);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return EARTH_RADIUS_KM * c;
};
