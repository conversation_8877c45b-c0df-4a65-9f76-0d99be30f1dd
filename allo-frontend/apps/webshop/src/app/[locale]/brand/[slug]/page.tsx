import { Button } from '@allo/ui';
import Image from 'next/image';
import { notFound } from 'next/navigation';
import storeLocationIllustration from '~/assets/illustrations/store-location.png';
import { Header, HeaderActions, HeaderLogo } from '~/components/header';
import { Menu } from '~/components/menu';
import { Link } from '~/i18n/navigation';
import { fetchBrand, fetchProducts } from '~/lib/mock-data/api';

interface BrandPageProps {
  params: Promise<{ slug: string }>;
}

export default async function BrandPage({ params }: BrandPageProps) {
  const { slug } = await params;

  const brand = await fetchBrand(slug);
  if (!brand) return notFound();

  const products = await fetchProducts();

  return (
    <>
      <Header>
        <HeaderLogo>
          <Image
            src="/imgs/king-loui.png"
            alt="Restaurant Logotype"
            width="56"
            height="26"
          />
        </HeaderLogo>
        <HeaderActions>
          <Button asChild>
            <Link href={`/locations/${brand.slug}`}>Select store</Link>
          </Button>
          {/* <SessionButton /> */}
        </HeaderActions>
      </Header>
      <main>
        <header className="text-sm">
          <div className="text-center flex flex-col items-center max-w-xs mx-auto py-10 px-4">
            <figure className="relative h-20 mb-3">
              <Image
                src={storeLocationIllustration.src}
                width={storeLocationIllustration.width}
                height={storeLocationIllustration.height}
                alt="Store location"
                className="object-contain w-full h-full"
              />
            </figure>
            <h1>No {brand.name} store selected</h1>
            <p className="text-foreground-secondary">
              Please select a store to start your order
            </p>
            <Button asChild variant="accent" className="mt-3" size="sm">
              <Link href={`/locations/${brand.slug}`}>Select store</Link>
            </Button>
          </div>
        </header>
        <Menu products={products} readOnly />
      </main>
    </>
  );
}
