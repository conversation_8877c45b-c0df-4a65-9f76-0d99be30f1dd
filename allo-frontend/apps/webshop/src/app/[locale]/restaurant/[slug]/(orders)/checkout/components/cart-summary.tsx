'use client';

import { <PERSON>ge, Button, cn, Textarea } from '@allo/ui';
import { useTranslations } from 'next-intl';
import posthog from 'posthog-js';
import { useMemo, useTransition } from 'react';
import { Box, BoxContent, BoxFooter, BoxHeader, BoxTitle } from '~/components/box';
import { CartItemCard } from '~/components/cart/cart-item-card';
import { CartPriceSummary } from '~/components/cart/cart-price-summary';
import { useRouter } from '~/i18n/navigation';
import { patchUpdateCart, PaymentChannel, postRequestCartPayment } from '~/lib/api';
import { useTailwindBreakpoint } from '~/lib/hooks/use-tailwind-breakpoint';
import {
  useOrderCart,
  useOrderConfiguration,
  useOrderDiscount,
  useOrderStore,
} from '~/lib/order/store';
import {
  getValidPaymentChannel,
  isBillingMethodValid,
  isValidOrder,
} from '~/lib/order/utils';
import { useRestaurant } from '~/lib/restaurant';
import { getCaptureId } from '~/lib/utils/posthog';

export const CartSummary = () => {
  const t = useTranslations();
  const { items, cartId, hasCartId, cartNotes, setCartNotes } = useOrderCart();
  const { discount, setDiscount } = useOrderDiscount();
  const { billing, address } = useOrderConfiguration();
  const { slug, id: restaurantId } = useRestaurant();
  const [isPending, startTransition] = useTransition();
  const order = useOrderStore();
  const canOrder = useMemo(() => isValidOrder(order), [order]);
  const router = useRouter();

  const isMd = useTailwindBreakpoint('md');

  const onPlaceOrder = () => {
    startTransition(() => {
      posthog.capture(getCaptureId('checkout_page', 'place_order', 'click'), {
        items: order.items,
        discount: order.discount,
        type: order.type,
        address: order.type === 'DELIVERY' ? order.address : undefined,
        time: order.time,
        billing: order.billing,
      });

      if (!billing || !hasCartId || !isBillingMethodValid(billing.paymentMethod)) {
        return;
      }

      const paymentChannel = getValidPaymentChannel(billing.paymentMethod);

      return postRequestCartPayment(
        restaurantId as string,
        cartId,
        paymentChannel,
        billing,
        address
      )
        .then((response) => {
          const { data } = response || {};

          if (data) {
            if (data.paymentChannel === PaymentChannel.ALLO_PAY_ONLINE) {
              if (data.paymentUrl) {
                window.location.href = data.paymentUrl;
              }
            } else {
              const orderId = data.orderId;
              router.push(`/restaurant/${slug}/orders/${orderId}`);
            }
          } else {
            // TODO: ERROR
          }
        })
        .catch((err) => {
          // debugger;
          // TODO: Error
        });
    });
  };

  return (
    <Box className="md:flex flex-col" collapsible={!isMd} initialExpanded={false}>
      <BoxHeader className="relative shrink-0">
        <div className="flex items-center gap-2">
          <BoxTitle>{t('cart-summary')}</BoxTitle>
          <Badge size="xs">{`${items.length} ${items.length === 1 ? t('item') : t('items')}`}</Badge>
        </div>
      </BoxHeader>
      <BoxContent className="p-0 md:p-0 md:overflow-y-auto">
        <div className="min-h-0 -mb-px">
          {items.map((item) => (
            <CartItemCard key={item.id} item={item} />
          ))}
        </div>
      </BoxContent>
      <BoxFooter className="shrink-0">
        <div className="flex flex-col gap-2">
          <h1>{cartNotes !== '' ? t('your-note') : t('add-note')}</h1>
          <Textarea
            rows={2}
            value={cartNotes}
            onChange={(e) => {
              posthog.capture(getCaptureId('checkout_page', 'cart_note', 'change'), {
                cartNotes: e.target.value,
              });

              setCartNotes(e.target.value);
            }}
            onBlur={(e) => {
              posthog.capture(getCaptureId('checkout_page', 'cart_note', 'blur'), {
                note: e.target.value,
              });
              patchUpdateCart(restaurantId || '', { notes: e.target.value }, cartId).then(
                (x) => {
                  if (x.data && x.data.notes) {
                    setCartNotes(x.data.notes);
                  }
                }
              );
            }}
            placeholder={t('cart-note-placeholder')}
            className="w-full"
          />
          <span />
        </div>
        <CartPriceSummary
          items={items}
          discount={discount}
          actions={{
            removeDiscount: () => {
              posthog.capture(getCaptureId('checkout_page', 'discount_code', 'remove'), {
                discount,
              });

              setDiscount(null);
            },
          }}
        />
        <Button
          variant="accent"
          className={cn('mt-3 w-full grow', !canOrder && 'opacity-50')}
          disabled={!canOrder || isPending}
          inert={!canOrder}
          isLoading={isPending}
          onClick={onPlaceOrder}
        >
          {t('place-order')}
        </Button>
      </BoxFooter>
    </Box>
  );
};
