'use client';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from '@allo/ui';
import {
  ClockIcon,
  MapIcon,
  MapPinIcon,
  MoonIcon,
  SmartphoneIcon,
  XIcon,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import posthog from 'posthog-js';
import { ComponentProps, useEffect } from 'react';
import useSWR from 'swr';
import { Hero, HeroBody, HeroContent, HeroCover, HeroMedia } from '~/components/hero';
import { IconButton } from '~/components/icon-button';
import ImageWithFallback from '~/components/ImageWithFallback/image-with-fallback';
import { InlineButton } from '~/components/inline-button';
import { Map, Marker } from '~/components/map';
import { useOrderConfigurationDialog } from '~/components/order-configuration/order-configuration-dialog';
import { Property } from '~/components/property';
import { getRestaurantStatus } from '~/lib/api';
import { WebshopRestaurant } from '~/lib/api/types.ts';
import { useIsValidOrderConfiguration } from '~/lib/order/store';
import { useRestaurantConfig } from '~/lib/store/restaurantConfig';
import {
  formatTime,
  getRestaurantOpenStatus,
  isRestaurantOpenByOpeningHours,
} from '~/lib/utils/dates.ts';
import { getImageSizes } from '~/lib/utils/next';
import { getCaptureId } from '~/lib/utils/posthog';

interface RestaurantHeroProps extends ComponentProps<'section'> {
  webshopRestaurant: WebshopRestaurant;
  slug: string;
}

export const RestaurantHero = ({ webshopRestaurant, slug }: RestaurantHeroProps) => {
  const { open } = useOrderConfigurationDialog();
  const isValidOrder = useIsValidOrderConfiguration();
  const imageCover = webshopRestaurant?.covers?.[0]?.src || '';
  const t = useTranslations();
  const { id } = webshopRestaurant;
  const { setRestaurantStatus } = useRestaurantConfig();

  const { data } = useSWR(
    ['restaurant-status'],
    async () => {
      return getRestaurantStatus(id as string);
    },
    { refreshInterval: 10000 }
  );

  useEffect(() => {
    setRestaurantStatus(data?.data?.isOpen || false);
  }, [data, setRestaurantStatus]);

  const isOpen =
    isRestaurantOpenByOpeningHours(webshopRestaurant.openingHours) || data?.data?.isOpen;

  return (
    <Hero>
      <HeroCover>
        <ImageWithFallback
          style={{
            borderRadius: 'inherit',
            objectFit: 'cover',
            width: '100%',
          }}
          src={imageCover}
          alt={webshopRestaurant.name}
          width={0}
          height={0}
          sizes={getImageSizes({ default: '100vw' })}
          fallbackSrc={'/imgs/item-no-image.png'}
        />
        {!isOpen && (
          <div className="absolute inset-0 bg-black/56 text-white flex items-center justify-center gap-1">
            <MoonIcon />
            <span className="text-sm leading-none">{t('closed')}</span>
          </div>
        )}
      </HeroCover>
      <HeroBody>
        <HeroContent className="text-sm flex flex-col gap-3">
          <h1 className="text-xl flex items-center gap-2">
            {webshopRestaurant.name}
            {!isOpen && (
              <Badge size="xs" variant="negative">
                {t('closed')}
              </Badge>
            )}
          </h1>
          <ul className="space-y-0.5 whitespace-nowrap mb-4">
            <li>
              <Property>
                <MapIcon />
                <span>{webshopRestaurant.address}</span>
              </Property>
            </li>
            <li>
              <Property>
                <ClockIcon />
                <span>{isOpen ? t('open') : t('closed')}</span>
              </Property>
            </li>
            <li>
              <RestaurantDetails webshopRestaurant={webshopRestaurant} slug={slug} />
            </li>
          </ul>

          {/* valid order warning */}
          {!isValidOrder && (
            <div className="mt-auto bg-accent/10 text-sm p-3 rounded-3xl space-y-2">
              <p className="ml-1.5 text-balance">
                Your address will help us confirm the store&apos;s availability.
              </p>
              <Button size="sm" variant="accent" onClick={open}>
                Manage Details
              </Button>
            </div>
          )}
        </HeroContent>
        <HeroMedia>
          {webshopRestaurant?.mapCoordinates?.lat &&
            webshopRestaurant?.mapCoordinates?.lng && (
              <Map
                mapId="restaurant-hero"
                defaultCenter={{
                  lat: webshopRestaurant?.mapCoordinates?.lat || 0,
                  lng: webshopRestaurant?.mapCoordinates?.lng || 0,
                }}
                className="w-full h-full"
              >
                <Marker
                  position={{
                    lat: webshopRestaurant?.mapCoordinates?.lat || 0,
                    lng: webshopRestaurant?.mapCoordinates?.lng || 0,
                  }}
                  label={webshopRestaurant.name}
                  labelVisibility="always"
                />
              </Map>
            )}
        </HeroMedia>
      </HeroBody>
    </Hero>
  );
};

interface RestaurantDetailsProps {
  webshopRestaurant: RestaurantHeroProps['webshopRestaurant'];
  slug: RestaurantHeroProps['slug'];
}

const RestaurantDetails = ({ webshopRestaurant, slug }: RestaurantDetailsProps) => {
  const openStatus = getRestaurantOpenStatus(webshopRestaurant.openingHours);
  const t = useTranslations();
  const { isOpen: isRestaurantOpenConfig } = useRestaurantConfig();

  return (
    <Drawer
      onOpenChange={(open) => {
        if (open) {
          posthog.capture(getCaptureId('restaurant_hero', 'view_details', 'click'), {
            restaurant_name: webshopRestaurant.name,
            restaurant_slug: slug,
          });
        }
      }}
    >
      <DrawerTrigger asChild>
        <InlineButton variant="secondary">{t('view-details')}</InlineButton>
      </DrawerTrigger>
      <DrawerContent className="*:border-t *:border-border-soft **:leading-none pb-12">
        <DrawerHeader className="flex gap-4 items-center justify-between !border-t-0">
          <DrawerTitle>Restaurant Details</DrawerTitle>
          <DrawerClose asChild>
            <IconButton>
              <XIcon />
            </IconButton>
          </DrawerClose>
        </DrawerHeader>
        {webshopRestaurant?.mapCoordinates?.lat &&
          webshopRestaurant?.mapCoordinates?.lng && (
            <Map
              mapId="restaurant-hero"
              defaultCenter={{
                lat: webshopRestaurant?.mapCoordinates?.lat || 0,
                lng: webshopRestaurant?.mapCoordinates?.lng || 0,
              }}
              className="w-full h-48 rounded-none border-none"
            >
              <Marker
                position={{
                  lat: webshopRestaurant?.mapCoordinates?.lat || 0,
                  lng: webshopRestaurant?.mapCoordinates?.lng || 0,
                }}
                label={webshopRestaurant.name}
                labelVisibility="always"
              />
            </Map>
          )}
        <DrawerMain>
          <h1 className="text-xl">{webshopRestaurant.name}</h1>
          {/* TODO: add back when we have tags */}
          {/*<ul className="mt-3 flex flex-wrap gap-1">*/}
          {/*  {restaurant.tags.map((tag) => (*/}
          {/*    <li key={tag}>*/}
          {/*      <Badge size="xs">{tag}</Badge>*/}
          {/*    </li>*/}
          {/*  ))}*/}
          {/*</ul>*/}
        </DrawerMain>
        <DrawerMain>
          <Property>
            <MapPinIcon />
            <span>{webshopRestaurant.address}</span>
          </Property>
        </DrawerMain>
        <DrawerMain>
          <Property>
            <SmartphoneIcon />
            <span>{webshopRestaurant.phone}</span>
          </Property>
          <Property className="mt-1.5 ml-4.5">{webshopRestaurant.email}</Property>
        </DrawerMain>
        <DrawerMain>
          <Property>
            <ClockIcon />
            <span>{isRestaurantOpenConfig ? t('open') : t('closed')}</span>
          </Property>
          {/* Opening hours section */}
          {webshopRestaurant && webshopRestaurant.openingHours ? (
            <ul className="mt-3 space-y-1.5 pl-4.5">
              {webshopRestaurant.openingHours.map((day) => {
                return (
                  <li
                    key={day.dayOfWeek}
                    className="flex items-center justify-between gap-1"
                  >
                    <span className="text-foreground-secondary capitalize">
                      {day.dayOfWeek}
                    </span>
                    <span className="space-y-1.5">
                      {day.periods?.map((period) => (
                        <div
                          key={`${period.opens || 'closed'}-${period.closes || 'closed'}`}
                          className="tabular-nums"
                        >
                          {formatTime(period.opens) || 'Closed'} -{' '}
                          {formatTime(period.closes) || 'Closed'}
                        </div>
                      ))}
                    </span>
                  </li>
                );
              })}
            </ul>
          ) : (
            <p className="mt-3 pl-4.5 text-foreground-secondary">
              Opening hours not available
            </p>
          )}
        </DrawerMain>
      </DrawerContent>
    </Drawer>
  );
};
