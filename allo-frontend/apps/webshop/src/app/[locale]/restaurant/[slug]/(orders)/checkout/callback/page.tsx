import { notFound, redirect } from 'next/navigation';
import { getCart, getRestaurant } from '~/lib/api';

interface Props {
  params: Promise<{ slug: string }>;
  searchParams: Promise<{
    cartId: string;
    success: boolean;
  }>;
}

/**
 * This page is used to handle the callback from the payment provider.
 * It will redirect based on the parameters to either the tracking page or back to the
 * checkout page.
 * @param props
 * @constructor
 */
export default async function CallbackPage({ params, searchParams }: Props) {
  const { slug } = await params;
  const { cartId, success } = await searchParams;
  const webshopRestaurant = await getRestaurant(slug);
  if (!webshopRestaurant.data) return notFound();

  if (success) {
    const cart = await getCart(webshopRestaurant.data.id || '', cartId);
    console.log(cart);
    if (cart.data) {
      const order = cart.data.orderId;
      if (order) {
        redirect(`/en/restaurant/${slug}/orders/${order}`);
      }
    } else {
      return notFound();
    }
  } else {
    redirect(`/en/restaurant/${slug}/checkout`);
  }

  return null;
}
