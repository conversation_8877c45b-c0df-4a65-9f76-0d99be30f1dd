import { cn } from '@allo/ui';
import { AddressDetails } from './components/address-details';
import { BillingDetails } from './components/billing-details';
import { CartSummary } from './components/cart-summary';

export default function CheckoutPage() {
  return (
    <main
      className={cn(
        '[--py:--spacing(8)]',
        'relative grow py-(--py) px-4 md:px-8 max-w-container-narrow mx-auto flex flex-col md:grid md:grid-cols-2 gap-3'
      )}
    >
      <div className="flex flex-col gap-3 w-full">
        <AddressDetails />
        <BillingDetails />
      </div>
      <div
        className={cn(
          '[--max-h:calc(100dvh-var(--header-height)-2*var(--py))]',
          'size-full overflow-hidden md:sticky md:top-[calc(var(--header-height)+var(--py))] md:max-h-(--max-h) md:*:max-h-(--max-h)'
        )}
      >
        <CartSummary />
      </div>
    </main>
  );
}
