'use client';

import { Button } from '@allo/ui';
import Image from 'next/image';
import posthog from 'posthog-js';
import tableIllustration from '~/assets/illustrations/table.png';
import { WebshopRestaurant } from '~/lib/api/types.ts';
import { getCaptureId } from '~/lib/utils/posthog';

interface BookATableBannerProps {
  restaurant: WebshopRestaurant;
  slug: string;
}

export const BookATableBanner = ({ restaurant, slug }: BookATableBannerProps) => {
  return (
    <div className="w-full sticky mt-4 bottom-0 z-20 bg-background-highlight border-t border-border-soft order-999">
      <div className="max-w-container mx-auto py-3 flex items-center gap-1.5">
        <Image
          src={tableIllustration}
          alt="Table Illustration"
          width={tableIllustration.width}
          height={tableIllustration.height}
          className="md:w-8 w-20 h-auto max-md:row-span-2"
        />
        <div className="grow flex flex-col md:items-center md:flex-row md:justify-between gap-1.5">
          <h1 className="text-sm">Book your table now and enjoy a good evening!</h1>
          <Button
            variant="accent"
            size="sm"
            className="w-fit"
            asChild
            onClick={() => {
              posthog.capture(getCaptureId('digital_menu_page', 'book_table', 'click'), {
                restaurant_name: restaurant.name,
                restaurant_slug: slug,
              });
            }}
          >
            <a href="#">Book a table</a>
          </Button>
        </div>
      </div>
    </div>
  );
};
