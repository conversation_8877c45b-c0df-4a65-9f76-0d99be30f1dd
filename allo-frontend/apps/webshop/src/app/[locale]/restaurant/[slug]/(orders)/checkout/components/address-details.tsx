'use client';

import { <PERSON><PERSON>, <PERSON><PERSON>, cn } from '@allo/ui';
import { BanIcon, CalendarClock, MapPin, StoreIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useTransition } from 'react';
import { Box, BoxContent, BoxHeader, BoxTitle } from '~/components/box';
import { InlineButton } from '~/components/inline-button';
import { Map, Marker } from '~/components/map';
import { AddressSelection } from '~/components/order-configuration/address-selection';
import { TimeSelection } from '~/components/order-configuration/time-selection';
import { SegmentedControl, SegmentedControlItem } from '~/components/segmented-control';
import { patchUpdateCart } from '~/lib/api';
import { useOrderCart, useOrderConfiguration } from '~/lib/order/store';
import { OrderAddress, OrderTime, OrderType } from '~/lib/order/types';
import { useRestaurant } from '~/lib/restaurant';
import { formatScheduledTime } from '~/lib/utils/dates';

export const AddressDetails = () => {
  const t = useTranslations();
  const restaurant = useRestaurant();
  const { type, address, time } = useOrderConfiguration();
  const [editing, setEditing] = useState<'address' | 'schedule' | null>(null);

  const preparationTime =
    type === 'DELIVERY'
      ? restaurant.deliveryPreparationTimeInMinutes
      : restaurant.pickupPreparationTimeInMinutes;

  return (
    <Box>
      <BoxHeader>
        <BoxTitle>
          {type === 'DELIVERY' ? t('delivery-details') : t('pickup-details')}
        </BoxTitle>
      </BoxHeader>
      <BoxContent>
        {editing === 'address' ? (
          <EditDetails exitEditMode={() => setEditing(null)} />
        ) : editing === 'schedule' ? (
          <EditSchedule exitEditMode={() => setEditing(null)} />
        ) : (
          <>
            {type === 'DELIVERY' &&
              address &&
              address.coordinates &&
              typeof address.coordinates.lat === 'number' &&
              typeof address.coordinates.lng === 'number' && (
                <Map
                  mapId="address-details"
                  className="h-40"
                  center={{ lat: address.coordinates.lat, lng: address.coordinates.lng }}
                  zoom={17}
                  controlled
                >
                  <Marker
                    position={{
                      lat: address.coordinates.lat,
                      lng: address.coordinates.lng,
                    }}
                    label="Delivery Address"
                    labelVisibility="always"
                  />
                </Map>
              )}
            {/*{type === 'PICKUP' && (*/}
            {/*  <Map*/}
            {/*    mapId="address-details"*/}
            {/*    className="h-40"*/}
            {/*    center={restaurant?.address?.coordinates}*/}
            {/*    zoom={17}*/}
            {/*    controlled*/}
            {/*  >*/}
            {/*    <Marker*/}
            {/*      position={restaurant?.address?.coordinates}*/}
            {/*      label="Pickup Location"*/}
            {/*      labelVisibility="always"*/}
            {/*    />*/}
            {/*  </Map>*/}
            {/*)}*/}
            <div
              className={`flex flex-col gap-1${type === 'DELIVERY' && address && address.coordinates && typeof address.coordinates.lat === 'number' && typeof address.coordinates.lng === 'number' ? ' mt-3' : ''}`}
            >
              <div className="flex items-start gap-1.5 text-sm">
                {type === 'DELIVERY' && (
                  <>
                    <MapPin
                      className={cn(
                        !address ? 'text-accent' : 'text-foreground-secondary',
                        'min-w-4 mr-[-1]'
                      )}
                    />
                    {address && <p>{address?.formattedAddress}</p>}
                    <InlineButton
                      variant={address ? 'secondary' : 'accent'}
                      onClick={() => setEditing('address')}
                    >
                      {address ? t('edit') : t('add-address')}
                    </InlineButton>
                  </>
                )}
                {type === 'PICKUP' && (
                  <>
                    <StoreIcon className="text-foreground-secondary mt-0.5" />
                    <p>
                      {t('pickup-at')} {restaurant?.address}
                    </p>
                  </>
                )}
              </div>
              {type === 'DELIVERY' && (
                <div className="flex flex-col items-start gap-1.5 text-sm">
                  <span className="ml-7">
                    Additional information: {address?.additionalAddressInfo}
                  </span>

                  <span className="ml-7">Apartment: {address?.apartment}</span>
                  {address?.instructions && (
                    <span className="ml-7">Instructions: {address?.instructions}</span>
                  )}
                </div>
              )}
              <div className="flex items-center gap-1.5 text-sm">
                <CalendarClock className="text-foreground-secondary" />
                <p>
                  {time.type === 'standard'
                    ? `${preparationTime?.min}-${preparationTime?.max} min`
                    : formatScheduledTime(time.date)}
                </p>
                <InlineButton variant="secondary" onClick={() => setEditing('schedule')}>
                  {t('edit')}
                </InlineButton>
              </div>
            </div>
          </>
        )}
      </BoxContent>
    </Box>
  );
};

interface EditDetailsProps {
  exitEditMode: () => void;
}

const EditDetails = ({ exitEditMode }: EditDetailsProps) => {
  const t = useTranslations();
  const { type, address, setAddress } = useOrderConfiguration();
  const { cartId } = useOrderCart();
  const restaurant = useRestaurant();
  const [isPending, startTransition] = useTransition();

  const handleConfirm = (address: OrderAddress) => {
    startTransition(() => {
      patchUpdateCart(restaurant?.id || '', { address }, cartId || '').then(() => {
        setAddress(address);
      });

      exitEditMode();
    });
  };

  return (
    <>
      {type === 'DELIVERY' && (
        <div>
          <h2 className="text-lg mb-4">{t('where-to-deliver')}</h2>
          <AddressSelection
            initialValue={address}
            onConfirm={handleConfirm}
            renderActions={({
              onConfirm,
              isValid,
              onDetailsConfirm,
              onDetailsCancel,
              isDetailsValid,
            }) => (
              <div className="flex gap-2 mt-4">
                {onConfirm && (
                  <>
                    <Button onClick={exitEditMode}>{t('cancel')}</Button>
                    <Button variant="accent" onClick={onConfirm} disabled={!isValid}>
                      {t('confirm-details')}
                    </Button>
                  </>
                )}
                {onDetailsCancel && (
                  <Button disabled={isPending} onClick={onDetailsCancel}>
                    {t('cancel')}
                  </Button>
                )}
                {onDetailsConfirm && (
                  <Button
                    variant="accent"
                    onClick={onDetailsConfirm}
                    disabled={!isDetailsValid || isPending}
                    isLoading={isPending}
                  >
                    {t('confirm-details')}
                  </Button>
                )}
              </div>
            )}
          />
        </div>
      )}
    </>
  );
};

interface EditScheduleProps {
  exitEditMode: () => void;
}

const EditSchedule = ({ exitEditMode }: EditScheduleProps) => {
  const t = useTranslations();
  const { type, time, setTime, setType } = useOrderConfiguration();
  const restaurant = useRestaurant();
  const { cartId } = useOrderCart();
  const [isPending, startTransition] = useTransition();
  const [internalTime, setInternalTime] = useState<OrderTime | null>(time);
  const config = useOrderConfiguration();

  const handleTypeChange = (value: OrderType) => {
    setType(value);
  };

  const handleConfirm = () => {
    if (!internalTime) {
      return null;
    }

    startTransition(() => {
      patchUpdateCart(
        restaurant?.id || '',
        {
          [type === 'DELIVERY' ? 'requestedDeliveryTime' : 'requestedPickupTime']:
            internalTime?.type === 'schedule'
              ? internalTime.date?.toISOString?.()
              : undefined,
          isAsap: internalTime?.type === 'standard',
        },
        cartId || ''
      ).then(() => {
        setTime(internalTime);
      });

      exitEditMode();
    });
  };

  return (
    <div>
      <h2 className="text-lg mb-4">{type === 'PICKUP' ? t('pick-up') : t('delivery')}</h2>
      <SegmentedControl
        className="mb-4"
        defaultValue={config.type}
        onChange={(value) => handleTypeChange(value as OrderType)}
      >
        <SegmentedControlItem value="DELIVERY" disabled={!restaurant.hasDelivery}>
          {t('delivery')}
          {!restaurant.hasDelivery && (
            <Badge size="xs">
              <BanIcon />
            </Badge>
          )}
        </SegmentedControlItem>
        <SegmentedControlItem value="PICKUP" disabled={!restaurant.hasPickup}>
          {t('pickup')}
          {!restaurant.hasPickup && (
            <Badge size="xs">
              <BanIcon />
            </Badge>
          )}
        </SegmentedControlItem>
      </SegmentedControl>
      <TimeSelection value={internalTime} onChange={setInternalTime} />
      <div className="flex gap-2 mt-4">
        <Button onClick={exitEditMode}>{t('cancel')}</Button>
        <Button
          variant="accent"
          isLoading={isPending}
          onClick={handleConfirm}
          disabled={!internalTime || isPending}
        >
          {t('confirm-details')}
        </Button>
      </div>
    </div>
  );
};
