import { Link } from '~/i18n/navigation';

import Image from 'next/image';
import { notFound } from 'next/navigation';
import { Header, HeaderActions, HeaderLogo } from '~/components/header';
import { getRestaurant } from '~/lib/api';

interface OrdersLayoutProps {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: OrdersLayoutProps) {
  const { slug } = await params;
  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;

  if (!webshopRestaurantData) return notFound();

  return {
    title: webshopRestaurantData.name,
  };
}

export default async function OrdersLayout({ children, params }: OrdersLayoutProps) {
  const { slug } = await params;
  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();

  return (
    <>
      <Header>
        <HeaderLogo>
          <Link href={`/restaurant/${slug}`}>
            <Image
              src={webshopRestaurantData?.logo || ''}
              alt={`${webshopRestaurantData?.name}-logo`}
              width="50"
              height="50"
            />
          </Link>
        </HeaderLogo>
        <HeaderActions>{/* <SessionButton /> */}</HeaderActions>
      </Header>
      {children}
    </>
  );
}
