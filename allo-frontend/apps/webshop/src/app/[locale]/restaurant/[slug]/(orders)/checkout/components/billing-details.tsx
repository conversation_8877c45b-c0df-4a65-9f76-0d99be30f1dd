'use client';

import {
  Card,
  Field,
  FieldIsolation,
  Input,
  Label,
  Listbox,
  ListboxOption,
  ListboxOptions,
  ListboxTrigger,
  Radio,
  RadioGroup,
} from '@allo/ui';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

import { COUNTRY_CODES } from '@monorepo/utils';

import { Box, BoxContent, BoxHeader, BoxTitle } from '~/components/box';
import { useOrderStore } from '~/lib/order/store';
import { OrderBilling } from '~/lib/order/types';
import { isValidOrderBilling } from '~/lib/order/utils';
import { useRestaurant } from '~/lib/restaurant';

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const isValidPhone = (phone: string): boolean => {
  const cleanPhone = phone.replace(/\D/g, '');
  return cleanPhone.length >= 7 && cleanPhone.length <= 15;
};

type ValidationErrors = {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
};

export const BillingDetails = () => {
  const t = useTranslations();
  const { setBillingDetails, type } = useOrderStore();
  const restaurant = useRestaurant();

  const [selectedCountry, setSelectedCountry] = useState(COUNTRY_CODES[0]);
  const [value, setValue] = useState<OrderBilling>({
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    paymentMethod: 'cash',
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = useCallback(
    (fieldName: keyof OrderBilling, fieldValue: string): string | undefined => {
      switch (fieldName) {
        case 'firstName':
          return fieldValue.trim().length < 2
            ? t('validation.first-name-required')
            : undefined;
        case 'lastName':
          return fieldValue.trim().length < 2
            ? t('validation.last-name-required')
            : undefined;
        case 'email':
          if (!fieldValue.trim()) return t('validation.email-required');
          return !isValidEmail(fieldValue) ? t('validation.email-invalid') : undefined;
        case 'phone':
          if (!fieldValue.trim()) return t('validation.phone-required');
          return !isValidPhone(fieldValue) ? t('validation.phone-invalid') : undefined;
        default:
          return undefined;
      }
    },
    [t]
  );

  useEffect(() => {
    const newErrors: ValidationErrors = {};
    const fieldsToValidate: (keyof ValidationErrors)[] = [
      'firstName',
      'lastName',
      'email',
      'phone',
    ];

    fieldsToValidate.forEach((fieldName) => {
      const error = validateField(fieldName, value[fieldName] as string);
      if (error) {
        newErrors[fieldName] = error;
      }
    });
    setErrors(newErrors);
  }, [value, validateField]);

  const handleFieldBlur = (fieldName: string) => {
    setTouched((prev) => ({ ...prev, [fieldName]: true }));
  };

  useEffect(() => {
    const isValid = isValidOrderBilling(value) && Object.keys(errors).length === 0;
    setBillingDetails(isValid ? value : null);
  }, [value, errors, setBillingDetails]);

  const paymentMethods = {
    delivery: restaurant.deliveryPaymentChannels,
    pickup: restaurant.pickupPaymentChannels,
  };

  useEffect(() => {
    if (paymentMethods[type?.toLowerCase() as keyof typeof paymentMethods].length === 1) {
      const v = paymentMethods[
        type?.toLowerCase() as keyof typeof paymentMethods
      ][0] as OrderBilling['paymentMethod'];
      if (v !== value.paymentMethod) {
        setValue((prev) => ({
          ...prev,
          paymentMethod: v,
        }));
      }
    }
  }, [paymentMethods]);

  return (
    <Box collapsible>
      <BoxHeader>
        <BoxTitle>{t('billing-details')}</BoxTitle>
      </BoxHeader>
      <BoxContent>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2 w-full">
            <Field className="flex flex-col gap-1 w-full">
              <Label required>{t('first-name')}</Label>
              <Input
                placeholder={t('first-name')}
                onChange={(e) =>
                  setValue((prev) => ({ ...prev, firstName: e.target.value }))
                }
                onBlur={() => handleFieldBlur('firstName')}
                className={errors.firstName && touched.firstName ? 'border-red-500' : ''}
              />
              <span className="text-negative text-xs h-3">
                {touched.firstName && errors.firstName}
              </span>
            </Field>
            <Field className="flex flex-col gap-1 w-full">
              <Label required>{t('last-name')}</Label>
              <Input
                placeholder={t('last-name')}
                onChange={(e) =>
                  setValue((prev) => ({ ...prev, lastName: e.target.value }))
                }
                onBlur={() => handleFieldBlur('lastName')}
                className={errors.lastName && touched.lastName ? 'border-red-500' : ''}
              />
              <span className="text-negative text-xs h-3">
                {touched.lastName && errors.lastName}
              </span>
            </Field>
          </div>
          <Field className="flex flex-col gap-1 items-start">
            <Label required>{t('email')}</Label>
            <div className="flex gap-2 items-center w-full">
              <Input
                className={`w-full ${errors.email && touched.email ? 'border-red-500' : ''}`}
                type="email"
                placeholder={t('email-placeholder')}
                onChange={(e) => setValue((prev) => ({ ...prev, email: e.target.value }))}
                onBlur={() => handleFieldBlur('email')}
              />
            </div>
            <span className="text-negative text-xs h-3">
              {touched.email && errors.email}
            </span>
          </Field>
          <Field className="flex flex-col gap-1 items-start">
            <Label required>{t('phone-number')}</Label>
            <div className="flex gap-2 items-center w-full">
              <FieldIsolation>
                <Listbox
                  value={selectedCountry}
                  onChange={setSelectedCountry}
                  matchReferenceWidth={false}
                  placement="bottom-start"
                >
                  <ListboxTrigger className="w-min" placeholder="+00">
                    {!!selectedCountry && (
                      <span className="flex items-center gap-2">
                        {selectedCountry.code}
                      </span>
                    )}
                  </ListboxTrigger>
                  <ListboxOptions className="max-h-[250px] w-80">
                    {COUNTRY_CODES.map((country) => (
                      <ListboxOption
                        key={country.code}
                        value={country}
                        className="flex-col items-start gap-0.5"
                      >
                        <span className="text-sm">{country.country}</span>
                        <span className="text-foreground-secondary text-xs">
                          {country.code}
                        </span>
                      </ListboxOption>
                    ))}
                  </ListboxOptions>
                </Listbox>
              </FieldIsolation>
              <Input
                className={`w-full ${errors.phone && touched.phone ? 'border-red-500' : ''}`}
                type="tel"
                placeholder={t('phone-placeholder')}
                value={value.phone}
                onChange={(e) => setValue((prev) => ({ ...prev, phone: e.target.value }))}
                onBlur={() => handleFieldBlur('phone')}
              />
            </div>
            <span className="text-negative text-xs h-3">
              {touched.phone && errors.phone}
            </span>
          </Field>
          <Field className="flex flex-col gap-2">
            <Label required>{t('select-payment-method')}</Label>
            <RadioGroup className="flex gap-2 flex-col sm:flex-row">
              {paymentMethods?.[
                type?.toLowerCase() as keyof typeof paymentMethods
              ]?.map?.((method) => (
                <Card
                  key={method}
                  depth
                  className="text-sm px-3 py-2 flex flex-1 items-center justify-between gap-2 rounded-xl"
                  asChild
                >
                  <label>
                    <span>{t(method.toLowerCase())}</span>
                    <Radio
                      id={method}
                      name="payment-method"
                      checked={value.paymentMethod === method}
                      onChange={(e) =>
                        e.target.checked &&
                        setValue((prev) => ({
                          ...prev,
                          paymentMethod: method as OrderBilling['paymentMethod'],
                        }))
                      }
                    />
                  </label>
                </Card>
              ))}
            </RadioGroup>
          </Field>
        </div>
      </BoxContent>
    </Box>
  );
};
