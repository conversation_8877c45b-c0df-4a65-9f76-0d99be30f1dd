import { notFound } from 'next/navigation';
import { getRestaurant } from '~/lib/api';
import { RestaurantContextProvider } from '~/lib/restaurant';

interface RestaurantLayoutProps {
  children: React.ReactNode;
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: RestaurantLayoutProps) {
  const { slug } = await params;
  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;

  if (!webshopRestaurantData) return notFound();

  return {
    title: webshopRestaurantData.name,
  };
}

export default async function RestaurantLayout({
  children,
  params,
}: RestaurantLayoutProps) {
  const { slug } = await params;

  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();

  return (
    <RestaurantContextProvider value={{ ...webshopRestaurantData, slug }}>
      {children}
    </RestaurantContextProvider>
  );
}
