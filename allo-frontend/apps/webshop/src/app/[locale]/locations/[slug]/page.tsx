import { Divider } from '@allo/ui';
import { notFound } from 'next/navigation';
import { RestaurantList } from '~/app/[locale]/locations/[slug]/components/restaurant-list.tsx';
import { getRestaurant } from '~/lib/api';
import { MultiRestaurantHero } from './components/multi-restaurant-hero';

interface LocationPageProps {
  params: Promise<{ slug: string }>;
}

export default async function LocationPage({ params }: LocationPageProps) {
  const { slug } = await params;

  const webshopRestaurant = await getRestaurant(slug);
  const webshopRestaurantData = webshopRestaurant.data;
  if (!webshopRestaurantData) return notFound();

  const image =
    Array.isArray(webshopRestaurantData.covers) && webshopRestaurantData.covers.length > 0
      ? webshopRestaurantData.covers[0]?.src || ''
      : '';

  return (
    <main>
      <MultiRestaurantHero
        title={webshopRestaurantData.name || ''}
        description={webshopRestaurantData.description || ''}
        tags={[]}
        image={image}
        restaurants={webshopRestaurantData.branchRestaurants || []}
      />
      <Divider className="mb-6" />
      <RestaurantList restaurants={webshopRestaurantData.branchRestaurants || []} />
    </main>
  );
}
