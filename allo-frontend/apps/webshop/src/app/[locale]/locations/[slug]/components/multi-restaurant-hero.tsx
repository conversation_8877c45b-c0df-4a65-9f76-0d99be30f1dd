'use client';

import { Badge } from '@allo/ui';
import Image from 'next/image';
import { Hero, HeroBody, HeroContent, HeroCover, HeroMedia } from '~/components/hero';
import { WebshopRestaurant } from '~/lib/api/types.ts';

interface MultiRestaurantHeroProps {
  title: string;
  description: string;
  tags: string[];
  image: string;
  restaurants: WebshopRestaurant[];
}

export const MultiRestaurantHero = ({
  title,
  description,
  tags,
  image,
  restaurants,
}: MultiRestaurantHeroProps) => {
  // const mapBounds = useMemo(
  //   () => calculateContainerBounds(restaurants.map(({ address }) => address.coordinates)),
  //   [restaurants]
  // );

  return (
    <Hero>
      <HeroCover>
        <Image src={image} alt="Brand Group Name" sizes="100vw" fill priority />
      </HeroCover>
      <HeroBody>
        <HeroContent className="pr-8">
          <h1 className="text-2xl mb-4 [&+:is(p)]:text-foreground-secondary">{title}</h1>
          <ul className="flex gap-1.5 flex-wrap mt-5 md:mt-7">
            {tags.map((tag, index) => (
              <li key={index}>
                <Badge size="sm">{tag}</Badge>
              </li>
            ))}
          </ul>
          <p className="text-base mt-3">{description}</p>
        </HeroContent>
        <HeroMedia>
          {/*<Map mapId="multi-restaurant-hero" defaultBounds={{ ...mapBounds, padding: 8 }}>*/}
          {/*  {restaurants.map((restaurant, index) => (*/}
          {/*    <Marker*/}
          {/*      key={index}*/}
          {/*      position={restaurant.address || ''}*/}
          {/*      clickable={true}*/}
          {/*      label={*/}
          {/*        <span inert className="flex items-center gap-1">*/}
          {/*          {restaurant.name} <ExternalLinkIcon />*/}
          {/*        </span>*/}
          {/*      }*/}
          {/*      onClick={() => {*/}
          {/*        posthog.capture(*/}
          {/*          getCaptureId('locations_page', 'location_marker', 'click'),*/}
          {/*          {*/}
          {/*            restaurant_name: restaurant.name,*/}
          {/*            restaurant_slug: restaurant.slug,*/}
          {/*            restaurant_address: restaurant.address.formattedAddress,*/}
          {/*          }*/}
          {/*        );*/}

          {/*        window.open(*/}
          {/*          getGoogleMapsPinUrl(restaurant.address.coordinates),*/}
          {/*          '_blank'*/}
          {/*        );*/}
          {/*      }}*/}
          {/*    />*/}
          {/*  ))}*/}
          {/*</Map>*/}
        </HeroMedia>
      </HeroBody>
    </Hero>
  );
};
