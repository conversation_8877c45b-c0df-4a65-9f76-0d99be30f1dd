import type { Viewport } from 'next';
import { getLocale } from 'next-intl/server';
import { PublicEnvScript } from 'next-runtime-env';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

import { NextIntlClientProvider } from 'next-intl';
import { locales } from '~/i18n';
import { cdnScriptUrl } from '~/lib/utils/cdn';

import '@fontsource-variable/bricolage-grotesque';
import '@fontsource-variable/inter';
import { Footer } from './components/footer';
import './globals.css';
import Providers from './providers';

// export const metadata: Metadata = {
//   title: 'allO Webshop',
// };

// export async function generateMetadata() {
//   return {
//     // title: 'allO Webshop',
//   };
// }

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
};

export default async function RootLayout({
  children,
}: Readonly<{ children: ReactNode }>) {
  const locale = await getLocale();

  if (!locale) return notFound();
  if (!locales.includes(locale)) return notFound();

  return (
    <html lang={locale} key={locale}>
      <head>
        <script src={`${cdnScriptUrl}`} async defer></script>
        <PublicEnvScript />
      </head>
      <body>
        <NextIntlClientProvider>
          <Providers>
            <div className="flex flex-col min-h-svh *:w-full has-[[data-fixed-header]]:pt-(--header-height)">
              {children}
              <Footer />
            </div>
          </Providers>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
