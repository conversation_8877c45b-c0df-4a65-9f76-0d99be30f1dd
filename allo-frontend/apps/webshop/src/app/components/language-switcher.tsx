'use client';

import { Dropdown, DropdownItem, DropdownItems, DropdownTrigger } from '@allo/ui';
import { EarthIcon } from 'lucide-react';
import { InlineButton } from '~/components/inline-button';
import { getCurrentLocale } from '~/lib/utils/locale';

const LANGUAGES = {
  en: {
    label: 'English',
    flag: '/imgs/flags/uk.svg',
  },
  // pt: {
  //   label: 'Português',
  //   flag: '/imgs/flags/pt.svg',
  // },
  zh: {
    label: '中文',
    flag: '/imgs/flags/cn.svg',
  },
  de: {
    label: 'Deutsch',
    flag: '/imgs/flags/de.svg',
  },
  // es: {
  //   label: 'Español',
  //   flag: '/imgs/flags/es.svg',
  // },
  // fr: {
  //   label: 'Français',
  //   flag: '/imgs/flags/fr.svg',
  // },
  // th: {
  //   label: 'ไทย',
  //   flag: '/imgs/flags/th.svg',
  // },
  // tk: {
  //   label: 'Türkçe',
  //   flag: '/imgs/flags/tk.svg',
  // },
  // vi: {
  //   label: 'Tiếng Việt',
  //   flag: '/imgs/flags/vn.svg',
  // },
  // hi: {
  //   label: 'हिंदी',
  //    flag: '/imgs/flags/in.svg',
  // },
} as const;

type ShortLang = keyof typeof LANGUAGES;

export const LanguageSwitcher = () => {
  const lang = (getCurrentLocale() as ShortLang) || 'en';
  const language = LANGUAGES[lang];

  const handleChangeLanguage = (key: ShortLang) => {
    const newPath = window?.location?.pathname?.replace?.(
      /^\/([a-zA-Z-]+)(\/|$)/,
      `/${key}$2`
    );

    window.location.replace(newPath);
  };

  return (
    <Dropdown>
      <DropdownTrigger asChild>
        <InlineButton>
          <EarthIcon />
          {language?.label}
        </InlineButton>
      </DropdownTrigger>
      <DropdownItems>
        {Object.entries(LANGUAGES).map(([key, { label, flag }]) => (
          <DropdownItem
            key={key}
            onClick={() => handleChangeLanguage(key as ShortLang)}
            disabled={key === lang}
          >
            {/* eslint-disable-next-line @next/next/no-img-element */}
            {flag && <img alt={`${label} flag`} src={flag} className="size-4" />}
            {label}
          </DropdownItem>
        ))}
      </DropdownItems>
    </Dropdown>
  );
};
