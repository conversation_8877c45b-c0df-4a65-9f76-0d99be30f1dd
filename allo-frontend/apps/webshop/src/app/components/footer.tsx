import { Divider } from '@allo/ui';
import { ExternalLinkIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { InlineButton } from '~/components/inline-button';
import { LanguageSwitcher } from './language-switcher';

export const Footer = () => {
  const t = useTranslations();
  return (
    <footer className="pt-5 mt-auto">
      <Divider />
      <div className="flex flex-col md:flex-row gap-x-6 gap-y-2 md:p-6.5 xl:px-12 p-4">
        <InlineButton
          asChild
          className="mr-auto max-md:mt-2 max-md:order-99"
          variant="secondary"
        >
          <a href="https://allo.restaurant" target="_blank" rel="noopener noreferrer">
            <ExternalLinkIcon />
            {t('footer-copyright')}
          </a>
        </InlineButton>
        <span className="flex max-md:-order-1 max-md:mb-4 align-baseline">
          <LanguageSwitcher />
        </span>
        <InlineButton asChild variant="secondary">
          <a
            href="https://allo.restaurant/privacy-policy"
            target="_blank"
            rel="noopener noreferrer"
          >
            {t('privacy-policy')}
          </a>
        </InlineButton>
        <InlineButton asChild variant="secondary">
          <a
            href="https://allo.restaurant/terms-guests"
            target="_blank"
            rel="noopener noreferrer"
          >
            {t('terms-and-conditions')}
          </a>
        </InlineButton>
      </div>
    </footer>
  );
};
