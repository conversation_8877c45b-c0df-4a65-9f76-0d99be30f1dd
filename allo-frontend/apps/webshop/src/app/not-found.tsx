import { Button } from '@allo/ui';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import notFoundImage from '~/assets/illustrations/not-found.png';
import { Link } from '~/i18n/navigation';

const TITLE = undefined;
const DESCRIPTION = undefined;

export default function NotFound() {
  const t = useTranslations();
  return (
    <main className="grow text-center h-full w-full mx-auto max-w-sm flex flex-col items-center justify-center gap-6 px-4">
      <Image
        src={notFoundImage.src}
        width={notFoundImage.width}
        height={notFoundImage.height}
        alt="Not found illustration"
      />
      <div className="space-y-1.5">
        <h1 className="text-xl">{t('page-not-found')}</h1>
        <p className="text-base">{t('not-found-description')}</p>
      </div>
      <Button asChild variant="accent">
        <Link href="/restaurant/king-loui">{t('return-to-homepage')}</Link>
      </Button>
    </main>
  );
}
