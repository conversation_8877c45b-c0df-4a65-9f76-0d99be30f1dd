{"common": {"hello": "Hello, <PERSON>!"}, "billing-details": "Billing details", "first-name": "First name", "last-name": "Last name", "email": "E-mail", "phone-number": "Phone number", "select-payment-method": "Select payment method", "cart-summary": "<PERSON>t <PERSON>mma<PERSON>", "place-order": "Place Order", "no-order-options-available": "No order options available", "delivery-details": "Delivery details", "pickup-details": "Pickup details", "add-address": "Add address", "pickup-at": "Pickup at", "thirty-to-fifty-min": "30-50 min", "confirm-details": "Confirm Details", "cancel": "Cancel", "where-to-deliver": "Where to deliver", "order-number": "Order #{orderId}", "in-progress": "In Progress", "thank-for-order": "Thank you for your order", "closed": "Closed", "open": "Open", "delivery": "Delivery", "pick-up": "Pick up", "no-address-added-yet": "No address added yet", "modify-order": "Modify Order", "footer-copyright": "allO 2025 @ all rights reserved", "not-found-description": "It seems the page you're looking for doesn't exist. Let's get you back on track.", "add-to-cart": "Add to cart", "email-placeholder": "<EMAIL>", "privacy-policy": "Privacy Policy", "terms-and-conditions": "Terms and Conditions", "book-table-banner": "Book your table now and enjoy a good evening!", "book-table": "Book a table", "view-details": "View details", "restaurant-details": "Restaurant Details", "select-store": "Select store", "no-store-selected": "No {brand} store selected", "please-select-store": "Please select a store to start your order", "return-to-homepage": "Return to homepage", "page-not-found": "Page not found", "page-not-found-description": "It seems the page you're looking for doesn't exist. Let's get you back on track.", "discount-code": "Discount Code", "checkout": "Checkout", "pick-up-window": "Pick up window", "app": "Pay Online", "cash": "Pay with Cash", "enter-your-address": "Enter your address", "pickup": "Pickup", "now": "Now", "schedule": "Schedule", "addresses": "Addresses", "confirm": "Confirm", "change-store": "Change store", "when-to-deliver": "When to deliver", "when-to-pick-up": "When to pick up", "address": "Address", "standard": "Standard", "scheduled": "Scheduled", "today": "Today", "tomorrow": "Tomorrow", "select-day-and-time": "Select day and time", "select": "Select", "no-options": "No options", "earliest-possible-arrival-time": "Earliest possible arrival time is around {start} to {end}min", "unable-to-load-delivery-window": "Unable to load delivery window", "no-slots-available": "No slots available for this day and time", "edit": "Edit", "too-far-away": "Too far away", "too-far-away-to-deliver": "Too far away to deliver", "additional-address-information": "Additional address information", "additional-address-information-placeholder": "Street number, building name, etc.", "apartment-suite-floor": "Apartment, suite, floor", "apartment-suite-floor-placeholder": "Apt 123, Suite 456, Floor 2", "instructions": "Instructions", "instructions-placeholder": "Delivery instructions, special notes, etc.", "failed-to-get-current-location": "Failed to get current location", "no-place-found": "No place found", "validation": {"first-name-required": "At least 2 characters", "last-name-required": "At least 2 characters", "email-required": "Email is required", "email-invalid": "Please enter a valid email address", "phone-required": "Phone number is required", "phone-invalid": "Please enter a valid phone number (7-15 digits)"}, "your-cart": "Your Cart", "your-cart-is-empty": "Your cart is empty", "apply": "Apply", "sold-out": "SOLD OUT", "phone-placeholder": "000 000 000", "item": "item", "items": "items", "restaurant-logotype": "Restaurant Logotype", "store-location": "Store location", "estimated-time": "Estimated time 14:45 - 15:45 PM", "thank-for-order-number": "Thank for your order #{orderNumber}", "not-found-illustration": "Not found illustration", "here-what-might-be-missing": "Here's what might be missing:", "cart-note-placeholder": "Special requests, allergies, dietary restrictions or greetings to the restaurant...", "add-note": "Add a note", "your-note": "Your note"}