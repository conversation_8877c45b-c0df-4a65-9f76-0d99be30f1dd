{"common": {"hello": "你好，世界！"}, "billing-details": "账单详情", "first-name": "名字", "last-name": "姓氏", "email": "电子邮件", "phone-number": "电话号码", "select-payment-method": "选择支付方式", "cart-summary": "购物车摘要", "place-order": "下单", "no-order-options-available": "没有可用的订单选项", "delivery-details": "送货详情", "pickup-details": "自取详情", "edit": "编辑", "add-address": "添加地址", "pickup-at": "自取于", "thirty-to-fifty-min": "30-50分钟", "confirm-details": "确认详情", "cancel": "取消", "where-to-deliver": "送到哪里", "order-number": "订单 #{orderId}", "in-progress": "进行中", "thank-for-order": "感谢您的订单", "closed": "已关闭", "open": "营业中", "delivery": "送货", "pick-up": "自取", "no-address-added-yet": "尚未添加地址", "modify-order": "修改订单", "footer-copyright": "allO 2025 @ 保留所有权利", "not-found-description": "您要查找的页面不存在。让我们帮您回到正确的页面。", "add-to-cart": "加入购物车", "email-placeholder": "<EMAIL>", "privacy-policy": "隐私政策", "terms-and-conditions": "条款和条件", "book-table-banner": "立即预订您的餐桌，享受美好夜晚！", "book-table": "预订餐桌", "view-details": "查看详情", "restaurant-details": "餐厅详情", "select-store": "选择门店", "no-store-selected": "未选择 {brand} 门店", "please-select-store": "请选择门店以开始下单", "return-to-homepage": "返回首页", "page-not-found": "未找到页面", "page-not-found-description": "您要查找的页面不存在。让我们帮您回到正确的页面。", "discount-code": "优惠码", "checkout": "结账", "pick-up-window": "自取时间段", "app": "在线支付", "cash": "现金支付", "enter-your-address": "输入您的地址", "pickup": "自取", "now": "现在", "schedule": "预约", "addresses": "地址", "confirm": "确认", "change-store": "更换门店", "when-to-deliver": "何时送货", "when-to-pick-up": "何时自取", "address": "地址", "standard": "标准", "scheduled": "已预约", "today": "今天", "tomorrow": "明天", "select-day-and-time": "选择日期和时间", "select": "选择", "no-options": "无选项", "earliest-possible-arrival-time": "最早可能到达时间约为{start}到{end}分钟", "unable-to-load-delivery-window": "无法加载配送时间窗口", "no-slots-available": "此日期和时间没有可用时段", "too-far-away": "距离太远", "too-far-away-to-deliver": "距离太远无法配送", "additional-address-information": "附加地址信息", "additional-address-information-placeholder": "街道号码、建筑名称等", "apartment-suite-floor": "公寓、套房、楼层", "apartment-suite-floor-placeholder": "公寓123、套房456、2楼", "instructions": "说明", "instructions-placeholder": "配送说明、特殊备注等", "failed-to-get-current-location": "获取当前位置失败", "no-place-found": "未找到地点", "validation": {"first-name-required": "名字至少需要2个字符", "last-name-required": "姓氏至少需要2个字符", "email-required": "电子邮件是必需的", "email-invalid": "请输入有效的电子邮件地址", "phone-required": "电话号码是必需的", "phone-invalid": "请输入有效的电话号码（7-15位数字）"}, "your-cart": "您的购物车", "your-cart-is-empty": "您的购物车是空的", "apply": "应用", "sold-out": "售罄", "phone-placeholder": "000 000 000", "item": "项", "items": "项", "restaurant-logotype": "餐厅标志", "store-location": "店铺位置", "estimated-time": "预计时间 14:45 - 15:45", "thank-for-order-number": "感谢您的订单 #{orderNumber}", "not-found-illustration": "未找到页面插图", "here-what-might-be-missing": "这里是可能缺少的内容：", "cart-note-placeholder": "特殊要求、过敏、饮食限制或给餐厅的留言..."}