---
apiVersion: "v1"
kind: "List"
items:
  # this backend config is used in multiple deployments - dont touch it without knowing what you do :D - and it will affect all of the apps in allo-frontend
  - apiVersion: cloud.google.com/v1
    kind: BackendConfig
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      connectionDraining:
        drainingTimeoutSec: 60
      healthCheck:
        checkIntervalSec: 15
        port: 3000
        requestPath: /healthz
        type: HTTP
      securityPolicy:
        name: global-sp-default
      timeoutSec: 80
      # https://cheatsheetseries.owasp.org/cheatsheets/REST_Security_Cheat_Sheet.html#security-headers
      # TODO: balance header sizes vs security...
      # - "Content-Security-Policy: default-src 'none'; frame-ancestors 'none'"
      # - "Content-Security-Policy: default-src 'self';script-src 'self' 'unsafe-inline' https://*.allo.restaurant/ https://*.posthog.com/ https://*.osano.com/ https://js-agent.newrelic.com/ https://bam.nr-data.net/ https://*.stripe.com/ https://maps.googleapis.com/;connect-src 'self' https://*.allo.restaurant/ https://*.posthog.com/ https://*.osano.com/ https://*.nr-data.net/ https://*.unsplash.com/ https://unsplash.com/ https://*.stripe.com/ https://maps.googleapis.com/;img-src 'self' blob: data: https://*.allo.restaurant/ https://storage.googleapis.com/ https://*.unsplash.com/ https://unsplash.com/ https://*.stripe.com/;style-src 'self' https://*.allo.restaurant/ https://*.osano.com/ https://fonts.googleapis.com/ 'unsafe-inline';font-src 'self' blob: data: https://*.allo.restaurant/ https://fonts.gstatic.com/;frame-ancestors 'self' https://*.allo.restaurant/;frame-src 'self' https://*.allo.restaurant/ https://*.osano.com/ https://*.stripe.com/;worker-src 'self' blob: data: https://*.osano.com/;"
      # - 'Permissions-Policy: accelerometer=(), ambient-light-sensor=(), autoplay=(), battery=(), camera=(), cross-origin-isolated=(), display-capture=(), document-domain=(), encrypted-media=(), execution-while-not-rendered=(), execution-while-out-of-viewport=(), fullscreen=(), geolocation=(), gyroscope=(), keyboard-map=(), magnetometer=(), microphone=(), midi=(), navigation-override=(), payment=(), picture-in-picture=(), publickey-credentials-get=(), screen-wake-lock=(), sync-xhr=(), usb=(), web-share=(), xr-spatial-tracking=()'
      # - "Feature-Policy: camera 'none'; microphone 'none'; midi 'none'; geolocation 'none'; usb 'none'"
      # - "Permissions-Policy: camera=(), microphone=(), midi=(), geolocation=(), interest-cohort=(), usb=()"
      customResponseHeaders:
        headers:
        - 'Strict-Transport-Security: max-age=86400; includeSubDomains'
        - 'X-Content-Type-Options: nosniff'
        - 'X-Frame-Options: SAMEORIGIN'
        - 'Referrer-Policy: no-referrer'
        - 'Cross-Origin-Resource-Policy: same-origin'
        - 'Cross-Origin-Embedder-Policy: same-origin'
        - 'Cross-Origin-Opener-Policy: same-origin'
        - 'X-Permitted-Cross-Domain-Policies: none'
  - apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          #app: "$INT_SERVICE_NAME"
          app.kubernetes.io/name: "$INT_SERVICE_NAME"
          app.kubernetes.io/instance: "$INT_SERVICE_NAME"
  - apiVersion: "v1"
    kind: "Service"
    metadata:
      annotations:
        cloud.google.com/backend-config: '{"default":"$INT_SERVICE_NAME"}' # backend configuration to use (eg for cloud armor)
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
      name: "$INT_SERVICE_NAME"
    spec:
      ports:
        - name: "http"
          port: 3000
          targetPort: 3000
      selector:
        #app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
  - apiVersion: "apps/v1"
    kind: "Deployment"
    metadata:
      labels:
        app: "$INT_SERVICE_NAME"
        app.kubernetes.io/name: "$INT_SERVICE_NAME"
        app.kubernetes.io/instance: "$INT_SERVICE_NAME"
        app.kubernetes.io/version: "$INT_SERVICE_VERSION"
        app.kubernetes.io/component: "$INT_SERVICE_NAME"
        app.kubernetes.io/component-type: "nodejs"
        app.kubernetes.io/part-of: "allO"
        kube-monkey/enabled: enabled
        kube-monkey/identifier: "$INT_SERVICE_NAME"
        kube-monkey/kill-mode: fixed
        kube-monkey/kill-value: "1"
        kube-monkey/mtbf: "2"
      name: "$INT_SERVICE_NAME"
    spec:
      replicas: 2
      selector:
        matchLabels:
          app: "$INT_SERVICE_NAME"
      template:
        metadata:
          labels:
            app: "$INT_SERVICE_NAME"
            app.kubernetes.io/name: "$INT_SERVICE_NAME"
            app.kubernetes.io/instance: "$INT_SERVICE_NAME"
            app.kubernetes.io/version: "$INT_SERVICE_VERSION"
            app.kubernetes.io/component: "$INT_SERVICE_NAME"
            app.kubernetes.io/component-type: "nodejs"
            app.kubernetes.io/part-of: "allO"
            kube-monkey/enabled: enabled
            kube-monkey/identifier: "$INT_SERVICE_NAME"
            kube-monkey/kill-mode: fixed
            kube-monkey/kill-value: "1"
            kube-monkey/mtbf: "2"
          annotations:
            # important for emptyDir volume mounts            
            cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
        spec:
          topologySpreadConstraints:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/name: "$INT_SERVICE_NAME"
                  app.kubernetes.io/instance: "$INT_SERVICE_NAME"
              matchLabelKeys:
                - pod-template-hash
              maxSkew: 1
              whenUnsatisfiable: DoNotSchedule
              topologyKey: "kubernetes.io/hostname"
          imagePullSecrets:
            - name: artifact-registry-publisher
          containers:
            - env:
                - name: "KUBERNETES_NAMESPACE"
                  valueFrom:
                    fieldRef:
                      fieldPath: "metadata.namespace"
                - name: "KUBERNETES_POD_IP"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "HOSTNAME"
                  valueFrom:
                    fieldRef:
                      fieldPath: "status.podIP"
                - name: "PORT"
                  value: "3000"
                - name: "NPM_CONFIG_LOGLEVEL"
                  value: "warn"
                - name: "NODE_ENV"
                  value: "production"
                - name: "NEXT_PUBLIC_LOG_LEVEL"
                  value: "info"
                - name: "NEXT_TELEMETRY_DISABLED"
                  value: "1"
                - name: "NEXT_PUBLIC_GOOGLE_MAPS_API_KEY"
                  value: "gcp:secretmanager:$INT_SERVICE_NAME-NEXT_PUBLIC_GOOGLE_MAPS_API_KEY"
                - name: "NEXT_PUBLIC_POSTHOG_HOST"
                  value: "https://eu.i.posthog.com"
                - name: "NEXT_PUBLIC_POSTHOG_KEY"
                  value: "gcp:secretmanager:$INT_SERVICE_NAME-NEXT_PUBLIC_POSTHOG_KEY"
                - name: "NEXT_PUBLIC_SERVICES_RESERVATION_SERVICE_URL"
                  value: "https://app.allo.restaurant/reservation-service"
                - name: "NEXT_INTERNAL_SERVICES_RESERVATION_SERVICE_URL"
                  value: "http://reservation-service:8080/reservation-service"
                - name: "NEXT_PUBLIC_BASE_URL"
                  value: "https://app.allo.restaurant"
                - name: "NEXT_PUBLIC_ALLO_CDN_URL"
                  value: "https://cdn.allo.restaurant"
              image: "$INT_SERVICE_IMAGE"
              imagePullPolicy: "IfNotPresent"
              name: "$INT_SERVICE_NAME"
              ports:
                - containerPort: 3000
                  name: "http"
                  protocol: "TCP"
              resources:
                requests:
                  cpu: 15m
                  memory: 100Mi
                limits:
                  memory: 200Mi # high limit added here - to protect the node and other services just in case ...
                  ephemeral-storage: 2Gi # limit added here - to protect the node and other services just in case ...
              lifecycle:
                preStop:
                  exec:
                    command: 
                    - "/bin/sh"
                    - "-ce" 
                    - |
                      sleep 10 # give k8s svc balancer the chance to unregister routing before shutting down
              startupProbe:
                httpGet:
                  scheme: HTTP
                  path: /healthz
                  port: 3000
                periodSeconds: 5
                timeoutSeconds: 10
                successThreshold: 1
                failureThreshold: 60 # number of tries every periodSeconds - total time would be failureThreshold * periodSeconds
              readinessProbe:
                httpGet:
                  scheme: HTTP
                  path: /healthz
                  port: 3000
                initialDelaySeconds: 5
                periodSeconds: 10
                timeoutSeconds: 3
                successThreshold: 2
                failureThreshold: 10
              livenessProbe:
                httpGet:
                  #Scheme to use for connecting to the host (HTTP or HTTPS). Defaults to HTTP.
                  scheme: HTTP
                  #Path to access on the HTTP server.
                  path: /healthz
                  #Name or number of the port to access on the container. Number must be in the range 1 to 65535.
                  port: 3000
                #Number of seconds after the container has started before liveness or readiness probes are initiated. Defaults to 0 seconds. Minimum value is 0.
                initialDelaySeconds: 5
                #How often (in seconds) to perform the probe. Default to 10 seconds. Minimum value is 1.
                periodSeconds: 10
                #Number of seconds after which the probe times out. Defaults to 1 second. Minimum value is 1.
                timeoutSeconds: 3
                #Minimum consecutive successes for the probe to be considered successful after having failed. Defaults to 1. Must be 1 for liveness. Minimum value is 1.
                successThreshold: 1
                #When a Pod starts and the probe fails, Kubernetes will try failureThreshold times before giving up. Giving up in case of liveness probe means restarting the container. In case of readiness probe the Pod will be marked Unready. Defaults to 3. Minimum value is 1
                failureThreshold: 10
              securityContext:
                allowPrivilegeEscalation: false
                readOnlyRootFilesystem: true
                seccompProfile:
                  type: "RuntimeDefault"
                capabilities:
                  drop:
                    - "ALL"
              volumeMounts:
                - mountPath: /tmp
                  name: tmp-volume
                - mountPath: /var/run
                  name: tmp-run-volume
                - mountPath: /var/log
                  name: tmp-log-volume
                - mountPath: /app/next/standalone/apps/webshop/.next/server/pages
                  name: tmp-next-standalone-next-server-pages-volume
                - mountPath: /app/next/standalone/apps/webshop/.next/cache
                  name: tmp-next-standalone-next-cache-volume
          securityContext:
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            fsGroup: 1000
          terminationGracePeriodSeconds: 20
          volumes:
          - name: tmp-volume
            emptyDir:
              sizeLimit: 128Mi
          - name: tmp-run-volume
            emptyDir:
              sizeLimit: 128Mi
          - name: tmp-log-volume
            emptyDir: 
              sizeLimit: 128Mi
          - name: tmp-next-standalone-next-cache-volume
            emptyDir:
              sizeLimit: 256Mi
          - name: tmp-next-standalone-next-server-pages-volume
            emptyDir:
              sizeLimit: 256Mi