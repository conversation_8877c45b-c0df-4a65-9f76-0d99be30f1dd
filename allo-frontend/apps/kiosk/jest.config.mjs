import sharedConfig from '@monorepo/jest-playwright-config/jest-config' with { type: 'json' };
import nextJest from 'next/jest.js';

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: './',
});

const config = {
  ...sharedConfig,
  coverageProvider: 'v8',
  testEnvironment: 'jest-fixed-jsdom',
  rootDir: './',
};

export default createJestConfig(config);
