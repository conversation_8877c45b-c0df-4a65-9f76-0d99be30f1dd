'use client';

import { TabletOrderThemeProvider } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import PostHog from '~/components/PostHog';
import StoreProvider from '~/store/StoreProvider';

/* eslint-disable react/no-unknown-property */
const Root = ({ children }) => {
  const globalStyles = `
    ::-webkit-scrollbar {
      width: 0;
      background: transparent; /* make scrollbar transparent */
      display: none;
    }
  `;

  return (
    <StoreProvider>
      <PostHog>
        <TabletOrderThemeProvider theme={'orange'}>
          <style global jsx>
            {globalStyles}
          </style>
          {children}
        </TabletOrderThemeProvider>
      </PostHog>
    </StoreProvider>
  );
};

Root.propTypes = {
  children: PropTypes.node,
};

export default Root;
