'use client';

import PropTypes from 'prop-types';
import { Fragment, useEffect } from 'react';
import { GlobalLoading } from '~/components/GlobalLoading';
import { GlobalNotification } from '~/components/GlobalNotification';
import { routes } from '~/lib/constants';
import Categories from '~/screens/categories';
import { Checkout } from '~/screens/checkout';
import { CheckoutPaymentMethod } from '~/screens/checkout-payment-method';
import Completed from '~/screens/completed';
import ConfirmResetCart from '~/screens/confirm-reset-cart';
import Creation from '~/screens/creation';
import Discover from '~/screens/discover';
import Idle from '~/screens/idle';
import { ItemAddedToOrder } from '~/screens/item-added-to-order';
import { ItemDetails } from '~/screens/item-details';
import PaymentInstructions from '~/screens/payment-instructions';
import Promotions from '~/screens/promotions';

import Pager from '~/screens/pager';
import { DiscountAddedToOrder } from '~/screens/promotions/PromotionAddedToOrder';
import Registration from '~/screens/registration';
import Splash from '~/screens/splash';
import Suggestions from '~/screens/suggestions';
import Summary from '~/screens/summary';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  initializeDevice,
  selectNotification,
  selectScreen,
} from '~/store/slices/device/deviceSlice';

const DynamicRouteContent = ({ route }) => {
  const routeComponents = {
    [routes.INITIALIZE]: <Splash />,
    [routes.REGISTER]: <Registration />,
    [routes.IDLE]: <Idle />,
    [routes.CREATE]: <Creation />,
    [routes.DISCOVER]: <Discover />,
    [routes.CATEGORIES]: <Categories />,
    [routes.PROMOTIONS]: <Promotions />,
    [routes.ORDER_SUMMARY]: <Summary />,
    [routes.SUGGESTIONS]: <Suggestions />,
    [routes.CHECKOUT]: <Checkout />,
    [routes.PAGER]: <Pager />,
    [routes.PAYMENT_INSTRUCTIONS]: <PaymentInstructions />,
    [routes.COMPLETED]: <Completed />,
    [routes.CHECKOUT_PAYMENT_METHOD]: <CheckoutPaymentMethod />,
  };

  return routeComponents[route] || <Splash />;
};

DynamicRouteContent.propTypes = {
  route: PropTypes.string.isRequired,
};

export default function Home() {
  const dispatch = useAppDispatch();
  const route = useAppSelector(selectScreen);
  const notification = useAppSelector(selectNotification);

  useEffect(() => {
    if (route === routes.INITIALIZE) {
      setTimeout(() => {
        dispatch(initializeDevice());
      }, 500);
    }
  }, [route, dispatch]);

  return (
    <Fragment>
      <DynamicRouteContent route={route} />
      <GlobalLoading />
      <ItemDetails />
      <ItemAddedToOrder />
      <ConfirmResetCart />
      <DiscountAddedToOrder />
      <GlobalNotification />
    </Fragment>
  );
}
