import '@allo/ui/style.css';
import { PublicEnvScript } from 'next-runtime-env';
import { Bricolage_Grotesque } from 'next/font/google';
import PropTypes from 'prop-types';
import Root from '~/app/root';
import { cdnUrl } from '~/lib/constants';

import './globals.css';

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata = {
  title: 'allO Kiosk',
  description: 'Order & Pay kiosk application',
};

const bricolageGrotesque = Bricolage_Grotesque({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

const themeAccentTertiary = '#151413';

export default function RootLayout({ children }) {
  const bodyStyle = {
    margin: 0,
    backgroundColor: themeAccentTertiary,
    overflow: 'hidden',
  };

  const cdnScriptUrl = cdnUrl + '/twicpics.js';

  return (
    <html
      style={{ scrollbarWidth: 'none', overflow: 'hidden', userSelect: 'none' }}
      lang="de"
    >
      <head>
        <title>allO Kiosk</title>
        <script src={`${cdnScriptUrl}`} async defer></script>
        <meta name="mobile-web-app-capable" content="yes" />
        <PublicEnvScript />
      </head>
      <body style={bodyStyle} className={bricolageGrotesque.className}>
        <Root>{children}</Root>
      </body>
    </html>
  );
}

RootLayout.propTypes = {
  children: PropTypes.node,
};
