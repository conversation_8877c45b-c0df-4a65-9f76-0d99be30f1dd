import { Box, Button } from '@allo/ui-lib';
import Image from 'next/image';
import { routes } from '~/lib/constants';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectRestaurant, setScreen } from '~/store/slices/device/deviceSlice';

const Idle = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);

  const handleStart = () => {
    dispatch(setScreen(routes.CREATE));
  };

  return (
    <Button
      variant="none"
      onClick={handleStart}
      sx={{
        backgroundImage: `url(${restaurant.backgroundUrl})`,
        width: '100%',
        height: '100%',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        position: 'relative',
      }}
    >
      <Box sx={{ position: 'absolute', top: 120 }}>
        <Image
          src={restaurant.logoUrl}
          width={120}
          height={120}
          alt={'restaurant logo'}
        />
      </Box>
    </Button>
  );
};

export default Idle;
