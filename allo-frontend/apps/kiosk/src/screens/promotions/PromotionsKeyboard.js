import { Box, Button, Typography } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import { useEffect } from 'react';
import KeyboardRemoveIcon from '~/components/icons/KeyboardRemoveIcon';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  addPromoCode,
  refreshCart,
  selectPromoCode,
  setPromoCodeInput,
} from '~/store/slices/cart/cartSlice';
import { selectLanguage } from '~/store/slices/device/deviceSlice';

export const PromotionsKeyboard = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);
  const promoCode = useAppSelector(selectPromoCode);

  const regularButtons = [
    { value: 1, key: 1 },
    { value: 2, key: 2 },
    { value: 3, key: 3 },
    { value: 4, key: 4 },
    { value: 5, key: 5 },
    { value: 6, key: 6 },
    { value: 7, key: 7 },
    { value: 8, key: 8 },
    { value: 9, key: 9 },
    { value: 0, key: 0 },
    { value: 'Q', key: 'Q' },
    { value: 'W', key: 'W' },
    { value: 'E', key: 'E' },
    { value: 'R', key: 'R' },
    { value: 'T', key: 'T' },
    { value: 'Y', key: 'Y' },
    { value: 'U', key: 'U' },
    { value: 'I', key: 'I' },
    { value: 'O', key: 'O' },
    { value: 'P', key: 'P' },
    { value: 'A', key: 'A' },
    { value: 'S', key: 'S' },
    { value: 'D', key: 'D' },
    { value: 'F', key: 'F' },
    { value: 'G', key: 'G' },
    { value: 'H', key: 'H' },
    { value: 'J', key: 'J' },
    { value: 'K', key: 'K' },
    { value: 'L', key: 'L' },
    { value: <KeyboardRemoveIcon />, key: 'REMOVE' },
    { value: 'Z', key: 'Z' },
    { value: 'X', key: 'X' },
    { value: 'C', key: 'C' },
    { value: 'V', key: 'V' },
    { value: 'B', key: 'B' },
    { value: 'N', key: 'N' },
    { value: 'M', key: 'M' },
  ];

  const handleAddDiscountCode = () => {
    dispatch(addPromoCode({ promoCode: promoCode }));
  };

  const handleKeyboardClick = (key) => {
    dispatch(setPromoCodeInput(key));
  };

  useEffect(() => {
    dispatch(refreshCart());
  }, [dispatch]);

  return (
    <Box
      sx={{
        display: 'flex',
        padding: '56px 48px',
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: 'start',
          paddingBottom: '56px',
        }}
      >
        <input
          type="text"
          name="promoCode"
          readOnly
          style={{
            border: 'none',
            color: 'black',
            width: '388px',
            padding: 0,
            height: '26px',
            borderRadius: 0,
            backgroundColor: 'transparent',
            fontFamily: 'inherit',
            fontSize: '25px',
            fontStyle: 'normal',
            fontWeight: '400',
            lineHeight: '104%',
            letterSpacing: '0.053px',
          }}
          placeholder={getI18n(language, 'enter-your-code')}
          value={promoCode}
        />
      </Box>
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(10, 1fr)',
          gridTemplateRows: 'repeat(3, 1fr) 1fr',
          gap: '11px',
        }}
      >
        {regularButtons.map(({ value, key }) => {
          const isRemoveIcon = key === 'REMOVE';
          return (
            <Button
              key={key}
              className={'button'}
              onClick={() => handleKeyboardClick(key)}
              sx={{
                height: '66px',
                width: '72px',
                borderRadius: '10px',
                border: '1px solid #EFEDEC',
                boxShadow: '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
                cursor: 'pointer',
                backgroundColor: (theme) => theme.palette.highlight,
                '&:active': {
                  boxShadow: 'none',
                  borderBottom: 'none',
                  border: `1px solid #EFEDEC`,
                },
                '&:focus': {
                  boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                  border: '1px solid #EFEDEC',
                },
              }}
            >
              {isRemoveIcon ? (
                <Box
                  sx={{
                    display: 'flex',
                  }}
                >
                  {value}
                </Box>
              ) : (
                <Typography
                  sx={{
                    fontSize: '21px',
                    fontWeight: '400',
                    lineHeight: '104%',
                    fontFamily: 'inherit',
                  }}
                >
                  {value}
                </Typography>
              )}
            </Button>
          );
        })}
        <Button
          className={'button'}
          onClick={handleAddDiscountCode}
          _renderTypography={false}
          disabled={isEmpty(promoCode)}
          variant="none"
          sx={{
            height: '66px',
            gridColumn: '8 / -1',
            borderRadius: '16px',
            border: 'none',
            boxShadow: '0px 6px 0px #E76444',
            backgroundColor: '#FF7452',
            fontFamily: 'inherit',
            cursor: 'pointer',
            '&:active': {
              boxShadow: 'none',
              border: 'none',
            },
            '&:focus': {
              boxShadow: '0px 6px 0px #E76444',
              border: 'none',
            },
            '&:disabled': {
              opacity: '0.4',
            },
          }}
        >
          <Typography
            sx={{
              fontSize: '21px',
              fontWeight: '400',
              lineHeight: '104%',
              fontFamily: 'inherit',
              color: (theme) => theme?.palette?.accent_foreground,
            }}
          >
            {getI18n(language, 'add-discount')}
          </Typography>
        </Button>
      </Box>
    </Box>
  );
};
