import { Box, Typography } from '@allo/ui-lib';
import { formatToLocaleNumber } from '@monorepo/utils';
import Image from 'next/image';
import { useEffect } from 'react';
import {
  ADDED_DISCOUNT_SCREEN_TIMEOUT,
  FULL_PAGE_MODAL_INDEX,
  routes,
} from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  hideDiscountConfirmationScreen,
  selectCartTotalPrice,
  selectShowDiscountAddedConfirmationScreen,
} from '~/store/slices/cart/cartSlice';
import { selectLanguage, setScreen } from '~/store/slices/device/deviceSlice';

export const DiscountAddedToOrder = () => {
  const total = useAppSelector(selectCartTotalPrice);
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);

  const showDiscountAddedConfirmationScreen = useAppSelector(
    selectShowDiscountAddedConfirmationScreen
  );

  useEffect(() => {
    if (!showDiscountAddedConfirmationScreen) return;
    setTimeout(() => {
      dispatch(hideDiscountConfirmationScreen());
      dispatch(setScreen(routes.ORDER_SUMMARY));
    }, ADDED_DISCOUNT_SCREEN_TIMEOUT);
  }, [dispatch, showDiscountAddedConfirmationScreen]);

  if (!showDiscountAddedConfirmationScreen) return null;

  return (
    <Box
      sx={{
        zIndex: FULL_PAGE_MODAL_INDEX,
        height: '100vh',
        width: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
      }}
    >
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
          maxWidth: '1080px',
          margin: '0 auto',
          backgroundColor: (theme) => theme.palette.background,
        }}
      >
        <Box
          sx={{
            marginTop: '430px',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Image
            src={'/discount-added-check.gif'}
            alt={'item-added-image'}
            width={320}
            height={320}
          />
          <Typography
            sx={{
              fontSize: '80px',
              width: '516px',
              textAlign: 'center',
              fontWeight: 400,
              lineHeight: '83px',
            }}
          >
            {getI18n(language, 'discount-applied-to-your-order')}
          </Typography>
          <Typography
            sx={{
              fontSize: '35px',
              lineHeight: '36px',
              textAlign: 'center',
              width: '516px',
              marginTop: '56px',
            }}
          >
            {getI18n(language, 'order-total-updated')}
          </Typography>
          <Typography
            sx={{
              marginTop: '16px',
              fontSize: '35px',
              lineHeight: '36px',
              textAlign: 'center',
              width: '516px',
              color: 'rgba(255, 116, 82, 1)',
            }}
          >
            €{formatToLocaleNumber(total)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
