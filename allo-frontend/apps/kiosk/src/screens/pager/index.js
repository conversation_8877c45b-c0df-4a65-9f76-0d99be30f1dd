import { Box } from '@allo/ui-lib';
import { useEffect } from 'react';
import { getI18n } from '~/lib/i18n/i18n';
import { PromotionsKeyboard } from '~/screens/pager/PagerKeyboard';
import { SummarySidebar } from '~/screens/summary/components/SummarySidebar';
import { SummaryTopBar } from '~/screens/summary/components/SummaryTopBar';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { refreshCart } from '~/store/slices/cart/cartSlice';
import { selectLanguage } from '~/store/slices/device/deviceSlice';

const Summary = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);

  useEffect(() => {
    dispatch(refreshCart());
  }, [dispatch]);

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'hidden',
        display: 'flex',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <SummarySidebar />
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          flex: 2,
          position: 'relative',
        }}
      >
        <SummaryTopBar title={getI18n(language, 'pager')} />
        <PromotionsKeyboard />
      </Box>
    </Box>
  );
};

export default Summary;
