import { Box, Button, Typography } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import { useEffect } from 'react';
import KeyboardRemoveIcon from '~/components/icons/KeyboardRemoveIcon';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  refreshCart,
  selectPagerNumber,
  setPager,
  setPagerNumber,
} from '~/store/slices/cart/cartSlice';
import { selectLanguage } from '~/store/slices/device/deviceSlice';

export const PromotionsKeyboard = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);
  const pagerNumber = useAppSelector(selectPagerNumber);

  const regularButtons = [
    { value: 1, key: 1 },
    { value: 2, key: 2 },
    { value: 3, key: 3 },
    { value: 4, key: 4 },
    { value: 5, key: 5 },
    { value: 6, key: 6 },
    { value: 7, key: 7 },
    { value: 8, key: 8 },
    { value: 9, key: 9 },
    { value: null, key: 'empty' },
    { value: 0, key: 0 },
    { value: <KeyboardRemoveIcon />, key: 'REMOVE' },
  ];

  const handleSetPager = () => {
    dispatch(setPager({ pagerNumber }));
  };

  const handleKeyboardClick = (key) => {
    dispatch(setPagerNumber(key));
  };

  useEffect(() => {
    dispatch(refreshCart());
  }, [dispatch]);

  return (
    <Box
      sx={{
        display: 'flex',
        padding: '56px 48px',
        flexDirection: 'column',
        height: '100%',
        justifyContent: 'space-between',
      }}
    >
      <Box
        sx={{
          background: 'url("/pager-background.png") no-repeat',
          backgroundSize: 'cover',
          height: '100%',
          margin: '-56px -48px',
          padding: '56px 48px',
        }}
      >
        <Typography
          sx={{
            fontSize: '60px',
            fontWeight: '400',
            lineHeight: '104%',
            fontFamily: 'inherit',
            marginBottom: '24px',
          }}
        >
          {getI18n(language, 'take-a-pager-and-enter-its-number-to-activate-it')}
        </Typography>
      </Box>
      <Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'start',
            paddingBottom: '56px',
          }}
        >
          <input
            type="text"
            name="promoCode"
            readOnly
            style={{
              border: 'none',
              color: 'black',
              width: '100%',
              padding: 0,
              height: '100px',
              borderRadius: 0,
              backgroundColor: 'transparent',
              fontFamily: 'inherit',
              fontSize: '60px',
              fontStyle: 'normal',
              fontWeight: '400',
              lineHeight: '104%',
              letterSpacing: '0.053px',
              textAlign: 'center',
            }}
            placeholder={getI18n(language, 'your-pager-number-here')}
            value={pagerNumber}
          />
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'space-between',
            gap: '11px',
            marginBottom: '24px',
          }}
        >
          {regularButtons.map(({ value, key }) => {
            const isRemoveIcon = key === 'REMOVE';
            const isSpacer = key === 'empty';

            if (isSpacer) {
              return <Box key={key} sx={{ width: '265px' }} />;
            }

            return (
              <Button
                key={key}
                className={'button'}
                onClick={() => handleKeyboardClick(key)}
                sx={{
                  height: '115px',
                  width: '265px',
                  borderRadius: '10px',
                  border: '1px solid #EFEDEC',
                  boxShadow: '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
                  cursor: 'pointer',
                  backgroundColor: (theme) => theme.palette.highlight,
                  '&:active': {
                    boxShadow: 'none',
                    borderBottom: 'none',
                    border: `1px solid #EFEDEC`,
                  },
                  '&:focus': {
                    boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    border: '1px solid #EFEDEC',
                  },
                }}
              >
                {isRemoveIcon ? (
                  <Box
                    sx={{
                      display: 'flex',
                    }}
                  >
                    {value}
                  </Box>
                ) : (
                  <Typography
                    sx={{
                      fontSize: '21px',
                      fontWeight: '400',
                      lineHeight: '104%',
                      fontFamily: 'inherit',
                    }}
                  >
                    {value}
                  </Typography>
                )}
              </Button>
            );
          })}
        </Box>
        <Button
          className={'button'}
          onClick={handleSetPager}
          _renderTypography={false}
          disabled={isEmpty(pagerNumber)}
          variant="none"
          sx={{
            width: '100%',
            height: '106px',
            borderRadius: '16px',
            border: 'none',
            boxShadow: '0px 6px 0px #E76444',
            backgroundColor: '#FF7452',
            fontFamily: 'inherit',
            cursor: 'pointer',
            '&:active': {
              boxShadow: 'none',
              border: 'none',
            },
            '&:focus': {
              boxShadow: '0px 6px 0px #E76444',
              border: 'none',
            },
            '&:disabled': {
              opacity: '0.4',
            },
          }}
        >
          <Typography
            sx={{
              fontSize: '21px',
              fontWeight: '400',
              lineHeight: '104%',
              fontFamily: 'inherit',
              color: (theme) => theme?.palette?.accent_foreground,
            }}
          >
            {getI18n(language, 'activate-pager-and-pay')}
          </Typography>
        </Button>
      </Box>
    </Box>
  );
};
