import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import Image from 'next/image';
import { defaultMerchantIconUrl } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { registerDevice, selectLanguage } from '~/store/slices/device/deviceSlice';

const Registration = () => {
  const dispatch = useAppDispatch();
  const language = useAppSelector(selectLanguage);

  const handleRegisterDevice = (e) => {
    e.preventDefault();
    const serialNumber = e?.target?.serialNumber?.value;
    const pairingCode = e?.target?.pairingCode?.value;
    if (serialNumber && pairingCode) {
      dispatch(
        registerDevice({
          serialNumber: serialNumber.toUpperCase(),
          pairingCode,
        })
      );
    }
  };

  return (
    <Box
      sx={{
        zIndex: 1000,
        height: '100vh',
        width: '100%',
        top: 0,
        left: 0,
        background: '#FAFAFA',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            height: '100%',
            overflow: 'hidden',
          }}
        >
          <Box
            sx={{
              width: '160px',
              borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Box
              sx={{
                height: '160px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Image
                src={defaultMerchantIconUrl}
                width={80}
                height={80}
                alt={'restaurant logo'}
              />
            </Box>
          </Box>
          <form onSubmit={handleRegisterDevice} style={{ flex: 1, position: 'relative' }}>
            <Box
              sx={{
                flex: 1,
                overflow: 'auto',
                height: 'calc(100% - 160px)',
              }}
            >
              <Box
                sx={{
                  zIndex: 3,
                  background: (theme) => theme?.palette.background,
                }}
              >
                <Box
                  sx={{
                    width: '100%',
                    height: '320px',
                    backgroundImage: 'none',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    backgroundSize: 'cover',
                    backgroundAttachment: 'fixed',
                  }}
                ></Box>
                <Box
                  sx={{
                    zIndex: 3,
                    flex: 2,
                    height: '160px',
                    minHeight: '160px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'left',
                    padding: '48px',
                    borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  }}
                >
                  <Typography
                    variant={TypographyVariant.medium_27}
                    sx={{
                      fontSize: 35,
                      fontWeight: 500,
                      width: '100%',
                      lineHeight: '104%',
                      letterSpacing: '-0.35px',
                    }}
                  >
                    {getI18n(language, 'register-device')}
                  </Typography>
                </Box>
                <Box
                  sx={{
                    padding: '48px',
                    borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  }}
                >
                  <Typography
                    variant={TypographyVariant.medium_27}
                    sx={{
                      fontSize: 21,
                      fontWeight: 400,
                      width: '100%',
                      lineHeight: '142%',
                      letterSpacing: '0.053px',
                    }}
                  >
                    {getI18n(language, 'serial-number')}
                  </Typography>
                  <input
                    type="text"
                    name="serialNumber"
                    style={{
                      border: 'none',
                      width: '388px',
                      padding: 0,
                      color: '#151413',
                      height: '62px',
                      borderRadius: 0,
                      marginTop: '32px',
                      backgroundColor: 'transparent',
                      fontFamily: 'inherit',
                      fontSize: '21px',
                      fontStyle: 'normal',
                      fontWeight: '400',
                      lineHeight: '142%',
                      letterSpacing: '0.053px',
                      textTransform: 'uppercase',
                    }}
                    placeholder={'ABCDEF'}
                  />
                </Box>
                <Box
                  sx={{
                    padding: '48px',
                    borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  }}
                >
                  <Typography
                    variant={TypographyVariant.medium_27}
                    sx={{
                      fontSize: 21,
                      fontWeight: 400,
                      width: '100%',
                      lineHeight: '142%',
                      letterSpacing: '0.053px',
                    }}
                  >
                    {getI18n(language, 'pairing-code')}
                  </Typography>
                  <input
                    type="number"
                    name="pairingCode"
                    style={{
                      border: 'none',
                      width: '388px',
                      padding: 0,
                      color: '#151413',
                      height: '62px',
                      borderRadius: 0,
                      marginTop: '32px',
                      backgroundColor: 'transparent',
                      fontFamily: 'inherit',
                      fontSize: '21px',
                      fontStyle: 'normal',
                      fontWeight: '400',
                      lineHeight: '142%',
                      letterSpacing: '0.053px',
                    }}
                    placeholder={'123456'}
                  />
                </Box>
              </Box>
            </Box>
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 0,
                right: 0,
                borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Box
                sx={{
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                }}
              >
                <Box
                  sx={{
                    height: '160px',
                    width: '100%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'end',
                    paddingRight: '24px',
                    paddingLeft: '24px',
                  }}
                >
                  <Button
                    className={'button-primary'}
                    sx={{
                      height: '106px',
                      paddingLeft: '56px',
                      paddingRight: '56px',
                      borderRadius: '16px',
                      border: 'none',
                      boxShadow: '0px 6px 0px #E76444',
                      backgroundColor: '#FF7452',
                      fontFamily: 'inherit',
                      cursor: 'pointer',
                      '&:active': {
                        boxShadow: 'none',
                        border: 'none',
                      },
                      '&:focus': {
                        boxShadow: '0px 6px 0px #E76444',
                        border: 'none',
                      },
                    }}
                    variant="none"
                    _renderTypography={false}
                  >
                    <Typography
                      sx={{
                        fontSize: '25px',
                        fontWeight: '400',
                        color: (theme) => theme?.palette?.accent_foreground,
                      }}
                    >
                      {getI18n(language, 'finish')}
                    </Typography>
                  </Button>
                </Box>
              </Box>
            </Box>
          </form>
        </Box>
      </Box>
    </Box>
  );
};

export default Registration;
