import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import Image from 'next/image';
import IconChecked20 from '~/components/icons/IconChecked20';
import ImageWithFallback from '~/components/ImageWithFallback';
import { DEFAULT_BUTTON_CLICK_TIMEOUT } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { generateCart } from '~/store/slices/cart/cartSlice';
import {
  selectDiningOptions,
  selectLanguage,
  selectLanguageOptions,
  selectRestaurant,
  setLanguage,
} from '~/store/slices/device/deviceSlice';
import { diningOptionValues } from '../../lib/constants/index.js';

const Creation = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const language = useAppSelector(selectLanguage);
  const languageOptions = useAppSelector(selectLanguageOptions);
  const diningOptions = useAppSelector(selectDiningOptions);

  const handleCreateCart = () => {
    setTimeout(() => {
      dispatch(generateCart());
    }, DEFAULT_BUTTON_CLICK_TIMEOUT);
  };

  const handleCreateCartToGo = () => {
    setTimeout(() => {
      dispatch(generateCart({ toGo: true }));
    }, DEFAULT_BUTTON_CLICK_TIMEOUT);
  };

  const handleUpdateLanguage = (systemLanguage) => {
    if (systemLanguage && systemLanguage.value) {
      dispatch(setLanguage(systemLanguage.value));
    }
  };

  const InHouseBtn = () => (
    <Button
      className={'button'}
      variant={'secondary'}
      _renderTypography={false}
      sx={{
        height: '326px',
        width: '100%',
        cursor: 'pointer',
        flex: 1,
        boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
        position: 'relative',
        border: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        borderRadius: '16px',
      }}
      onClick={handleCreateCart}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <Image src={'/eat-here-image.png'} alt={'eat here'} width={80} height={80} />
        <Typography variant={TypographyVariant.medium_35}>
          {getI18n(language, 'eat-here')}
        </Typography>
      </Box>
    </Button>
  );

  const ToGoBtn = () => (
    <Button
      className={'button'}
      variant={'secondary'}
      _renderTypography={false}
      sx={{
        height: '326px',
        width: '100%',
        cursor: 'pointer',
        flex: 1,
        boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
        position: 'relative',
        border: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        borderRadius: '16px',
      }}
      onClick={handleCreateCartToGo}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          gap: '12px',
        }}
      >
        <Image src={'/takeaway-image.png'} alt={'eat here'} width={80} height={80} />
        <Typography variant={TypographyVariant.medium_35}>
          {getI18n(language, 'takeaway')}
        </Typography>
      </Box>
    </Button>
  );

  return (
    <Box
      sx={{
        height: '100vh',
        overflow: 'auto',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
        }}
      >
        <Box
          sx={{
            width: '100%',
            height: '1392px',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            // backgroundImage: `url(${restaurant.backgroundUrl})`,
            // height: "100%",
            // backgroundPosition: "center",
            // backgroundSize: "cover",
            // backgroundRepeat: "no-repeat",
          }}
        >
          {restaurant?.backgroundUrl && (
            <ImageWithFallback
              src={restaurant?.backgroundUrl}
              height={1392}
              width={1080}
              alt={'restaurant background'}
            />
          )}
        </Box>

        <Box
          sx={{
            display: 'flex',
            flexDirection: 'row',
            padding: '24px',
            gap: '24px',
            width: '100%',
          }}
        >
          {diningOptions?.map((option) => {
            if (option === diningOptionValues.IN_HOUSE) {
              return <InHouseBtn key={diningOptionValues.IN_HOUSE} />;
            }
            if (option === diningOptionValues.TO_GO) {
              return <ToGoBtn key={diningOptionValues.TO_GO} />;
            }
          })}
          {isEmpty(diningOptions) ? <InHouseBtn /> : null}
          {isEmpty(diningOptions) ? <ToGoBtn /> : null}
        </Box>
        <Box
          sx={{
            borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            backgroundColor: (theme) => theme?.palette?.highlight,
            display: 'flex',
            flexDirection: 'row',
            width: '100%',
            height: '160px',
          }}
        >
          <Box
            sx={{
              flex: 1,
              padding: '24px',
              display: 'flex',
              flexDirection: 'row',
              gap: '20px',
            }}
          >
            {languageOptions.map((languageOption) => {
              const isSelected = languageOption?.value === language;
              return (
                <Button
                  className={'button'}
                  key={languageOption.value}
                  variant={'secondary'}
                  sx={{
                    boxShadow: isSelected
                      ? 'none'
                      : '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                    flex: 1,
                    height: '100%',
                    border: (theme) =>
                      `1px solid ${isSelected ? theme?.palette?.accent_primary : theme?.palette?.foreground_6}`,
                    position: 'relative',
                  }}
                  _renderTypography={false}
                  onClick={() => handleUpdateLanguage(languageOption)}
                >
                  {isSelected && (
                    <Box sx={{ position: 'absolute', top: '-8px', right: '-8px' }}>
                      <IconChecked20 height={28} width={28} />
                    </Box>
                  )}
                  <Typography
                    sx={{
                      fontSize: '21px',
                      fontWeight: '400',
                      lineHeight: '104%',
                      letterSpacing: '0.043px',
                    }}
                  >
                    {languageOption.label}
                  </Typography>
                </Button>
              );
            })}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Creation;
