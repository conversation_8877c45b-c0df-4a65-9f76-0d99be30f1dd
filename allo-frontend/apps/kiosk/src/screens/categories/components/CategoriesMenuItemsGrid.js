import { Box } from '@allo/ui-lib';
import { CategoriesMenuItem } from '~/screens/categories/components/CategoriesMenuItem';

export const CategoriesMenuItemsGrid = (props) => {
  const { item } = props;

  return (
    <Box
      sx={{
        display: 'flex',
        paddingTop: '20px',
        flexWrap: 'wrap',
        justifyContent: 'start',
        gap: '2.3%',
      }}
    >
      {item?.items?.map((i) => (
        <CategoriesMenuItem item={i} key={`item_${i.code}`} />
      ))}
    </Box>
  );
};
