import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  selectSelectedMenuCategoryId,
  setSelectedMenuCategoryId,
} from '~/store/slices/menu/menuSlice';

export const CategoriesSidebarItem = ({ title = 'Title', id, emoji }) => {
  const dispatch = useAppDispatch();
  const selectedMenuCategoryId = useAppSelector(selectSelectedMenuCategoryId);

  const handleSelectMenuCategory = (e) => {
    e.stopPropagation();
    e.preventDefault();
    dispatch(setSelectedMenuCategoryId({ id }));
  };

  return (
    <Button
      variant={'none'}
      sx={{
        padding: '0 16px',
        height: '160px',
        minHeight: '160px',
        borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        backgroundColor:
          selectedMenuCategoryId === id ? 'rgba(246, 245, 244, 1)' : 'inherit',
      }}
      onClick={handleSelectMenuCategory}
      _renderTypography={false}
    >
      <Box sx={{ display: 'flex', alignItems: 'baseline', gap: '6px' }}>
        {emoji && (
          <Box component={'span'} sx={{ fontSize: 19 }}>
            {emoji}
          </Box>
        )}
        <Typography
          variant={TypographyVariant.medium_27}
          sx={{
            fontSize: 17,
            fontWeight: 400,
            margin: '0 auto',
            width: '100%',
            textAlign: 'center',
            lineHeight: '104%',
            letterSpacing: '0.048px',
          }}
        >
          {title}
        </Typography>
      </Box>
    </Button>
  );
};

CategoriesSidebarItem.propTypes = {
  title: PropTypes.string,
  id: PropTypes.string,
  emoji: PropTypes.string,
};
