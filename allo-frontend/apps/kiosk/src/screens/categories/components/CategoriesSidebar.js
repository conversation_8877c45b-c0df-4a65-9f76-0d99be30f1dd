import { Box, Button, Typography } from '@allo/ui-lib';
import Image from 'next/image';
import IconBack_28 from '~/components/icons/IconBack_28';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { CategoriesSidebarItem } from '~/screens/categories/components/CategoriesSidebarItem';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectCartHasItems } from '~/store/slices/cart/cartSlice';
import {
  selectLanguage,
  selectRestaurant,
  setScreen,
  setShowCancellationScreen,
  toggleAccessibility,
} from '~/store/slices/device/deviceSlice';
import { selectSelectedVersionedItems } from '~/store/slices/menu/menuSlice';

export const CategoriesSidebar = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const language = useAppSelector(selectLanguage);
  const cartHasItems = useAppSelector(selectCartHasItems);

  const selectedVersionedMenuItems = useAppSelector(selectSelectedVersionedItems);

  const handleResetCart = () => {
    dispatch(setShowCancellationScreen());
  };

  const handleBack = () => {
    dispatch(setScreen(routes.DISCOVER));
  };

  const handleToggleAccessibility = () => {
    dispatch(toggleAccessibility());
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: '160px',
        minWidth: '160px',
        borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: (theme) => theme.palette.highlight,
      }}
    >
      <Box
        sx={{
          height: '160px',
          minHeight: '160px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <Image src={restaurant.logoUrl} width={80} height={80} alt={'restaurant logo'} />
      </Box>
      <Button
        variant="none"
        onClick={handleBack}
        sx={{
          height: '80px',
          minHeight: '80px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <IconBack_28 />
      </Button>
      <Box
        sx={{
          overflow: 'auto',
          paddingBottom: '160px',
        }}
      >
        {selectedVersionedMenuItems?.map((item) => (
          <CategoriesSidebarItem
            id={item.id}
            key={item.id}
            title={item.title}
            emoji={item.emoji}
          />
        ))}
      </Box>
      <Box
        sx={{
          height: '160px',
          position: 'absolute',
          bottom: 0,
          left: 0,
          width: '100%',
          backgroundColor: (theme) => theme.palette.highlight,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '160px',
              height: '80px',
              borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            }}
          >
            <Button
              variant={'none'}
              onClick={handleToggleAccessibility}
              disabled
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                height: '100%',
                opacity: 0.4,
              }}
            >
              <Typography sx={{ fontSize: '17px', lineHeight: '104%' }}>
                {getI18n(language, 'accessibility')}
              </Typography>
            </Button>
          </Box>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              width: '160px',
              height: '80px',
              borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            }}
          >
            <Button
              variant={'none'}
              onClick={handleResetCart}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                width: '100%',
                height: '100%',
                fontFamily: 'inherit',
              }}
            >
              <Typography
                sx={{
                  fontSize: '17px',
                  lineHeight: '104%',
                  fontWeight: '400px',
                }}
              >
                {getI18n(language, cartHasItems ? 'cancel' : 'restart')}
              </Typography>
            </Button>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};
