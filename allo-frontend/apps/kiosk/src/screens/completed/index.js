import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import Image from 'next/image';
import React, { useEffect } from 'react';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useGetPrinterContentQuery } from '~/services/printerContentService';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectOrderNumber } from '~/store/slices/cart/cartSlice';
import {
  resetDevice,
  selectDeviceId,
  selectLanguage,
  selectRestaurant,
  setScreen,
} from '~/store/slices/device/deviceSlice';

const Completed = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const deviceId = useAppSelector(selectDeviceId);
  const orderNumber = useAppSelector(selectOrderNumber);
  const language = useAppSelector(selectLanguage);
  const [showResetButton, setShowResetButton] = React.useState(true);
  const timeoutRef = React.useRef(null);
  const { data, isFetching: _isFetching } = useGetPrinterContentQuery(
    {
      restaurantId: restaurant?.id,
      deviceId: deviceId,
    },
    {
      pollingInterval: 2000,
      skipPollingIfUnfocused: true,
    }
  );

  const sendDataToPrinter = (data) => {
    try {
      // eslint-disable-next-line no-undef
      JSBridge.print(data?.dataBytes || []);
    } catch (e) {
      console.log(e);
    }
  };

  useEffect(() => {
    if (data) {
      setShowResetButton(true);
      timeoutRef.current = setTimeout(() => {
        resetDeviceHandler();
      }, 20000);
      sendDataToPrinter(data);
      // check if we have more to print
    }
  }, [data]);

  const _handleCreateCart = (_toGo = false) => {
    // dispatch(generateCart(toGo));
    dispatch(setScreen(routes.PAYMENT_INSTRUCTIONS));
  };

  const _handleCreateCartToGo = () => {
    dispatch(setScreen(routes.PAYMENT_INSTRUCTIONS));
  };

  const resetDeviceHandler = () => {
    clearTimeout(timeoutRef.current);
    dispatch(resetDevice());
  };

  return (
    <Box
      style={{
        height: '100%',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
        position: 'relative',
      }}
    >
      <Image
        style={{
          borderRadius: 'inherit',
          objectFit: 'cover',
          position: 'absolute',
          top: '0px',
          left: '0px',
          width: '100%',
          height: '100%',
        }}
        src={'/printing-instructions-image.png'}
        alt={'payment-completed-image'}
        width={1080}
        height={1920}
      />
      <Box
        sx={{
          position: 'absolute',
          left: '80px',
          top: '80px',
        }}
      >
        <Image src={restaurant.logoUrl} width={80} height={80} alt={'restaurant logo'} />
      </Box>
      <Typography
        variant={TypographyVariant.medium_27}
        sx={{
          fontSize: 80,
          fontWeight: 500,
          margin: '0 auto',
          width: 'calc(100% - 80px)',
          textAlign: 'left',
          lineHeight: '106%',
          letterSpacing: '0.16px',
          position: 'absolute',
          top: '218px',
          left: '80px',
        }}
      >
        {getI18n(language, 'thank-you')}!
        <span style={{ display: 'block' }}>
          {getI18n(language, 'your-order-number-is')}
        </span>
        <span
          data-testid="order-number"
          style={{
            display: 'block',
            marginTop: '32px',
            fontSize: '120px',
            color: 'rgba(255, 116, 82, 1)',
          }}
        >
          {orderNumber}
        </span>
      </Typography>
      <Box
        style={{
          position: 'absolute',
          bottom: '80px',
          left: '80px',
          display: 'flex',
          gap: '56px',
        }}
      >
        {/*<Image*/}
        {/*  style={{}}*/}
        {/*  src={"/arrow-down-image.png"}*/}
        {/*  alt={"arrow-down"}*/}
        {/*  width={113}*/}
        {/*  height={160}*/}
        {/*/>*/}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between',
          }}
        >
          <Typography
            sx={{
              fontSize: '32px',
              lineHeight: 'unset',
            }}
          >
            {getI18n(language, 'your-ticket-is-printing')}...
          </Typography>
          {showResetButton && (
            <Button
              onClick={resetDeviceHandler}
              sx={{
                minWidth: '120px',
                height: '72px',
                paddingLeft: '24px',
                paddingRight: '24px',
                marginTop: '40px',
                borderRadius: '16px',
                border: 'none',
                boxShadow: '0px 6px 0px #E76444',
                backgroundColor: '#FF7452',
                fontFamily: 'inherit',
                cursor: 'pointer',
                '&:active': {
                  boxShadow: 'none',
                  border: 'none',
                },
                '&:focus': {
                  boxShadow: '0px 6px 0px #E76444',
                  border: 'none',
                },
              }}
              variant="primary"
              _renderTypography={false}
            >
              <Typography
                sx={{
                  fontSize: '21px',
                  fontWeight: '400',
                  color: (theme) => theme?.palette?.accent_foreground,
                }}
              >
                {getI18n(language, 'finish')}
              </Typography>
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Completed;
