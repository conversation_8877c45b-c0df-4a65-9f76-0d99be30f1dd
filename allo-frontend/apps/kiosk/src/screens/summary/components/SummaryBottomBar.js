import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import { BOTTOM_PANEL_Z_INDEX, routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { formatToLocaleNumber } from '~/lib/utils';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import {
  selectCartTotalDiscount,
  selectCartTotalPrice,
  selectCartTotalWithoutDiscount,
} from '~/store/slices/cart/cartSlice';
import {
  selectAllowPromotions,
  selectEnablePager,
  selectLanguage,
  setScreen,
} from '~/store/slices/device/deviceSlice';
import { selectHasSuggestions } from '~/store/slices/menu/menuSlice';

export const SummaryBottomBar = () => {
  const dispatch = useAppDispatch();
  const cartTotal = useAppSelector(selectCartTotalPrice);
  const totalDiscount = useAppSelector(selectCartTotalDiscount);
  const totalWithoutDiscount = useAppSelector(selectCartTotalWithoutDiscount);
  const allowPromotions = useAppSelector(selectAllowPromotions);
  const hasSuggestions = useAppSelector(selectHasSuggestions);
  const enablePager = useAppSelector(selectEnablePager);

  const language = useAppSelector(selectLanguage);

  const handleCheckout = () => {
    const nextRoute = enablePager ? routes.PAGER : routes.CHECKOUT;
    dispatch(setScreen(hasSuggestions ? routes.SUGGESTIONS : nextRoute));
  };

  const handleAddingDiscount = () => {
    dispatch(setScreen(routes.PROMOTIONS));
  };

  return (
    <Box
      sx={{
        // height: "160px",
        // minHeight: "160px",
        width: '100%',
        position: 'absolute',
        bottom: 0,
        left: 0,
        zIndex: BOTTOM_PANEL_Z_INDEX,
        display: 'flex',
      }}
    >
      <Box
        sx={{
          flex: 2,
          // height: "160px",
          width: '100%',
          borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
          backgroundColor: (theme) => theme?.palette?.highlight,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'end',
          paddingRight: '24px',
          paddingLeft: '24px',
          paddingBottom: '30px', // 24px + 6px since shadow doesnt count in the 112 button height
          paddingTop: '23px', // 24px - 1px due to top border not counted in padding
        }}
      >
        <Box
          sx={{
            marginLeft: '4px',
            marginRight: '4px',
            marginBottom: '40px',
            marginTop: '24px',
            width: '100%',
          }}
        >
          {totalDiscount > 0 && (
            <Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  marginBottom: '16px',
                }}
              >
                <Typography
                  variant={TypographyVariant.medium_19}
                  sx={{
                    fontSize: '21px',
                    fontWeight: '400',
                    lineHeight: '104%',
                    color: '#726E6A',
                  }}
                >
                  {getI18n(language, 'subtotal')}
                </Typography>
                <Typography
                  variant={TypographyVariant.medium_19}
                  sx={{
                    fontSize: '21px',
                    fontWeight: '400',
                    lineHeight: '104%',
                    color: '#726E6A',
                  }}
                >
                  €{formatToLocaleNumber(totalWithoutDiscount)}
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  marginBottom: '16px',
                }}
              >
                <Typography
                  variant={TypographyVariant.medium_19}
                  sx={{
                    fontSize: '21px',
                    fontWeight: '400',
                    lineHeight: '104%',
                    color: '#726E6A',
                  }}
                >
                  {getI18n(language, 'discount')}
                </Typography>
                <Typography
                  variant={TypographyVariant.medium_19}
                  sx={{
                    fontSize: '21px',
                    fontWeight: '400',
                    lineHeight: '104%',
                    color: '#726E6A',
                  }}
                >
                  - €{formatToLocaleNumber(totalDiscount)}
                </Typography>
              </Box>
            </Box>
          )}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <Typography
              variant={TypographyVariant.medium_19}
              sx={{
                fontSize: '25px',
                fontWeight: '400',
                lineHeight: '104%',
              }}
            >
              {getI18n(language, 'total')}
            </Typography>
            <Typography
              variant={TypographyVariant.medium_19}
              sx={{
                fontSize: '25px',
                fontWeight: '400',
                lineHeight: '104%',
              }}
            >
              €{formatToLocaleNumber(cartTotal)}
            </Typography>
          </Box>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            width: '100%',
            gap: '24px',
          }}
        >
          {allowPromotions && (
            <Button
              className={'button'}
              variant={'secondary'}
              onClick={handleAddingDiscount}
              _renderTypography={false}
              sx={{
                flex: 1,
                minWidth: 'fit-content',
                height: '106px',
                paddingLeft: '56px',
                paddingRight: '56px',
                boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
                fontFamily: 'inherit',
                cursor: 'pointer',
                border: `1px solid #EFEDEC`,
                borderRadius: '16px',
                '&:active': {
                  boxShadow: 'none',
                  borderBottom: 'none',
                  border: `1px solid #EFEDEC`,
                },
              }}
            >
              <Typography
                sx={{
                  fontSize: '25px',
                  textAlign: 'center',
                  fontWeight: '400',
                }}
              >
                {getI18n(language, 'add-discount-code')}
              </Typography>
            </Button>
          )}
          <Button
            className={'button'}
            sx={{
              height: '106px',
              paddingLeft: '56px',
              paddingRight: '56px',
              borderRadius: '16px',
              border: 'none',
              width: '100%',
              boxShadow: '0px 6px 0px #E76444',
              backgroundColor: '#FF7452',
              fontFamily: 'inherit',
              cursor: 'pointer',
              '&:active': {
                boxShadow: 'none',
                border: 'none',
              },
              '&:focus': {
                boxShadow: '0px 6px 0px #E76444',
                border: 'none',
              },
            }}
            onClick={handleCheckout}
            variant="none"
            _renderTypography={false}
          >
            <Box
              component="span"
              sx={{
                display: 'flex',
                gap: '8px',
              }}
            >
              <Typography
                sx={{
                  fontSize: '25px',
                  fontWeight: '400',
                  color: (theme) => theme?.palette?.accent_foreground,
                }}
              >
                {getI18n(language, 'pay-now')}
              </Typography>
            </Box>
          </Button>
        </Box>
      </Box>
    </Box>
  );
};
