import { Box } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import ImageWithFallback from '~/components/ImageWithFallback';

export const SummaryItemImage = ({ thumbnailUrl = '' }) => {
  return (
    <Box
      borderRadius={'md_12'}
      sx={{
        minWidth: '272px',
        width: '272px',
        minHeight: '272px',
        height: '272px',
      }}
    >
      <ImageWithFallback
        style={{ borderRadius: 'inherit' }}
        width={272}
        height={272}
        src={thumbnailUrl}
        fallbackSrc={'/item-no-image.png'}
        alt={'image'}
      />
    </Box>
  );
};

SummaryItemImage.propTypes = {
  thumbnailUrl: PropTypes.string,
};
