import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import { isEmpty } from '@monorepo/utils';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { getI18n } from '~/lib/i18n/i18n';
import { RemarkWithTooltip } from '~/screens/item-details/components/RemarkWithTooltip';
import { useAppSelector } from '~/store/hooks';
import { selectLanguage } from '~/store/slices/device/deviceSlice';
import { selectRemarksFromItemCode } from '~/store/slices/itemDetails/itemDetailsSlice';

export const SummaryItemContent = ({
  name,
  optionItems,
  extraItems,
  handleModify,
  code,
}) => {
  const language = useAppSelector(selectLanguage);
  const remarks = useAppSelector((state) => selectRemarksFromItemCode(state, code));

  const hasAdditions = !isEmpty(optionItems) || !isEmpty(extraItems);
  const [activeRemarkId, setActiveRemarkId] = useState(null);
  return (
    <Box
      sx={{
        flex: 2,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box
        sx={{
          minHeight: '44px',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <Typography
          sx={{
            fontSize: '25px',
            lineHeight: 'unset',
            fontWeight: '500',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {name}
        </Typography>
      </Box>
      {remarks && remarks.length > 0 && (
        <Box sx={{ display: 'flex', gap: '8px', marginTop: '16px' }}>
          {remarks.map((remark) => (
            <RemarkWithTooltip
              key={remark.id}
              activeRemarkId={activeRemarkId}
              setActiveRemarkId={setActiveRemarkId}
              remark={remark}
            />
          ))}
        </Box>
      )}
      {hasAdditions && (
        <Box
          sx={{
            paddingTop: '16px',
            display: 'flex',
            flexDirection: 'column',
            gap: '8px',
          }}
        >
          {optionItems.map((item) => (
            <SummaryOptionItem key={item.id} optionItem={item} />
          ))}
          {extraItems.map((item) => (
            <SummaryOptionItem key={item.id} optionItem={item} />
          ))}
        </Box>
      )}
      <Box
        sx={{
          paddingTop: '16px',
        }}
      >
        <Button
          onClick={handleModify}
          variant={'none'}
          sx={{
            padding: '13px 16px',
            border: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            background: (theme) => theme?.palette?.highlight,
            borderRadius: '10px',
          }}
          _renderTypography={false}
        >
          <Typography variant={TypographyVariant.medium_15}>
            {getI18n(language, 'modify')}
          </Typography>
        </Button>
      </Box>
    </Box>
  );
};

SummaryItemContent.propTypes = {
  id: PropTypes.string,
  code: PropTypes.string,
  name: PropTypes.string,
  optionItems: PropTypes.array,
  extraItems: PropTypes.array,
  handleModify: PropTypes.func,
  remarks: PropTypes.array,
};

export const SummaryOptionItem = ({ optionItem }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'start',
      }}
    >
      <Box
        sx={{
          width: '22px',
          height: '22px',
          background: 'rgba(139, 191, 159, 0.32)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '4px',
        }}
      >
        <Typography
          sx={{
            fontSize: '13px',
            lineHeight: 'unset',
            fontWeight: '500',
          }}
        >
          {optionItem.qtd}
        </Typography>
      </Box>
      <Typography
        sx={{
          paddingLeft: '12px',
          fontSize: '17px',
          lineHeight: 'unset',
          fontWeight: '400',
        }}
      >
        {`${optionItem.name} - €${optionItem.total.toFixed(2)}`}
      </Typography>
    </Box>
  );
};

SummaryOptionItem.propTypes = {
  optionItem: PropTypes.object.required,
};
