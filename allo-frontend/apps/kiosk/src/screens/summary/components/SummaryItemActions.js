import { Box, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { QuantitySelector } from '~/components/QuantitySelector';
import { formatToLocaleNumber } from '~/lib/utils';

export const SummaryItemActions = ({ value = 0, total = 0, increment, decrement }) => {
  return (
    <Box
      sx={{
        minWidth: '130px',
        width: '130px',
        height: '100%',
      }}
    >
      <QuantitySelector value={value} increment={increment} decrement={decrement} />
      <Box
        sx={{
          marginTop: '20px',
          width: '100%',
          display: 'flex',
          justifyContent: 'end',
        }}
      >
        <Typography
          sx={{
            fontWeight: '400',
            fontSize: '17px',
            marginRight: '4px',
          }}
        >
          €{formatToLocaleNumber(total)}
        </Typography>
      </Box>
    </Box>
  );
};

SummaryItemActions.propTypes = {
  value: PropTypes.number,
  total: PropTypes.number,
  increment: PropTypes.func,
  decrement: PropTypes.func,
};
