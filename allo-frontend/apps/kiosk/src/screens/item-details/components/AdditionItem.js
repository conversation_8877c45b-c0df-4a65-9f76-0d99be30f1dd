import { Box, Button, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { QuantitySelector } from '~/components/QuantitySelector';
import { formatToLocaleNumber } from '~/lib/utils';
import { RemarkWithTooltip } from '~/screens/item-details/components/RemarkWithTooltip';
import { useAppDispatch } from '~/store/hooks';
import { updateAdditionItem } from '~/store/slices/itemDetails/itemDetailsSlice';

export const AdditionItem = ({
  item,
  additionIndex,
  additionItemIndex,
  additionQtd,
  additionMax,
  additionType,
  additionFulFilled,
}) => {
  const dispatch = useAppDispatch();
  const { _min, max, qtd } = item || {};

  const [activeRemarkId, setActiveRemarkId] = useState(null);
  const isRadio = additionQtd === 1; // at least one option
  const isCheckbox =
    (additionQtd > 1 && max === 1) || additionMax === 1 || (additionMax > 1 && max === 1); // max one extra
  const isQuantity = !isRadio && !isCheckbox; // is not radio or checkbox and qtd more than one

  const increment = () => {
    dispatch(
      updateAdditionItem({
        additionType,
        additionIndex,
        additionItemIndex,
      })
    );
  };

  const decrement = () => {
    dispatch(
      updateAdditionItem({
        additionType,
        additionIndex,
        additionItemIndex,
        decrement: true,
      })
    );
  };

  const update = () => {
    dispatch(
      updateAdditionItem({
        additionType,
        additionIndex,
        additionItemIndex,
      })
    );
  };

  const Component = ({ children }) => {
    const style = {
      width: '100%',
      background: 'inherit',
      borderRadius: '16px',

      border: '1px solid #EFEDEC',
      boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
      backgroundColor: (theme) => theme.palette.highlight,
      '&:active': {
        boxShadow: 'none',
        border: '1px solid #EFEDEC',
      },
      '&:focus': {
        boxShadow: '0px 5px 0px #F6F5F4, 0px 6px 0px 0px #EFEDEC',
        border: '1px solid #EFEDEC',
      },
    };
    if (isQuantity) {
      return (
        <Box
          sx={{
            ...style,
            boxShadow: 'none',
          }}
        >
          {children}
        </Box>
      );
    } else {
      const disabled = !isRadio && additionFulFilled && !qtd;
      return (
        <Button
          variant="none"
          _renderTypography={false}
          className={disabled ? 'button-disabled' : 'button'}
          onClick={update}
          sx={{
            ...style,
            boxShadow: disabled ? 'none' : '0px 5px 0px #F6F5F4,0px 6px 0px 0px #EFEDEC',
          }}
          disabled={disabled}
        >
          {children}
        </Button>
      );
    }
  };

  return (
    <Component>
      <Box
        borderRadius={'lg_16'}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          minHeight: '110px',
          background: (theme) => theme.palette.highlight,
          // border: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
          padding: '8px',
          width: '100%',
        }}
      >
        <Box sx={{ padding: '8px' }}>
          <Typography
            sx={{
              fontSize: '17px',
              fontWeight: '400',
              lineHeight: 'unset',
            }}
          >
            {item.name}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            height: isQuantity ? '44px' : '32px',
          }}
        >
          <Box sx={{ paddingLeft: '8px' }}>
            <Typography
              sx={{
                fontSize: '15px',
                fontWeight: '400',
                lineHeight: 'unset',
              }}
            >
              + €{formatToLocaleNumber(item?.unitPrice)}
            </Typography>
          </Box>
          <Box sx={{ display: 'flex' }}>
            {item?.remarks && item?.remarks.length > 0 && (
              <Box
                sx={{
                  marginRight: '8px',
                  display: 'flex',
                  gap: '8px',
                }}
              >
                {item.remarks.map((remark) => (
                  <RemarkWithTooltip
                    key={remark.id}
                    remark={remark}
                    setActiveRemarkId={setActiveRemarkId}
                    activeRemarkId={activeRemarkId}
                  />
                ))}
              </Box>
            )}
            {isRadio && item?.qtd === 1 && (
              <Box sx={{ display: 'flex' }}>
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 16C0 7.16344 7.16344 0 16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16Z"
                    fill="#FF7452"
                  />
                  <path
                    d="M0.5 16C0.5 7.43959 7.43959 0.5 16 0.5C24.5604 0.5 31.5 7.43959 31.5 16C31.5 24.5604 24.5604 31.5 16 31.5C7.43959 31.5 0.5 24.5604 0.5 16Z"
                    stroke="black"
                    strokeOpacity="0.08"
                  />
                  <rect x="10" y="10" width="12" height="12" rx="6" fill="white" />
                </svg>
              </Box>
            )}
            {isRadio && !item?.qtd && (
              <Box
                sx={{
                  width: '32px',
                  height: '32px',
                  border: '1px solid rgba(0, 0, 0, 0.08)',
                  borderRadius: '100%',
                }}
              />
            )}
            {isCheckbox && item?.qtd === 1 && (
              <Box sx={{ display: 'flex' }}>
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 32 32"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 16C0 10.3995 0 7.59921 1.08993 5.46009C2.04867 3.57847 3.57847 2.04867 5.46009 1.08993C7.59921 0 10.3995 0 16 0C21.6005 0 24.4008 0 26.5399 1.08993C28.4215 2.04867 29.9513 3.57847 30.9101 5.46009C32 7.59921 32 10.3995 32 16C32 21.6005 32 24.4008 30.9101 26.5399C29.9513 28.4215 28.4215 29.9513 26.5399 30.9101C24.4008 32 21.6005 32 16 32C10.3995 32 7.59921 32 5.46009 30.9101C3.57847 29.9513 2.04867 28.4215 1.08993 26.5399C0 24.4008 0 21.6005 0 16Z"
                    fill="#FF7452"
                  />
                  <path
                    d="M12.4286 17.4286L15.2857 19.5714L19.5714 12.4286"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="square"
                    strokeLinejoin="round"
                  />
                </svg>
              </Box>
            )}
            {isCheckbox && !item?.qtd && (
              <Box
                sx={{
                  width: '32px',
                  height: '32px',
                  border: '1px solid rgba(0, 0, 0, 0.08)',
                  borderRadius: '8px',
                }}
              />
            )}
            {isQuantity && (
              <Box sx={item?.qtd && { minWidth: '130px' }}>
                <QuantitySelector
                  value={item?.qtd}
                  increment={increment}
                  decrement={decrement}
                  disableIncrement={additionFulFilled || qtd === max}
                />
              </Box>
            )}
          </Box>
        </Box>
      </Box>
    </Component>
  );
};

AdditionItem.propTypes = {
  item: PropTypes.object,
  additionIndex: PropTypes.number,
  additionItemIndex: PropTypes.number,
  additionQtd: PropTypes.number,
  additionMax: PropTypes.number,
  additionType: PropTypes.string,
  additionFulFilled: PropTypes.bool,
};
