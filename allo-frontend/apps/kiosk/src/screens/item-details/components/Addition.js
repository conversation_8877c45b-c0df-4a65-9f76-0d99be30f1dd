import { Box, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';
import { getI18n } from '~/lib/i18n/i18n';
import { AdditionItem } from '~/screens/item-details/components/AdditionItem';
import { LayoutTwoColumns } from '~/screens/item-details/components/LayoutTwoColumns';
import { useAppSelector } from '~/store/hooks';
import { selectLanguage } from '~/store/slices/device/deviceSlice';
import { selectUnfulfilledAdditionsIds } from '~/store/slices/itemDetails/itemDetailsSlice';

const WarningCircleSvg = () => (
  <svg
    width="26"
    height="26"
    viewBox="0 0 26 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.6001 13.0001C2.6001 7.25634 7.25634 2.6001 13.0001 2.6001V2.6001C18.7439 2.6001 23.4001 7.25634 23.4001 13.0001V13.0001C23.4001 18.7439 18.7439 23.4001 13.0001 23.4001V23.4001C7.25634 23.4001 2.6001 18.7439 2.6001 13.0001V13.0001Z"
      stroke="#E14C4F"
      strokeWidth="2"
    />
    <path
      d="M13 7.80005L13 15.6"
      stroke="#E14C4F"
      strokeWidth="2"
      strokeLinejoin="round"
    />
    <path
      d="M13 18.2H13.0149"
      stroke="#E14C4F"
      strokeWidth="2.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const Addition = ({ data, type, additionIndex }) => {
  const language = useAppSelector(selectLanguage);
  const unfulfilledAdditionsIds = useAppSelector(selectUnfulfilledAdditionsIds);

  const isFulfilled = unfulfilledAdditionsIds.includes(data.id);

  return (
    <Box
      id={data.id}
      sx={{
        padding: '56px 48px 48px 48px',
      }}
    >
      <Typography
        id={`${data.id}`}
        sx={{
          fontSize: '25px',
          fontWeight: '500',
          lineHeight: 'unset',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        {data.name}
        {type === 'options' && data?.qtd && data?.qtd > 1 && (
          <span
            style={{
              color: isFulfilled ? 'rgba(225, 76, 79, 1)' : '#726E6A',
              marginLeft: '12px',
            }}
          >
            {data.qtd}
          </span>
        )}
        {type === 'options' && (
          <span
            style={{
              color: isFulfilled ? 'rgba(225, 76, 79, 1)' : '#726E6A',
              marginLeft: '6px',
            }}
          >
            {getI18n(language, 'required')}
          </span>
        )}
        {isFulfilled && (
          <Box component={'span'} sx={{ display: 'flex', marginLeft: '6px' }}>
            <WarningCircleSvg />
          </Box>
        )}
        {data.fulfilled && (
          <Box component={'span'} sx={{ display: 'flex', marginLeft: '6px' }}>
            <svg
              width="26"
              height="26"
              viewBox="0 0 26 26"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M2.6001 13.0001C2.6001 7.25634 7.25634 2.6001 13.0001 2.6001V2.6001C18.7439 2.6001 23.4001 7.25634 23.4001 13.0001V13.0001C23.4001 18.7439 18.7439 23.4001 13.0001 23.4001V23.4001C7.25634 23.4001 2.6001 18.7439 2.6001 13.0001V13.0001Z"
                stroke="#726E6A"
                strokeWidth="2"
              />
              <path
                d="M10.5 14L12.5 15.5L15.5 10.5"
                stroke="#726E6A"
                strokeWidth="2"
                strokeLinecap="square"
                strokeLinejoin="round"
              />
            </svg>
          </Box>
        )}
      </Typography>
      <Box
        sx={{
          marginTop: '20px',
        }}
      >
        <LayoutTwoColumns>
          {data.items.map((item, index) => (
            <AdditionItem
              key={item.id}
              item={item}
              additionId={data.id}
              additionIndex={additionIndex}
              additionItemIndex={index}
              additionMax={data.max}
              additionQtd={data.qtd}
              additionType={type}
              additionFulFilled={data.fulfilled} // this means the parents has enough to proceed with ordering
            />
          ))}
        </LayoutTwoColumns>
      </Box>
    </Box>
  );
};

Addition.propTypes = {
  data: PropTypes.object,
  type: PropTypes.string,
  additionIndex: PropTypes.number,
};
