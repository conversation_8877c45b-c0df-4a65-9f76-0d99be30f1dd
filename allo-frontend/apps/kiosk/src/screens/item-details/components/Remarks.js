import { Box, Typography } from '@allo/ui-lib';
import { useState } from 'react';
import { getI18n } from '~/lib/i18n/i18n';
import { RemarkWithTooltip } from '~/screens/item-details/components/RemarkWithTooltip';
import { useAppSelector } from '~/store/hooks';
import { selectLanguage } from '~/store/slices/device/deviceSlice';

export const Remarks = ({ remarks = [], showTitle = true }) => {
  const [activeRemarkId, setActiveRemarkId] = useState(null);
  const language = useAppSelector(selectLanguage);
  return (
    <Box
      sx={{
        padding: '48px',
      }}
    >
      {showTitle && (
        <Typography variant={'medium_17'}>{getI18n(language, 'allergen')}</Typography>
      )}
      <Box
        sx={{
          display: 'flex',
          gap: '6px',
          flexWrap: 'wrap',
          marginTop: '28px',
        }}
      >
        {remarks.map((remark, index) => (
          <RemarkWithTooltip
            key={remark.id}
            activeRemarkId={activeRemarkId}
            setActiveRemarkId={setActiveRemarkId}
            remark={remark}
          />
        ))}
      </Box>
    </Box>
  );
};
