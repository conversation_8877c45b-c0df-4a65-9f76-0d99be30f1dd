import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import React, { useRef } from 'react';

import {
  arrow,
  autoUpdate,
  FloatingArrow,
  FloatingPortal,
  offset,
  useDismiss,
  useFloating,
  useFocus,
  useInteractions,
  useRole,
} from '@floating-ui/react';
import { remarkIcons } from '~/lib/constants';

export const RemarkWithTooltip = ({ remark, activeRemarkId, setActiveRemarkId }) => {
  const [isRemarkTooltipOpen, setIsRemarkTooltipOpen] = React.useState(false);
  const updateActiveRemark = (remarkId) => {
    if (activeRemarkId === remarkId) {
      setActiveRemarkId(null);
      setIsRemarkTooltipOpen(false);
      return;
    }
    setActiveRemarkId(remarkId);
    setIsRemarkTooltipOpen(true);
  };

  const arrowRef = useRef(null);
  const { refs, floatingStyles, context } = useFloating({
    open: isRemarkTooltipOpen,
    onOpenChange: setIsRemarkTooltipOpen,
    placement: 'top',
    // Make sure the tooltip stays on the screen
    whileElementsMounted: autoUpdate,
    middleware: [
      offset(5),
      arrow({
        element: arrowRef,
      }),
    ],
  });

  const focus = useFocus(context);
  const dismiss = useDismiss(context);

  const role = useRole(context, { role: 'tooltip' });

  const { getReferenceProps, getFloatingProps } = useInteractions([focus, dismiss, role]);

  return (
    <Box key={remark.id}>
      <Button
        ref={refs.setReference}
        {...getReferenceProps()}
        onClick={() => updateActiveRemark(remark.id)}
        variant={'none'}
        sx={{
          padding: '9px 10px',
          display: 'flex',
          alignItems: 'center',
          gap: '4px',
          borderRadius: (theme) => theme?.palette?.borderRadius?.sm_10,
          border: '1px solid rgba(239, 237, 236, 1)',
          backgroundColor: 'rgba(246, 245, 244, 1)',
        }}
      >
        {remark.iconIdentifier && remarkIcons[remark.iconIdentifier]
          ? remarkIcons[remark.iconIdentifier].icon
          : remarkIcons.DEFAULT.icon}
        <Typography variant={TypographyVariant.regular_13}>
          {remark.shortText || remark.annotation}
        </Typography>
      </Button>
      {isRemarkTooltipOpen && activeRemarkId && remark.id === activeRemarkId && (
        <FloatingPortal>
          <Box
            sx={{
              zIndex: 1200,
              maxWidth: '258px',
              padding: '10px',
              backgroundColor: (theme) => theme?.palette?.foreground,
              borderRadius: (theme) => theme?.palette?.borderRadius?.md_12,
            }}
            ref={refs.setFloating}
            style={floatingStyles}
            {...getFloatingProps()}
          >
            <Typography
              variant={TypographyVariant.regular_13_loose}
              sx={{ color: (theme) => theme?.palette?.highlight }}
            >
              {remark.description}
            </Typography>
            <FloatingArrow ref={arrowRef} context={context} />
          </Box>
        </FloatingPortal>
      )}
    </Box>
  );
};
