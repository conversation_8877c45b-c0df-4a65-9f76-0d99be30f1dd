import { Box, Button, Typography, TypographyVariant } from '@allo/ui-lib';
import Image from 'next/image';
import PropTypes from 'prop-types';
import { useEffect } from 'react';
import ImageWithFallback from '~/components/ImageWithFallback';
import { routes } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { formatToLocaleNumber } from '~/lib/utils';
import RecommendationsMenuItem from '~/screens/discover/components/RecommendationsMenuItem';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectCartHasItems, selectCartTotalPrice } from '~/store/slices/cart/cartSlice';
import {
  selectLanguage,
  selectRestaurant,
  selectTopImageUrl,
  setScreen,
  setShowCancellationScreen,
  toggleAccessibility,
} from '~/store/slices/device/deviceSlice';
import {
  getMenuCategories,
  getMenuGroups,
  selectHasRecommendations,
  selectMenuGroups,
  selectRecommendations,
  setSelectedMenuGroup,
} from '~/store/slices/menu/menuSlice';

const ButtonBox = ({ menuGroup, handleSelectMenuGroup }) => {
  const { name, imageUrl, gridColumn } = menuGroup || {};

  return (
    <Button
      variant="none"
      onClick={() => handleSelectMenuGroup(menuGroup)}
      sx={{
        borderRadius: '16px',
        background: imageUrl ? `url(${imageUrl})` : (theme) => theme?.palette?.highlight,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        padding: '8px',
        width: '100%',
        height: 260,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        gridColumn,
        fontFamily: (theme) => theme?.typography?.medium_25?.fontFamily,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',
          alignItems: 'flex-start',
          height: '100%',
        }}
      >
        <Box
          sx={{
            maxWidth: '466px',
            padding: '8px 12px',
            background: (theme) => theme?.palette?.background,
            borderRadius: '8px',
          }}
        >
          <Typography variant={TypographyVariant.medium_15}>{name}</Typography>
        </Box>
      </Box>
    </Button>
  );
};

ButtonBox.propTypes = {
  menuGroup: PropTypes.object,
  handleSelectMenuGroup: PropTypes.func,
};

const Discover = () => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);
  const menuGroups = useAppSelector(selectMenuGroups);

  const cartHasItems = useAppSelector(selectCartHasItems);
  const cartTotal = useAppSelector(selectCartTotalPrice);

  const language = useAppSelector(selectLanguage);
  const topImageUrl = useAppSelector(selectTopImageUrl);

  const hasRecommendations = useAppSelector(selectHasRecommendations);
  const recommendations = useAppSelector(selectRecommendations);

  useEffect(() => {
    dispatch(getMenuGroups());
    // TODO: Reset selected menu category, so next time you go into categories it
    // defaults to the null state (affects the UI of the sidebar)
    // dispatch(setSelectedMenuCategoryId({ id: null }));
    // dispatch(hideImageAddedConfirmationScreen());
  }, [dispatch]);

  useEffect(() => {
    dispatch(getMenuCategories());
  }, [dispatch]);

  const handleSelectMenuGroup = (group) => {
    dispatch(setSelectedMenuGroup(group));
  };

  const handleCheckout = () => {
    dispatch(setScreen(routes.ORDER_SUMMARY));
  };

  const handleToggleAccessibility = () => {
    dispatch(toggleAccessibility());
  };

  const handleResetCart = () => {
    dispatch(setShowCancellationScreen());
  };

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'auto',
        maxWidth: '1080px',
        margin: '0 auto',
        backgroundColor: (theme) => theme.palette.background,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          height: '100%',
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            width: '160px',
            borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: (theme) => theme.palette.highlight,
          }}
        >
          <Box
            sx={{
              height: '160px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
            }}
          >
            <Image
              src={restaurant.logoUrl}
              width={80}
              height={80}
              alt={'restaurant logo'}
            />
          </Box>
          {menuGroups.map((menuGroup) => (
            <Button
              variant={'none'}
              key={`menu_${menuGroup?.id}`}
              onClick={() => handleSelectMenuGroup(menuGroup)}
              sx={{
                height: '160px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '16px',
                whiteSpace: 'break-spaces',
                borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Typography
                variant={TypographyVariant.medium_27}
                sx={{
                  fontSize: 17,
                  fontWeight: 400,
                  margin: '0 auto',
                  width: '100%',
                  textAlign: 'center',
                  lineHeight: '104%',
                  letterSpacing: '0.048px',
                }}
              >
                {menuGroup.name}
              </Typography>
            </Button>
          ))}
          <Box sx={{ flex: 1 }} />
          <Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '160px',
                height: '80px',
                borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Button
                variant={'none'}
                onClick={handleToggleAccessibility}
                disabled
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100%',
                  height: '100%',
                  opacity: '0.4',
                }}
              >
                <Typography sx={{ fontSize: '17px', lineHeight: '104%' }}>
                  {getI18n(language, 'accessibility')}
                </Typography>
              </Button>
            </Box>
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                width: '160px',
                height: '80px',
                borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
              }}
            >
              <Button
                variant={'none'}
                onClick={handleResetCart}
                sx={{
                  display: 'flex',
                  padding: '20px',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '100%',
                  height: '100%',
                  fontFamily: 'inherit',
                }}
              >
                <Typography
                  sx={{
                    fontSize: '17px',
                    lineHeight: '104%',
                    fontWeight: '400px',
                  }}
                >
                  {getI18n(language, cartHasItems ? 'cancel' : 'restart')}
                </Typography>
              </Button>
            </Box>
          </Box>
        </Box>
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          {topImageUrl && (
            <Box sx={{ display: 'flex', flexDirection: 'row', gap: '2px' }}>
              <Box
                sx={{
                  flex: 1,
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  width: '100%',
                  height: '480px',
                }}
              >
                {topImageUrl && (
                  <ImageWithFallback
                    src={topImageUrl}
                    height={480}
                    width={920}
                    alt={'restaurant discover header image'}
                  />
                )}
              </Box>
            </Box>
          )}
          <Box
            sx={{
              overflow: 'auto',
              padding: '56px 48px 160px 48px',
            }}
          >
            <Typography
              variant={TypographyVariant.medium_25}
              sx={{
                fontWeight: 500,
                margin: '0 auto',
                width: '100%',
                lineHeight: '104%',
                letterSpacing: '-0.35px',
                marginBottom: '16px',
              }}
            >
              {getI18n(language, 'explore-our-menu')}
            </Typography>
            <Box
              sx={{
                display: 'grid',
                gridTemplateColumns: 'repeat(3, 1fr)',
                gridGap: '16px',
              }}
            >
              {menuGroups &&
                menuGroups.map((menuGroup) => (
                  <ButtonBox
                    key={menuGroup.id}
                    menuGroup={menuGroup}
                    handleSelectMenuGroup={handleSelectMenuGroup}
                  />
                ))}
            </Box>
            {hasRecommendations && (
              <Box sx={{ marginTop: '72px' }}>
                <Typography
                  variant={TypographyVariant.medium_25}
                  sx={{
                    fontWeight: 500,
                    margin: '0 auto',
                    width: '100%',
                    lineHeight: '104%',
                    letterSpacing: '-0.35px',
                    marginBottom: '16px',
                  }}
                >
                  {getI18n(language, 'people-are-loving-these')}
                </Typography>
                <Box
                  sx={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(2, 1fr)',
                    gridGap: '16px',
                    paddingBottom: '160px',
                  }}
                >
                  {recommendations.map((i) => (
                    <RecommendationsMenuItem item={i} key={`item_${i.code}`} />
                  ))}
                </Box>
              </Box>
            )}
          </Box>
          {cartHasItems && (
            <Box
              sx={{
                position: 'absolute',
                bottom: 0,
                left: 160,
                right: 0,
              }}
            >
              <Box
                sx={{
                  height: '160px',
                  width: '100%',
                  borderTop: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
                  backgroundColor: (theme) => theme?.palette?.highlight,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  paddingRight: '24px',
                }}
              >
                <Button
                  className={'button-primary'}
                  sx={{
                    height: '106px',
                    paddingLeft: '56px',
                    paddingRight: '56px',
                    borderRadius: '16px',
                    border: 'none',
                    boxShadow: '0px 6px 0px #E76444',
                    backgroundColor: '#FF7452',
                    fontFamily: 'inherit',
                    cursor: 'pointer',
                    '&:active': {
                      boxShadow: 'none',
                      border: 'none',
                    },
                    '&:focus': {
                      boxShadow: '0px 6px 0px #E76444',
                      border: 'none',
                    },
                  }}
                  onClick={handleCheckout}
                  variant="none"
                  _renderTypography={false}
                >
                  <Box
                    component="span"
                    sx={{
                      display: 'flex',
                      gap: '8px',
                    }}
                  >
                    <Typography
                      sx={{
                        fontSize: '25px',
                        fontWeight: '400',
                        color: (theme) => theme?.palette?.accent_foreground,
                      }}
                    >
                      {getI18n(language, 'checkout')}
                      <span style={{ marginLeft: '6px' }}>{`·`}</span>
                      <span style={{ marginLeft: '6px' }}>
                        €{formatToLocaleNumber(cartTotal)}
                      </span>
                    </Typography>
                  </Box>
                </Button>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default Discover;
