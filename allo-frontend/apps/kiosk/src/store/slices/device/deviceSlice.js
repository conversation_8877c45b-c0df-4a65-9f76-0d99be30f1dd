import { isEmpty } from '@monorepo/utils';
import {
  cartStatus,
  CURRENT_ORDER_CART_KEY,
  defaultLanguageValues,
  defaultMerchantIconUrl,
  defaultSystemLanguages,
  pairedStatus,
  paymentMethodTypes,
  routes,
  systemLanguageOptions,
  systemLanguages,
  transformMediaUrl,
} from '~/lib/constants';
import { api } from '~/lib/utils/api';
import { createAppSlice } from '~/store/createAppSlice';
import { generateCart, setPager } from '~/store/slices/cart/cartSlice';
import { requestPayment } from '~/store/slices/checkout/checkoutSlice';
import {
  addItem,
  removeItemQtd,
  updateItem,
  updateItemQtd,
} from '~/store/slices/itemDetails/itemDetailsSlice';
import { setSelectedMenuGroup } from '~/store/slices/menu/menuSlice';
import { diningOptionValues } from '../../../lib/constants/index.js';
import {
  cancelPayment,
  initializeDevice,
  registerDevice,
  resetDevice,
} from './deviceThunks';

const initialState = {
  language: systemLanguages.de.value,
  accessibility: false,
  showCancellationScreen: false,
  route: routes.INITIALIZE,
  pairedStatus: pairedStatus.NOT_INITIALIZED,
  pairingToken: null,
  id: null,
  name: null,
  completedOrderId: null,
  globalLoading: false,
  restaurant: {
    id: null,
    name: '',
    logoUrl: defaultMerchantIconUrl,
    backgroundUrl: '/idle-image.jpg',
    paymentMethods: ['CASH'],
    languages: defaultLanguageValues,
  },
  kioskConfiguration: {
    languages: [],
    paymentMethods: [],
    enableBypassSinglePaymentMethod: false,
    diningOptions: [diningOptionValues.IN_HOUSE, diningOptionValues.TO_GO],
    heroImageUrl: null,
    topImageUrl: null,
    allowPromotions: false,
    enablePager: false,
  },
  isPaymentCanceling: false,
  notification: {
    messageKeyI18n: null,
    descriptionKeyI18n: null,
  },
};

export const deviceInitialState = initialState;

export const deviceSlice = createAppSlice({
  name: 'device',
  initialState,
  reducers: (create) => ({
    setScreen: create.reducer((state, data) => {
      state.route = data?.payload;
    }),
    setAlloPayCartData: create.reducer((_state, _data) => {}),
    setLanguage: create.reducer((state, data) => {
      state.language = data?.payload || systemLanguages.de.value;
    }),
    toggleAccessibility: create.reducer((state, _data) => {
      state.accessibility = !state.accessibility;
    }),
    setGlobalLoading: create.reducer((state, data) => {
      state.globalLoading = data?.payload;
    }),
    setShowCancellationScreen: create.reducer((state, _data) => {
      state.showCancellationScreen = true;
    }),
    closeCancellationScreen: create.reducer((state, _data) => {
      state.showCancellationScreen = false;
    }),
    setNotification: create.reducer((state, data) => {
      state.notification.messageKeyI18n = data?.payload;
    }),
  }),
  extraReducers: (builder) => {
    builder
      .addCase(initializeDevice.pending, (state, _action) => {
        state.status = 'pending';
        state.globalLoading = true;
      })
      .addCase(initializeDevice.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const { data } = action?.payload || {};
        const isPaired = data?.paired;
        if (isPaired) {
          state.pairedStatus = pairedStatus.IS_PAIRED;
          state.route = routes.CREATE;

          state.id = data?.id;
          state.name = data?.name;

          const { id, name, logoUrl } = data?.restaurant || {};
          state.restaurant.id = id;
          state.restaurant.name = name;
          if (logoUrl) {
            state.restaurant.logoUrl = transformMediaUrl(logoUrl);
          }

          const background = data?.kioskConfiguration?.heroImageUrl;
          const paymentMethods = data?.kioskConfiguration?.paymentMethods;
          const languages = data?.kioskConfiguration?.languages;

          state.restaurant.backgroundUrl = background;
          state.restaurant.paymentMethods = paymentMethods;
          state.restaurant.languages = isEmpty(languages)
            ? defaultLanguageValues
            : languages;

          const cart = data?.cart;
          const status = cart?.status;
          const kioskConfiguration = data?.kioskConfiguration;
          if (cart) {
            if (status === cartStatus.PAYING) {
              state.route = routes.PAYMENT_INSTRUCTIONS;
            } else {
              state.route = routes.DISCOVER;
            }
          }

          if (kioskConfiguration) {
            state.kioskConfiguration.languages = kioskConfiguration?.languages || [];
            state.kioskConfiguration.paymentMethods =
              kioskConfiguration?.paymentMethods || [];
            state.kioskConfiguration.enableBypassSinglePaymentMethod =
              kioskConfiguration?.enableBypassSinglePaymentMethod || false;
            state.kioskConfiguration.diningOptions =
              kioskConfiguration?.diningOptions || [
                diningOptionValues.IN_HOUSE,
                diningOptionValues.TO_GO,
              ];
            state.kioskConfiguration.heroImageUrl = kioskConfiguration?.heroImageUrl;
            state.kioskConfiguration.topImageUrl = kioskConfiguration?.topImageUrl;
            state.kioskConfiguration.allowPromotions =
              kioskConfiguration?.allowPromotions;
            state.kioskConfiguration.enablePager = kioskConfiguration?.enablePager;
          }
        } else {
          state.pairedStatus = pairedStatus.NOT_PAIRED;
          state.route = routes.REGISTER;
        }
        state.globalLoading = false;
      })
      .addCase(initializeDevice.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? 'Unknown Error';
      })
      .addCase(registerDevice.pending, (state, _action) => {
        state.status = 'pending';
      })
      .addCase(registerDevice.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const { data } = action?.payload || {};
        const isPaired = data?.paired;
        if (isPaired) {
          state.pairedStatus = pairedStatus.IS_PAIRED;
          state.route = routes.INITIALIZE;
        } else {
          state.pairedStatus = pairedStatus.NOT_PAIRED;
          state.route = routes.REGISTER;
        }
      })
      .addCase(registerDevice.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? 'Unknown Error';
      })
      .addCase(generateCart.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const { data } = action?.payload || {};
        if (!isEmpty(data)) {
          state.route = routes.DISCOVER;
        }
        state.globalLoading = false;
      })
      .addCase(setSelectedMenuGroup, (state, _action) => {
        state.route = routes.CATEGORIES;
      })
      .addCase(requestPayment.fulfilled, (state, action) => {
        const { method = paymentMethodTypes.CASH } = action?.meta?.arg || {};
        state.globalLoading = false;
        switch (method) {
          case paymentMethodTypes.CASH:
            state.route = routes.COMPLETED;
            break;
          case paymentMethodTypes.ALLO_PAY:
            state.route = routes.PAYMENT_INSTRUCTIONS;
            break;
          default:
            state.route = routes.COMPLETED;
        }
      })
      .addCase(resetDevice.fulfilled, (state, _action) => {
        localStorage.removeItem(CURRENT_ORDER_CART_KEY);
        state.route = routes.INITIALIZE;
        if (!isEmpty(state?.restaurant?.languages)) {
          state.language = state.restaurant?.languages[0];
        } else {
          state.language = systemLanguages.de.value;
        }
        api.defaults.headers.common['content-language'] = state.language;
        state.showCancellationScreen = false;
        return state;
      })
      .addCase(cancelPayment.pending, (state, _action) => {
        state.isPaymentCanceling = true;
        state.globalLoading = true;
      })
      .addCase(cancelPayment.fulfilled, (state, _action) => {
        state.isPaymentCanceling = false;
        state.globalLoading = false;
        state.route = routes.CHECKOUT;
      })
      .addCase(cancelPayment.rejected, (state, _action) => {
        state.isPaymentCanceling = false;
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(addItem.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(addItem.fulfilled, (state, _action) => {
        state.globalLoading = false;
      })
      .addCase(addItem.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(generateCart.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(generateCart.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(updateItemQtd.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(updateItemQtd.fulfilled, (state, _action) => {
        state.globalLoading = false;
      })
      .addCase(updateItemQtd.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(updateItem.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(updateItem.fulfilled, (state, _action) => {
        state.globalLoading = false;
      })
      .addCase(updateItem.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(removeItemQtd.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(removeItemQtd.fulfilled, (state, action) => {
        state.globalLoading = false;
        if (
          state.route === routes.ORDER_SUMMARY &&
          isEmpty(action?.payload?.data?.items)
        ) {
          state.route = routes.DISCOVER;
        }
      })
      .addCase(removeItemQtd.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(requestPayment.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(requestPayment.rejected, (state, _action) => {
        state.globalLoading = false;
        if (_action?.error?.code === 'ERR_NETWORK') {
          state.notification.messageKeyI18n = 'please-try-again';
          state.notification.descriptionKeyI18n = 'there-was-a-connectivity-issue';
        }
      })
      .addCase(setPager.pending, (state, _action) => {
        state.globalLoading = true;
      })
      .addCase(setPager.fulfilled, (state, _action) => {
        state.globalLoading = false;
        state.route = routes.CHECKOUT;
      })
      .addCase(setPager.rejected, (state, _action) => {
        state.globalLoading = false;
        state.notification.messageKeyI18n = 'please-try-again';
        state.notification.descriptionKeyI18n = 'an-error-occurred';
      });
  },
  selectors: {
    selectLanguage: (device) => device.language,
    selectLanguageOptions: (device) => {
      const languageOptions = [];
      device?.restaurant?.languages.forEach((language) => {
        const foundSystemLanguage = systemLanguageOptions.find(
          (t) => t.value === language
        );
        if (foundSystemLanguage) {
          languageOptions.push(foundSystemLanguage);
        }
      }, []);
      if (isEmpty(languageOptions)) {
        return defaultSystemLanguages;
      }

      return languageOptions;
    },
    selectAccessibility: (device) => device.accessibility,
    selectShowCancellationScreen: (device) => device.showCancellationScreen,
    selectDeviceId: (device) => device.id,
    selectDeviceName: (device) => device.name,
    selectScreen: (device) => device.route,
    selectPairingToken: (device) => device.pairingToken,
    selectRestaurant: (device) => device.restaurant,
    selectGlobalLoading: (device) => device.globalLoading,
    selectTopImageUrl: (device) => device.kioskConfiguration.topImageUrl,
    selectLanguages: (device) => device.kioskConfiguration.languages,
    selectPaymentMethods: (device) => device.kioskConfiguration.paymentMethods,
    selectDiningOptions: (device) => device.kioskConfiguration.diningOptions,
    selectAllowPromotions: (device) => device.kioskConfiguration.allowPromotions,
    selectEnablePager: (device) => device.kioskConfiguration.enablePager,
    selectIsPaymentCanceling: (device) => device.isPaymentCanceling,
    selectEnableBypassSinglePaymentMethod: (device) =>
      device.enableBypassSinglePaymentMethod,
    selectNotification: (device) => device.notification,
  },
});

export const {
  setScreen,
  setLanguage,
  toggleAccessibility,
  setShowCancellationScreen,
  closeCancellationScreen,
  setNotification,
} = deviceSlice.actions;

// Selectors returned by `slice.selectors` take the root state as their first argument.
export const {
  selectScreen,
  selectRestaurant,
  selectDeviceId,
  selectDeviceName,
  selectLanguage,
  selectLanguageOptions,
  selectAccessibility,
  selectShowCancellationScreen,
  selectGlobalLoading,
  selectTopImageUrl,
  selectLanguages,
  selectAllowPromotions,
  selectEnablePager,
  selectPaymentMethods,
  selectDiningOptions,
  selectIsPaymentCanceling,
  selectEnableBypassSinglePaymentMethod,
  selectNotification,
} = deviceSlice.selectors;

export * from './deviceThunks';
