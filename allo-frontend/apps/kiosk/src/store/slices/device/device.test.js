import {
  mockNetworkResponseMeWithCart,
  mockNetworkResponseMeWithoutCart,
} from '~/__mocks__/apiMocks/api_me_calls';
import { routes } from '~/lib/constants';
import { store } from '../../store';
import { deviceInitialState, initializeDevice } from './deviceSlice';

describe('device slice - store test', () => {
  beforeAll(() => {
    // IF WE WANT ALL OF OUT CALLS TO BE MOCKED
    // mockNetworkResponse();
  });

  it('Should have an empty state', () => {
    const state = store.getState();
    expect(state.device).toEqual(deviceInitialState);
  });

  it('Should initialize device, REJECTED', async () => {
    // Will be rejected since axios call is not mocked.
    const result = await store.dispatch(initializeDevice());
    expect(result.type).toBe('device/_me/rejected');
    const { error } = result;
    const state = store.getState().device;
    expect(state.status).toBe('failed');
    expect(state.error).toBe(error.message);
  });

  it('Should initialize the device with no cart, FULFILLED', async () => {
    mockNetworkResponseMeWithoutCart();
    const result = await store.dispatch(initializeDevice());
    expect(result.type).toBe('device/_me/fulfilled');
    const state = store.getState().device;
    const { restaurant, kioskConfiguration } = result.payload.data;
    expect(state.kioskConfiguration).not.toBe(null);
    expect(state.kioskConfiguration.languages).toEqual(kioskConfiguration.languages);
    expect(state.restaurant.id).toEqual(restaurant.id);
    expect(state.restaurant.name).toEqual(restaurant.name);
    expect(state.restaurant.backgroundUrl).toEqual(kioskConfiguration.heroImageUrl);
    expect(state.restaurant.paymentMethods).toEqual(kioskConfiguration.paymentMethods);
  });

  it('Should initialize the device, with no cart and go to the CREATE route', async () => {
    mockNetworkResponseMeWithoutCart();
    const result = await store.dispatch(initializeDevice());
    expect(result.type).toBe('device/_me/fulfilled');
    const state = store.getState().device;
    expect(state.route).toBe(routes.CREATE);
  });

  it('Should initialize the device, with cart and go to the DISCOVER route', async () => {
    mockNetworkResponseMeWithCart();
    const result = await store.dispatch(initializeDevice());
    expect(result.type).toBe('device/_me/fulfilled');
    const state = store.getState().device;
    expect(state.route).toBe(routes.DISCOVER);
  });
});
