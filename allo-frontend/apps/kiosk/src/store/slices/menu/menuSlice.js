import { isEmpty } from '@monorepo/utils';
import { transformMediaUrl } from '~/lib/constants';
import { createAppSlice } from '~/store/createAppSlice';
import { resetDevice } from '~/store/slices/device/deviceSlice';
import { getMenuCategories, getMenuGroups } from './menuThunks';

const initialState = {
  menuGroups: [],
  versionedMenu: {
    status: 'pending',
    menuVersion: '',
    items: [],
  },
  selectedMenuGroup: null,
  selectedMenuItems: {
    menuIds: [],
  },
  menuItemsByCode: {},
  status: '',
  selectedMenuCategoryId: null,
  suggestions: [],
  recommendations: [],
};

export const menuInitialState = initialState;

const getSelectedMenuItemsFromVersionedMenuSelector = (state) => {
  const items = state.versionedMenu.items;
  const menuIds = state.selectedMenuItems.menuIds;
  const selectedItems = [];
  menuIds.forEach((menuId) => {
    const item = items.find((item) => item.id === menuId);
    if (item && item.items && item.items.length > 0) {
      selectedItems.push(item);
    }
  });
  return selectedItems;
};

const transformMenuGroupGridAndMedia = (menuGroupsArray) => {
  if (isEmpty(menuGroupsArray)) {
    return [];
  }

  let startColumn = 1;
  let maxColumn = 4;

  return menuGroupsArray.map((item) => {
    const { widthSize = 1 } = item || {};
    let endColumn = startColumn + widthSize;
    if (endColumn > maxColumn) {
      startColumn = 1;
      endColumn = startColumn + widthSize;
    }
    const gridColumn = `${startColumn} / ${endColumn}`;
    startColumn = endColumn;
    return { ...item, gridColumn, imageUrl: transformMediaUrl(item?.imageUrl) };
  });
};

export const menuSlice = createAppSlice({
  name: 'menu',
  initialState,
  reducers: (create) => ({
    setSelectedMenuGroup: create.reducer((state, data) => {
      state.selectedMenuGroup = data?.payload;
      state.selectedMenuItems.menuIds = data?.payload?.menuIds;
    }),
    setSelectedMenuCategory: create.reducer((state, data) => {
      state.selectedMenuCategoryId = data?.payload?.id;
      state.selectedMenuCategory = data?.payload;
    }),
    setSelectedMenuCategoryId: create.reducer((state, data) => {
      state.selectedMenuCategoryId = data?.payload.id;
    }),
  }),
  extraReducers: (builder) => {
    builder
      .addCase(getMenuGroups.pending, (state, _action) => {
        state.status = 'pending';
      })
      .addCase(getMenuGroups.fulfilled, (state, action) => {
        state.status = 'succeeded';
        const { data } = action?.payload || {};
        if (!isEmpty(data?.items)) {
          state.menuGroups = transformMenuGroupGridAndMedia(data?.items);
        }
      })
      .addCase(getMenuGroups.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.error.message ?? 'Unknown Error';
      })
      .addCase(getMenuCategories.pending, (state, _action) => {
        state.versionedMenu.status = 'pending';
      })
      .addCase(getMenuCategories.fulfilled, (state, action) => {
        state.versionedMenu.status = 'succeeded';
        const { data } = action?.payload || {};
        state.versionedMenu.items = data?.items || [];
        state.versionedMenu.menuVersion = data?.menuVersion;

        // Suggestions are displayType === "CARD" && !recommended
        if (data?.items) {
          const suggestionsMenu = (data?.items || []).find(
            (m) => m.displayType === 'CARD' && !m.recommended
          );
          if (suggestionsMenu && suggestionsMenu.items) {
            state.suggestions =
              suggestionsMenu.items.length > 6
                ? suggestionsMenu.items.slice(0, 6)
                : suggestionsMenu.items;
          }
          const recommendationsMenu = (data?.items || []).find((m) => m.recommended);
          if (recommendationsMenu && recommendationsMenu.items) {
            state.recommendations =
              recommendationsMenu.items.length > 10
                ? recommendationsMenu.items.slice(0, 10)
                : recommendationsMenu.items;
          }
        }
      })
      .addCase(resetDevice.fulfilled, (_state, _action) => {
        return initialState;
      });
  },
  selectors: {
    selectMenuGroups: (menu) => menu.menuGroups,
    selectSelectedMenuGroup: (menu) => menu.selectedMenuGroup,
    selectVersionedMenu: (menu) => menu.versionedMenu,
    selectSelectedVersionedItems: getSelectedMenuItemsFromVersionedMenuSelector,
    selectSelectedMenuCategoryId: (menu) => menu.selectedMenuCategoryId,
    selectSuggestions: (menu) => menu.suggestions,
    selectRecommendations: (menu) => menu.recommendations,
    selectHasSuggestions: (menu) => !isEmpty(menu.suggestions),
    selectHasRecommendations: (menu) => !isEmpty(menu.recommendations),
  },
});

export const {
  setSelectedMenuGroup,
  setSelectedMenuCategory,
  setSelectedMenuCategoryId,
} = menuSlice.actions;

export const {
  selectMenuGroups,
  selectSelectedMenuGroup,
  selectVersionedMenu,
  selectSelectedVersionedItems,
  selectSelectedMenuCategoryId,
  selectSuggestions,
  selectHasSuggestions,
  selectRecommendations,
  selectHasRecommendations,
} = menuSlice.selectors;

export * from './menuThunks';
