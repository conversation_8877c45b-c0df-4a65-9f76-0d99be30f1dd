import { isEmpty } from '@monorepo/utils';
import { CURRENT_ORDER_CART_KEY } from '~/lib/constants';
import { createAppSlice } from '~/store/createAppSlice';
import { requestPayment } from '~/store/slices/checkout/checkoutSlice';
import { initializeDevice, resetDevice } from '~/store/slices/device/deviceSlice';
import {
  addItem,
  removeItemQtd,
  updateItem,
  updateItemQtd,
} from '~/store/slices/itemDetails/itemDetailsSlice';
import { getMenuGroups } from '~/store/slices/menu/menuSlice';
import { addPromoCode, generateCart, refreshCart } from './cartThunks';

const initialState = {
  customer: null,
  id: null,
  restaurantId: null,
  status: '',
  total: 0,
  cart: null,
  loading: true,
  orderNumber: 0,
  toGo: false,
  cartHasItems: false,
  promoCode: '',
  showDiscountAddedConfirmationScreen: false,
  pagerNumber: '',
};

export const cartInitialState = initialState;

export const cartSlice = createAppSlice({
  name: 'cart',
  initialState,
  reducers: (create) => ({
    setCart: create.reducer((state, order) => {
      console.log(order);
    }),
    hideDiscountConfirmationScreen: create.reducer((state, _data) => {
      state.showDiscountAddedConfirmationScreen = false;
    }),
    setPromoCodeInput: create.reducer((state, data) => {
      if (data.payload === 'REMOVE') {
        state.promoCode = state.promoCode.slice(0, -1);
      } else {
        state.promoCode += data.payload;
      }
    }),
    setPagerNumber: create.reducer((state, data) => {
      if (data.payload === 'REMOVE') {
        state.pagerNumber = state.pagerNumber.slice(0, -1);
      } else {
        state.pagerNumber += data.payload;
      }
    }),
    setOrderNumber: create.reducer((state, data) => {
      state.orderNumber = data?.payload;
    }),
  }),
  extraReducers: (builder) => {
    builder
      .addCase(generateCart.fulfilled, (state, action) => {
        localStorage.setItem(CURRENT_ORDER_CART_KEY, action?.payload?.data?.id);
        const data = action?.payload;
        if (!data) return;
        state.customer = data.customer;
        state.id = data.id;
        state.restaurantId = data.restaurantId;
        state.status = data.status;
        state.total = data.total;
        state.totalDiscounts = data.cart?.totalDiscounts ?? 0; //TODO: Needed here ?
        state.itemsTotal = data.cart?.itemsTotal ?? 0; //TODO: Needed here ?
        state.toGo = data.toGo;
        getMenuGroups();
      })
      .addCase(initializeDevice.fulfilled, (state, action) => {
        const { data } = action?.payload || {};
        const isPaired = data?.paired;
        if (isPaired) {
          const { cart } = data;
          if (!cart) return;
          state.cart = cart;
          state.customer = cart.customer;
          state.id = cart.id;
          state.restaurantId = cart.restaurantId;
          state.status = cart.status;
          state.total = cart.total;
          state.totalDiscounts = cart.totalDiscounts ?? 0;
          state.itemsTotal = cart.itemsTotal ?? 0;
          state.cartHasItems = !isEmpty(cart?.items);
        }
      })
      .addCase(refreshCart.pending, (state, _action) => {
        state.loading = true;
      })
      .addCase(refreshCart.fulfilled, (state, action) => {
        const { data } = action?.payload || {};
        if (!data) return;
        state.loading = false;
        const { cart } = data;
        if (!cart) return;
        state.cart = cart;
        state.total = cart?.total;
        state.totalDiscounts = cart?.totalDiscounts ?? 0;
        state.itemsTotal = cart?.itemsTotal ?? 0;
        state.cartHasItems = !isEmpty(cart?.items);
      })
      .addCase(addItem.fulfilled, (state, action) => {
        const { data } = action?.payload || {};

        if (!data) return;

        const cart = data;
        if (!cart) return;
        state.cart = cart;
        state.customer = cart.customer;
        state.id = cart.id;
        state.restaurantId = cart.restaurantId;
        state.status = cart.status;
        state.total = cart.total;

        state.total = data.total;
        state.cartHasItems = !isEmpty(cart?.items);
      })
      .addCase(updateItem.fulfilled, (state, action) => {
        const { data } = action?.payload || {};

        if (!data) return;

        const cart = data;
        if (!cart) return;
        state.cart = cart;
        state.customer = cart.customer;
        state.id = cart.id;
        state.restaurantId = cart.restaurantId;
        state.status = cart.status;
        state.total = cart.total;

        state.total = data.total;
        state.cartHasItems = !isEmpty(cart?.items);
      })
      .addCase(updateItemQtd.fulfilled, (state, action) => {
        const { data } = action?.payload || {};

        if (!data) return;

        const cart = data;
        if (!cart) return;
        state.cart = cart;
        state.customer = cart.customer;
        state.id = cart.id;
        state.restaurantId = cart.restaurantId;
        state.status = cart.status;
        state.total = cart.total;

        state.total = data.total;
        state.cartHasItems = !isEmpty(cart?.items);
      })
      .addCase(removeItemQtd.fulfilled, (state, action) => {
        const { data } = action?.payload || {};

        if (!data) return;

        const cart = data;
        if (!cart) return;
        state.cart = cart;
        state.customer = cart.customer;
        state.id = cart.id;
        state.restaurantId = cart.restaurantId;
        state.status = cart.status;
        state.total = cart.total;

        state.total = data.total;
        state.cartHasItems = !isEmpty(cart?.items);
      })
      .addCase(requestPayment.fulfilled, (state, action) => {
        const { data } = action?.payload || {};
        if (!data) return;
        state.orderNumber = data.orderNumber;
      })
      .addCase(addPromoCode.pending, (state, _action) => {
        state.loading = true;
      })
      .addCase(addPromoCode.fulfilled, (state, action) => {
        state.showDiscountAddedConfirmationScreen = true;
        const { data } = action?.payload || {};
        if (!data) return;
        state.total = data.total;
      })
      .addCase(addPromoCode.rejected, (state, _action) => {
        state.promoCode = '';
        state.showDiscountAddedConfirmationScreen = false;
      })
      .addCase(resetDevice.fulfilled, (_state, _action) => {
        return initialState;
      });
  },
  selectors: {
    selectCartItems: (state) => state.cart?.items || [],
    selectLoading: (state) => state.loading,
    selectCartTotalPrice: (state) => state.total,
    selectCartTotalDiscount: (state) => state.totalDiscounts,
    selectCartTotalWithoutDiscount: (state) => state.itemsTotal,
    selectOrderNumber: (state) => state.orderNumber,
    selectToGo: (state) => state.toGo,
    selectCartId: (state) => state.id,
    selectCartHasItems: (state) => state.cartHasItems,
    selectShowDiscountAddedConfirmationScreen: (state) =>
      state.showDiscountAddedConfirmationScreen,
    selectPromoCode: (state) => state.promoCode,
    selectPagerNumber: (state) => state.pagerNumber,
  },
});

// Action creators are generated for each case reducer function.
export const {
  setCart,
  hideDiscountConfirmationScreen,
  setPromoCodeInput,
  setPagerNumber,
  setOrderNumber,
} = cartSlice.actions;

// Selectors returned by `slice.selectors` take the root state as their first argument.
export const {
  selectCartItems,
  selectLoading,
  selectCartTotalPrice,
  selectCartTotalDiscount,
  selectCartTotalWithoutDiscount,
  selectOrderNumber,
  selectToGo,
  selectCartId,
  selectCartHasItems,
  selectShowDiscountAddedConfirmationScreen,
  selectPromoCode,
  selectPagerNumber,
} = cartSlice.selectors;

export * from './cartThunks';
