import { mockNetworkResponseGenerateCart } from '~/__mocks__/apiMocks/api_generate_cart_calls';
import { mockNetworkResponseMeWithCart } from '~/__mocks__/apiMocks/api_me_calls';
import { initializeDevice } from '~/store/slices/device/deviceSlice';
import { store } from '../../store';
import { cartInitialState, generateCart } from './cartSlice';

describe('cart slice - store test', () => {
  it('Should have an empty state', () => {
    const state = store.getState();
    expect(state.cart).toEqual(cartInitialState);
  });

  it('Should initialize the cart slice with cart data from the ME call', async () => {
    mockNetworkResponseMeWithCart();

    const result = await store.dispatch(initializeDevice());
    expect(result.type).toBe('device/_me/fulfilled');
    const state = store.getState().cart;
    expect(state.cart).toEqual(result.payload.data.cart);
  });

  it('Should generate a new cart and store the data', async () => {
    mockNetworkResponseGenerateCart();
    const result = await store.dispatch(generateCart());
    // console.log(result);
  });
});
