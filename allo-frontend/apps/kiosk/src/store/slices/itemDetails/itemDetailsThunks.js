import { createAsyncThunk } from '@reduxjs/toolkit';
import { ORDER_API } from '~/lib/constants/services';
import { api } from '~/lib/utils/api';

export const addItem = createAsyncThunk('cart/_add-item', (arg, { getState }) => {
  const { itemDetails } = getState();
  const {
    item: { code },
    selectedExtras,
    selectedOptions,
    qtd,
  } = itemDetails;
  return api.post(
    `/${ORDER_API}/current/items`,
    {
      code,
      qtd,
      selectedOptions,
      selectedExtras,
    },
    {
      params: {
        headers: ['cart'],
      },
    }
  );
});

export const updateItem = createAsyncThunk('cart/_update-item', (arg, { getState }) => {
  const { itemDetails } = getState();
  const {
    item: { id: _id, code, cartItemId },
    selectedExtras,
    selectedOptions,
    qtd,
  } = itemDetails;

  if (!cartItemId) return;
  if (!qtd) return;

  return api.put(
    `/${ORDER_API}/current/items`,
    {
      orderItemId: cartItemId ?? null,
      code,
      qtd,
      selectedOptions,
      selectedExtras,
    },
    {
      params: {
        headers: ['cart'],
      },
    }
  );
});

export const updateItemQtd = createAsyncThunk(
  'cart/_update-item-qtd',
  (arg, { getState: _getState }) => {
    return api.post(
      `/${ORDER_API}/current/items`,
      {
        orderItemId: arg?.item?.id,
        code: arg?.item?.code,
        qtd: arg?.item?.qtd,
        selectedOptions: arg?.item?.selectedOptions,
        selectedExtras: arg?.item?.selectedExtras,
      },
      {
        params: {
          headers: ['cart'],
        },
      }
    );
  }
);

export const removeItemQtd = createAsyncThunk(
  'cart/_delete-item-qtd',
  (arg, { getState: _getState }) => {
    return api.delete(`/${ORDER_API}/current/items`, {
      params: {
        headers: ['cart'],
      },
      data: {
        orderItemId: arg?.item?.id,
        code: arg?.item?.code,
        qtd: arg?.item?.qtd,
        selectedOptions: arg?.item?.selectedOptions,
        selectedExtras: arg?.item?.selectedExtras,
      },
    });
  }
);
