'use client';
import PropTypes from 'prop-types';
import { useRef } from 'react';
import { Provider } from 'react-redux';
import { makeStore } from '~/store/store';

export default function StoreProvider({ children }) {
  const storeRef = useRef();
  if (!storeRef.current) {
    storeRef.current = makeStore();
  }

  return <Provider store={storeRef.current}>{children}</Provider>;
}

StoreProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
