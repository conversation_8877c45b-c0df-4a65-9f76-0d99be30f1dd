export const DEFAULT_TEST_TIMEOUT = 1000;

export const sleep = async (page, ms) => {
  await page.waitForTimeout(ms || DEFAULT_TEST_TIMEOUT);
};

export const sleepSetTimeout = (ms = DEFAULT_TEST_TIMEOUT) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const handlePairDevice = async (page) => {
  await page.evaluate(() => {
    const PAIR_DEVICE_DATA_RESPONSE_MOCK = {
      id: '01JVM7TS2VTANNC4WRMEB82362',
      restaurantId: '62b1b639230d5d186d059699',
      paired: true,
      token: '01JVM7W6FY82XPY1Q5BNH6KADX',
      remainingPairingAttempts: 5,
    };

    localStorage.setItem(
      '_allO_pairing_data',
      JSON.stringify(PAIR_DEVICE_DATA_RESPONSE_MOCK)
    );
    localStorage.setItem('_allO_token', PAIR_DEVICE_DATA_RESPONSE_MOCK.token);
  });

  await page.reload();

  await page.waitForTimeout(DEFAULT_TEST_TIMEOUT);
};

export const selectEnglishLanguage = async (page) => {
  await page.click('button:has-text("English")');
};
