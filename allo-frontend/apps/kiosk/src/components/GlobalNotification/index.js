import { Box, Typography, TypographyVariant } from '@allo/ui-lib';
import { useEffect } from 'react';
import { TOAST_NOTIFICATION_Z_INDEX } from '~/lib/constants';
import { getI18n } from '~/lib/i18n/i18n';
import { useAppSelector } from '~/store/hooks';
import {
  selectLanguage,
  selectNotification,
  setNotification,
} from '~/store/slices/device/deviceSlice';
import { useAppDispatch } from '../../store/hooks.js';

export const GlobalNotification = () => {
  const dispatch = useAppDispatch();
  const notification = useAppSelector(selectNotification);
  const language = useAppSelector(selectLanguage);

  const themeAccentTertiary = '#151413';

  useEffect(() => {
    if (notification?.messageKeyI18n) {
      const timeoutId = setTimeout(() => {
        dispatch(setNotification(null));
      }, 1500); // 3 seconds

      return () => {
        clearTimeout(timeoutId);
      };
    }
  }, [notification?.messageKeyI18n]);

  if (!notification?.messageKeyI18n) return null;

  return (
    <Box
      sx={{
        zIndex: TOAST_NOTIFICATION_Z_INDEX,
        transform: 'translate(-50%)',
        left: '50%',
        top: 0,
        background: 'transparent',
        position: 'fixed',
      }}
    >
      <Box
        sx={{
          padding: '8px',
          marginTop: '16px',
          backgroundColor: themeAccentTertiary,
          borderRadius: '16px',
          width: '300px',
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '8px',
            flexDirection: 'column',
          }}
        >
          <Typography
            variant={TypographyVariant.medium_27}
            sx={{
              fontSize: 17,
              fontWeight: 400,
              margin: '0 auto',
              width: '100%',
              textAlign: 'left',
              lineHeight: '104%',
              letterSpacing: '0.048px',
              color: (theme) => theme?.palette?.accent_foreground,
            }}
          >
            {getI18n(language, notification?.messageKeyI18n)}
          </Typography>
          {!!notification?.descriptionKeyI18n && (
            <Typography
              variant={TypographyVariant.medium_27}
              sx={{
                fontSize: 17,
                fontWeight: 400,
                margin: '0 auto',
                width: '100%',
                textAlign: 'left',
                lineHeight: '104%',
                letterSpacing: '0.048px',
                color: (theme) => theme?.palette?.accent_foreground,
                marginTop: '4px',
                opacity: 0.6,
              }}
            >
              {getI18n(language, notification?.descriptionKeyI18n)}
            </Typography>
          )}
        </Box>
      </Box>
    </Box>
  );
};
