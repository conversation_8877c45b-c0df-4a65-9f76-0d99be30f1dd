import { Box, Button } from '@allo/ui-lib';
import Image from 'next/image';
import PropTypes from 'prop-types';
import IconBack_28 from '~/components/icons/IconBack_28';
import { useAppDispatch, useAppSelector } from '~/store/hooks';
import { selectRestaurant, setScreen } from '~/store/slices/device/deviceSlice';

export const SidebarGeneric = ({ backButtonRoute = null }) => {
  const dispatch = useAppDispatch();
  const restaurant = useAppSelector(selectRestaurant);

  const handleBack = () => {
    if (!backButtonRoute) return;
    dispatch(setScreen(backButtonRoute));
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: '160px',
        minWidth: '160px',
        borderRight: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: (theme) => theme?.palette?.highlight,
      }}
    >
      <Box
        sx={{
          height: '160px',
          minHeight: '160px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <Image src={restaurant.logoUrl} width={80} height={80} alt={'restaurant logo'} />
      </Box>
      <Button
        variant="none"
        onClick={handleBack}
        sx={{
          height: '80px',
          minHeight: '80px',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: (theme) => `1px solid ${theme?.palette?.foreground_6}`,
        }}
      >
        <IconBack_28 />
      </Button>
    </Box>
  );
};

SidebarGeneric.propTypes = {
  backButtonRoute: PropTypes.string,
};
