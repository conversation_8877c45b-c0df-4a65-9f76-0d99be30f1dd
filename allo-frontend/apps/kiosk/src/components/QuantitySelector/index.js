import { Box, IconButton, Typography } from '@allo/ui-lib';
import PropTypes from 'prop-types';

export const QuantitySelector = ({ value, increment, decrement, disableIncrement }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        height: '40px',
        width: '100%',
      }}
    >
      {!!value && (
        <IconButton
          className={'button'}
          onClick={decrement}
          sx={{
            height: '40px',
            width: '44px',
            borderRadius: '10px',
            border: '1px solid #EFEDEC',
            boxShadow: '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
            backgroundColor: (theme) => theme.palette.highlight,
            '&:active': {
              boxShadow: 'none',
              border: '1px solid #EFEDEC',
            },
            '&:focus': {
              boxShadow: '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
              border: '1px solid #EFEDEC',
            },
          }}
          icon={value > 1 ? 'MINUS' : 'THRASH'}
        />
      )}
      {!!value && (
        <Typography
          sx={{
            fontSize: '17px',
            fontWeight: '400',
          }}
        >
          {value}
        </Typography>
      )}
      <IconButton
        className={disableIncrement ? 'button-disabled' : 'button'}
        onClick={increment}
        disabled={disableIncrement}
        sx={{
          height: '40px',
          width: '44px',
          borderRadius: '10px',
          border: '1px solid #EFEDEC',
          boxShadow: disableIncrement
            ? 'none'
            : '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
          backgroundColor: (theme) => theme.palette.highlight,
          opacity: disableIncrement ? 0.4 : 1,
          '&:active': {
            boxShadow: 'none',
            border: '1px solid #EFEDEC',
          },
          '&:focus': {
            boxShadow: disableIncrement
              ? 'none'
              : '0px 3px 0px #F6F5F4, 0px 4px 0px 0px #EFEDEC',
            border: '1px solid #EFEDEC',
          },
        }}
        icon={'PLUS'}
      />
    </Box>
  );
};

QuantitySelector.propTypes = {
  value: PropTypes.number,
  increment: PropTypes.func,
  decrement: PropTypes.func,
  disableIncrement: PropTypes.bool,
};
