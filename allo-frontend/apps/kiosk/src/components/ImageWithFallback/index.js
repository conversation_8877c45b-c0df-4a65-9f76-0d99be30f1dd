import Image from 'next/image';
import PropTypes from 'prop-types';
import { useState } from 'react';
import { cdnUrl } from '~/lib/constants';

/**
 * A wrapper around `next/image` that will display a fallback image if the original
 * image fails to load. If the original image fails to load, it will be replaced
 * by the fallback image.
 *
 * @param {Object} props
 * @param {string} props.src - The URL of the image to load.
 * @param {string} props.fallbackSrc - The URL of the fallback image to load if
 * the original image fails.
 * @param {...Object} [props] - Any other props you'd pass to `next/image`.
 */
const ImageWithFallback = (props) => {
  const { src, fallbackSrc, ...rest } = props;
  const [imgSrc, _setImgSrc] = useState(src ?? fallbackSrc);

  const shortImgSrc = imgSrc
    ? imgSrc.replace('https://storage.googleapis.com/leviee_public/', '')
    : null;
  //console.log(shortImgSrc)
  const dataTwicSrc = 'image:' + shortImgSrc;
  const _dataTwicBackground = 'url(' + fallbackSrc + ')';

  const shortImgSrcDevCdnPreview =
    cdnUrl + '/c/p/twicpics/' + shortImgSrc + '?twic=v1/output=preview';

  const _shortImgSrcDevCdn = cdnUrl + '/c/p/twicpics/' + shortImgSrc;

  //console.log(src + " ->>  " + shortImgSrc + " ->>  " + shortImgSrcDevCdnPreview + " -------> " + fallbackSrc);
  return (
    <div className="isolation">
      <div
        className="cls-optimization"
        style={rest?.height && rest?.width ? { paddingTop: rest?.height } : null}
      >
        <Image
          className="media"
          {...rest}
          src={shortImgSrcDevCdnPreview}
          data-twic-src={dataTwicSrc}
          onError={(e) => {
            if (fallbackSrc) {
              e.target.srcset = fallbackSrc;
              e.target.src = fallbackSrc;
            }
          }}
        />
        <Image
          className="placeholder"
          {...rest}
          src={shortImgSrcDevCdnPreview}
          onError={(e) => {
            if (fallbackSrc) {
              e.target.srcset = fallbackSrc;
              e.target.src = fallbackSrc;
            }
          }}
        />
      </div>
    </div>
  );
};

ImageWithFallback.propTypes = {
  src: PropTypes.string.isRequired,
  fallbackSrc: PropTypes.string.isRequired,
};

export default ImageWithFallback;
