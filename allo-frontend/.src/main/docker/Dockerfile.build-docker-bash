ARG DOCKER_VERSION=28.3.1
ARG ALPINE_VERSION=3.22
ARG DOCKER_BASE_IMAGE=docker:${DOCKER_VERSION}-alpine${ALPINE_VERSION}

FROM ${DOCKER_BASE_IMAGE} AS static-docker-source
FROM gcr.io/google.com/cloudsdktool/google-cloud-cli:alpine

# Comment the line below if you don't want `docker`
COPY --from=static-docker-source /usr/local/bin/docker /usr/local/bin/docker
# Comment the line below if you don't want `docker-buildx`
COPY --from=static-docker-source /usr/local/libexec/docker/cli-plugins/docker-buildx /usr/local/libexec/docker/cli-plugins/docker-buildx


#
# ---- Base Node ----  ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#FROM ${DOCKER_BASE_IMAGE} AS base
RUN    apk add --no-cache \
         bash

RUN    docker --version

# Don't run production as root
#RUN   addgroup --system --gid 1000 allo
#RUN   adduser --system --uid 1000 allo

#USER allo
# ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----   ----
#
