#!/bin/bash
#set -euxo pipefail
set -e

USER=${USER:-`whoami`}
USER_UID=${USER_UID:-`id -u ${USER}`}
USER_GID=${USER_GID:-`id -g ${USER}`}
DIR=`pwd`
test -t 0 && DOCKER_USE_TTY="-it"

BUILD_IMAGE="${BUILD_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/node:20-slim-corepack-turbo}"

# ------------------------------------------------------

echo "- turbo prune .."
docker run ${DOCKER_USE_TTY} --rm \
  -u "${USER_UID}":"${USER_GID}" \
  -v "${DIR}":"${DIR}"  \
  -w "${DIR}" \
  -e BUILD_APP="$BUILD_APP" \
  -e OUTPUT_FOLDER="$OUTPUT_FOLDER" \
  --entrypoint /bin/bash \
  ${BUILD_IMAGE} -c 'export PATH="$(pwd)/node_modules/.bin":$PATH; echo $PATH; turbo prune $BUILD_APP --docker --out-dir "$OUTPUT_FOLDER"'
echo ""

