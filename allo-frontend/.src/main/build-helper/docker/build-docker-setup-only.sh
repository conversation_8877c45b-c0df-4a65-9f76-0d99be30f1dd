#!/bin/bash
#set -euxo pipefail
set -e

USER=${USER:-`whoami`}
USER_UID=${USER_UID:-`id -u ${USER}`}
USER_GID=${USER_GID:-`id -g ${USER}`}
DIR=`pwd`
test -t 0 && DOCKER_USE_TTY="-it"

BUILD_IMAGE="${BUILD_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/node:20-slim-corepack-turbo}"

# ------------------------------------------------------

current=`date +%s`
last_modified="0"
if test -f .npmrc; then last_modified=`date -r .npmrc +%s`; fi

if [ $((current - last_modified)) -gt 3600 ]; then
  echo "- .npmrc .. ${BUILD_IMAGE}"

  gcloud -v >/dev/null 2>&1 || { echo >&2 "E: I require gcloud but it's not installed. Aborting."; exit 1; }

  JS_BUILD_PREFER_CUSTOM_REGISTRY="${JS_BUILD_PREFER_CUSTOM_REGISTRY:-}"
  JS_BUILD_DEFAULT_REGISTRY="${JS_BUILD_DEFAULT_REGISTRY:-https://registry.npmjs.org}"


  JS_BUILD_CUSTOM_SCOPE="${JS_BUILD_CUSTOM_SCOPE:-allo}"
  JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT="${JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT:-endless-gizmo-264508}"
  JS_BUILD_CUSTOM_SCOPE_REGISTRY="${JS_BUILD_CUSTOM_SCOPE_REGISTRY:-https://europe-npm.pkg.dev/$JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT/allo-npm}"
  JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN="${JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN:-$(gcloud auth print-access-token --project=$JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT)}"


  JS_BUILD_REGISTRY_GCLOUD_PROJECT="${JS_BUILD_REGISTRY_GCLOUD_PROJECT:-endless-gizmo-264508}"
  JS_BUILD_REGISTRY="${JS_BUILD_REGISTRY:-https://europe-west3-npm.pkg.dev/$JS_BUILD_REGISTRY_GCLOUD_PROJECT/registry-npmjs-org}"
  JS_BUILD_REGISTRY_AUTHTOKEN="${JS_BUILD_REGISTRY_AUTHTOKEN:-$(gcloud auth print-access-token --project=$JS_BUILD_REGISTRY_GCLOUD_PROJECT)}"

  docker run ${DOCKER_USE_TTY} --rm \
    -u "${USER_UID}":"${USER_GID}" \
    -v "${DIR}":"${DIR}"  \
    -w "${DIR}" \
    -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
    -e JS_BUILD_PREFER_CUSTOM_REGISTRY="${JS_BUILD_PREFER_CUSTOM_REGISTRY}" \
    -e JS_BUILD_DEFAULT_REGISTRY="${JS_BUILD_DEFAULT_REGISTRY}" \
    -e JS_BUILD_CUSTOM_SCOPE="${JS_BUILD_CUSTOM_SCOPE}" \
    -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT="${JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT}" \
    -e JS_BUILD_CUSTOM_SCOPE_REGISTRY="${JS_BUILD_CUSTOM_SCOPE_REGISTRY}" \
    -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN="${JS_BUILD_CUSTOM_SCOPE_REGISTRY_AUTHTOKEN}" \
    -e JS_BUILD_REGISTRY_GCLOUD_PROJECT="${JS_BUILD_REGISTRY_GCLOUD_PROJECT}" \
    -e JS_BUILD_REGISTRY="${JS_BUILD_REGISTRY}" \
    -e JS_BUILD_REGISTRY_AUTHTOKEN="${JS_BUILD_REGISTRY_AUTHTOKEN}" \
    --entrypoint /bin/bash \
   ${BUILD_IMAGE} '.src/main/build-helper/build-setup-only.sh'
   echo ""
fi
