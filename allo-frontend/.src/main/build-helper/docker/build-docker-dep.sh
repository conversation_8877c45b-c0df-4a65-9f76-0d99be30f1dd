#!/bin/bash
#set -euxo pipefail
set -e

USER=${USER:-`whoami`}
USER_UID=${USER_UID:-`id -u ${USER}`}
USER_GID=${USER_GID:-`id -g ${USER}`}
DIR=`pwd`
test -t 0 && DOCKER_USE_TTY="-it"
BUILD_IMAGE="${BUILD_IMAGE:-europe-docker.pkg.dev/iac-dev-432418/allo-docker/allo/node:20-slim-corepack-turbo}"

# ------------------------------------------------------

docker run ${DOCKER_USE_TTY} --rm \
  -u "${USER_UID}":"${USER_GID}" \
  -v "${DIR}":"${DIR}"  \
  -w "${DIR}" \
  -v "${HOME}/.gitconfig":"/home/<USER>/.gitconfig" \
  -e JS_BUILD_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_CUSTOM_SCOPE_REGISTRY_GCLOUD_PROJECT=iac-dev-432418 \
  -e JS_BUILD_PREFER_CUSTOM_REGISTRY="" \
  --entrypoint /bin/bash \
  ${BUILD_IMAGE} '.src/main/build-helper/build-dep.sh'


